const formatTime = (date) => {
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();
  const hour = date.getHours();
  const minute = date.getMinutes();
  const second = date.getSeconds();
  return `${[year, month, day].map(formatNumber).join('-')} ${[
    hour,
    minute,
    second,
  ]
    .map(formatNumber)
    .join(':')}`;
};
const formatNumber = (n) => {
  n = n.toString();
  return n[1] ? n : `0${n}`;
};
// 防止处理多次点击
function noMultipleClicks(methods, info) {
  // methods是需要点击后需要执行的函数， info是点击需要传的参数
  let that = this;
  if (that.noClick) {
    // 第一次点击
    that.noClick = false;
    if (info && info !== '') {
      // info是执行函数需要传的参数
      methods(info);
    } else {
      methods();
    }
    setTimeout(() => {
      that.noClick = true;
    }, 2000);
  } else {
    // 这里是重复点击的判断
    // uni.showToast({
    //     icon: "none",
    //     title: "点击太频繁啦，请稍后再试~"
    // })
  }
}
// 内容替换规则： 碰到。换行空两格展示
const changeText = (content) => {
  let newContent = '';
  if (content && typeof content == 'string') {
    let flag = false;
    newContent = content.trim();
    if (newContent.charAt(newContent.length - 1) == '。') {
      newContent = newContent.slice(0, -1);
      flag = true;
    }
    newContent = newContent.replace(/。/g, '。<br/>&nbsp;&nbsp;');
    newContent = newContent.replace(/#/g, '<br/>&nbsp;&nbsp;');
    newContent = `&nbsp;&nbsp;${newContent}`;
    if (flag) {
      newContent += '。';
    }
    return newContent;
  }
  return newContent;
};
const retBigSrt = (num) => {
  const changeNum = [
    '零',
    '一',
    '二',
    '三',
    '四',
    '五',
    '六',
    '七',
    '八',
    '九',
  ];

  num = parseInt(num);

  // 处理 1-9
  if (num >= 1 && num <= 9) {
    return changeNum[num];
  }

  // 处理 10-19，特殊处理避免"一十"
  if (num >= 10 && num <= 19) {
    if (num === 10) {
      return '十';
    } else {
      return '十' + changeNum[num % 10];
    }
  }

  // 处理 20-99
  if (num >= 20 && num <= 99) {
    const tens = Math.floor(num / 10);
    const ones = num % 10;
    if (ones === 0) {
      return changeNum[tens] + '十';
    } else {
      return changeNum[tens] + '十' + changeNum[ones];
    }
  }

  // 处理 100 及以上的数字，保留原有逻辑
  const unit = ['', '十', '百'];
  const getWan = (temp) => {
    const strArr = temp.toString().split('').reverse();
    let newNum = '';
    for (var i = 0; i < strArr.length; i++) {
      newNum =
        (i === 0 && strArr[i] === 0
          ? ''
          : i > 0 && strArr[i] === 0 && strArr[i - 1] === 0
          ? ''
          : changeNum[strArr[i]] + (strArr[i] === 0 ? unit[0] : unit[i])) +
        newNum;
    }
    return newNum;
  };
  const overWan = Math.floor(num / 100);
  let noWan = num % 100;
  if (noWan.toString().length < 2) noWan = '0' + noWan;
  return overWan ? getWan(overWan) + '百' + getWan(noWan) : getWan(num);
};
// 获取 1-60天数对应的一到六十的list
const getCheckInDayList = () => {
  let list = [];
  for (let i = 1; i < 61; i++) {
    list.push({
      label: `入住第${retBigSrt(i)}天`,
      value: i,
    });
  }
  list.forEach((item) => {
    item.label = item.label.replace(/零/g, '');
  });
  return list;
};
// 获取请帖编辑数据
const translateShowDataFromStore = (store) => {
  const { pages, editList, backGroundImage, backMusicUrl, groupList } = store;
  const result = [];
  pages.forEach((it) => {
    const arr = [];
    it.forEach((item) => {
      const { nodeType } = editList[item];
      if (nodeType === 'ITEM_TYPE_GROUP') {
        const groupItems = groupList[item];
        const { rect } = editList[item];
        groupItems.forEach((key) => {
          const obj = editList[key];
          Object.assign(obj.rect, {
            top: rect.top + obj.rect.top,
            left: rect.left + obj.rect.left,
          });
          arr.push(obj);
        });
        return;
      }
      const obj = editList[item];
      if (obj) {
        arr.push({
          pageKey: item,
          ...obj,
        });
      }
    });
    result.push(arr);
  });
  return result;
};

/**
 * 对象转URL 参数
 * @param data Object
 * @returns {string}
 */
const queryParams = (data) => {
  let str = '';
  Object.keys(data).forEach((key) => {
    str += `${key}=${data[key]}&`;
  });
  return str.slice(0, -1);
};

// 导出
export default {
  getCheckInDayList,
  changeText,
  noMultipleClicks, //禁止多次点击
  formatTime,
  translateShowDataFromStore,
  queryParams,
  retBigSrt, // 数字转中文
};
