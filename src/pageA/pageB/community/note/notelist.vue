<template>
    <view class="note-list">
        <navigation background="#ffffff" :title="bgTitle" :isSowArrow="true"></navigation>
        <view class="card" v-for="(item, index) in pageList" :key="index">
            <service-card-vue :item="item"></service-card-vue>
            <img-content-area-vue :listData="item" :index="index" @onDetail="details"></img-content-area-vue>
        </view>
        <u-back-top :scroll-top="scrollTop" top="1000"></u-back-top>
    </view>
</template>
<script setup>
    import imgContentAreaVue from "@/components/imgContentArea.vue";
    import serviceCardVue from "@/pageA/pageB/community/component/serviceCard.vue";
    import {
        onShow,
        onHide,
        onLoad,
        onPageScroll,
        onReachBottom,
        onShareAppMessage,
        onShareTimeline
    } from "@dcloudio/uni-app";
    import {
        ref,
        nextTick,
        reactive,
        getCurrentInstance,
        onMounted
    } from "vue";
    import {
        useStore
    } from "vuex";
    const store = useStore();
    const instance = getCurrentInstance();
    const App = instance.appContext.config.globalProperties
    onShareAppMessage(() => {

    })
    onShareTimeline(() => {

    })
    const bgTitle = ref('胡哥')
    const scrollTop = ref(0)
    const pageList = ref([])
    const types = ref(1)
    const pageNum = ref(1)
    const pageSize = ref(4)
    const taskNodeId = ref('')
    onLoad((options) => {
        console.log('optionssssss', options)
        taskNodeId.value = options.taskNodeId
        bgTitle.value = options.nodeName
    })
    onMounted(async () => {
        await getList()
    })
    onReachBottom(() => {
        if (types.value == 1) {
            pageNum.value = pageNum.value + 1
            getList();
        }
    })
    onPageScroll((e) => {
        scrollTop.value = e.scrollTop
    })
    const details = () => {

    }
    const getList = async () => {
        let data = {
            pageSize: pageSize.value,
            pageNum: pageNum.value,
            taskNodeId: taskNodeId.value
        };
        store.commit("m_user/changeLoading", true);
        const res = await App.$axios.get(App.$api.getDiaryList, data)
        try {
            if (res.data.code == 200) {
                let data = res.data.rows;
                types.value = data.length == 4 ? 1 : 2;
                data.forEach((item) => {
                    if (item.imgs && item.imgs.length > 0) {
                        item.contentPhotos = item.imgs
                    }
                    if (item.content.length > 115) {
                        item.packUpShow = false;
                        item.moreBtns = true;
                        item.moreBtn = true;
                    } else {
                        item.packUpShow = false;
                        item.moreBtns = false;
                        item.moreBtn = false;
                    }

                })
                pageList.value = pageList.value.concat(data);
            }
        } catch (e) {

        }
        store.commit("m_user/changeLoading", false);
    }
</script>
<style lang="less" scoped>
    @import "./notelist.less";
</style>