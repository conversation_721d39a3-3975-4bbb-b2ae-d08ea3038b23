<template>
  <view v-if="Object.keys(item).length > 0">
    <view v-if="showContent" @tap="onDetail">
      <view v-if="pageType == 'note'" class="note">
        {{ item.content }}
      </view>
      <view style="white-space: pre-wrap" v-else v-html="content"> </view>
    </view>
    <view v-else @tap="onDetail">
      <view
        :class="item.moreBtns ? 'beyond' : ''"
        :data-postid="item.postId"
        v-html="content"
      >
      </view>
      <view class="packUp" v-if="item.moreBtn" @tap.stop="packUp">
        {{ item.packUpShow ? '收回' : '展开' }}
      </view>
    </view>
    <!-- 视频 -->
    <view class="img_box" v-if="item.videos && item.videos.length > 0">
      <view class="videos" v-for="(res, index1) in item.videos" :key="index1">
        <video
          :id="`myVideo${index}`"
          @touchstart="onTouchStart"
          @touchend="onTouchEnd"
          class="video"
          :src="res"
          @tap="previewVideo"
          :data-url="item.video"
          :data-src="res"
          :data-index="index1"
          :controls="true"
        ></video>
      </view>
    </view>
    <!-- 图片  v-if="!item.videos"-->
    <view class="img_box" v-else>
      <template v-if="item.imgs && item.imgs.length > 0">
        <view
          :id="`loadImg${index}`"
          :class="[
            'loadImg',
            item.imgs.length > 1 ? 'many_img' : '',
            item.listShow || pageType == 'note' ? 'active' : '',
          ]"
        >
          <view
            class="img_item many"
            v-for="(res, index1) in item.imgs"
            :key="index1"
          >
            <image
              v-if="item.listShow || pageType == 'note'"
              class="img"
              :src="res + '?x-oss-process=image/quality,q_60'"
              @tap="previewImage"
              :data-url="item.imgs"
              :data-src="res"
              :data-sources="item.imgs"
              :data-index="index1"
              mode="aspectFill"
            ></image>
          </view>
        </view>
      </template>
    </view>
  </view>
</template>

<script>
import { nextTick } from 'vue';
import utils from '@/utils/util.js';
export default {
  props: {
    listData: {
      type: Object,
      default: () => {},
    },
    // 懒加载id
    index: {
      type: Number,
      default: 0,
    },
    // 是否控制展开内容
    showContent: {
      type: Boolean,
      default: true,
    },
    // 服务笔记展示数据需要特殊处理
    pageType: {
      type: String,
      default: 'static',
    },
  },
  data() {
    return {
      observe: null,
      videoContext: null,
      isZoomed: false, // 是否缩放状态
    };
  },
  mounted() {
    this.showImg();
    this.videoContext = uni.createVideoContext(`myVideo${this.index}`, this);
  },
  beforeDestroy() {
    this.observe.disconnect();
  },
  computed: {
    item() {
      if (this.pageType == 'note') {
        // 展示图片
        if (this.listData.imgs && this.listData.imgs.length > 2) {
          this.listData.imgs = this.listData.imgs.slice(0, 2);
        }
      }
      return this.listData;
    },
    content() {
      return this.item.content;
    },
  },
  methods: {
    onTouchStart() {
      this.isZoomed = true; // 触摸开始时设置为缩放状态
    },
    onTouchEnd() {
      this.isZoomed = false; // 触摸结束时设置为非缩放状态
      if (!this.isZoomed) {
        this.pauseVideo(); // 如果不是缩放状态，暂停视频播放
      }
    },
    pauseVideo() {
      if (this.videoContext) {
        this.videoContext.pause();
      }
    },
    showImg() {
      let timer = setTimeout(() => {
        this.$nextTick(() => {
          this.observe = uni
            .createIntersectionObserver(this, {
              thresholds: [0],
            })
            .relativeToViewport()
            .observe('#loadImg' + this.index, (ret) => {
              if (ret.intersectionRatio > 0) {
                this.item.listShow = true;
                timer = null;
              }
            });
        });
      }, 500);
    },
    packUp(e) {
      this.item.packUpShow = !this.item.packUpShow;
      this.item.moreBtns = !this.item.moreBtns;
    },
    onDetail() {
      this.$emit('onDetail', this.item);
    },
    previewVideo(event) {
      this.preview('video', event);
    },
    previewImage(event) {
      this.preview('image', event);
    },
    preview(type, event) {
      let that = this;
      let url = event.currentTarget.dataset.url;
      let src = event.currentTarget.dataset.src;
      let index = event.currentTarget.dataset.index;
      console.log('previewwww', url, src, index);
      let maparr = [];
      if (type == 'video') {
        maparr.push({
          type,
          url: src,
        });
      } else {
        url.forEach((item) => {
          maparr.push({
            type,
            url: item,
          });
        });
      }
      // 既有视频又有图片用这个
      uni.previewMedia({
        sources: maparr,
        // 官方文档 : 素材展示列表 是个数组对象 一个url链接 一个type 判断这是视频还是图片
        // 接收image 和 video 所以处理数据时 最好给个type 指定 image 或者 video
        current: index,
        // 当前显示的资源序号
        autoplay: true,
      });
    },
  },
};
</script>

<style lang="less" scoped>
.beyond {
  font-size: 28rpx;
  color: #333333;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 5;
}

.packUp {
  color: #4d669b;
  font-size: 30rpx;
}

.img_box {
  margin-top: 20rpx;
  padding-left: 4rpx;
}

.videos {
  width: 48%;
  height: 280rpx;
}

.videos video {
  width: 100%;
  height: 100%;
  border-radius: 10rpx;
  display: block;
}

.img_box .many_img {
  display: flex;
  flex-wrap: wrap;
}

.img_item.many {
  width: 49%;
  height: 280rpx;
  margin-bottom: 10rpx;
  border-radius: 10px;
  overflow: hidden;
}

.img_item.many image {
  width: 100%;
  height: 100%;
}

.img_item.many:nth-child(2n) {
  margin-left: 1%;
}

.loadImg.active {
  transition: all 2.5s ease-in-out;
  opacity: 1;
}

.note {
  height: 80rpx;
  font-weight: 400;
  font-size: 23rpx;
  color: #333333;
  line-height: 40rpx;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  flex-wrap: wrap;
  white-space: pre-wrap;
}
</style>
