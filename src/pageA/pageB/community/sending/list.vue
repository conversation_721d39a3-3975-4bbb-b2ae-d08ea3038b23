<template>
    <view class="main">
        <view class="mains">
            <view class="title" style="margin-bottom: 20rpx">优质动态宝妈（{{total}}）</view>
            <view class="list" @tap="homepage(item)" v-for="(item, index) in customerPage" :key="index">
                <image :src="item.avatar || $defaultAvatar"></image>
                <view class="content">
                    <view class="left">
                        <view class="flex">
                            <view class="name">{{item.name}}</view>
                            <!-- <view class="tag">入住第32天</view> -->
                        </view>
                        <view class="title">优质动态宝妈</view>
                    </view>
                    <view class="right">进入主页</view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
    export default {
        data() {
            return {
                customerPage: [],
                pageSize: 10,
                pageNum: 1,
                types: '',
                bgColor: '',
                total: 0
            };
        },
        /**
         * 生命周期函数--监听页面加载
         */
        onLoad(options) {
            this.customerPageFun();
        },
        /**
         * 生命周期函数--监听页面初次渲染完成
         */
        onReady() {},
        /**
         * 生命周期函数--监听页面显示
         */
        onShow() {},
        /**
         * 生命周期函数--监听页面隐藏
         */
        onHide() {},
        /**
         * 生命周期函数--监听页面卸载
         */
        onUnload() {},
        /**
         * 页面相关事件处理函数--监听用户下拉动作
         */
        onPullDownRefresh() {},
        /**
         * 页面上拉触底事件的处理函数
         */
        onReachBottom() {
            if (this.types == 1) {
                this.setData({
                    pageNum: this.pageNum + 1
                });
                this.customerPageFun();
            }
        },
        onPageScroll: function(e) {
            if (e.scrollTop > 0) {
                this.setData({
                    bgColor: '#ffffff'
                });
            } else if (e.scrollTop < 2) {
                this.setData({
                    bgColor: ''
                });
            }
        },
        /**
         * 用户点击右上角分享
         */
        onShareAppMessage() {},

        onShareTimeline() {},
        methods: {
            homepage(item) {
                let params = {
                    userId: item.userId,
                    momId: item.momId,
					uuid:item.uuid
                };
                this.$jumpPage("/pageA/pageB/community/note/notedetail", params);

            },
            customerPageFun() {
                // 宝妈说
                let data = {
                    pageSize: this.pageSize,
                    pageNum: this.pageNum,
                    isShow: true
                };
                this.$axios.get(this.$api.getMomPage, data).then((res) => {
                    if (res.data.code == 200) {
                        let data = res.data.rows;
                        let types = data.length == 10 ? 1 : 2;
                        let dtnow;
                        if (this.pageNum == 1) {
                            dtnow = [];
                        } else {
                            dtnow = this.customerPage;
                        }
                        this.setData({
                            customerPage: dtnow.concat(data),
                            types: types,
                            total: res.data.total
                        });
                    }
                });
            },

            next(e) {
                let userId = e.currentTarget.dataset.userid;
                let postId = e.currentTarget.dataset.postid;
                uni.navigateTo({
                    url: '/pageA/pageB/community/sending/detail?userId=' + userId + '&postId=' + postId
                });
            }
        }
    };
</script>
<style scoped>
    @import './list.css';
</style>