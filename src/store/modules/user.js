export default {
    namespaced: true,
    state: () => ({
        user_isLogin: false,
        user_loading: false, // 全局request loading
        avatorName: false, // 设置头像
        // 未读消息数量
        unreadCounts: {
            userUnreadCount: 0,    // 用户未读消息数（作为普通用户）
            agentUnreadCount: 0,   // 客服未读消息数（作为客服）
            totalUnreadCount: 0    // 总未读消息数
        }
    }),
    getters: {
        // 获取总未读数量（用于tabbar徽章）
        getTotalUnreadCount: (state) => state.unreadCounts.totalUnreadCount,
        // 获取用户未读数量（用于"我的消息"）
        getUserUnreadCount: (state) => state.unreadCounts.userUnreadCount,
        // 获取客服未读数量（用于"客服中心"）
        getAgentUnreadCount: (state) => state.unreadCounts.agentUnreadCount
    },
    mutations: {
        setAvatorName(state, show) {
            state.avatorName = show
        },
        changeLoading(state, isLoading) {
            state.user_loading = isLoading
        },
        setLoginStatus(state, login) {
            state.user_isLogin = login
        },
        checkLogin(state) {
            if (!state.user_isLogin) {
                uni.navigateTo({
                    url: '/pageA/login'
                })
            }
        },
        // 设置未读消息数量
        setUnreadCounts(state, counts) {
            state.unreadCounts = {
                userUnreadCount: counts.userUnreadCount || 0,
                agentUnreadCount: counts.agentUnreadCount || 0,
                totalUnreadCount: counts.totalUnreadCount || 0
            }
        },
        // 清空未读消息数量
        clearUnreadCounts(state) {
            state.unreadCounts = {
                userUnreadCount: 0,
                agentUnreadCount: 0,
                totalUnreadCount: 0
            }
        }
    },
};