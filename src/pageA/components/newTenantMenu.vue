<template>
	<view class="tenant-menu-container">
		<view class="tenant-menu">
			<view class="menu-main">
				<!-- 月子套餐 -->
				<view class="menu-left" @click="handleMenuClick(0)">
					<view class="menu-title">
						<text>月子套餐</text>
						<text>专业舒适的房间</text>
					</view>
					<image src="@/static/images/home/<USER>" />
				</view>
				<view class="menu-right">
					<!-- 月子膳食 -->
					<view class="menu-top" @click="handleMenuClick(1)">
						<view class="menu-title">
							<text>月子膳食</text>
							<text>精选食材烹饪</text>
						</view>
						<image src="@/static/images/home/<USER>" />
					</view>
					<!-- 护理团队 -->
					<view class="menu-bottom" @click="handleMenuClick(2)">
						<view class="menu-title">
							<text>护理团队</text>
							<text>贴心专业服务</text>
						</view>
						<image src="@/static/images/home/<USER>" />
					</view>
				</view>
			</view>
			<view class="menu-tabs">
				<view class="menu-tab" v-for="tab in visibleTabs" :key="tab.key" @click="handleMenuClick(tab.key)">
					<view class="tab-img">
						<image :src="tab.icon" />
					</view>
					<view class="tab-text">{{ tab.name }}</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
	import {
		onMounted,
		ref,
		computed
	} from 'vue';

	const tenantId = ref('');

	// 统一的菜单配置
	const menuItems = {
		// 主菜单项
		0: {
			name: '月子套餐',
			path: '/pageA/pageB/home/<USER>',
			type: 'navigate',
		},
		1: {
			name: '月子膳食',
			path: '/pageA/pageB/home/<USER>',
			type: 'navigate'
		},
		2: {
			name: '护理团队',
			path: '/pageA/pageB/home/<USER>',
			type: 'navigate',
		},

		// 底部菜单项
		// introduce: {
		// 	name: '品牌介绍',
		// 	path: '/pageA/pageB/home/<USER>',
		// 	type: 'navigate',
		// 	icon: 'http://cdn.xiaodingdang1.com/2024/07/31/70c3e33ce76841dc88fd831dd18e7fed.png',
		// 	showCondition: (tenantId) => false,
		// },
		happyEver: {
			name: '馨喜如初',
			appId: 'wx9869e3ffafa073e2',
			path: 'pages/home/<USER>/index',
			type: 'miniProgram',
			icon: 'http://cdn.xiaodingdang1.com/2025/06/12/32e0b960abc9493baceea98c2d7452f0.png',
			showCondition: (tenantId) => true,
		},
		device: {
			name: '会所设施',
			path: '/pageA/pageB/home/<USER>',
			type: 'navigate',
			icon: 'http://cdn.xiaodingdang1.com/2025/06/12/91a39cdab3ad449bb62c5c2dd32875d9.png',
			showCondition: () => true,
		},
		invite: {
			name: '产后康复',
			path: '/pageA/pageB/home/<USER>',
			type: 'navigate',
			icon: 'http://cdn.xiaodingdang1.com/2025/06/12/1757c2c7662e41518bf973f5e3967650.png',
			showCondition: () => true,
		},
		maternitymatron: {
			name: '移动月嫂',
			path: '/pageA/pageB/home/<USER>',
			type: 'navigate',
			icon: 'http://cdn.xiaodingdang1.com/2025/06/12/8cc62714128840028c6566a1a25f7a7c.png',
			showCondition: () => true,
		},
		rehabilitation: {
			name: '宝宝请帖',
			path: '/pageA/pageB/home/<USER>',
			type: 'navigate',
			icon: 'http://cdn.xiaodingdang1.com/2025/06/12/877fc2e1b41340519986c90cbc8a7626.png',
			showCondition: () => true,
		},
	};

	// 计算属性：根据tenantId过滤显示的tabs
	const visibleTabs = computed(() => {
		const tabKeys = [
			// 'introduce',
			'happyEver',
			'device',
			'invite',
			'maternitymatron',
			'rehabilitation',
		];
		return tabKeys
			.map((key) => ({
				key,
				...menuItems[key]
			}))
			.filter((tab) =>
				tab.showCondition ? tab.showCondition(tenantId.value) : true,
			);
	});

	onMounted(() => {
		tenantId.value = uni.getStorageSync('tenantId');
	});

	// 内部跳转处理函数
	const handleMenuClick = (menuKey) => {
		const menuItem = menuItems[menuKey];

		if (!menuItem) {
			console.warn(`未找到菜单配置: ${menuKey}`);
			return;
		}

		if (menuItem.type === 'miniProgram') {
			// 小程序跳转
			uni.navigateToMiniProgram({
				appId: menuItem.appId,
				path: menuItem.path,
				success: () => console.log(`跳转${menuItem.name}成功`),
				fail: (err) => console.error(`跳转${menuItem.name}失败:`, err),
			});
		} else {
			// 普通页面跳转
			uni.navigateTo({
				url: menuItem.path,
				success: () => console.log(`跳转${menuItem.name}成功`),
				fail: (err) => console.error(`跳转${menuItem.name}失败:`, err),
			});
		}
	};
</script>

<style lang="scss" scoped>
	.tenant-menu-container {
		width: 100%;
		padding: 0 24rpx;
		margin-bottom: 20rpx;

		.tenant-menu {
			border-radius: 20rpx;
			border: 3rpx solid #fff;
			background: rgba(255, 255, 255, 0.6);
			box-shadow: -4px -4px 4px 0px rgba(246, 246, 246, 0.5) inset,
				4px 4px 4px 0px rgba(246, 246, 246, 0.5) inset;
			backdrop-filter: blur(5px);
			padding: 14rpx;
		}

		.menu-main {
			display: flex;
			margin-bottom: 20rpx;
		}

		.menu-left {
			flex: 1;
			display: flex;
			flex-direction: column;
			align-items: flex-end;
			// justify-content: space-between;
			padding: 16rpx 41rpx 22rpx 42rpx;
			filter: drop-shadow(0px 0px 6px rgba(178, 178, 178, 0.25));
			background: url(@/static/images/home/<USER>/100% 100% no-repeat;
			height: 200rpx;

			.menu-title {
				width: 100%;

				text:nth-child(1) {
					color: #323232;
					font-weight: bold;
					font-size: 32rpx;
				}

				text:nth-child(2) {
					font-size: 24rpx;
					color: #5C5C5C;
				}
			}

			image {
				width: 116rpx;
				height: 96rpx;
			}
		}

		.menu-right {
			display: flex;
			flex-direction: column;
			align-items: flex-end;
			row-gap: 12rpx;

			.menu-top {
				display: flex;
				justify-content: space-between;
				align-items: center;
				width: 312rpx;
				padding: 15rpx 30rpx;
				background: url(@/static/images/home/<USER>/100% 100% no-repeat;
				filter: drop-shadow(0px 0px 6px rgba(178, 178, 178, 0.25));
				height: 95rpx;

				image {
					width: 68rpx;
					height: 68rpx;
				}
			}

			.menu-bottom {
				display: flex;
				justify-content: space-between;
				align-items: center;
				width: 326rpx;
				padding: 15rpx 30rpx;
				filter: drop-shadow(0px 0px 6px rgba(178, 178, 178, 0.25));
				background: url(@/static/images/home/<USER>/100% 100% no-repeat;
				height: 95rpx;
				image {
					width: 68rpx;
					height: 68rpx;
				}
			}
		}

		.menu-title {
			display: flex;
			flex-direction: column;

			text:nth-child(1) {
				color: #323232;
				font-size: 24rpx;
				font-weight: bold;
			}

			text:nth-child(2) {
				font-size: 20rpx;
				color: #5C5C5C;
			}
		}
	}

	.menu-tabs {
		display: flex;
		justify-content: space-between;

		.menu-tab {
			display: flex;
			flex-direction: column;
			align-items: center;
			padding: 10rpx;
			background: rgba(255, 255, 255, 0.1);
			border-radius: 10rpx;
		}

		.tab-img {
			width: 62rpx;
			height: 62rpx;

			image {
				width: 100%;
				height: 100%;
			}
		}

		.tab-text {
			font-size: 22rpx;
			color: #333333;
			text-align: center;
		}
	}
</style>