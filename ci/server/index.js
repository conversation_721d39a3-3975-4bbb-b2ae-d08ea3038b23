#!/usr/bin/env node

/**
 * 小程序 CI 图形化管理服务器
 * 提供 Web 界面进行项目配置和操作
 */

const express = require('express');
const cors = require('cors');
const multer = require('multer');
const WebSocket = require('ws');
const http = require('http');
const path = require('path');
const fs = require('fs');
const { spawn } = require('child_process');
const chokidar = require('chokidar');

// 导入配置
const { projects, defaultConfig, getProject, validateProject, getProjectNames, jsonManager } = require('../config/config-loader.js');
const JsonConfigManager = require('../config/json-manager.js');

const app = express();
const server = http.createServer(app);
const wss = new WebSocket.Server({ server });

// 配置管理器实例
const configManager = jsonManager;

// 辅助函数：获取最新的项目数据
function getLatestProject(projectId) {
  const result = configManager.getAllProjects();
  if (!result.success) {
    return null;
  }
  return result.data.find(p => p.id === projectId);
}

// 中间件
app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname, 'public')));

// 配置文件上传
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.resolve(__dirname, '../private');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    // 确保文件名以 .key 结尾
    const filename = file.originalname.endsWith('.key') 
      ? file.originalname 
      : file.originalname + '.key';
    cb(null, filename);
  }
});

const upload = multer({ 
  storage,
  fileFilter: (req, file, cb) => {
    // 只允许上传 .key 文件
    if (file.originalname.endsWith('.key') || file.mimetype === 'application/octet-stream') {
      cb(null, true);
    } else {
      cb(new Error('只允许上传 .key 格式的私钥文件'));
    }
  }
});

// WebSocket 连接管理
const clients = new Set();

wss.on('connection', (ws) => {
  clients.add(ws);
  console.log('客户端已连接');

  ws.on('close', () => {
    clients.delete(ws);
    console.log('客户端已断开');
  });
});

// 广播消息到所有客户端
function broadcast(message) {
  const data = JSON.stringify(message);
  clients.forEach(client => {
    if (client.readyState === WebSocket.OPEN) {
      client.send(data);
    }
  });
}

// API 路由

// ==================== 项目管理 API ====================

// 获取所有项目（支持 CRUD）
app.get('/api/projects/all', (req, res) => {
  try {
    const result = configManager.getAllProjects();
    res.json(result);
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 创建项目
app.post('/api/projects', (req, res) => {
  try {
    const result = configManager.createProject(req.body);
    if (result.success) {
      // 重新加载配置以更新缓存
      try {
        const { reloadConfig } = require('../config/config-loader.js');
        reloadConfig();
      } catch (reloadError) {
        console.warn('重新加载配置失败:', reloadError.message);
      }

      broadcast({
        type: 'projectCreated',
        projectId: req.body.id,
        project: req.body
      });
    }
    res.json(result);
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 更新项目
app.put('/api/projects/:id', (req, res) => {
  try {
    const { id } = req.params;
    const result = configManager.updateProject(id, req.body);
    if (result.success) {
      // 重新加载配置以更新缓存
      try {
        const { reloadConfig } = require('../config/config-loader.js');
        reloadConfig();
      } catch (reloadError) {
        console.warn('重新加载配置失败:', reloadError.message);
      }

      broadcast({
        type: 'projectUpdated',
        projectId: id,
        project: req.body
      });
    }
    res.json(result);
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 删除项目
app.delete('/api/projects/:id', (req, res) => {
  try {
    const { id } = req.params;
    const result = configManager.deleteProject(id);
    if (result.success) {
      // 重新加载配置以更新缓存
      try {
        const { reloadConfig } = require('../config/config-loader.js');
        reloadConfig();
      } catch (reloadError) {
        console.warn('重新加载配置失败:', reloadError.message);
      }

      broadcast({
        type: 'projectDeleted',
        projectId: id
      });
    }
    res.json(result);
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 获取备份列表
app.get('/api/config/backups', (req, res) => {
  try {
    const result = configManager.getBackups();
    res.json(result);
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 恢复备份
app.post('/api/config/restore', (req, res) => {
  try {
    const { backupName } = req.body;
    const result = configManager.restoreBackup(backupName);
    if (result.success) {
      broadcast({
        type: 'configRestored',
        backupName
      });
    }
    res.json(result);
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// ==================== 项目操作 API ====================

// 重新加载配置
app.post('/api/config/reload', (req, res) => {
  try {
    const { reloadConfig } = require('../config/config-loader.js');
    const newConfig = reloadConfig();

    broadcast({
      type: 'configReloaded',
      timestamp: Date.now()
    });

    res.json({
      success: true,
      message: '配置已重新加载',
      data: {
        projectCount: Object.keys(newConfig.projects).length
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 获取项目列表
app.get('/api/projects', (req, res) => {
  try {
    // 使用 configManager 获取最新的项目数据
    const result = configManager.getAllProjects();
    if (!result.success) {
      return res.status(500).json(result);
    }

    const projectList = result.data.map(project => {
      // 验证项目配置
      const validation = validateProject(project.id);
      const privateKeyExists = fs.existsSync(project.privateKeyPath);
      const buildPathExists = fs.existsSync(project.projectPath);

      return {
        id: project.id,
        name: project.name,
        appid: project.appid,
        environments: Object.keys(project.environments),
        status: {
          configValid: validation.valid,
          privateKeyExists,
          buildPathExists,
          error: validation.error
        }
      };
    });

    res.json({
      success: true,
      data: projectList
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 获取项目详情
app.get('/api/projects/:id', (req, res) => {
  try {
    const { id } = req.params;

    // 使用 configManager 获取最新的项目数据
    const result = configManager.getAllProjects();
    if (!result.success) {
      return res.status(500).json(result);
    }

    const project = result.data.find(p => p.id === id);
    if (!project) {
      return res.status(404).json({
        success: false,
        error: `项目 "${id}" 不存在`
      });
    }

    const validation = validateProject(id);
    const privateKeyExists = fs.existsSync(project.privateKeyPath);
    const buildPathExists = fs.existsSync(project.projectPath);

    res.json({
      success: true,
      data: {
        ...project,
        status: {
          configValid: validation.valid,
          privateKeyExists,
          buildPathExists,
          error: validation.error
        }
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 上传私钥文件
app.post('/api/projects/:id/upload-key', upload.single('privateKey'), (req, res) => {
  try {
    const { id } = req.params;

    // 获取最新的项目数据
    const project = getLatestProject(id);
    if (!project) {
      return res.status(404).json({
        success: false,
        error: `项目 "${id}" 不存在`
      });
    }

    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: '请选择私钥文件'
      });
    }

    // 重命名文件为项目对应的名称
    const expectedFilename = path.basename(project.privateKeyPath);
    const uploadedPath = req.file.path;
    const targetPath = path.resolve(__dirname, '../private', expectedFilename);

    if (uploadedPath !== targetPath) {
      fs.renameSync(uploadedPath, targetPath);
    }

    // 设置文件权限
    fs.chmodSync(targetPath, 0o600);

    broadcast({
      type: 'privateKeyUploaded',
      projectId: id,
      filename: expectedFilename
    });

    res.json({
      success: true,
      message: '私钥文件上传成功',
      filename: expectedFilename
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 执行构建
app.post('/api/projects/:id/build', (req, res) => {
  try {
    const { id } = req.params;
    const { environment } = req.body;

    // 获取最新的项目数据
    const project = getLatestProject(id);
    if (!project) {
      return res.status(404).json({
        success: false,
        error: `项目 "${id}" 不存在`
      });
    }

    const envConfig = project.environments[environment];

    if (!envConfig) {
      return res.status(400).json({
        success: false,
        error: `环境 "${environment}" 不存在`
      });
    }

    // 启动构建进程
    const buildCommand = envConfig.buildCommand.split(' ');
    const command = buildCommand[0];
    const args = buildCommand.slice(1);

    const buildProcess = spawn(command, args, {
      cwd: path.resolve(__dirname, '../..'),
      stdio: 'pipe'
    });

    const processId = Date.now().toString();

    // 发送构建开始消息
    broadcast({
      type: 'buildStarted',
      projectId: id,
      environment,
      processId
    });

    // 监听输出
    buildProcess.stdout.on('data', (data) => {
      broadcast({
        type: 'buildOutput',
        projectId: id,
        processId,
        output: data.toString()
      });
    });

    buildProcess.stderr.on('data', (data) => {
      broadcast({
        type: 'buildError',
        projectId: id,
        processId,
        error: data.toString()
      });
    });

    buildProcess.on('close', (code) => {
      broadcast({
        type: 'buildCompleted',
        projectId: id,
        processId,
        success: code === 0,
        exitCode: code
      });
    });

    res.json({
      success: true,
      message: '构建已启动',
      processId
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 执行上传
app.post('/api/projects/:id/upload', (req, res) => {
  try {
    const { id } = req.params;
    const { environment, version, desc } = req.body;

    // 获取最新的项目数据
    const project = getLatestProject(id);
    if (!project) {
      return res.status(404).json({
        success: false,
        error: `项目 "${id}" 不存在`
      });
    }

    // 检查私钥文件
    if (!fs.existsSync(project.privateKeyPath)) {
      return res.status(400).json({
        success: false,
        error: '私钥文件不存在，请先上传私钥文件'
      });
    }

    // 启动上传进程
    const args = [
      path.resolve(__dirname, '../scripts/upload.js'),
      id,
      environment
    ];

    if (version) args.push(version);
    if (desc) args.push(desc);

    const uploadProcess = spawn('node', args, {
      cwd: path.resolve(__dirname, '../..'),
      stdio: 'pipe'
    });

    const processId = Date.now().toString();

    // 发送上传开始消息
    broadcast({
      type: 'uploadStarted',
      projectId: id,
      environment,
      version,
      desc,
      processId
    });

    // 监听输出
    uploadProcess.stdout.on('data', (data) => {
      broadcast({
        type: 'uploadOutput',
        projectId: id,
        processId,
        output: data.toString()
      });
    });

    uploadProcess.stderr.on('data', (data) => {
      broadcast({
        type: 'uploadError',
        projectId: id,
        processId,
        error: data.toString()
      });
    });

    uploadProcess.on('close', (code) => {
      broadcast({
        type: 'uploadCompleted',
        projectId: id,
        processId,
        success: code === 0,
        exitCode: code
      });
    });

    res.json({
      success: true,
      message: '上传已启动',
      processId
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 生成预览
app.post('/api/projects/:id/preview', (req, res) => {
  try {
    const { id } = req.params;
    const { environment, pagePath, searchQuery } = req.body;

    // 获取最新的项目数据
    const project = getLatestProject(id);
    if (!project) {
      return res.status(404).json({
        success: false,
        error: `项目 "${id}" 不存在`
      });
    }

    // 检查私钥文件
    if (!fs.existsSync(project.privateKeyPath)) {
      return res.status(400).json({
        success: false,
        error: '私钥文件不存在，请先上传私钥文件'
      });
    }

    // 启动预览进程
    const args = [
      path.resolve(__dirname, '../scripts/preview.js'),
      id,
      environment
    ];

    if (pagePath) {
      args.push('--page');
      args.push(pagePath);
    }
    if (searchQuery) {
      args.push('--query');
      args.push(searchQuery);
    }

    const previewProcess = spawn('node', args, {
      cwd: path.resolve(__dirname, '../..'),
      stdio: 'pipe'
    });

    const processId = Date.now().toString();

    // 发送预览开始消息
    broadcast({
      type: 'previewStarted',
      projectId: id,
      environment,
      pagePath,
      searchQuery,
      processId
    });

    // 监听输出
    previewProcess.stdout.on('data', (data) => {
      broadcast({
        type: 'previewOutput',
        projectId: id,
        processId,
        output: data.toString()
      });
    });

    previewProcess.stderr.on('data', (data) => {
      broadcast({
        type: 'previewError',
        projectId: id,
        processId,
        error: data.toString()
      });
    });

    previewProcess.on('close', (code) => {
      broadcast({
        type: 'previewCompleted',
        projectId: id,
        processId,
        success: code === 0,
        exitCode: code
      });
    });

    res.json({
      success: true,
      message: '预览生成已启动',
      processId
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 获取预览二维码
app.get('/api/projects/:id/preview-qrcode', (req, res) => {
  try {
    const { id } = req.params;
    const { environment } = req.query;
    
    const qrcodePath = path.resolve(__dirname, `../../preview-${id}-${environment}.jpg`);
    
    if (!fs.existsSync(qrcodePath)) {
      return res.status(404).json({
        success: false,
        error: '预览二维码不存在'
      });
    }

    res.sendFile(qrcodePath);
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 启动服务器
const PORT = process.env.PORT || 3001;

server.listen(PORT, () => {
  console.log(`🚀 小程序 CI 管理服务器已启动`);
  console.log(`📱 Web 界面: http://localhost:${PORT}`);
  console.log(`🔌 WebSocket: ws://localhost:${PORT}`);
});
