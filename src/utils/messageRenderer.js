/**
 * 消息渲染工具
 * 使用 marked 解析 Markdown，配合 mp-html 在小程序中渲染
 */

import { marked } from 'marked';

/**
 * 配置 marked 选项
 */
const configureMarked = () => {
  // 设置 marked 选项
  marked.setOptions({
    // 启用换行符转换
    breaks: true,
    // 启用 GitHub 风格的 Markdown
    gfm: true,
    // 禁用危险的 HTML
    sanitize: false,
    // 启用智能引号
    smartypants: true,
    // 启用表格支持
    tables: true,
    // 启用任务列表
    taskLists: true,
  });

  // 自定义渲染器
  const renderer = new marked.Renderer();

  marked.use({ renderer });
};

/**
 * HTML 转义函数
 * @param {string} text - 需要转义的文本
 * @returns {string} 转义后的文本
 */
const escapeHtml = (text) => {
  const map = {
    '&': '&amp;',
    '<': '&lt;',
    '>': '&gt;',
    '"': '&quot;',
    "'": '&#39;',
  };
  return text.replace(/[&<>"']/g, (m) => map[m]);
};

/**
 * 渲染 AI 消息内容（仅用于 AI 消息的 Markdown 渲染）
 * @param {string} content - 原始消息内容
 * @returns {string} 渲染后的 HTML 内容
 */
export const renderMessage = (content) => {
  if (!content) return '';

  // 初始化 marked 配置
  configureMarked();

  try {
    // AI 消息使用 Markdown 渲染
    const htmlContent = marked(content);

    // 添加消息容器类
    return `<div class="ai-message-content">${htmlContent}</div>`;
  } catch (error) {
    console.error('消息渲染失败:', error);
    // 降级处理：返回转义后的纯文本
    return `<p class="message-error">${escapeHtml(content)}</p>`;
  }
};

/**
 * 获取 mp-html 组件配置
 * @returns {Object} mp-html 配置选项
 */
export const getMpHtmlConfig = () => {
  return {
    // 标签样式
    tagStyle: {
      // 代码块样式
      'pre':
        'background-color: #f5f5f5; padding: 12px; border-radius: 6px; margin: 8px 0; overflow-x: auto;',
      'code':
        'font-family: "SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas, "Courier New", monospace; font-size: 14px;',

      // 行内代码样式
      'code.inline-code':
        'background-color: #f0f0f0; padding: 2px 4px; border-radius: 3px; font-size: 14px;',

      // 链接样式
      'span.link': 'color: #007AFF; text-decoration: underline;',

      // 图片样式
      'img.message-image':
        'max-width: 100%; height: auto; border-radius: 6px; margin: 8px 0;',

      // 列表样式
      'ul.message-list': 'margin: 8px 0; padding-left: 20px;',
      'ol.message-list': 'margin: 8px 0; padding-left: 20px;',
      'li.message-list-item': 'margin: 4px 0; line-height: 1.5;',

      // 表格样式
      'table.message-table':
        'width: 100%; border-collapse: collapse; margin: 8px 0;',
      'th': 'border: 1px solid #ddd; padding: 8px; background-color: #f5f5f5; font-weight: bold;',
      'td': 'border: 1px solid #ddd; padding: 8px;',

      // 引用块样式
      'blockquote.message-blockquote':
        'border-left: 4px solid #007AFF; padding-left: 12px; margin: 8px 0; color: #666; font-style: italic;',

      // 标题样式
      'h1.message-heading':
        'font-size: 20px; font-weight: bold; margin: 16px 0 8px 0; color: #333;',
      'h2.message-heading':
        'font-size: 18px; font-weight: bold; margin: 14px 0 6px 0; color: #333;',
      'h3.message-heading':
        'font-size: 16px; font-weight: bold; margin: 12px 0 4px 0; color: #333;',
      'h4.message-heading':
        'font-size: 15px; font-weight: bold; margin: 10px 0 4px 0; color: #333;',
      'h5.message-heading':
        'font-size: 14px; font-weight: bold; margin: 8px 0 2px 0; color: #333;',
      'h6.message-heading':
        'font-size: 13px; font-weight: bold; margin: 8px 0 2px 0; color: #333;',

      // 段落样式
      'p.message-paragraph': 'margin: 8px 0; line-height: 1.6; color: #333;',
      'p.user-message': 'margin: 0; line-height: 1.6; color: #333;',
      'p.message-error': 'margin: 0; line-height: 1.6; color: #ff4444;',

      // 文本样式
      'strong.message-strong': 'font-weight: bold;',
      'em.message-em': 'font-style: italic;',
      'del.message-del': 'text-decoration: line-through;',

      // AI 消息容器
      'div.ai-message-content': 'line-height: 1.6; color: #333;',
    },

    // 显示设置
    showImgMenu: false, // 不显示图片菜单
    selectable: true, // 允许选择文本

    // 错误处理
    errorImg: '', // 图片加载失败时的占位图

    // 加载提示
    loadingImg: '', // 图片加载中的占位图
  };
};

/**
 * 处理 mp-html 链接点击事件
 * @param {Object} event - 点击事件
 */
export const handleLinkTap = (event) => {
  const { attrs } = event.detail;
  if (attrs && attrs['data-href']) {
    const href = attrs['data-href'];
    console.log('点击链接:', href);

    // 在小程序中处理链接点击
    // 可以复制到剪贴板或者打开外部浏览器
    uni.setClipboardData({
      data: href,
      success: () => {
        uni.showToast({
          title: '链接已复制',
          icon: 'success',
        });
      },
    });
  }
};

export default {
  renderMessage,
  getMpHtmlConfig,
  handleLinkTap,
};
