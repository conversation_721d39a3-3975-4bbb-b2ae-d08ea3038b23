<template>
	<view class="poster-container">
		<view class="poster-wrapper"
			style="background-image: url('http://cdn.xiaodingdang1.com/2025/07/16/f4c0c1800bae4f59bcd246fdcbba473e.png'); background-size: cover; width: 100%; ">
			<view class="poster-main">
				<!-- 内容区域 -->
				<view class="poster-content">
					<view class="poster-titles">
						<view class="poster-title">
							一天菜单食谱
						</view>
						<view class="poster-title-level">
							迎月湾国际月子会所
						</view>
					</view>
					<view class="poster-card">
						<view class="poster-menu">
							<view class="poster-menu-coment">
									<image
										src="http://cdn.xiaodingdang1.com/2025/07/16/24b836d0e4ef45edbfb2053c1ba1e4aa.png"
										mode="" style="width: 46rpx;height: 47rpx;"></image>
								<view class="poster-menu-coment1">
									<view class="poster-menu-coment1_1">
										早餐
									</view>
									<view class="poster-menu-coment1_2">
										{{data.breakfast}}
									</view>
								</view>
							</view>
							<view class="poster-menu-coment">
									<image
										src="http://cdn.xiaodingdang1.com/2025/07/16/24b836d0e4ef45edbfb2053c1ba1e4aa.png"
										mode="" style="width: 46rpx;height: 47rpx;"></image>
								<view class="poster-menu-coment1">
									<view class="poster-menu-coment1_1">
										午餐
									</view>
									<view class="poster-menu-coment1_2">
										{{data.lunch}}
									</view>
								</view>
							</view>
							<view class="poster-menu-coment">
									<image
										src="http://cdn.xiaodingdang1.com/2025/07/16/24b836d0e4ef45edbfb2053c1ba1e4aa.png"
										mode="" style="width: 46rpx;height: 47rpx;"></image>
								<view class="poster-menu-coment1">
									<view class="poster-menu-coment1_1">
										下午茶
									</view>
									<view class="poster-menu-coment1_2">
										{{data.afternoonTea}}
									</view>
								</view>
							</view>
							<view class="poster-menu-coment">
									<image
										src="http://cdn.xiaodingdang1.com/2025/07/16/24b836d0e4ef45edbfb2053c1ba1e4aa.png"
										mode="" style="width: 46rpx;height: 47rpx;"></image>
								<view class="poster-menu-coment1">
									<view class="poster-menu-coment1_1">
										晚餐
									</view>
									<view class="poster-menu-coment1_2">
										{{data.dinner}}
									</view>
								</view>
							</view>
							<view class="poster-menu-coment">
									<image
										src="http://cdn.xiaodingdang1.com/2025/07/16/24b836d0e4ef45edbfb2053c1ba1e4aa.png"
										mode="" style="width: 46rpx;height: 47rpx;"></image>
								<view class="poster-menu-coment1">
									<view class="poster-menu-coment1_1">
										夜宵
									</view>
									<view class="poster-menu-coment1_2">
										{{data.nightSnack}}
									</view>
								</view>
							</view>
						</view>
						<view class="poster-footer">
							<view class="poster-footer__right" v-if="qrCode">
								<image class="poster-footer__qr" :src="qrCode" />
								<view class="poster-footer__titles">
									<view class="poster-footer__title">
										<text>长按识别二维码或</text>
									</view>
									<view class="poster-footer__title">
										<text>扫码浏览详情</text>
									</view>
								</view>

							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
	import {
		ref,
		onMounted,
		getCurrentInstance
	} from 'vue';

	// Props 定义
	const props = defineProps({
		// 海报类型
		type: {
			type: String,
			default: 'menu'
		},
		// 动态ID
		postId: {
			type: String,
			required: true
		}
	});

	// 响应式数据
	const instance = getCurrentInstance();
	const isLoading = ref(false);
	const data = ref({});
	const clubName = ref(uni.getStorageSync('clubName'));
	const clubLogo = ref(uni.getStorageSync('clubInfo')?.logo);
	const defaultLogan = ref('专业呵护新起点');
	const str1 = ref('不用来回体验');
	const str2 = ref('在线上就能查看合适再选择');
	const qrCode = ref('');

	/**
	 * 获取视频首帧
	 */
	const getVideoFirstFrame = async (videoUrl) => {
		if (!videoUrl) {
			throw new Error('视频URL不能为空');
		}

		try {
			const res = await instance.proxy.$axios.get(
				instance.proxy.$api.videoFirstFrame, {
					videoUrl
				}
			);

			if (res.data.code === 200) {
				return res.data.data.frameUrl;
			} else {
				throw new Error(res.data.msg || '获取视频首帧失败');
			}
		} catch (error) {
			console.error('获取视频首帧失败:', error);
			throw error;
		}
	};

	/**
	 * 获取动态信息
	 */
	const getFeedPostInfo = async () => {

		isLoading.value = true;
		const now = new Date();
		const formattedTime =
			`${now.getFullYear()}-${String(now.getMonth()+1).padStart(2,'0')}-${String(now.getDate()).padStart(2,'0')} `
		console.log(formattedTime);
		try {
			const res = await instance.proxy.$axios.get(
				instance.proxy.$api.menuMyList, {
					date: formattedTime
				}
			);

			if (res.data.code === 200) {
				const feedData = res.data.data;

				// 处理视频首帧
				if (feedData?.videos?.length) {
					const videoUrl = feedData.videos[0];
					const frameUrl = await getVideoFirstFrame(videoUrl);
					feedData.contentPhotos = [frameUrl];
				}

				data.value = feedData;
				console.log('获取动态信息成功:', feedData);
				return feedData;
			} else {
				throw new Error(res.data.msg || '获取动态信息失败');
			}
		} catch (error) {
			console.error('获取动态信息失败:', error);
			throw error;
		} finally {
			isLoading.value = false;
		}
	};

	/**
	 * 获取二维码
	 */
	const getQrCode = async () => {
		try {
			const res = await instance.proxy.$axios.get(instance.proxy.$api.getQrCode, {
				pathUrl: 'pageA/pageB/home/<USER>',
				uuid: props.postId,
				diaryId: '',
			});

			if (res.data.code === 200) {
				qrCode.value = res.data.msg;
				console.log('获取二维码成功:', res.data.msg);
			} else {
				throw new Error(res.data.msg || '获取二维码失败');
			}
		} catch (error) {
			console.error('获取二维码失败:', error);
			throw error;
		}
	};

	/**
	 * 初始化数据
	 */
	const initData = async () => {
		try {
			await Promise.all([
				getFeedPostInfo(),
				getQrCode()
			]);
			console.log('动态海报数据初始化完成');
		} catch (error) {
			console.error('动态海报数据初始化失败:', error);
			uni.showToast({
				title: '数据加载失败',
				icon: 'none',
			});
		}
	};

	// 组件挂载时初始化数据
	onMounted(() => {
		initData();
	});
</script>

<style lang="scss" scoped>
	@import './menu.scss';

	/* 动态海报特有样式 */
</style>
<style>
.poster-card {
  display: inline-block;
  width: auto;
  min-height: auto;
  padding: 20rpx;
}
.poster-menu {
  position: relative;
  z-index: 2;
}
</style>