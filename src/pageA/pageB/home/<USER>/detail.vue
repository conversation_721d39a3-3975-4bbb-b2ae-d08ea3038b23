<template>
  <view>
    <navigation
      :title="title"
      :background="bgColor"
      :isSowArrow="true"
    ></navigation>
    <view style="height: 600rpx; width: 100%">
      <swiper-banner :imgList="imgList" :list="banner_list"></swiper-banner>
    </view>
    <view class="main">
      <view class="card">
        <view class="card-top flex">
          <view class="card-top-price">{{
            inforList.price ? inforList.price : "----"
          }}</view>
          <view class="card-top-desc"
            >已有{{ inforList?.disinfectionFrequency }}人咨询</view
          >
        </view>
        <view class="card-title">{{ inforList.roomName }}</view>
        <view
          class="card-config flex"
          v-if="
            inforList.bathroomFacilities &&
            inforList.bathroomFacilities.length > 0
          "
        >
          <view class="card-config-label">配置</view>
          <scroll-view :scroll-x="true" class="card-config-tag flex">
            <view
              class="card-config-tag-enum"
              v-for="(res, i) in inforList.bathroomFacilities"
              :key="i"
            >
              {{ res }}
            </view>
          </scroll-view>
        </view>
        <view class="card-service flex">
          <view class="card-service-label">服务</view>
          <scroll-view :scroll-x="true" class="card-service-tag flex">
            <view
              class="card-service-tag-enum"
              v-for="(item, index) in tag"
              :key="index"
            >
              {{ item }}
            </view>
          </scroll-view>
        </view>
      </view>
      <view class="devices">
        <view class="page-title">房间设施</view>
        <view class="devices-content">
          <view class="roomFacility2">
            <view class="roomFacility1">
              <image
                src="http://cdn.xiaodingdang1.com/2024/11/23/7b2fae05a71141069fe8c71677dbbcaa.png"
                mode=""
                style="width: 50rpx; height: 50rpx"
              />
              <view class="roomFacilityTitle">便利设施</view>
            </view>
            <view class="roomFacilityFGX"></view>
            <view class="roomFacilityConment">
              <view
                class="roomFacilityConment1"
                v-for="(item, index) in inforList.facilityFeatures"
                :key="index"
              >
                {{ item }}
              </view>
            </view>
          </view>
          <view class="roomFacility2">
            <view class="roomFacility1">
              <image
                src="http://cdn.xiaodingdang1.com/2024/11/23/939024bbd99c4bf0923591b3363a7d57.png"
                mode=""
                style="width: 50rpx; height: 50rpx"
              />
              <view class="roomFacilityTitle">卫浴配套</view>
            </view>
            <view class="roomFacilityFGX"></view>
            <view class="roomFacilityConment">
              <view
                class="roomFacilityConment1"
                v-for="(item, index) in inforList.bathroomFacilities"
                :key="index"
              >
                {{ item }}
              </view>
            </view>
          </view>
          <view class="roomFacility2">
            <view class="roomFacility1">
              <image
                src="http://cdn.xiaodingdang1.com/2024/11/23/5da398bbdfbb4bdbb087bd5db3e1399e.png"
                mode=""
                style="width: 50rpx; height: 50rpx"
              />
              <view class="roomFacilityTitle">媒体娱乐</view>
            </view>
            <view class="roomFacilityFGX"></view>
            <view class="roomFacilityConment">
              <view
                class="roomFacilityConment1"
                v-for="(item, index) in inforList.mediaFeatures"
                :key="index"
              >
                {{ item }}
              </view>
            </view>
          </view>
          <view class="roomFacility2">
            <view class="roomFacility1">
              <image
                src="http://cdn.xiaodingdang1.com/2024/11/23/a28feb4eedef435e980b66ad3b39c8e7.png"
                mode=""
                style="width: 50rpx; height: 50rpx"
              />
              <view class="roomFacilityTitle">室外景观</view>
            </view>
            <view class="roomFacilityFGX"></view>
            <view class="roomFacilityConment">
              <view
                class="roomFacilityConment1"
                v-for="(item, index) in inforList.outdoorFeatures"
                :key="index"
              >
                {{ item }}
              </view>
            </view>
          </view>
        </view>
      </view>
      <!-- <view class="package">
                <view class="page-title">套餐详情</view>
                <view class="package-content">
                    <template v-for="(model, index) in modalData" :key="index">
                        <view class="title">{{ model.title }}</view>
                        <view class="subtitle" v-for="(item, i) in model.itemArray" :key="i">
                            <view class="left">{{ item.key }}</view>
                            <view class="right">{{ item.value }}</view>
                        </view>
                    </template>
                </view>
            </view> -->
      <!-- <view class="sign">
                <view class="page-title">签约礼物</view>
                <image style="width: 365px; margin: 0 auto; display: block"
                    src="http://cdn.xiaodingdang1.com/2024/11/23/df8b6ab891cc4f0cabff357d3ae0b612.png"></image>
            </view> -->
      <view class="question">
        <view class="page-title">大家都在问</view>
        <view class="question-content">
          <question-vue category="room"></question-vue>
        </view>
      </view>
      <view
        class="detail"
        v-if="inforList.textPhotos && inforList.textPhotos.length > 0"
      >
        <view class="page-title">图文详情</view>
        <view class="detail-content">
          <image
            class="third-img"
            :src="item"
            mode=""
            v-for="(item, index) in inforList.textPhotos"
            :key="index"
          >
          </image>
        </view>
      </view>
      <view class="recomment">
        <view class="page-title">为您推荐</view>
        <view class="recomment-content">
          <recomment-vue category="room"></recomment-vue>
        </view>
      </view>
    </view>
    <new-bottom :from="SessionFrom"></new-bottom>
  </view>
</template>
<script setup>
import questionVue from "../component/question.vue";
import recommentVue from "../component/recomment.vue";
import swiperBanner from "@/components/swiperBanner.vue";
import {
  onShow,
  onHide,
  onLoad,
  onPageScroll,
  onShareAppMessage,
  onShareTimeline,
} from "@dcloudio/uni-app";
import { ref, reactive, onMounted, computed, getCurrentInstance } from "vue";
const instance = getCurrentInstance();
const App = instance.appContext.config.globalProperties;
onShareAppMessage(() => {});
onShareTimeline(() => {});
const title = ref("");
onShow(() => {
  let newDateTime = Date.parse(new Date());
  let pointStart = {
    eventType: "PAGE_VIEW",
    pageUrl: "/pageA/pageB/home/<USER>/detail",
    module: "room",
    eventTime: newDateTime,
  };

  let iftoken = uni.getStorageSync("token");
  if (iftoken) {
    App.$point.basePoint(pointStart);
  }
});
onHide(() => {
  let newDateTime = Date.parse(new Date());
  let pointEnd = {
    eventId: uni.getStorageSync("eventId"),
    leaveTime: newDateTime,
  };
  App.$point.reportEnd(pointEnd);
});
const bgColor = ref("");
onPageScroll((e) => {
  if (e.scrollTop > 0) {
    bgColor.value = "#ffffff";
    title.value = "详情";
  } else if (e.scrollTop < 2) {
    bgColor.value = "";
    title.value = "";
  }
});
const suiteId = ref("");
const SessionFrom =
  uni.getStorageSync("tenantId") +
  "," +
  uni.getStorageSync("userInfo").nickname +
  "," +
  "月子套房";
onLoad(async (options) => {
  console.log("optionsssss", options);
  suiteId.value = options.suiteId;
  await pageInit();
});
const pageInit = async () => {
  try {
    await suiteInfo();
  } catch {}
};
const inforList = reactive({});
const banner_list = reactive([]);
const imgList = reactive([]);
const suiteInfo = async () => {
  //查询房间信息
  const res = await App.$axios.get(App.$api.suiteInfo, {
    suiteId: suiteId.value,
  });
  console.log(11111111);
  if (res.data.code == 200) {
    let data = res.data.data;
    let list = [];
    data.suiteVideos.forEach((item) => {
      let data = {
        type: "video",
        url: item,
      };
      list.push(data);
    });
    data.suitePhotos.forEach((item) => {
      let data = {
        type: "image",
        url: item,
      };
      list.push(data);
    });
    Object.assign(inforList, data);
    Object.assign(banner_list, list);
    Object.assign(imgList, data.suitePhotos);
  }
};
</script>

<style lang="less" scoped>
@import "./detail.less";
</style>
