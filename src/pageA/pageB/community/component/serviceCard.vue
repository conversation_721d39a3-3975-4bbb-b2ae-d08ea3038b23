<template>
    <view class="service-card">
        <view class="detail">
            <view class="detail-left">
                <image :src="item.staffPhotos || $defaultAvatar"></image>
            </view>
            <view class="detail-right">
                <view class="flex">
                    <view :class="['detail-right-name', 'detail-ye']">
                        {{ item.staffName || $defaultName }}
                    </view>
                    <view class="detail-right-content flex" v-if="item && item.staffPost">
                        <view class="detail-right-content-staf">
                            <image src="http://cdn.xiaodingdang1.com/2024/12/31/7ab70df46904400aae76508164f5a0ac.png">
                            </image>
                            {{ item.staffPost }}
                        </view>
                    </view>
                </view>
                <view class="detail-right-date">
                    <!--  {{ item.createTime ||  util.formatTime(new Date())}} -->
                </view>
            </view>
        </view>
        <scroll-view scroll-x="true" class="detail-tag">
            <template>
                <view :class="['detail-tag-enum', 'bg-blue'] ">
                    {{ item.nodeName }}
                </view>
            </template>
        </scroll-view>
    </view>
</template>

<script setup>
    import util from '@/utils/util.js'
    import {
        onShow,
        onHide,
        onLoad,
        onPageScroll,
        onReachBottom
    } from "@dcloudio/uni-app";
    import {
        ref,
        nextTick,
        reactive,
        getCurrentInstance,
        onMounted
    } from "vue";
    const props = defineProps({
        item: {
            type: Object,
            default: () => {}
        }
    })
</script>

<style lang="less" scoped>
    .service-card {
        margin-bottom: 20rpx;
    }

    .detail {
        display: flex;
        position: relative;
        align-items: center;
        margin-bottom: 20rpx;

        &-left {
            width: 80rpx;
            height: 80rpx;
            margin-right: 4rpx;

            image {
                height: 100%;
                width: 100%;
                border-radius: 80rpx;
                display: block;
            }
        }

        &-right {
            flex: 1;
            margin-left: 16rpx;

            &-name {
                font-weight: 500;
                font-size: 30rpx;
                color: #777777;
                margin-right: 12rpx;
            }

            &-content {
                flex: 1;
                justify-content: space-between;
                align-items: center;

                &-staf {
                    display: flex;
                    align-items: center;
                    padding: 4rpx 20rpx;
                    background: linear-gradient(90deg, #F6E0B7 0%, #FFE7C0 100%);
                    box-shadow: inset 0rpx 0rpx 4rpx 0rpx rgba(255, 255, 255, 0.25);
                    border-radius: 40rpx;

                    image {
                        height: 26rpx;
                        width: 26rpx;
                        display: inline-block;
                    }

                    font-weight: 500;
                    font-size: 24rpx;
                    color: #7A4E2B;
                }
            }

            &-tag {
                text-align: center;
                padding: 16rpx;
                border-radius: 80rpx;
                color: #30A9FF;
                font-size: 24rpx;
                font-weight: 600;
                position: relative;
                margin-left: 8rpx;
                background: #E9F2FF;
                position: absolute;
                right: 0;

                image {
                    width: 94rpx;
                    height: 48rpx;
                    transform: rotate(-10deg);
                    position: absolute;
                    left: 76rpx;
                    z-index: 1;
                    top: -5rpx;
                }
            }


            &-date {
                font-weight: 400;
                font-size: 24rpx;
                color: #AAAAAA;
            }
        }

    }

    .detail-tag {
        display: flex;
        justify-content: center;
        align-items: center;
        overflow-x: scroll;
        overflow-y: hidden;
        white-space: nowrap;

        &-enum {
            display: inline-block;
            margin-right: 28rpx;
            border-radius: 10rpx;
            padding: 4rpx 16rpx;
            font-weight: 400;
            font-size: 22rpx;
        }

    }

    .detail-ye {
        color: #FF6E00;
    }

    .bg-blue {
        font-size: 22rpx;
        background: #1FA2FF;
        color: #E9F2FF;
        margin-right: 16rpx;
    }
</style>