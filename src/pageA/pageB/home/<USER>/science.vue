<template>
    <view class="submain" :style="{'minHeight': contentHeight + 'px'}">
        <view class="title">产康科普</view>
        <view class="content">
            盆底肌是指封闭骨盆底的肌肉群，犹如一张「吊网」，将尿道、膀胱、阴道、子宫、直肠等脏器紧紧吊住，从而维持正常位置以便行使其功能。一旦这张「吊网」弹性变差，「吊力」不足，便会导致「网」内的器官无法维持在正常位置，从而出现相应功能障碍，如大小便失禁、盆底脏器脱垂等。
        </view>
        <view class="title">为什么做</view>
        <view class="content">
            <view class="flex">
                <view>
                    <view class="li"></view>
                </view>
                <view>由于子宫逐渐增大，盆底肌的软组织受到不同程度的损伤，破坏了盆底对子宫等器官的慢性牵拉作用。</view>
            </view>
            <view class="flex">
                <view>
                    <view class="li"></view>
                </view>
                <view>妊娠期由于激素水平的改变，导致盆底结缔组织胶原代谢改变，进而削弱了盆底的支撑功能。</view>
            </view>
        </view>
        <view class="title">产康目的</view>
        <view class="content">
            <view class="flex">
                <view>
                    <view class="li"></view>
                </view>
                <view>防止盆腔脏器脱垂加重。</view>
            </view>
            <view class="flex">
                <view>
                    <view class="li"></view>
                </view>
                <view>减少尿失禁。</view>
            </view>
            <view class="flex">
                <view>
                    <view class="li"></view>
                </view>
                <view>促进血液循环。</view>
            </view>
            <view class="flex">
                <view>
                    <view class="li"></view>
                </view>
                <view>增加盆底肌肉的强度、耐力和支持力。</view>
            </view>
            <view class="flex">
                <view>
                    <view class="li"></view>
                </view>
                <view>解肌肉异常的高张力、恢复肌肉弹性。</view>
            </view>
        </view>
    </view>
</template>
<script setup>
    // 产康页面 科普
    import {
        onShow,
        onHide,
        onLoad,
        onPageScroll
    } from "@dcloudio/uni-app";
    import {
        ref,
        reactive,
        onMounted,
        computed,
        getCurrentInstance
    } from "vue";
    const instance = getCurrentInstance();
    const App = instance.appContext.config.globalProperties
    const props = defineProps({
        contentHeight: {
            type: Number,
            default: 0
        }
    })
    onLoad((options) => {
        console.log('optionsssss', options)
        pageInit()
    })
    const pageInit = async () => {
        try {

        } catch {

        }
    }
</script>
<style lang="less" scoped>
    .submain {
        padding: 24rpx 24rpx 124rpx 24rpx;
        background: white;
        overflow: hidden;

        .title {
            font-weight: 500;
            font-size: 28rpx;
            color: #333333;
            line-height: 33rpx;
            text-align: left;
            font-style: normal;
            text-transform: none;
            margin: 20rpx 0;
        }

        .content {
            font-weight: 400;
            font-size: 28rpx;
            color: #333333;
            // line-height: 42rpx;
            // text-align: left;
            font-style: normal;
            text-transform: none;

            .flex {
                display: flex;
                // align-items: center;
            }

            .li {
                position: relative;
                width: 24rpx;
                height: 24rpx;
                background-color: #f0f0f0;
                border-radius: 12rpx;
                margin: 8rpx;
            }

            .li::before {
                content: "";
                // display: inline-block;
                width: 12rpx;
                height: 12rpx;
                background-color: #D1D1D1;
                border-radius: 6rpx;
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
            }
        }
    }
</style>