<template>
  <view>
    <view class="main">
      <view class="top">
        <view class="top-img">
          <swiper-banner :imgList="imgList" :list="banner_list"></swiper-banner>
        </view>
        <view class="top-price">
          <view class="top-price-icon">￥</view>
          <view class="top-price-text">{{
            inforList.price ? inforList.price : '----'
          }}</view>
          <view class="top-price-qes"
            >已有人{{ inforList.consultNumber }}咨询</view
          >
        </view>
        <view class="top-des">{{ inforList.projectName }}</view>
        <view class="top-list">
          <view
            class="top-list-enum"
            v-for="(item, i) in inforList.tag"
            :key="i"
            >{{ item }}</view
          >
        </view>
        <view class="top-button">
          <button
            @click="handleContact"
            style="
              width: 200rpx;
              padding: 0 28rpx;
              border-radius: 100rpx;
              background: #ff4f61;
              color: #ffffff;
              font-size: 28rpx;
              font-weight: bold;
            "
          >
            咨询报价
          </button>
        </view>
      </view>
      <view class="tabbar">
        <!-- <scroll-view id="tab-bar" class="scroll-h" :scroll-x="true" :show-scrollbar="false"
					:scroll-into-view="scrollInto">
					<view v-for="(tab, index) in comList" :key="tab.name" class="uni-tab-item" :id="tab.id"
						@click="ontabtap(tab)">
						<view class="uni-tab-item-title" v-if="tab.check"
							:class="tabIndex == tab.id ? 'uni-tab-item-title-active' : ''">{{ tab.name }}</view>
					</view>
				</scroll-view> -->
        <u-tabs
          :list="comList"
          :is-scroll="false"
          :current="current"
          @change="ontabtap"
          active-color="#FF4F61"
        ></u-tabs>
      </view>
      <view class="content">
        <specifics
          :inforList="inforList"
          v-show="comId === 'specifics' && inforList.isShowProjectDetails"
        >
        </specifics>
        <care :contentHeight="pageTop" v-show="comId === 'care'"></care>
        <instrument
          :inforList="inforList"
          :contentHeight="pageTop"
          v-show="comId === 'instrument' && inforList.isShowDevice"
        ></instrument>
        <mother :contentHeight="pageTop" v-show="comId === 'mother'"></mother>
        <!-- <science
          :contentHeight="pageTop"
          v-show="comId === 'science'"
        ></science> -->
        <view class="waterfallcard">
          <waterfall-card-vue
            class="waterfall-card"
            ref="waterfallRef"
            :list="pageList"
            :title="3"
            @detail="detail"
            v-show="comId === 'science'"
          >
          </waterfall-card-vue>
        </view>
      </view>
    </view>
    <new-bottom
      :from="SessionFrom"
      :type="3"
      :id="inforList.projectId"
    ></new-bottom>
    <rightMenuBarVue :scrollTop="scrollTop"></rightMenuBarVue>
  </view>
</template>
<script setup>
import waterfallCardVue from '@/components/waterfallCard.vue';
import rightMenuBarVue from '@/components/rightMenuBar.vue';
import swiperBanner from '@/components/swiperBanner.vue';
import specifics from '../component/specifics.vue';
import care from '../component/care.vue';
import instrument from '../component/instrument.vue';
import mother from '../component/mother.vue';
import science from '../component/science.vue';
import {
  onShow,
  onHide,
  onLoad,
  onPageScroll,
  onShareAppMessage,
  onShareTimeline,
  onReachBottom,
} from '@dcloudio/uni-app';
import { ref, reactive, onMounted, computed, getCurrentInstance } from 'vue';
import { useStore } from 'vuex';
import index from '../../../../uni_modules/zp-mixins';
const store = useStore();
const instance = getCurrentInstance();
const App = instance.appContext.config.globalProperties;

onShareAppMessage(() => {});
onShareTimeline(() => {});
onShow(async () => {
  let newDateTime = Date.parse(new Date());
  let pointStart = {
    eventType: 'PAGE_VIEW',
    pageUrl: '/pageA/pageB/home/<USER>/detail',
    module: 'recovery',
    eventTime: newDateTime,
    pageTitle: '产后康复',
  };
  let iftoken = uni.getStorageSync('token');
  if (iftoken) {
    App.$point.basePoint(pointStart);
  }
  await suiteInfo();
});
onHide(() => {
  let newDateTime = Date.parse(new Date());
  let pointEnd = {
    eventId: uni.getStorageSync('eventId'),
    leaveTime: newDateTime,
  };
  App.$point.reportEnd(pointEnd);
});
const types = ref(1);
const pageNum = ref(1);
const pageSize = ref(10);
const pageList = ref([]);
const bgColor = ref('');
const pageScrollTop = ref('');
const pageTop = ref('');
const scrollTop = ref('');
onPageScroll((e) => {
  if (e.scrollTop > 0) {
    bgColor.value = '#ffffff';
  } else if (e.scrollTop < 2) {
    bgColor.value = '';
  }
  pageScrollTop.value = Number(e.scrollTop) + 318;
  scrollTop.value = e.scrollTop;
});
const projectId = ref('');
const SessionFrom =
  uni.getStorageSync('tenantId') +
  ',' +
  uni.getStorageSync('userInfo').nickname +
  ',' +
  '产后康复';
onLoad((options) => {
  console.log('onLoad', options);
  projectId.value = options.projectId;
  pageInit(options);
});
const pageInit = async (options) => {
  try {
    // await suiteInfo();
    await reviewListFun();
    await recoveryRecommendation();
    // await signing()
  } catch {}
};
onReachBottom(() => {
  if (types.value == 1) {
    pageNum.value = pageNum.value + 1;
    getList();
  }
});
onMounted(() => {
  getList();
});
const getList = () => {
  store.commit('m_user/changeLoading', true);
  let data = {
    pageSize: pageSize.value,
    pageNum: pageNum.value,
    type: 'USER',
    isAsc: 'desc',
    orderByColumn: 'fp.create_time',
    nodeType: 'CHKF',
  };
  // if (postId.value) {
  //   data.postId = postId.value;
  // }
  App.$axios
    .get(App.$api.customerPage, data)
    .then((res) => {
      if (res.data.code == 200) {
        let data = res.data.rows;
        types.value = data.length == 10 ? 1 : 2;
        pageList.value = pageList.value.concat(data);
      }
    })
    .finally(() => {
      store.commit('m_user/changeLoading', false);
    });
};
const inforList = reactive({});
const banner_list = reactive([]);
const imgList = reactive([]);
const reviewList = reactive([]);
const signingShow = ref(false);
const suiteInfo = async () => {
  //查询产后康复信息
  const result = await App.$axios.get(App.$api.postpartumRecoveryInfo, {
    projectId: projectId.value,
  });
  if (result.data.code == 200) {
    let data = result.data.data;
    let slideshow = [];
    if (data.videos) {
      if (data.videos.length > 0) {
        data.videos.forEach((item) => {
          let list = {
            type: 1,
            video: item,
          };
          slideshow.push(list);
        });
      }
    }
    if (data.backgroundPhotos && data.backgroundPhotos.length > 0) {
      data.backgroundPhotos.forEach((item) => {
        let list = {
          type: 0,
          url: item,
        };
        slideshow.push(list);
      });
    } else {
      data.displayPhotos.forEach((item) => {
        let list = {
          type: 0,
          url: item,
        };
        slideshow.push(list);
      });
    }
    let tabList = [];
    if (!data.isShowProjectDetails && !data.isShowDevice) {
      tabList.push(
        {
          id: 4,
          name: '护理人员',
          com: 'care',
          disabled: true,
        },
        {
          id: 5,
          name: '案例',
          com: 'science',
          disabled: true,
        },
      );
    }
    if (data.isShowProjectDetails && data.isShowDevice) {
      tabList.push(
        {
          id: 1,
          name: '详情',
          com: 'specifics',
          disabled: data.isShowProjectDetails,
        },
        {
          id: 2,
          name: '仪器',
          com: 'instrument',
          disabled: data.isShowDevice,
        },
        {
          id: 4,
          name: '护理人员',
          com: 'care',
          disabled: true,
        },
        {
          id: 5,
          name: '案例',
          com: 'science',
          disabled: true,
        },
      );
    }
    if (data.isShowProjectDetails && !data.isShowDevice) {
      tabList.unshift(
        {
          id: 1,
          name: '详情',
          com: 'specifics',
          disabled: data.isShowProjectDetails,
        },
        {
          id: 4,
          name: '护理人员',
          com: 'care',
          disabled: true,
        },
        {
          id: 5,
          name: '案例',
          com: 'science',
          disabled: true,
        },
      );
    }
    if (data.isShowDevice && !data.isShowProjectDetails) {
      tabList.unshift(
        {
          id: 2,
          name: '仪器',
          com: 'instrument',
          disabled: data.isShowDevice,
        },
        {
          id: 4,
          name: '护理人员',
          com: 'care',
          disabled: true,
        },
        {
          id: 5,
          name: '案例',
          com: 'science',
          disabled: true,
        },
      );
    }

    Object.assign(comList, tabList);
    Object.assign(inforList, data);
    Object.assign(banner_list, slideshow);
    Object.assign(imgList, data.displayPhotos);
  }
};
const reviewListFun = async () => {
  //查询商家总体评价列表
  let data = {
    pageSize: 3,
    pageNum: 1,
    reviewType: 'recovery',
  };
  const res = await App.$axios.get(App.$api.reviewPage, data);
  if (res.data.code == 200) {
    Object.assign(reviewList, res.data.rows);
  }
};

// const signing = async (contractGiftId) => {
//     const res = await App.$axios.get(App.$api.signing, {
//         contractGiftId: contractGiftId
//     })
//     if (res.data.code == 200) {
//         this.setData({
//             signingShow: true
//         });
//         signingShow.value = true
//         suiteInfo();
//     }
// }
// const claim = async (e) => {
//     let contractGiftId = e.currentTarget.dataset.contractgiftid;
//     let isExistContractGift = e.currentTarget.dataset.isexist;
//     uni.setStorageSync('isExistContractGift', isExistContractGift);
//     if (!isExistContractGift) {
//         await signing(contractGiftId);
//     } else {
//         signingShow.value = true
//     }
// }
const scrollInto = ref('');
let tabIndex = ref(1);
let current = ref(0);
let comId = ref('specifics');
const comList = reactive([]);
const ontabtap = (index) => {
  let item = comList[index];
  current.value = index;
  switchTab(item);
};
const ontabchange = (item) => {
  switchTab(item);
};
const switchTab = (item) => {
  if (tabIndex.value == item.id) {
    return;
  }
  tabIndex.value = item.id;
  comId.value = item.com;
  pageTop.value = pageScrollTop.value;
};

const handleContact = () => {
  uni.navigateTo({
    url: '/subPackages/customerService/pages/userChat',
  });
};
</script>
<style lang="less" scoped>
@import './newdetail.less';
</style>
