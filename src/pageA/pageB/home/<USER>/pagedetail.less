.main {
    padding-top: 260rpx;
    padding-bottom: 180rpx;
    background: #F8F9F9;
    position: relative;
    .flex {
        display: flex;
    }
    .page-title {
        font-weight: 500;
        font-size: 32rpx;
        color: #333333;
        line-height: 38rpx;
        text-align: left;
        font-style: normal;
        text-transform: none;
        padding: 22rpx 0;
        background: #F8F9F9;
        padding-left: 24rpx;
    }
    .third-img {
        min-height: 360rpx;
        width: 100%;
        margin-top: 20rpx;
        border-radius: 20rpx;
    }
}
.card {
    width: 710rpx;
    position: absolute;
    top: -128rpx;
    z-index: 2;
    background: #FFFFFF;
    border-radius: 20rpx;
    text-align: left;
    font-style: normal;
    text-transform: none;
    padding: 32rpx 32rpx 40rpx 32rpx;
    margin-left: 20rpx;
    &-top {
        justify-content: space-between;
        align-items: center;
       margin: 0 24rpx 32rpx 24rpx;
        &-price {
           font-weight: bold;
           font-size: 56rpx;
           color: #FD2D39;
        }
        &-desc {
            font-weight: 400;
            font-size: 24rpx;
            color: #AAAAAA;
            line-height: 60rpx;
        }
    }
    &-title {
        margin: 0 24rpx 42rpx 24rpx;
        font-weight: 600;
        font-size: 36rpx;
        color: #333333;
        line-height: 42rpx;
    }
    &-config {
        position: relative;
        margin: 0 24rpx 30rpx 24rpx;
        &-label {
            font-weight: 400;
            font-size: 28rpx;
            color: #AAAAAA;
            margin-right: 28rpx;
            width: 56rpx;
            position: relative;
            top: 4rpx;
        }
        &-tag {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow-x: scroll;
            overflow-y: hidden;
            white-space: nowrap;
            &-enum {
                display: inline-block;
                background: #FFEEEF;
                border-radius: 6rpx;
                font-weight: 400;
                font-size: 24rpx;
                color: #FD2D39;
                line-height: 28rpx;
                padding: 4rpx 12rpx;
                margin-right: 24rpx;
            }
        }
    }
    &-service {
        margin: 0 24rpx;
        position: relative;
        &-label {
            font-weight: 400;
            font-size: 28rpx;
            color: #AAAAAA;
            margin-right: 32rpx;
            width: 56rpx;
            position: relative;
            top: 6rpx;
        }
        &-tag {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow-x: scroll;
            overflow-y: hidden;
            white-space: nowrap;
            &-enum {
                background-color: #F8F8F8;
                display: inline-block;
                font-weight: 400;
                font-size: 24rpx;
                color: #333333;
                padding: 4rpx 12rpx;
                margin-right: 24rpx;
            }
        }
    }
}

.devices {
    margin-bottom: 20rpx;
    .devices-content {
        background-color: white;
        padding: 36rpx 36rpx 24rpx 36rpx;
        border-radius: 20rpx;
        margin: 0 24rpx;
    }
    /*房间设施新*/
    .roomFacility {
        width: 100%;
    }
    
    .roomFacility1 {
        text-align: center;
        width: 20%;
    }
    
    .roomFacility2 {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 40rpx;
    }
    
    .roomFacilityTitle {
        color: #333333;
        font-size: 24rpx;
    }
    
    .roomFacilityFGX {
        width: 2rpx;
        min-height: 116rpx;
        background: #ebebeb;
    }
    
    .roomFacilityConment {
        width: 70%;
        display: flex;
        flex-wrap: wrap;
    }
    
    .roomFacilityConment1 {
        width: 33%;
        font-size: 24rpx;
        margin-bottom: 16rpx;
        color: #333333;
    }
    
}
.package {
    margin-bottom: 20rpx;
    text-align: left;
    font-style: normal;
    text-transform: none;
    .package-content {
        background-color: white;
        padding: 36rpx;
        border-radius: 20rpx;
        margin: 0 24rpx;
        .title {
                font-weight: 500;
                font-size: 24rpx;
                color: #000000;
                padding: 16rpx 20rpx;
                background-color: #F6F6F6;
                border: 2rpx solid #F1F1F1;
                border-top: none;
               
               
            }
        .title:first-child {
             border-top: 2rpx solid #F1F1F1;
              border-radius: 6rpx 6rpx 0 0;
        }
        .subtitle:last-child {
              border-radius:  0 0 6rpx 6rpx;
        }
            .subtitle {
                display: flex;
                align-items: center;
                border: 2rpx solid #F1F1F1;
                border-top: none;
                font-weight: 400;
                font-size: 22rpx;
                color: #000000;
                 padding: 16rpx 20rpx;
                .left {
                    width: 195rpx;
                }
                .right {
                    flex: 1
                }
            }
        }
}
 .question {
     margin-bottom: 20rpx;
            .question-content {
                background-color: white;
                padding: 24rpx 24rpx;
                border-radius: 20rpx;
                 margin:  0 24rpx;
            }
    }
.detail { 
    margin-bottom: 20rpx;
    .detail-content {
        padding: 0 24rpx 24rpx 24rpx;
        background-color: white;
        border-radius: 20rpx;
    }
}
.recomment {
    margin-bottom: 20rpx;
    .recomment-content {
        padding: 0 24rpx;
         background-color: white;
    }
}
.sign {
    margin-bottom: 20rpx;
    image {
        border-radius: 20rpx;
    }
}
