import { ref, computed } from 'vue'

export function usePermissions() {
  const mealShow = ref(false)
  const complaintShow = ref(false)
  const nurseShow = ref(false)
  const isCustomerService = ref(false)

  // 检查用户权限
  const checkPermissions = () => {
    const roles = uni.getStorageSync('roles')
    const permissions = uni.getStorageSync('permissions')
    
    if (!roles || roles.length === 0) return

    const userRole = roles[0]
    
    // 检查护理记录和意见反馈权限 (仅客户可见)
    if (userRole === 'CUSTOMER') {
      nurseShow.value = true
      complaintShow.value = true
    }
    
    // 检查每日餐点权限
    if ([
      'CUSTOMER',
      'ADMIN', 
      'BOSS',
      'APP_SUPER_ADMIN',
      'SALES_MANAGER'
    ].includes(userRole)) {
      mealShow.value = true
    }
    
    // 检查客服权限
    if (permissions && permissions.includes('customer:service:chat')) {
      isCustomerService.value = true
    }
  }

  return {
    mealShow,
    complaintShow,
    nurseShow,
    isCustomerService,
    checkPermissions
  }
}
