<template>
  <view class="main">
    <view class="coment">
      <view class="fgx"></view>
      <view>
        <view class="list1">
          <view class="title"> 宝妈姓名 </view>
          <view class="name">
            {{ detailsList.momName ? detailsList.momName : "--" }}
          </view>
        </view>
        <view class="fgx"></view>
      </view>
      <view>
        <view class="list1">
          <view class="title"> 宝宝胞胎数 </view>
          <view class="name">
            {{ detailsList.fetusNumber ? detailsList.fetusNumber : "--" }}
          </view>
        </view>
        <view class="fgx"></view>
      </view>
      <view>
        <view class="list1">
          <view class="title"> 宝宝姓名 </view>
          <view class="name">
            {{ detailsList.babyName ? detailsList.babyName : "--" }}
          </view>
        </view>
        <view class="fgx"></view>
      </view>
      <view>
        <view class="list1">
          <view class="title"> 宝宝性别 </view>
          <view class="name">
            {{ detailsList.gender ? detailsList.gender : "--" }}
          </view>
        </view>
        <view class="fgx"></view>
      </view>
      <view>
        <view class="list1">
          <view class="title"> 分娩医院 </view>
          <view class="name">
            {{
              detailsList.deliveryHospital ? detailsList.deliveryHospital : "--"
            }}
          </view>
        </view>
        <view class="fgx"></view>
      </view>
      <view>
        <view class="list1">
          <view class="title"> 出生日期 </view>
          <view class="name">
            {{ detailsList.birthDate ? detailsList.birthDate : "--" }}
          </view>
        </view>
        <view class="fgx"></view>
      </view>
      <view>
        <view class="list1">
          <view class="title"> 出生体重 </view>
          <view class="name">
            {{ detailsList.birthWeight ? detailsList.birthWeight : "--" }}
          </view>
        </view>
        <view class="fgx"></view>
      </view>
      <view>
        <view class="list1">
          <view class="title"> 出生身高 </view>
          <view class="name">
            {{ detailsList.birthHeight ? detailsList.birthHeight : "--" }}
          </view>
        </view>
        <view class="fgx"></view>
      </view>
      <view>
        <view class="list1">
          <view class="title"> 头围 </view>
          <view class="name">
            {{
              detailsList.headCircumference
                ? detailsList.headCircumference
                : "--"
            }}
          </view>
        </view>
        <view class="fgx"></view>
      </view>
      <view>
        <view class="list1">
          <view class="title"> 出生方式 </view>
          <view class="name">
            {{ detailsList.deliveryMethod ? detailsList.deliveryMethod : "--" }}
          </view>
        </view>
        <view class="fgx"></view>
      </view>
      <view>
        <view class="list1">
          <view class="title"> 接种疫苗 </view>
          <view class="name">
            {{
              detailsList.vaccinationRecords
                ? detailsList.vaccinationRecords
                : "--"
            }}
          </view>
        </view>
        <view class="fgx"></view>
      </view>
      <view>
        <view class="list2">
          <view class="title2"> 备注信息 </view>
          <view class="name2">
            {{ detailsList.remarks ? detailsList.remarks : "" }}
          </view>
        </view>
        <view class="fgx"></view>
      </view>
      <view>
        <view class="list2">
          <view class="title2"> 照片 </view>
          <view class="chooseImg2">
            <view
              class="chooseImg"
              v-for="(item, index) in detailsList.photoPath"
              :key="index"
            >
              <view class="chooseImg1">
                <image
                  :src="item"
                  mode=""
                  style="width: 100%; height: 100%; border-radius: 8rpx"
                ></image>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      bgColor: "#ffffff",
      title: "",
      detailsList: "",
    };
  },
  methods: {
    configGetField() {
      this.changeLoading(true);
      this.$axios
        .get(this.$api.getMyDetail)
        .then((res) => {
          if (res.data.code == 200) {
            this.setData({
              detailsList: res.data.data,
            });
          }
        })
        .finally(() => {
          this.changeLoading(false);
        });
    },
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.title = options.title;
    this.configGetField();
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {},
  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {},
  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {},
  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {},
  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {},
  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {},
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {},

  onShareTimeline() {},
};
</script>

<style lang="less" scoped>
@import "./nursedetails.less";
</style>
