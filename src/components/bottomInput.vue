<template>
    <template v-if="showInput">
        <view class="input-section" :style="{bottom: inputBottom}">
            <textarea @blur="sendOut" :adjust-position="false" type="text" maxlength="100" class="input_input"
                :value="content" auto-focus="true" placeholder="说点什么..." @input="bindinput" confirm-type="send"
                @confirm="sendOut('send')" />
            <button type="primary" class="send_out" :disabled="content ? false : true" @click.stop="sendOut('send')">
                发送
            </button>
        </view>
    </template>
</template>

<script setup>
    import {
        onLoad,
        onUnload
    } from "@dcloudio/uni-app";
    import {
        ref,
        reactive,
        getCurrentInstance,
        computed,
        onMounted,
        onUnmounted,
        inject,
        watch
    } from "vue";
    const instance = getCurrentInstance();
    const content = ref('')
    const props = defineProps({
        show: {
            type: Boolean,
            default: false
        },
        // 计算键盘高度
        inputBottomHeight: {
            type: Number,
            default: 0
        }
    })
    const inputBottom = ref(0)
    watch(() => props.inputBottomHeight, (newVal, oldVal) => {
        inputBottom.value = props.inputBottomHeight
    })
    const emits = defineEmits(['callback', 'close'])
    const showInput = computed(() => props.show)
    const bindinput = (e) => {
        content.value = e.detail.value
    }
    const sendOut = (type) => {
        console.log('sendOutsendOut', type)
        emits(type == 'send' && content.value ? 'callback' : 'close', content.value)
        if (type == 'send') {
            content.value = ''
        }
    }
</script>

<style lang="less" scoped>
    .input-section {
        position: absolute;
        display: flex;
        align-items: center;
        width: 100%;
        left: 0rpx;
        z-index: 500;
        background: #f2f2f2;
        padding-bottom: 40rpx;
        padding-top: 20rpx;
    }

    .send_out {
        margin: 0;
        padding: 0;
        text-align: center;
        border: 1rpx solid #cccccc;
        border-radius: 10rpx;
        width: 140rpx;
        height: 80rpx;
        line-height: 80rpx;
        /* 将发送按钮固定在底部 */
        position: absolute;
        right: 24rpx;
        bottom: 40rpx;
    }

    .input_input {
        background: #fff;
        margin-left: 24rpx;
        z-index: 1000;
        width: 520rpx;
        height: 80rpx;
        padding-left: 35rpx;
        font-size: 30rpx;
        border-radius: 6rpx;
    }
</style>