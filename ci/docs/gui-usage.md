# 小程序 CI 图形化管理界面使用指南

## 🎯 概述

图形化管理界面提供了直观、易用的 Web 界面来管理多个小程序项目的构建、上传和预览操作。

## 🚀 启动界面

### 方式一：使用 npm scripts（推荐）

```bash
# 启动图形化界面
pnpm run ci:gui

# 启动并自动打开浏览器
pnpm run ci:gui --open
```

### 方式二：直接运行脚本

```bash
# 启动图形化界面
node scripts/start-gui.js

# 启动并自动打开浏览器
node scripts/start-gui.js --open
```

### 方式三：直接启动服务器

```bash
# 仅启动服务器（不包含启动脚本的额外功能）
node server/index.js
```

启动成功后，访问 http://localhost:3001 即可使用图形化界面。

## 📱 界面功能

### 1. 项目概览

界面会显示所有配置的项目，每个项目卡片包含：

- **项目信息**：项目名称和 AppID
- **状态指示器**：
  - 🟢 配置正常 / 🔴 配置错误
  - 🟢 私钥已配置 / 🟡 缺少私钥
  - 🟢 构建产物存在 / 🟡 需要构建

### 2. 私钥文件管理

对于缺少私钥的项目，界面会显示上传区域：

- **拖拽上传**：直接将 .key 文件拖拽到上传区域
- **点击上传**：点击上传区域选择文件
- **自动重命名**：上传的文件会自动重命名为项目对应的私钥文件名
- **权限设置**：自动设置文件权限为 600（仅所有者可读写）

### 3. 环境选择

每个项目都有环境选择器，支持：

- **开发环境** (develop)
- **体验环境** (trial)  
- **正式环境** (release)

选择环境后，所有操作都会基于该环境进行。

### 4. 项目操作

#### 🔨 构建项目

- 点击"构建项目"按钮
- 系统会根据选择的环境执行对应的构建命令
- 实时显示构建日志
- 构建完成后自动刷新项目状态

#### 📤 上传小程序

- 点击"上传小程序"按钮
- 弹出配置对话框：
  - **版本号**：可手动输入或留空自动生成
  - **版本描述**：版本更新说明
- 确认后开始上传
- 实时显示上传进度和日志

#### 👁️ 生成预览

- 点击"生成预览"按钮
- 弹出配置对话框：
  - **页面路径**：如 `pages/index/index`（可选）
  - **页面参数**：如 `id=123&name=test`（可选）
- 确认后生成预览二维码
- 二维码生成完成后会显示在项目卡片中

### 5. 实时日志

- **自动显示**：操作开始后自动显示日志区域
- **实时更新**：通过 WebSocket 实时接收日志信息
- **颜色区分**：
  - 🔵 普通信息（蓝色）
  - 🟢 成功信息（绿色）
  - 🔴 错误信息（红色）
- **清空功能**：可手动清空日志

### 6. 预览二维码

- 预览生成成功后会显示二维码
- 使用微信扫描二维码即可预览小程序
- 二维码会自动缓存，避免重复生成

## 🔧 技术特性

### WebSocket 实时通信

- 服务器与客户端通过 WebSocket 保持连接
- 实时推送构建、上传、预览的进度信息
- 自动重连机制，确保连接稳定

### 文件上传安全

- 只允许上传 .key 格式的私钥文件
- 自动设置正确的文件权限
- 文件自动重命名为项目对应的名称

### 进程管理

- 每个操作都会启动独立的子进程
- 支持多个项目同时操作
- 进程状态实时监控

### 响应式设计

- 支持桌面和移动设备
- 自适应布局
- 现代化的 UI 设计

## 🛠️ 故障排除

### 常见问题

1. **服务器启动失败**
   ```
   Error: listen EADDRINUSE :::3001
   ```
   解决方案：端口 3001 被占用，请关闭占用该端口的程序或修改端口配置

2. **WebSocket 连接失败**
   ```
   WebSocket connection failed
   ```
   解决方案：检查防火墙设置，确保 3001 端口可访问

3. **私钥上传失败**
   ```
   只允许上传 .key 格式的私钥文件
   ```
   解决方案：确保上传的文件是从微信公众平台下载的 .key 格式私钥文件

4. **构建失败**
   ```
   构建失败 (退出码: 1)
   ```
   解决方案：检查项目依赖是否安装完整，构建命令是否正确

### 调试模式

启动服务器时会自动开启 Node.js 调试模式，可以通过开发者工具进行调试。

### 日志查看

- 服务器日志会在终端中显示
- 客户端操作日志会在界面中实时显示
- 可以通过浏览器开发者工具查看详细的网络请求

## 🔒 安全注意事项

1. **私钥文件安全**：
   - 私钥文件仅存储在本地服务器
   - 不会通过网络传输到其他地方
   - 自动设置正确的文件权限

2. **网络安全**：
   - 服务器仅监听本地地址 (localhost)
   - 不对外网开放
   - 建议在内网环境使用

3. **文件权限**：
   - 上传的私钥文件自动设置为 600 权限
   - 只有文件所有者可以读写

## 📈 性能优化

- 使用 WebSocket 减少 HTTP 请求
- 日志自动限制数量，避免内存溢出
- 二维码缓存机制，避免重复生成
- 响应式设计，优化移动端体验

## 🔄 更新日志

- v1.0.0: 初始版本，支持基本的图形化操作
- 支持多项目管理
- 支持实时日志显示
- 支持拖拽上传私钥文件
- 支持 WebSocket 实时通信
