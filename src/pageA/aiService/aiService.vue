<template>
  <view class="ai-service-container" @touchmove.stop.prevent>
    <image
      src="https://cdn.xiaodingdang1.com/2025/06/20/08989406ec6d4b92ac02f34a96220ba9.png"
      mode="aspectFill"
      class="page-bg"
    ></image>
    <!-- 导航栏 -->
    <view class="nav-bar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="nav-left">
        <view class="nav-btn" @click="showSidebar">
          <view class="nav-btn-icon">
            <image src="/static/images/chat/list.png" mode="aspectFill"></image>
          </view>
          <text class="nav-btn-text">历史</text>
        </view>
        <view
          class="nav-btn"
          :class="{ 'nav-btn-disabled': isLoading }"
          @click="refreshChat"
        >
          <view class="nav-btn-icon">
            <image src="/static/images/chat/chat.png" mode="aspectFill"></image>
          </view>
          <text class="nav-btn-text">新会话</text>
        </view>
        <button class="nav-btn share-btn" open-type="share">
          <view class="nav-btn-icon">
            <image src="/static/images/chat/share.png" mode="aspectFit"></image>
          </view>
          <text class="nav-btn-text">分享</text>
        </button>
      </view>
      <view class="nav-title">AI 智能助理</view>
      <view class="nav-right"></view>
    </view>

    <!-- 聊天区域 -->
    <view class="chat-section">
      <scroll-view
        id="chat-scroll-view"
        class="chat-area"
        scroll-y
        :scroll-top="scrollTopValue"
        :scroll-with-animation="true"
        @scrolltoupper="handleScrollToUpper"
        @scroll="handleScroll"
        :upper-threshold="SCROLL_CONFIG.UPPER_THRESHOLD"
      >
        <view id="chat-container" class="chat-container">
          <!-- 静默加载历史消息 - 无UI提示 -->

          <!-- AI欢迎消息 - 只在新会话中显示 -->
          <view
            v-if="!isHistoryConversation"
            class="message-item ai-message"
            id="welcome-msg"
          >
            <view class="message-content">
              <text class="message-text">您好，我是您的智能助理。</text>
              <text class="message-text"
                >很高兴能和您互动，我可以为您答疑、提供内容上的帮助，有任何需要可以跟我聊聊，我会尽力为您提供优质的内容服务。</text
              >
            </view>
          </view>

          <!-- 快捷推荐问题 - 只在新会话中显示 -->
          <QuickQuestions
            :visible="!isHistoryConversation"
            @select-question="selectQuickQuestion"
          />

          <!-- 聊天消息列表 -->
          <template v-for="(message, index) in messageList" :key="message.id">
            <!-- 消息内容 -->
            <view
              :id="`msg-${message.id}`"
              class="message-item"
              :class="getMessageClass(message.type)"
            >
              <view class="message-content">
                <!-- AI消息使用富文本渲染 -->
                <template v-if="message.type === 'ai'">
                  <AiMessageRenderer :content="message.content" />

                  <!-- 相关案例推荐 -->
                  <RelatedCases
                    v-if="!message.isStreaming"
                    :cases="relatedCasesCache.get(message.id) || []"
                    :conversation-id="currentConversationId"
                    :user-message="getUserMessageForRecommendation(message.id)"
                    @view-more="handleRelatedCasesViewMore"
                  />
                  <!-- 流式输出指示器 -->
                  <view v-if="message.isStreaming" class="typing-indicator">
                    <view class="typing-dot"></view>
                    <view class="typing-dot"></view>
                    <view class="typing-dot"></view>
                  </view>
                </template>

                <!-- 用户消息使用普通文本 -->
                <template v-else>
                  <text class="message-text">{{ message.content }}</text>
                </template>
              </view>

              <!-- AI消息的推荐组件 - 每条AI消息完成后都显示 -->
              <template v-if="shouldShowRecommendations(message)">
                <!-- 宝妈说推荐 -->
                <MomSays
                  :recommendations="momSaysCache.get(message.id) || []"
                  :conversation-id="currentConversationId"
                  :user-message="getUserMessageForRecommendation(message.id)"
                  @view-more="handleMomSaysViewMore"
                />

                <!-- 相关问题推荐 -->
                <RelatedQuestions
                  :questions="relatedQuestionsCache.get(message.id) || []"
                  @select-question="selectRelatedQuestion"
                />
              </template>
            </view>
          </template>
        </view>
      </scroll-view>
      <!-- 输入区域 -->
      <ChatInput
        v-model="inputMessage"
        :loading="isLoading"
        @send="sendMessage"
      />
    </view>

    <!-- 侧边栏组件 -->
    <ChatSidebar
      ref="chatSidebarRef"
      :visible="sidebarVisible"
      :current-conversation-id="currentConversationId"
      @close="hideSidebar"
      @select-session="selectSession"
    />

    <!-- 自定义 tabbar -->
    <CustomTabBar :fixed="false" activeKey="ai-service" />
  </view>
</template>

<script setup>
import {
  ref,
  computed,
  nextTick,
  onUnmounted,
  getCurrentInstance,
  watch,
} from 'vue';
import {
  onLoad,
  onShow,
  onShareAppMessage,
  onShareTimeline,
} from '@dcloudio/uni-app';
import ChatSidebar from './components/ChatSidebar.vue';
import ChatInput from './components/ChatInput.vue';
import QuickQuestions from './components/QuickQuestions.vue';
import RelatedQuestions from './components/RelatedQuestions.vue';
import MomSays from './components/MomSays.vue';
import RelatedCases from './components/RelatedCases.vue';
import CustomTabBar from '@/components/CustomTabBar.vue';
import AiMessageRenderer from './components/AiMessageRenderer.vue';
import {
  createConversationApi,
  chatSSEApi,
  getConversationMessagesApi,
  getChatRecommendationsApi,
  getQuestionRecommendationsApi,
} from '@/api/aiService.js';
import { checkHasLogined } from '@/utils/auth.js';

// 数据
const inputMessage = ref('');
const messageList = ref([]);
const sidebarVisible = ref(false);
const isLoading = ref(false);
const currentConversationId = ref(null);
const currentSSEClient = ref(null);
const userInfo = ref({});
const chatSidebarRef = ref(null);

// 会话类型状态
const isHistoryConversation = ref(false); // 是否为历史会话

// 分页相关状态
const currentPage = ref(1);
const pageSize = ref(20);
const hasMoreMessages = ref(true);
const isLoadingHistory = ref(false);

// 滚动位置管理
const scrollTop = ref(0);
const scrollContentHeight = ref(0);
const scrollTopValue = ref(0); // 用于控制滚动位置
const scrollLock = ref(false); // 滚动锁，防止冲突

// 当前正在接收的AI消息
const currentAIMessage = ref({
  id: null,
  content: '',
  isStreaming: false,
});

// 当前用户问题（用于立即触发相关问题推荐）
const currentUserQuestion = ref('');

// 推荐数据缓存（按消息ID存储）
const relatedQuestionsCache = ref(new Map());
const relatedCasesCache = ref(new Map());
const momSaysCache = ref(new Map());

// 推荐数据加载状态跟踪（按消息ID存储）
const recommendationLoadingStatus = ref(new Map());

// 初始化推荐数据加载状态
const initRecommendationLoadingStatus = (messageId) => {
  recommendationLoadingStatus.value.set(messageId, {
    relatedQuestions: false,
    relatedCases: false,
    momSays: false,
    allCompleted: false,
  });
};

// 更新推荐数据加载状态
const updateRecommendationLoadingStatus = (
  messageId,
  type,
  completed = true,
) => {
  const status = recommendationLoadingStatus.value.get(messageId);
  if (status) {
    status[type] = completed;
    status.allCompleted =
      status.relatedQuestions && status.relatedCases && status.momSays;
    recommendationLoadingStatus.value.set(messageId, status);

    console.log(`消息 ${messageId} 推荐数据加载状态更新:`, status);

    // 如果所有推荐数据都加载完成，执行滚动到底部
    if (status.allCompleted) {
      console.log(`消息 ${messageId} 所有推荐数据加载完成，执行滚动到底部`);
      nextTick(() => {
        setTimeout(() => {
          scrollToBottom();
        }, 300); // 给一点时间让DOM更新
      });
    }
  }
};

// 检查推荐数据是否全部加载完成
const isRecommendationLoadingCompleted = (messageId) => {
  const status = recommendationLoadingStatus.value.get(messageId);
  return status ? status.allCompleted : false;
};

// 获取最后一个用户问题（保留用于兼容性）
const getLastUserQuestion = () => {
  for (let i = messageList.value.length - 1; i >= 0; i--) {
    if (messageList.value[i].type === 'user') {
      return messageList.value[i].content;
    }
  }
  return '';
};

// 获取指定AI消息对应的用户问题
const getUserMessageForRecommendation = (aiMessageId) => {
  // 找到AI消息的索引
  const aiMessageIndex = messageList.value.findIndex(
    (msg) => msg.id === aiMessageId,
  );
  if (aiMessageIndex === -1) return '';

  // 从AI消息往前找最近的用户消息
  for (let i = aiMessageIndex - 1; i >= 0; i--) {
    if (messageList.value[i].type === 'user') {
      return messageList.value[i].content;
    }
  }

  // 如果没找到，返回最后一个用户问题
  return getLastUserQuestion();
};

// 计算属性
const canSend = computed(() => {
  return inputMessage.value.trim().length > 0 && !isLoading.value;
});

// 消息渲染功能已移至 AiMessageRenderer 组件中

// 获取消息样式类
const getMessageClass = (messageType) => {
  return {
    'user-message': messageType === 'user',
    'ai-message': messageType === 'ai',
  };
};

// 判断是否应该显示推荐组件
const shouldShowRecommendations = (message) => {
  const shouldShow = message.type === 'ai' && !message.isStreaming;
  if (message.type === 'ai') {
    console.log(
      `消息 ${message.id} 推荐显示检查: isStreaming=${message.isStreaming}, shouldShow=${shouldShow}`,
    );
  }
  return shouldShow;
};

// 消息转换函数
const transformMessage = (msg) => ({
  id: msg.id || Date.now() + Math.random(),
  type: MESSAGE_TYPE[msg.type],
  content: msg.content || '',
  createTime: msg.createTime || new Date().toISOString(),
  isStreaming: false,
});

// 更新分页状态
const updatePaginationState = (currentPageNum, hasMore, messagesLength) => {
  currentPage.value = currentPageNum;
  hasMoreMessages.value =
    hasMore &&
    currentPageNum <= PAGINATION_CONFIG.MAX_PAGES &&
    messagesLength < PAGINATION_CONFIG.MAX_MESSAGES;
};

// 记录加载限制日志
const logLoadingLimits = (currentPageNum, messagesLength) => {
  if (currentPageNum > PAGINATION_CONFIG.MAX_PAGES) {
    console.log(
      `已达到最大页数限制(${PAGINATION_CONFIG.MAX_PAGES}页)，停止加载`,
    );
  }
  if (messagesLength >= PAGINATION_CONFIG.MAX_MESSAGES) {
    console.log(
      `已达到最大消息数限制(${PAGINATION_CONFIG.MAX_MESSAGES}条)，停止加载`,
    );
  }
};

// 统一的错误处理函数
const handleError = (error, context, showToast = true) => {
  console.error(`${context}:`, error);
  if (showToast) {
    uni.showToast({
      title: `${context}，请重试`,
      icon: 'none',
    });
  }
};

// 统一的API响应处理
const handleApiResponse = (res, successCallback, errorContext) => {
  if (res.code === 200 && res.data) {
    return successCallback(res.data);
  } else {
    console.error(`${errorContext}失败:`, res.msg || '未知错误');
    return false;
  }
};

const statusBarHeight = ref(0);

onLoad((options) => {
  statusBarHeight.value = uni.getMenuButtonBoundingClientRect().top;
  userInfo.value = uni.getStorageSync('userInfo') || {};

  if (options.conversationId) {
    currentConversationId.value = options.conversationId;
    isHistoryConversation.value = true;
    loadConversation(options.conversationId);
  } else {
    isHistoryConversation.value = false;
  }
});

onShow(() => {
  scrollToBottom();
});

onUnmounted(() => {
  if (currentSSEClient.value) {
    currentSSEClient.value.close();
  }
});

const createNewConversation = async (firstMessage = '') => {
  try {
    const res = await createConversationApi({
      title: firstMessage || '新会话',
    });

    return (
      handleApiResponse(
        res,
        (data) => {
          currentConversationId.value = data;
          return data;
        },
        '创建会话',
      ) || null
    );
  } catch (error) {
    handleError(error, '创建会话失败，请检查网络连接');
    throw error;
  }
};

const loadConversation = async (conversationId) => {
  try {
    // 重置分页状态
    resetPaginationState();
    isLoadingHistory.value = true;
    await loadAllHistoryMessages(conversationId);
  } catch (error) {
    handleError(error, '加载会话失败');
  } finally {
    isLoadingHistory.value = false;
  }
};

// 重置分页状态
const resetPaginationState = () => {
  currentPage.value = 1;
  hasMoreMessages.value = true;
};

// 常量定义
const MESSAGE_TYPE = {
  USER: 'user',
  ASSISTANT: 'ai',
};

const PAGINATION_CONFIG = {
  MAX_PAGES: 50,
  MAX_MESSAGES: 1000,
  PAGE_SIZE: 20,
  LOAD_DELAY: 100,
};

const SCROLL_CONFIG = {
  UPPER_THRESHOLD: 50,
  SCROLL_DELAY: 800,
};

// 自动加载所有历史消息
const loadAllHistoryMessages = async (conversationId) => {
  try {
    let allMessages = [];
    let currentPageNum = 1;
    let hasMore = true;

    console.log('开始自动加载历史消息...');

    while (
      hasMore &&
      currentPageNum <= PAGINATION_CONFIG.MAX_PAGES &&
      allMessages.length < PAGINATION_CONFIG.MAX_MESSAGES
    ) {
      console.log(`正在加载第${currentPageNum}页历史消息`);

      const res = await getConversationMessagesApi(conversationId, {
        pageNum: currentPageNum,
        pageSize: pageSize.value,
      });

      if (res.code === 200 && res.data) {
        const { records, pages, current } = res.data;

        // 处理消息数据
        const pageMessages = records.map(transformMessage);

        // 将消息添加到开头（保持时间顺序）
        allMessages = [...pageMessages, ...allMessages];

        console.log(
          `第${currentPageNum}页加载完成，获得${pageMessages.length}条消息，累计${allMessages.length}条`,
        );

        // 检查是否还有更多页
        hasMore = current < pages;
        currentPageNum++;

        // 更新分页状态
        updatePaginationState(currentPageNum, hasMore, allMessages.length);
      } else {
        console.error('加载历史消息失败:', res.msg || '未知错误');
        hasMore = false;
      }

      // 添加小延迟，避免请求过于频繁
      if (hasMore && currentPageNum <= PAGINATION_CONFIG.MAX_PAGES) {
        await new Promise((resolve) =>
          setTimeout(resolve, PAGINATION_CONFIG.LOAD_DELAY),
        );
      }
    }

    // 设置所有消息到列表
    messageList.value = allMessages;

    logLoadingLimits(currentPageNum, allMessages.length);

    console.log(`自动加载完成，总共加载了${allMessages.length}条历史消息`);

    // 滚动到最后一天消息位置
    nextTick(() => {
      scrollToBottom();
    });
  } catch (error) {
    console.error('自动加载历史消息异常:', error);
    throw error;
  }
};

// 加载历史消息（分页方式，保留用于滚动加载）
const loadHistoryMessages = async (conversationId, isFirstLoad = false) => {
  try {
    if (!hasMoreMessages.value && !isFirstLoad) {
      console.log('没有更多历史消息了');
      return;
    }

    if (isLoadingHistory.value && !isFirstLoad) {
      console.log('正在加载历史消息，跳过重复请求');
      return;
    }

    isLoadingHistory.value = true;

    console.log(`加载第${currentPage.value}页历史消息`);

    const res = await getConversationMessagesApi(conversationId, {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
    });

    console.log('历史消息API响应:', res);

    if (res.code === 200 && res.data) {
      const { records, pages, current } = res.data;

      // 处理消息数据
      const historyMessages = records.map(transformMessage);

      console.log(`加载到${historyMessages.length}条历史消息`);

      if (isFirstLoad) {
        // 首次加载，直接设置消息列表
        messageList.value = historyMessages;

        // 滚动到最后一天消息位置
        nextTick(() => {
          scrollToBottom();
        });
      } else {
        // 加载更多，插入到消息列表开头
        const oldFirstMessageId =
          messageList.value.length > 0 ? messageList.value[0].id : null;
        messageList.value = [...historyMessages, ...messageList.value];
      }

      // 更新分页状态
      hasMoreMessages.value = current < pages;
      currentPage.value = current + 1;

      console.log(
        `分页状态更新: 当前页${current}, 总页数${pages}, 还有更多消息: ${hasMoreMessages.value}`,
      );
    } else {
      console.error('加载历史消息失败:', res.msg || '未知错误');
      if (isFirstLoad) {
        uni.showToast({
          title: res.msg || '加载历史消息失败',
          icon: 'none',
        });
      }
    }
  } catch (error) {
    console.error('加载历史消息异常:', error);
    if (isFirstLoad) {
      uni.showToast({
        title: '加载历史消息失败，请检查网络连接',
        icon: 'none',
      });
    }
  } finally {
    isLoadingHistory.value = false;
  }
};

// 处理滚动事件
const handleScroll = (e) => {
  const { scrollTop: currentScrollTop, scrollHeight } = e.detail;
  scrollTop.value = currentScrollTop;

  if (scrollHeight > 0) {
    scrollContentHeight.value = scrollHeight;
  }
};

// 处理滚动到顶部事件 - 自动静默加载
const handleScrollToUpper = () => {
  // 只在历史会话中才触发加载更多历史消息
  if (isHistoryConversation.value) {
    console.log('滚动到顶部，自动静默加载更多历史消息');
    loadMoreHistory();
  }
};

// 自动加载更多历史消息（滚动触发）
const loadMoreHistory = async () => {
  // 只在历史会话中才允许加载更多历史消息
  if (!isHistoryConversation.value) {
    return;
  }

  if (!currentConversationId.value) {
    console.log('没有当前会话ID，无法自动加载更多历史消息');
    return;
  }

  if (!hasMoreMessages.value) {
    console.log('已加载全部历史消息');
    return;
  }

  if (isLoadingHistory.value) {
    console.log('正在加载历史消息，跳过重复请求');
    return;
  }

  console.log('自动加载更多历史消息');

  await loadHistoryMessages(currentConversationId.value, false);
};

const showSidebar = () => {
  sidebarVisible.value = true;
};

const hideSidebar = () => {
  sidebarVisible.value = false;
};

// 选择问题并发送
const selectQuestion = (questionText) => {
  inputMessage.value = questionText;
  nextTick(() => {
    sendMessage();
  });
};

const selectQuickQuestion = selectQuestion;
const selectRelatedQuestion = selectQuestion;

// 处理宝妈说查看更多
const handleMomSaysViewMore = (data) => {
  console.log('查看更多宝妈说:', data);
  // 组件内部已经处理了跳转逻辑，这里可以做一些额外的处理
  // 比如埋点统计等
};

// 处理相关案例查看更多
const handleRelatedCasesViewMore = (data) => {
  console.log('查看更多相关案例:', data);
  // 组件内部已经处理了跳转逻辑，这里可以做一些额外的处理
  // 比如埋点统计等
};

const sendMessage = async () => {
  if (!canSend.value) return;

  // 检查登录状态，未登录时跳转登录页面
  const isLoggedIn = await checkHasLogined();
  if (!isLoggedIn) {
    console.log('用户未登录，跳转到登录页面');
    uni.showModal({
      title: '提示',
      content: '请先登录后再使用AI助理功能',
      confirmText: '去登录',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          uni.navigateTo({
            url: '/pageA/login',
          });
        }
      },
    });
    return;
  }

  const message = inputMessage.value.trim();
  inputMessage.value = '';

  // 确保有会话ID
  const conversationId = await ensureConversationExists(message);
  if (!conversationId) {
    inputMessage.value = message;
    return;
  }

  // 添加用户消息
  addUserMessage(message);

  // 滚动到底部
  await scrollToBottom();

  // 发送SSE请求获取AI回复
  await sendSSEChatMessage(message);
};

// 确保会话存在
const ensureConversationExists = async (message) => {
  console.log('检查会话是否存在，当前会话ID:', currentConversationId.value);

  if (currentConversationId.value) {
    console.log('使用现有会话ID:', currentConversationId.value);
    return currentConversationId.value;
  }

  console.log('当前没有会话ID，开始创建新会话');

  // 显示创建会话的加载状态
  uni.showLoading({
    title: '创建会话中...',
    mask: true,
  });
  let conversationId;
  try {
    conversationId = await createNewConversation(message);
    console.log('新会话创建结果:', conversationId);
  } catch (error) {
    console.error('创建新会话异常:', error);
    return null;
  } finally {
    uni.hideLoading();
  }

  if (!conversationId) {
    console.error('会话创建失败，返回null');
    handleError(null, '会话创建失败', true);
    return null;
  }

  console.log('新会话创建成功，会话ID:', conversationId);
  return conversationId;
};

// 添加用户消息
const addUserMessage = (content) => {
  const userMsg = {
    id: Date.now(),
    type: MESSAGE_TYPE.USER,
    content,
    createTime: new Date().toISOString(),
  };
  messageList.value.push(userMsg);

  // 记录当前用户问题，用于立即触发相关问题推荐
  currentUserQuestion.value = content;
};

// 为指定消息获取相关问题推荐
const fetchRelatedQuestionsForMessage = async (messageId, userQuestion) => {
  if (!userQuestion) {
    console.warn('缺少必要参数：userQuestion');
    updateRecommendationLoadingStatus(messageId, 'relatedQuestions', true);
    return;
  }

  // 检查登录状态，未登录时不获取推荐
  const isLoggedIn = await checkHasLogined();
  if (!isLoggedIn) {
    console.log('用户未登录，不获取相关问题推荐');
    updateRecommendationLoadingStatus(messageId, 'relatedQuestions', true);
    return;
  }

  try {
    console.log(
      `为消息 ${messageId} 立即获取相关问题推荐，用户问题:`,
      userQuestion,
    );

    const requestData = {
      userQuestion: userQuestion,
      recommendationCount: 12,
      knowledgeId: '',
    };

    const res = await getQuestionRecommendationsApi(requestData);

    console.log(`消息 ${messageId} 的相关问题推荐API响应:`, res);

    if (res.code === 200 && res.data) {
      let questionList = [];

      if (
        res.data.recommendedQuestions &&
        Array.isArray(res.data.recommendedQuestions)
      ) {
        questionList = res.data.recommendedQuestions;
      } else {
        console.warn('相关问题推荐数据格式异常:', res.data);
        updateRecommendationLoadingStatus(messageId, 'relatedQuestions', true);
        return;
      }

      // 过滤和验证问题数据
      const validQuestions = questionList.filter((question) => {
        const hasText = question.questionText;
        if (!hasText) {
          console.warn('发现无效问题数据:', question);
          return false;
        }
        return true;
      });

      // 将数据存储到缓存中
      relatedQuestionsCache.value.set(messageId, validQuestions);
      console.log(`消息 ${messageId} 的相关问题数据已缓存:`, validQuestions);
    } else {
      console.error(
        `消息 ${messageId} 获取相关问题推荐失败:`,
        res.msg || '未知错误',
      );
    }
  } catch (error) {
    console.error(`消息 ${messageId} 获取相关问题推荐异常:`, error);
  } finally {
    // 无论成功还是失败，都标记为完成
    updateRecommendationLoadingStatus(messageId, 'relatedQuestions', true);
  }
};

// 为指定消息获取聊天推荐数据（相关案例和宝妈说）
const fetchChatRecommendationsForMessage = async (
  messageId,
  conversationId,
  userMessage,
) => {
  if (!conversationId || !userMessage) {
    console.warn('缺少必要参数：conversationId 或 userMessage');
    updateRecommendationLoadingStatus(messageId, 'relatedCases', true);
    updateRecommendationLoadingStatus(messageId, 'momSays', true);
    return;
  }

  // 检查登录状态，未登录时不获取推荐
  const isLoggedIn = await checkHasLogined();
  if (!isLoggedIn) {
    console.log('用户未登录，不获取聊天推荐数据');
    updateRecommendationLoadingStatus(messageId, 'relatedCases', true);
    updateRecommendationLoadingStatus(messageId, 'momSays', true);
    return;
  }

  try {
    console.log(`为消息 ${messageId} 获取聊天推荐数据，用户问题:`, userMessage);

    const requestData = {
      conversationId: conversationId,
      userMessage: userMessage,
      recommendationLimit: 6,
    };

    const res = await getChatRecommendationsApi(requestData);

    console.log(`消息 ${messageId} 的聊天推荐API响应:`, res);

    if (res.code === 200 && res.data) {
      // 处理相关案例数据
      if (Array.isArray(res.data.relatedCases)) {
        relatedCasesCache.value.set(messageId, res.data.relatedCases);
        console.log(
          `消息 ${messageId} 的相关案例数据已缓存:`,
          res.data.relatedCases,
        );
      }

      // 处理宝妈说数据
      if (Array.isArray(res.data.momSays)) {
        momSaysCache.value.set(messageId, res.data.momSays);
        console.log(`消息 ${messageId} 的宝妈说数据已缓存:`, res.data.momSays);
      }
    } else {
      console.error(
        `消息 ${messageId} 获取聊天推荐失败:`,
        res.msg || '未知错误',
      );
    }
  } catch (error) {
    console.error(`消息 ${messageId} 获取聊天推荐异常:`, error);
  } finally {
    // 无论成功还是失败，都标记为完成
    updateRecommendationLoadingStatus(messageId, 'relatedCases', true);
    updateRecommendationLoadingStatus(messageId, 'momSays', true);
  }
};

// 创建AI消息占位符
const createAIMessagePlaceholder = () => {
  const aiMessageId = Date.now() + 1;
  console.log(`创建AI消息占位符 ${aiMessageId}，设置isStreaming为true`);

  currentAIMessage.value = {
    id: aiMessageId,
    content: '',
    isStreaming: true,
  };

  const aiMsg = {
    id: aiMessageId,
    type: MESSAGE_TYPE.ASSISTANT,
    content: '',
    createTime: new Date().toISOString(),
    isStreaming: true,
  };

  messageList.value.push(aiMsg);
  console.log(`AI消息 ${aiMessageId} 已添加到消息列表，isStreaming: true`);

  // 初始化推荐数据加载状态
  initRecommendationLoadingStatus(aiMessageId);

  // 立即为这个AI消息请求推荐数据
  if (currentUserQuestion.value && currentConversationId.value) {
    // 并行请求相关问题推荐
    fetchRelatedQuestionsForMessage(aiMessageId, currentUserQuestion.value);
    // 并行请求聊天推荐（相关案例和宝妈说）
    fetchChatRecommendationsForMessage(
      aiMessageId,
      currentConversationId.value,
      currentUserQuestion.value,
    );
  } else {
    // 如果没有用户问题或会话ID，直接标记所有推荐为完成
    updateRecommendationLoadingStatus(aiMessageId, 'relatedQuestions', true);
    updateRecommendationLoadingStatus(aiMessageId, 'relatedCases', true);
    updateRecommendationLoadingStatus(aiMessageId, 'momSays', true);
  }

  return aiMessageId;
};

// 创建SSE事件处理器
const createSSEEventHandlers = (aiMessageId) => ({
  onMessage: (chunk, fullContent) => {
    updateAIMessage(aiMessageId, fullContent, true);
    nextTick(() => {
      scrollToBottom();
    });
  },

  onError: (error) => {
    handleSSEError(error, aiMessageId);
  },

  onComplete: (fullContent) => {
    completeAIMessage(aiMessageId, fullContent);
  },
});

// 更新AI消息内容
const updateAIMessage = (messageId, content, isStreaming = false) => {
  console.log(`更新AI消息 ${messageId}，isStreaming: ${isStreaming}`);

  const msgIndex = messageList.value.findIndex((msg) => msg.id === messageId);
  if (msgIndex > -1) {
    // 使用Vue的响应式更新
    messageList.value[msgIndex] = {
      ...messageList.value[msgIndex],
      content,
      isStreaming,
    };
    console.log(
      `消息列表中消息 ${messageId} 的isStreaming已更新为: ${isStreaming}`,
    );

    if (currentAIMessage.value && currentAIMessage.value.id === messageId) {
      currentAIMessage.value.content = content;
      currentAIMessage.value.isStreaming = isStreaming;
      console.log(`currentAIMessage的isStreaming已更新为: ${isStreaming}`);
    }
  } else {
    console.warn(`未找到消息 ${messageId}`);
  }
};

// 处理SSE错误
const handleSSEError = (error, messageId) => {
  console.error('SSE聊天错误:', error);
  console.log(`SSE错误，消息 ${messageId}，设置isStreaming为false`);

  isLoading.value = false;

  // 先更新消息列表中的消息状态
  updateAIMessage(messageId, '抱歉，我遇到了一些问题，请稍后再试。', false);

  // 使用nextTick确保DOM更新
  nextTick(() => {
    console.log(`nextTick: 确保错误消息 ${messageId} 的isStreaming状态已更新`);

    // 然后清理当前AI消息引用
    if (currentAIMessage.value && currentAIMessage.value.id === messageId) {
      currentAIMessage.value.isStreaming = false;
      currentAIMessage.value = null;
    }

    // 清理SSE客户端引用
    currentSSEClient.value = null;
  });

  handleError(error, '发送失败', true);
};

// 完成AI消息
const completeAIMessage = (messageId, content) => {
  console.log(`完成AI消息 ${messageId}，设置isStreaming为false`);

  isLoading.value = false;

  // 先更新消息列表中的消息状态
  updateAIMessage(messageId, content, false);

  // 使用nextTick确保DOM更新
  nextTick(() => {
    console.log(`nextTick: 确保消息 ${messageId} 的isStreaming状态已更新`);

    // 然后清理当前AI消息引用
    if (currentAIMessage.value && currentAIMessage.value.id === messageId) {
      currentAIMessage.value.isStreaming = false;
      currentAIMessage.value = null;
    }

    // 清理SSE客户端引用
    currentSSEClient.value = null;
  });

  // 清空当前用户问题，避免重复触发相关问题推荐
  currentUserQuestion.value = '';

  // 检查推荐数据是否已经全部加载完成，如果是则立即滚动
  // 否则等待推荐数据加载完成后再滚动
  if (isRecommendationLoadingCompleted(messageId)) {
    console.log(`消息 ${messageId} 推荐数据已完成，立即滚动到底部`);
    nextTick(() => {
      setTimeout(() => {
        scrollToBottom();
      }, 300);
    });
  } else {
    console.log(`消息 ${messageId} 推荐数据未完成，等待推荐数据加载完成后滚动`);
  }
};

// 发送SSE聊天消息
const sendSSEChatMessage = async (userMessage) => {
  try {
    if (!currentConversationId.value) {
      handleError(null, '会话异常', true);
      return;
    }

    isLoading.value = true;

    // 创建AI消息占位符
    const aiMessageId = createAIMessagePlaceholder();
    await scrollToBottom();

    // 发送SSE请求
    const sseClient = await chatSSEApi(
      {
        userId: uni.getStorageSync('userId'),
        userMessage: userMessage,
        conversationId: currentConversationId.value,
      },
      createSSEEventHandlers(aiMessageId),
    );

    // 保存SSE客户端引用
    currentSSEClient.value = sseClient;
  } catch (error) {
    isLoading.value = false;
    handleError(error, '发送失败');
  }
};

// 获取元素高度的通用函数
const getElementHeight = async (selector) => {
  return new Promise((resolve) => {
    const query = uni.createSelectorQuery();
    query
      .select(selector)
      .boundingClientRect((data) => {
        resolve(data ? data.height : 0);
      })
      .exec();
  });
};

// 滚动到底部
const scrollToBottom = async () => {
  await nextTick();

  if (scrollLock.value) return;

  try {
    const [viewHeight, contentHeight] = await Promise.all([
      getElementHeight('#chat-scroll-view'),
      getElementHeight('#chat-container'),
    ]);

    if (contentHeight > viewHeight) {
      scrollTopValue.value = contentHeight - viewHeight;
    }
  } catch (error) {
    console.error('滚动到底部失败:', error);
  }
};

const selectSession = (session) => {
  console.log('尝试选择历史会话:', session);

  // 如果AI正在回复中，禁止切换会话
  if (isLoading.value) {
    console.log('AI正在回复中，禁止切换会话');
    uni.showToast({
      title: 'AI正在回复中，请等待回复完成',
      icon: 'none',
      duration: 2000,
    });
    return; // 直接返回，不执行后续操作
  }

  console.log('开始切换到历史会话:', session);

  // 关闭当前SSE连接（如果有的话）
  if (currentSSEClient.value) {
    console.log('关闭当前SSE连接');
    try {
      currentSSEClient.value.close();
    } catch (error) {
      console.warn('关闭SSE连接时出错:', error);
    }
    currentSSEClient.value = null;
  }

  // 重置相关状态
  isLoading.value = false;
  if (currentAIMessage.value) {
    currentAIMessage.value = null;
  }

  // 清空输入框
  inputMessage.value = '';

  // 设置历史会话状态
  currentConversationId.value = session.conversationId || session.id;
  messageList.value = [];
  isHistoryConversation.value = true;

  console.log('切换到历史会话，会话ID:', currentConversationId.value);

  // 加载历史会话
  loadConversation(currentConversationId.value);
};

const refreshChat = () => {
  console.log('尝试创建新会话');

  // 如果AI正在回复中，禁止创建新会话
  if (isLoading.value) {
    console.log('AI正在回复中，禁止创建新会话');
    uni.showToast({
      title: 'AI正在回复中，请等待回复完成',
      icon: 'none',
      duration: 2000,
    });
    return; // 直接返回，不执行后续操作
  }

  console.log('开始创建新会话，重置聊天状态');

  // 关闭当前SSE连接（如果有的话）
  if (currentSSEClient.value) {
    console.log('关闭当前SSE连接');
    try {
      currentSSEClient.value.close();
    } catch (error) {
      console.warn('关闭SSE连接时出错:', error);
    }
    currentSSEClient.value = null;
  }

  // 重置所有状态
  resetChatState();

  // 清空输入框
  inputMessage.value = '';

  console.log('新会话创建完成，当前状态:', {
    currentConversationId: currentConversationId.value,
    isHistoryConversation: isHistoryConversation.value,
    messageListLength: messageList.value.length,
    isLoading: isLoading.value,
    inputMessage: inputMessage.value,
  });

  // 滚动到顶部，显示欢迎消息和快捷问题
  nextTick(() => {
    scrollTopValue.value = 0;
  });

  // 显示成功提示
  uni.showToast({
    title: '新会话已创建',
    icon: 'success',
    duration: 1500,
  });
};

// 重置聊天状态
const resetChatState = () => {
  console.log('重置聊天状态开始');

  // 清空消息列表
  messageList.value = [];

  // 重置会话状态
  isHistoryConversation.value = false;
  currentConversationId.value = null;

  // 重置加载状态
  isLoading.value = false;
  isLoadingHistory.value = false;

  // 重置分页状态
  resetPaginationState();

  // 重置滚动状态
  scrollLock.value = false;
  scrollTopValue.value = 0;

  // 重置用户问题
  currentUserQuestion.value = '';

  // 重置AI消息状态
  if (currentAIMessage.value) {
    currentAIMessage.value = null;
  }

  // 清空缓存
  relatedQuestionsCache.value.clear();
  relatedCasesCache.value.clear();
  momSaysCache.value.clear();
  recommendationLoadingStatus.value.clear();

  console.log('重置聊天状态完成');
};

// 分享功能
onShareAppMessage(() => {
  return {
    title: 'AI 智能助理 - 您的专属智能客服',
    path: '/pageA/aiService/aiService',
    // imageUrl:
    //   'https://cdn.xiaodingdang1.com/2025/06/20/08989406ec6d4b92ac02f34a96220ba9.png',
  };
});

onShareTimeline(() => {
  return {
    title: 'AI 智能助理 - 您的专属智能客服',
    path: '/pageA/aiService/aiService',
  };
});
</script>

<style lang="scss" scoped>
.ai-service-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #eff0f6;
}

.page-bg {
  width: 100vw;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
}

.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 32rpx;
  min-height: 108rpx;
  z-index: 1;
}

.nav-title {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  font-family: 'PingFang SC', sans-serif;
  font-size: 36rpx;
  font-weight: 500;
  color: #333333;
  line-height: 103rpx;
  text-align: center;
}

.nav-left {
  display: flex;
  align-items: flex-start;
  gap: 24rpx;
  z-index: 2;
}

.nav-right {
  display: flex;
  align-items: center;
  z-index: 2;
  min-width: 0;
}

.nav-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;

  .nav-btn-icon {
    width: 34rpx;
    height: 34rpx;
    display: flex;
    align-items: center;
    justify-content: center;

    image {
      width: 100%;
      height: 100%;
    }
  }

  .nav-btn-text {
    font-family: 'PingFang SC', sans-serif;
    font-size: 18rpx;
    font-weight: 500;
    color: #333333;
    text-align: center;
  }
}

.nav-btn-disabled {
  opacity: 0.4;
  pointer-events: none;
}

.share-btn {
  line-height: 1.5;
  background: transparent;
  border: none;
  padding: 0;
  margin: 0;

  &::after {
    border: none;
  }
}

.chat-section {
  position: relative;
  flex: 1;
  height: 0;
}

.chat-area {
  overflow-y: auto;
  height: 100%;
  .chat-container {
    padding: 32rpx;
    position: relative;
    padding-bottom: 150rpx;
  }
}

.message-item {
  margin-bottom: 16px;
}

.ai-message .message-content {
  background-color: #fff;
  padding: 12px 16px;
  border-radius: 12px;
  border-top-left-radius: 4px;
}

.user-message {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.user-message .message-content {
  background-color: #4285f4;
  padding: 12px 16px;
  border-radius: 12px;
  border-bottom-right-radius: 4px;
}

.user-message .message-text {
  color: #fff;
}

.message-text {
  font-size: 15px;
  line-height: 1.4;
  color: #333;
  display: block;
  margin-bottom: 8px;
  white-space: pre-wrap; /* 保留换行和空格 */
  word-wrap: break-word;
  word-break: break-word;
  overflow-wrap: break-word;
}

.message-text:last-child {
  margin-bottom: 0;
}

.time-divider {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 20px 0;
}

.time-text {
  font-size: 12px;
  color: #999;
  background-color: #f5f5f5;
  padding: 4px 12px;
  border-radius: 12px;
}

.typing-indicator {
  display: inline-flex;
  align-items: center;
  gap: 3px;
  margin-left: 8px;
  vertical-align: middle;
}

.typing-dot {
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background-color: #999;
  animation: typing-bounce 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-dot:nth-child(2) {
  animation-delay: -0.16s;
}

.typing-dot:nth-child(3) {
  animation-delay: 0s;
}

@keyframes typing-bounce {
  0%,
  80%,
  100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}

.loading-indicator {
  position: absolute;
  bottom: 120px;
  left: 16px;
  background-color: #fff;
  padding: 12px 16px;
  border-radius: 12px;
  border-top-left-radius: 4px;
}

.loading-dots {
  display: flex;
  gap: 4px;
}

.dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #ccc;
  animation: bounce 1.4s infinite ease-in-out;
}

.dot:nth-child(1) {
  animation-delay: -0.32s;
}
.dot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes bounce {
  0%,
  80%,
  100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}
</style>
