.main {
    background: #f6f6f6;
    padding: 36rpx 24rpx;
}

.list {
    padding: 28rpx 28rpx;
    background: #ffffff;
    border-radius: 20rpx;
    position: relative;
    margin-bottom: 20rpx;
}

.listTitle {
    color: #333333;
    font-size: 32rpx;
    font-weight: bold;
}

.fgx {
    width: 100%;
    height: 1rpx;
    background: #ebebeb;
    margin: 28rpx 0;
}

.listComent1 {
    color: #777777;
    font-size: 28rpx;
}

.listComent2 {
    font-size: 28rpx;
    color: #333333;
}

.listComent {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    margin-bottom: 10rpx;
}

.label {
    position: absolute;
    width: 156rpx;
    height: 56rpx;
    background: #ff6c11;
    color: #ffffff;
    font-size: 28rpx;
    font-weight: bold;
    text-align: center;
    line-height: 56rpx;
    border-top-right-radius: 20rpx;
    border-bottom-left-radius: 45rpx;
    top: 0;
    right: 0;
}

.label1 {
    position: absolute;
    width: 156rpx;
    height: 56rpx;
    background: #aaaaaa;
    color: #ffffff;
    font-size: 28rpx;
    font-weight: bold;
    text-align: center;
    line-height: 56rpx;
    border-top-right-radius: 20rpx;
    border-bottom-left-radius: 45rpx;
    top: 0;
    right: 0;
}

.reason {
    padding: 24rpx 24rpx;
    background: #f6f6f6;
    border-radius: 8rpx;
}

.dispose {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 20rpx;
}

.disposeBtn {
    width: 176rpx;
    height: 66rpx;
    background: #3f6fff;
    color: #ffffff;
    font-size: 24rpx;
    text-align: center;
    line-height: 66rpx;
    border-radius: 6rpx;
}