<template>
  <view class="main">
    <view class="mainss">
      <navigation
        :background="bgColor"
        title="宝妈社区"
        :isSowArrow="true"
      ></navigation>
      <view class="headTab">
        <view @tap="headTab" data-index="2">
          <image
            src="http://cdn.xiaodingdang1.com/2025/02/06/31bfec99c71a4ebb8720cf35a088b01f.png"
            mode=""
            style="width: 356rpx; height: 130rpx"
            v-if="tabIndex == 2"
          />
          <image
            class="relativeBottom"
            src="http://cdn.xiaodingdang1.com/2025/02/06/57ed4d1f047e47819c4800441f0f4a56.png"
            mode=""
            style="width: 356rpx; height: 120rpx"
            v-if="tabIndex != 2"
          />
        </view>
        <view @tap="headTab" data-index="1">
          <image
            src="http://cdn.xiaodingdang1.com/2025/02/06/bddb2ecd37d849b68ff79845d8bc5e9e.png"
            mode=""
            style="width: 356rpx; height: 130rpx"
            v-if="tabIndex == 1"
          />
          <image
            class="relativeBottom"
            src="http://cdn.xiaodingdang1.com/2025/02/06/248e97c540164c829b923cb08e435c16.png"
            mode=""
            style="width: 356rpx; height: 120rpx"
            v-if="tabIndex != 1"
          />
        </view>
      </view>
      <view class="contentBg">
        <view class="backContentLeft" v-if="tabIndex == 1">
          <view
            class="topic"
            @tap="topicDetails"
            :data-topicid="item.topicId"
            v-for="(item, index) in hotList"
            :key="index"
          >
            <view class="topic1">
              <image
                v-if="item.topicPhotos"
                :src="item.topicPhotos[0]"
                mode=""
                class="topicImg"
              />
              <view class="topic2">
                <view class="topic1Text1">
                  <text class="topic1Text1_1">#</text>
                  <text class="topic1Text1_2">{{ item.topicName }}</text>
                </view>
                <view class="topic1Text2">
                  <text class="topic1Text2_1">简介：</text>
                  <text class="topic1Text2_2">{{ item.topicDescription }}</text>
                </view>
                <view class="topic1Text3">
                  <!-- body -->
                  <view class="avatar-list-stacked">
                    <text
                      class="avatar"
                      :style="
                        'background: url(' + res.avatar ||
                        $defaultAvatar +
                          ') center center;background-repeat:no-repeat;background-size: cover; background-position:0px 0px;  '
                      "
                      v-for="(res, i) in item.userList"
                      :key="i"
                    ></text>
                  </view>
                  <view class="topic1Text3_2">
                    <image
                      src="http://cdn.xiaodingdang1.com/1/120240125/328f8e35-a207-4845-b987-71ca8fd2c79c.png"
                      mode=""
                      style="width: 30rpx; height: 30rpx; margin-right: 4rpx"
                    />
                    <text class="topic1Text3_1">{{ item.postNum }}人参与</text>
                  </view>
                </view>
              </view>
              <view class="title">热门</view>
            </view>
          </view>
          <view class="packageText">
            <view class="packageText1"></view>
            <view class="packageText2" @tap="topic">
              <view class="more">更多话题</view>
              <view class="packageImg">
                <image
                  src="http://cdn.xiaodingdang1.com/1/120240115/2ee8ae21-7a8f-45d9-a490-fc0879a0d56b.png"
                  class="packageImgs"
                ></image>
              </view>
            </view>
          </view>
          <view class="packageText">
            <view class="packageText1">社区互动</view>
            <view class="packageText2"></view>
          </view>
          <view
            class="discuss"
            v-for="(item, index) in communityRecord"
            :key="index"
          >
            <view class="discus">
              <view class="discuss1">
                <view class="discuss1_1">
                  <image
                    :src="item.avatar || $defaultAvatar"
                    mode=""
                    style="width: 80rpx; height: 80rpx; border-radius: 50rpx"
                  />
                  <view class="discuss1_2">
                    <view class="discuss1_3">
                      {{ item.nickname }}
                    </view>
                    <view class="discuss1_4">
                      <text>{{ item.hisTimeStr }}</text>
                      <text class="discussName">{{ item.postIp }}</text>
                    </view>
                  </view>
                </view>
                <view
                  class="discuss1_5"
                  @tap="communityBookmark"
                  :data-postid="item.postId"
                  :data-index="index"
                >
                  <view v-if="!item.isBookmark">
                    <image
                      src="http://cdn.xiaodingdang1.com/2024/04/11/fc942fa7cdcd4b6db6dc5dd6e96d43b9png"
                      mode=""
                      style="width: 30rpx; height: 30rpx"
                    />
                  </view>
                  <view v-if="item.isBookmark">
                    <image
                      src="http://cdn.xiaodingdang1.com/2024/07/03/f567a0c7e397403fb4889a9dbe4ff00b.png"
                      mode=""
                      style="width: 30rpx; height: 30rpx"
                    />
                  </view>
                  <view class="discussTitle">收藏</view>
                </view>
              </view>
              <view>
                <view
                  :class="item.moreBtns ? 'beyonds' : 'discuss2'"
                  @tap="comment"
                  :data-postid="item.postId"
                >
                  <text class="discuss2_1">#{{ item.topicName }}</text>
                  <text class="discuss2_2">{{ item.content }}</text>
                </view>
                <view
                  class="packUp"
                  v-if="item.moreBtn"
                  @tap="packUps"
                  :data-index="index"
                >
                  {{ item.packUpShow ? "收回" : "展开" }}
                </view>
                <!-- 视频 -->
                <view
                  class="img_box"
                  v-if="item.videos && item.videos.length > 0"
                >
                  <view
                    class="videos"
                    v-for="(res, index1) in item.videos"
                    :key="index1"
                  >
                    <video
                      class="video"
                      :src="res"
                      @tap="preview"
                      :data-src="item.videos[0]"
                      data-index="0"
                      :ontrols="false"
                    ></video>
                  </view>
                </view>
                <!-- 图片 -->
                <view class="img_box" v-else>
                  <template v-if="item.imageUrl">
                    <view
                      id="loadImg{{index}}"
                      :class="[
                        'loadImg',
                        item.imageUrl && item.imageUrl.length > 1
                          ? 'many_img'
                          : '',
                        item.listShow ? 'active' : '',
                      ]"
                    >
                      <view
                        :class="
                          'img_item ' +
                          (item.imageUrl.length == 1 ||
                          item.imageUrl.length == 2 ||
                          item.imageUrl.length == 4
                            ? 'many'
                            : item.imageUrl.length >= 3
                            ? 'four'
                            : '')
                        "
                        v-for="(res, index1) in item.imageUrl"
                        :key="index1"
                      >
                        <image
                          v-if="item.listShow"
                          class="img"
                          :src="res"
                          @tap="previewImage"
                          :data-url="item.imageUrl"
                          :data-src="res"
                          :data-sources="item.imageUrl"
                          :data-index="index"
                          mode="aspectFill"
                        >
                        </image>
                      </view>
                    </view>
                  </template>
                </view>
              </view>
            </view>

            <view class="discuss4"></view>

            <view class="discusss5">
              <button class="custom-button" open-type="share">
                <view class="discuss5_1">
                  <image
                    src="http://cdn.xiaodingdang1.com/1/120240125/118c00f3-3132-46c7-83b3-628a1a90e2db.png"
                    mode=""
                    style="width: 26rpx; height: 26rpx"
                  />
                  <view class="discuss5_2">分享</view>
                </view>
              </button>
              <view class="custom-button">
                <view
                  class="discuss5_1"
                  @tap="comment"
                  :data-postid="item.postId"
                >
                  <image
                    src="http://cdn.xiaodingdang1.com/1/120240125/bac59985-fa99-4d0a-a9bd-e216fd0635c4.png"
                    mode=""
                    style="width: 26rpx; height: 26rpx"
                    v-if="!item.isFavorite"
                  />
                  <image
                    src="http://cdn.xiaodingdang1.com/2024/04/11/26b650690b474b42b1ddde47afd5291apng"
                    mode=""
                    style="width: 26rpx; height: 26rpx"
                    v-if="item.isFavorite"
                  />
                  <view
                    :class="item.isFavorite ? 'discuss5Active_2' : 'discuss5_2'"
                    >评论</view
                  >
                </view>
              </view>
              <view class="custom-button">
                <view
                  class="discuss5_1"
                  id="discuss5_1"
                  @tap="like"
                  :data-postid="item.postId"
                  :data-index="index"
                >
                  <image
                    src="http://cdn.xiaodingdang1.com/2024/07/03/8b67ac62cc964b9f8d40830de110a6a1.png"
                    mode=""
                    style="width: 26rpx; height: 26rpx"
                    v-if="item.isLike"
                  />
                  <image
                    src="http://cdn.xiaodingdang1.com/1/120240125/058cf0f5-7185-4671-961c-ee263c166795.png"
                    mode=""
                    style="width: 26rpx; height: 26rpx"
                    v-if="!item.isLike"
                  />
                  <view :class="item.isLike ? 'discuss5Active_2' : 'discuss5_2'"
                    >顶一下</view
                  >
                </view>
              </view>
            </view>
          </view>
        </view>
        <view class="backContentRight" v-if="tabIndex == 2">
          <view class="content">
            <view class="content-tab">
              <u-tabs
                ref="tabs1"
                bar-width="80"
                :list="activeList"
                active-color="#373536"
                inactive-color="#8C8A8B"
                font-size="30"
                v-model="current"
                @change="change"
              ></u-tabs>
            </view>
            <view class="content-main">
              <template v-if="pageList.length > 0">
                <waterfall-card-vue
                  class="waterfall-card"
                  ref="waterfallRef"
                  :list="pageList"
                  @detail="detail"
                >
                </waterfall-card-vue>
              </template>
            </view>
          </view>
          <u-back-top :scroll-top="scrollTop" top="800"></u-back-top>
          <!-- <view class="node">
                    <view class="nodeTitle">精选服务节点</view>
                    <view class="nodeTitle1">真实服务细节 让住过的宝妈亲口告诉您</view>
                    <view class="nodeImg">
                        <image src="http://cdn.xiaodingdang1.com/2024/08/19/d59e52ec4b9941738502a793f3cfa2bc.png"
                            mode="" style="width: 154rpx; height: 182rpx" />
                    </view>
                    <view class="nodeNext" @tap="momDetails" :data-postid="item.postId"
                        v-for="(item, index) in nodeList" :key="index">
                        <view class="nodeNext1">
                            <view class="next1">
                                <view class="nextImg" style="width: 30rpx; height: 30rpx">
                                    <image
                                        src="http://cdn.xiaodingdang1.com/2024/08/19/e369ae0a7ba8473a896503c247fb0140.png"
                                        mode="" style="width: 100%; height: 100%" />
                                </view>
                                <view class="nodeNext2">
                                    {{ item.content }}
                                </view>
                            </view>
                            <view>
                                <image
                                    src="http://cdn.xiaodingdang1.com/1/120240115/2ee8ae21-7a8f-45d9-a490-fc0879a0d56b.png"
                                    mode="" style="width: 8rpx; height: 18rpx" />
                            </view>
                        </view>

                        <view class="nodeFGX"></view>
                    </view>
                </view>
                <view class="newTab">
                    <view class="newTab1" @tap="newTab" data-index="0" data-type="ALL">
                        <image src="http://cdn.xiaodingdang1.com/2024/08/20/6f4a239a37c543baa0009d9d1968c7e7.png"
                            mode="" style="width: 72rpx; height: 51rpx" v-if="newTabIndex == 0" />
                        <view v-if="newTabIndex != 0" class="newTab2">推荐</view>
                    </view>
                    <view class="newTab1" @tap="newTab" data-index="1" data-type="USER">
                        <image src="http://cdn.xiaodingdang1.com/2024/08/20/fdd7771e41914216b57af03d4caada3a.png"
                            mode="" style="width: 108rpx; height: 51rpx" v-if="newTabIndex == 1" />
                        <view v-if="newTabIndex != 1" class="newTab2">宝妈说</view>
                    </view>
                    <view class="newTab1" @tap="newTab" data-index="2" data-type="CLUB">
                        <image src="http://cdn.xiaodingdang1.com/2024/08/20/d44c29e9e38c4be4aae86875d2097ab9.png"
                            mode="" style="width: 144rpx; height: 51rpx" v-if="newTabIndex == 2" />
                        <view v-if="newTabIndex != 2" class="newTab2">会所动态</view>
                    </view>
                </view>
                <view class="preciousMother" v-for="(item, index) in userPageList" :key="index">
                    <image src="http://cdn.xiaodingdang1.com/2024/08/19/0e0811dd2839421cbdd5bed46a85a5d0.png" mode=""
                        style="height: 48rpx; width: 100%" v-if="item.type == 'USER'" />

                    <image src="http://cdn.xiaodingdang1.com/2024/08/19/6212dff4d4864ea7933a978eb8d576e0.png" mode=""
                        style="height: 48rpx; width: 100%" v-if="item.type == 'CLUB'" />

                    <image src="http://cdn.xiaodingdang1.com/2024/08/19/a4419237fb47479eaaa96ed79968bee3.png" mode=""
                        style="height: 48rpx; width: 100%" v-if="item.type == 'STAFF'" />

                    <view class="preciousMother1">
                        <view class="mother1">
                            <view class="mother3">
                                <image :src="item.avatar || $defaultAvatar" mode=""
                                    style="width: 80rpx; height: 80rpx; border-radius: 100rpx" @tap="momCommunity"
                                    :data-userid="item.userId" />
                                <view class="mother1_2">
                                    <view style="display: flex">
                                        <view class="mother1Text2">{{ item.nickname || $defaultName }}</view>
                                        <image
                                            src="http://cdn.xiaodingdang1.com/2024/08/20/9d5d73050d974a55b07f5982484179bf.png"
                                            mode="" style="width: 175rpx; height: 38rpx; margin-left: 10rpx"
                                            v-if="item.type == 'STAFF'" />
                        
                                        <image
                                            src="http://cdn.xiaodingdang1.com/2024/08/20/28fe622b429246c1b9529cd1f6a82b3d.png"
                                            mode="" style="width: 138rpx; height: 50rpx; margin-left: 10rpx"
                                            v-if="item.type == 'USER'" />
                        
                                        <image
                                            src="http://cdn.xiaodingdang1.com/2024/08/19/4fb0a2dc7df44055b9d8bf7172d1d054.png"
                                            mode="" style="width: 72rpx; height: 38rpx; margin-left: 10rpx"
                                            v-if="item.type == 'CLUB'" />
                                    </view>
                                    <view class="mother1Text1">
                                        {{ item.createTimes }}
                                    </view>
                                </view>
                            </view>
                            <view class="lengthStay" v-if="item.type == 'USER'">入住第{{ item.liveDays }}天</view>
                    
                        </view>
                        <view v-if="item.type == 'STAFF'">
                            <view class="ageLimit">
                                从业年限
                                <text class="ageLimit1">{{ item.staffInfo.yearsEmployment }}</text>
                                服务人员数量
                                <text class="ageLimit2">{{ item.staffInfo.serviceNum }}人</text>
                            </view>
                            <view class="ageLimit3">
                                <view class="ageLimit5">人员标签</view>
                                <view class="ageLimit4" v-for="(item, index1) in item.staffInfo.staffTags"
                                    :key="index1">
                                    {{ item }}
                                </view>
                            </view>
                        </view>
                        <view :class="item.moreBtns ? 'beyond' : 'mother2'" @tap="momDetails(item)">
                            {{ item.content }}
                        </view>
                        <view class="packUp" v-if="item.moreBtn" @tap="packUp" :data-index="index">
                            {{ item.packUpShow ? '收回' : '展开' }}
                        </view>
                        <view class="img_box" v-if="item.videos && item.videos.length > 0">
                            <view class="videos" v-for="(res, index1) in item.videos" :key="index1">
                                <video class="video" :src="res" @tap="preview" :data-src="item.videos[0]" data-index="0"
                                    :ontrols="false"></video>
                            </view>
                        </view>
                        <view class="img_box" v-else>
                            <tempalte v-if="item.contentPhotos">
                                <view id="loadImg{{index}}" v-if="item.contentPhotos"
                                    :class="['loadImg', item.contentPhotos.length > 1 ? 'many_img' : '', item.listShow? 'active': '']">
                                    <view :class="
                                        'img_item ' +
                                        (item.contentPhotos.length == 1 || item.contentPhotos.length == 2 || item.contentPhotos.length == 4
                                            ? 'many'
                                            : item.contentPhotos.length >= 3
                                            ? 'four'
                                            : '')
                                    " v-for="(res, index1) in item.contentPhotos" :key="index1">
                                        <image v-if="item.listShow" class="img" :src="res" @tap="previewImage"
                                            :data-url="item.contentPhotos" :data-src="res"
                                            :data-sources="item.contentPhotos" :data-index="index" mode="aspectFill">
                                        </image>
                                    </view>
                                </view>
                            </tempalte>
                        </view>
                        <view class="comment1">
                            <view class="comment2" v-for="(e, j) in item.comments" :key="j">
                                <text class="mmcomment">{{ e.nickname }}:</text>
                                <text class="mmcomments">{{ e.comment }}</text>
                            </view>
                        </view>
                        <view>
                            <view class="discuss4"></view>
                            <view class="discusss6">
                                <functionbutton-vue :item="item"></functionbutton-vue>
                            </view>
                        </view>
                    </view>
                </view> -->
        </view>
      </view>
    </view>
    <new-request-loading></new-request-loading>
    <mask-dialog></mask-dialog>
  </view>
</template>

<script>
import functionbuttonVue from "@/components/functionbutton.vue";
import waterfallCardVue from "@/components/waterfallCard.vue";
export default {
  components: {
    functionbuttonVue,
    waterfallCardVue,
  },
  data() {
    return {
      observe: null,
      typeTab: "ALL",
      nodeList: [],
      newTabIndex: 0,
      menu: "",
      navBarHeight: "",
      navStatusBarHeight: "",
      bgColor: "",
      activeHead: false,
      types: "",
      pageSize: 10,
      pageNum: 1,
      featuredList: [],

      questionList: [
        {
          questionText:
            "一大早，大家就开始忙碌起来，挑选一大早，大家就开始忙碌起来，挑选",
        },
      ],

      tabLists: [
        {
          name: "宝妈说",
          index: 2,
        },
        {
          name: "热门话题",
          index: 1,
        },
      ],

      tagIndex: -1,
      taskNodeId: "",
      userList: [],
      roomFeedbackList: [],
      clubName: "",
      userPageList: [],
      date: "",
      userId: "",
      dayIndex: 0,
      userIndex: 0,
      hotList: [],
      communityRecord: [],
      arriveUserList: [],
      dayList: [],
      tabIndex: 2,

      res: {
        avatar: "",
      },

      i: "",
      j: "",

      e: {
        nickname: "",
        comment: "",
      },
      activeList: [
        {
          name: "热门推荐",
        },
        {
          name: "最新动态",
        },
      ],
      scrollTop: 0,
      current: 0,
      orderByColumn: "is_featured,fp.likes_count",
      pageList: [],
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.uniLoadInit(options);
    // uni.$on('reLoadPage', this.uniLoadInit(options));
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    this.observe = uni.createIntersectionObserver(this, {
      thresholds: [0],
    });
  },
  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.uniShowInit();
    // uni.$on('reShowPage', this.uniShowInit());
  },
  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {
    this.observe.disconnect();
  },
  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    var newDateTime = Date.parse(new Date());
    let data = {
      eventId: uni.getStorageSync("eventId"),
      leaveTime: newDateTime,
    };
    this.$point.reportEnd(data);
    this.observe.disconnect();
  },
  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},
  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    if (this.types == 1 && this.tabIndex == 2) {
      this.setData({
        pageNum: this.pageNum + 1,
      });
      this.getList();
      // this.customerPage();
    }
    if (this.types == 1 && this.tabIndex == 1) {
      this.setData({
        pageNum: this.pageNum + 1,
      });
      this.communityRecordFun();
    }
  },
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {},

  onShareTimeline() {},
  onPageScroll: function (e) {
    if (e.scrollTop > 0) {
      this.setData({
        bgColor: "#fff",
      });
    } else if (e.scrollTop < 2) {
      this.setData({
        bgColor: "",
      });
    }
    this.scrollTop = e.scrollTop;
  },
  methods: {
    change(index) {
      this.current = index;
      this.orderByColumn = !!index
        ? "fp.create_time"
        : "is_featured,fp.likes_count";
      this.pageNum = 1;
      this.pageList = [];
      this.getList();
    },
    getList() {
      this.changeLoading(true);
      let data = {
        pageSize: this.pageSize,
        pageNum: this.pageNum,
        type: "USER",
        isAsc: "desc",
        orderByColumn: this.orderByColumn,
      };
      if (this.postId) {
        data.postId = this.postId;
      }
      this.$axios
        .get(this.$api.customerPage, data)
        .then((res) => {
          if (res.data.code == 200) {
            let data = res.data.rows;
            let types = data.length == 10 ? 1 : 2;
            let dtnow = this.pageNum == 1 ? [] : this.pageList;
            this.setData({
              pageList: dtnow.concat(data),
              types: types,
            });
            this.$nextTick(() => {
              setTimeout(() => {
                // this.$refs.waterfallRef.visualizeLoadingImages();
              }, 400);
            });
          }
        })
        .finally(() => {
          this.changeLoading(false);
        });
    },
    detail(item) {
      uni.navigateTo({
        url:
          "/pageA/pageB/community/sending?userId=" +
          item.userId +
          "&postId=" +
          item.postId,
      });
    },
    uniLoadInit() {
      uni.setNavigationBarColor({
        frontColor: "#ffffff",
        backgroundColor: "#fff",
        animation: {
          duration: 400,
          timingFunc: "easeIn",
        },
      });
      if (this.tabIndex == 2) {
        // this.hotList()
        // this.communityRecord()
        this.arriveUserListFun();
        // this.customerPage();
        this.getList();
        this.featuredListFun();
        this.featuredNodList();
      }
      this.setData({
        clubName: uni.getStorageSync("clubName"),
      });
      var newDateTime = Date.parse(new Date());
      let data = {
        eventType: "PAGE_VIEW",
        pageUrl: "/pageA/pageB/community/motherspeak",
        module: "community",
        eventTime: newDateTime,
      };
      let iftoken = uni.getStorageSync("token");
      if (iftoken) {
        this.$point.basePoint(data);
      }
    },
    uniShowInit() {
      // 获取手机系统信息
      uni.getSystemInfo({
        success: (res) => {
          // 手机系统状态栏高度
          uni.setStorageSync("statusBarHeight", res.statusBarHeight);
          const platform = res.platform;
          const menu = uni.getMenuButtonBoundingClientRect();
          //menu为胶囊，判断是否能读到胶囊位置，读不到则用具体一般数值表示
          if (menu) {
            this.setData({
              menu: menu,
              navBarHeight: menu.height + (menu.top - res.statusBarHeight) * 2,
              navStatusBarHeight:
                res.statusBarHeight +
                menu.height +
                (menu.top - res.statusBarHeight) * 2,
            });
            // wx.setStorageSync('menu', menu)
            // // 导航栏高度
            // wx.setStorageSync('navBarHeight', menu.height+(menu.top-res.statusBarHeight) *2 )
            // // 状态栏加导航栏
            // wx.setStorageSync('navStatusBarHeight', res.statusBarHeight+ menu.height+(menu.top-res.statusBarHeight) *2 )
          } else {
            this.setData({
              menu: null,
              navBarHeight: platform === "android" ? 48 : 44,
              navStatusBarHeight:
                res.statusBarHeight + (platform === "android" ? 48 : 44),
            });
            // wx.setStorageSync('menu', null)
            // // 导航栏高度
            // wx.setStorageSync('navBarHeight', platform === 'android' ? 48 : 44)
            //  // 状态栏加导航栏
            //  wx.setStorageSync('navStatusBarHeight', res.statusBarHeight+ (platform === 'android' ? 48 : 44) )
          }
          console.log(this.menu);
          console.log(this.navBarHeight);
          console.log(this.navStatusBarHeight);
        },
        fail(err) {
          console.log(err);
        },
      });
      if (this.tabIndex == 2) {
        // this.hotList()
        // this.communityRecord()
        // this.arriveUserList()
        // this.customerPage()
        // this.featuredList()
      }
    },
    topic() {
      //话题
      uni.navigateTo({
        url: "/pageA/pageB/community/topicdetail/topic",
      });
    },

    comment(e) {
      //评论详情
      let postId = e.currentTarget.dataset.postid;
      uni.navigateTo({
        url:
          "/pageA/pageB/community/topicdetail/commentdetail?postId=" + postId,
      });
    },

    community() {
      uni.navigateTo({
        url: "/pageA/pageB/community",
      });
    },

    tabLeft() {
      this.setData({
        tabIndex: 1,
      });
      this.hotListFun();
      this.communityRecordFun();
    },

    tabRight() {
      this.setData({
        tabIndex: 2,
      });
      this.arriveUserListFun();
    },
    visualizeRecordImages() {
      let that = this;
      let communityRecord = that.communityRecord;
      for (let i in communityRecord) {
        uni
          .createIntersectionObserver(that, {
            thresholds: [0],
          })
          .relativeToViewport()
          .observe("#loadImg" + i, (ret) => {
            if (ret.intersectionRatio > 0) {
              communityRecord[i].listShow = true;
            }
            that.setData({
              [`communityRecord[${i}].listShow`]: communityRecord[i].listShow,
            });
          });
      }
      console.log("communityRecord", that.communityRecord);
    },
    communityRecordFun() {
      //查询宝妈社区动态列表
      this.changeLoading(true);
      let data = {
        pageSize: this.pageSize,
        pageNum: this.pageNum,
        orderType: 1,
      };
      this.$axios
        .get(this.$api.communityRecord, data)
        .then((res) => {
          if (res.data.code == 200) {
            let data = res.data.data.rows;
            let types = data.length == 10 ? 1 : 2;
            let dtnow;
            if (this.pageNum == 1) {
              dtnow = [];
            } else {
              dtnow = this.communityRecord;
            }
            data.forEach((item) => {
              if (item.content.length > 115) {
                item.packUpShow = false;
                item.moreBtns = true;
                item.moreBtn = true;
              } else {
                item.packUpShow = false;
                item.moreBtns = false;
                item.moreBtn = false;
              }
            });
            this.setData({
              communityRecord: dtnow.concat(data),
              types: types,
            });
            // #ifdef MP-WEIXIN
            wx.nextTick(() => {
              this.visualizeRecordImages();
            });
            // #endif
            // #ifdef H5
            this.$nextTick(() => {
              this.visualizeRecordImages();
            });
            // #endif
          }
        })
        .finally(() => {
          this.changeLoading(false);
        });
    },

    hotListFun() {
      //热门话题列表

      let data = {
        limit: 2,
      };
      this.$axios.get(this.$api.hotList, data).then((res) => {
        if (res.data.code == 200) {
          this.setData({
            hotList: res.data.data,
          });
        }
      });
    },

    arriveUserListFun() {
      //查询已到店入住客户列表

      this.$axios.get(this.$api.arriveUserList).then((res) => {
        if (res.data.code == 200) {
          this.setData({
            arriveUserList: res.data.data,
            userId: res.data.data ? res.data.data[0].userId : "",
          });
          // this.dayList()
          //   this.getCustomerServiceStep()
        }
      });
    },

    user(e) {
      let userId = e.currentTarget.dataset.userid;
      let index = e.currentTarget.dataset.index;
      this.setData({
        userIndex: index,
        userId: userId,
      });
      uni.navigateTo({
        url: "/pageA/pageB/community/motherspeak/momcommunity",
      });
      //  wx.navigateTo({
      //   url: '/packageA/pages/momCommunity/momCommunity?userId='+userId,
      // })
      //  this.dayList()
      // this.getCustomerServiceStep()
      //  this.feedPostUserPage()
    },

    dayListFun() {
      //查询客户入住天数列表

      let data = {
        userId: this.userId,
      };
      this.$axios.get(this.$api.customerAllTasks, data).then((res) => {
        if (res.data.code == 200) {
          let data = res.data.data;
          let listTab = [
            {
              taskName: "全部",
              feedPostId: "",
            },
          ];
          data.forEach((item) => {
            listTab.push(item);
          });
          this.setData({
            dayList: listTab,
            // date:res.data.data[0].date
          });
          this.feedPostUserPage();
          this.roomFeedbackListFun();
        }
      });
    },

    swichTag(e) {
      let current = e.currentTarget.dataset.current;
      let taskNodeId = e.currentTarget.dataset.tasknodeid;
      this.setData({
        tagIndex: current,
        taskNodeId: taskNodeId,
      });
      this.feedPostUserPage();
    },

    swichNav(e) {
      let current = e.currentTarget.dataset.current;
      let date = e.currentTarget.dataset.date;
      let feedPostId = e.currentTarget.dataset.feedpostid;
      let taskName = e.currentTarget.dataset.taskname;
      this.setData({
        dayIndex: current,
        date: date,
        tagIndex: -1,
      });
      if (taskName == "全部") {
        this.setData({
          taskNodeId: "",
        });
        this.feedPostUserPage();
        return;
      }
      // this.feedPostInfo(feedPostId)
      this.feedPostUserPage();
    },

    feedPostInfo(feedPostId) {
      //查询动态详情查询动态详情

      let data = {};
      if (feedPostId) {
        data.postId = feedPostId;
      }
      this.$axios.get(this.$api.feedPostInfo, data).then((res) => {
        if (res.data.code == 200) {
          if (res.data.data) {
            let data = [];
            data.push(res.data.data);
            this.setData({
              userPageList: data,
            });
          } else {
            this.setData({
              userPageList: [],
            });
          }
        }
      });
    },

    feedPostUserPage() {
      //查询指定用户朋友圈列表
      this.changeLoading(true);
      let data = {
        pageSize: 1000,
        pageNum: 1,
      };
      if (this.date) {
        data.date = this.date;
      }
      if (this.taskNodeId) {
        data.taskNodeId = this.taskNodeId;
      }
      this.$axios
        .get(this.$api.customerServiceStep, data)
        .then((res) => {
          if (res.data.code == 200) {
            let data = res.data.rows;
            data.forEach((item) => {
              if (item.content.length > 115) {
                item.packUpShow = false;
                item.moreBtns = true;
                item.moreBtn = true;
              } else {
                item.packUpShow = false;
                item.moreBtns = false;
                item.moreBtn = false;
              }
            });
            this.setData({
              userPageList: data,
            });
          }
        })
        .finally(() => {
          this.changeLoading(false);
        });
    },

    roomFeedbackListFun() {
      //查询指定用户评价列表

      let data = {
        pageSize: 1000,
        pageNum: 1,
        userId: this.userId,
      };
      this.$axios.get(this.$api.roomFeedbackList, data).then((res) => {
        if (res.data.code == 200) {
          this.setData({
            roomFeedbackList: res.data.data.rows,
          });
        }
      });
    },

    topicDetails(e) {
      let topicId = e.currentTarget.dataset.topicid;
      uni.navigateTo({
        url: "/pageA/pageB/community/topicdetail?topicId=" + topicId,
      });
    },

    communityBookmark(e) {
      //社区动态收藏
      let postId = e.currentTarget.dataset.postid;
      let index = e.currentTarget.dataset.index;
      let data = {
        postId: postId,
      };
      this.setData({
        [`communityRecord[${index}].isBookmark`]:
          !this.communityRecord[index].isBookmark,
      });
      this.$axios.put(this.$api.communityBookmark, data).then((res) => {
        if (res.data.code == 200) {
          // this.communityRecordFun();
        }
      });
    },
    like(e) {
      //社区点赞
      let index = e.currentTarget.dataset.index;
      let data = {
        postId: e.currentTarget.dataset.postid,
      };
      this.setData({
        [`communityRecord[${index}].isLike`]:
          !this.communityRecord[index].isLike,
      });
      this.$axios.put(this.$api.saveLike, data).then((res) => {
        if (res.data.code == 200) {
          // wx.showToast({
          // 	title:  '点赞成功',
          // 	icon: 'none',
          // 	duration: 2000//持续的时间
          // })
          // this.communityRecordFun();
        }
      });
    },

    likesCount(e) {
      //点赞
      let postId = e.currentTarget.dataset.postid;
      let index = e.currentTarget.dataset.index;
      let data = {
        postId: postId,
      };
      this.setData({
        [`userPageList[${index}].isLike`]: !this.userPageList[index].isLike,
      });
      this.$axios.get(this.$api.feedPostLikes, data).then((res) => {
        if (res.data.code == 200) {
          // this.customerPage();
        }
      });
    },

    favoriteCount(e) {
      //收藏
      let postId = e.currentTarget.dataset.postid;
      let index = e.currentTarget.dataset.index;
      let data = {
        postId: postId,
      };
      this.setData({
        [`userPageList[${index}].isFavorite`]:
          !this.userPageList[index].isFavorite,
      });
      this.$axios.get(this.$api.feedPostFavorite, data).then((res) => {
        if (res.data.code == 200) {
          // this.customerPage();
        }
      });
    },

    previewImage: function (e) {
      console.log(e);
      var current = e.currentTarget.dataset.src;
      var url = e.currentTarget.dataset.url;
      uni.previewImage({
        current: current,
        // 当前显示图片的http链接
        urls: url, // 需要预览的图片http链接列表
      });
    },

    getCustomerServiceStep() {
      //获取用户服务过程天数以及对应的节点

      let data = {
        userId: this.userId,
      };
      this.$axios.get(this.$api.getCustomerServiceStep, data).then((res) => {
        if (res.data.code == 200) {
          let data = res.data.data;
          let listTab = [
            {
              dayName: "全部",
              feedPostId: "",
            },
          ];
          data.forEach((item) => {
            listTab.push(item);
          });
          this.setData({
            dayList: listTab,
            // date:res.data.data[0].date
          });
          this.feedPostUserPage();
          this.roomFeedbackListFun();
        }
      });
    },

    preview(event) {
      let src = event.currentTarget.dataset.src;
      let maparr = [];
      maparr.push({
        type: "video",
        url: src,
      });
      let index = event.currentTarget.dataset.index;
      // 既有视频又有图片用这个
      console.log(maparr);
      uni.previewMedia({
        sources: maparr,
        // 官方文档 : 素材展示列表 是个数组对象 一个url链接 一个type 判断这是视频还是图片
        // 接收image 和 video 所以处理数据时 最好给个type 指定 image 或者 video
        current: index,
        // 当前显示的资源序号
      });
    },

    titleClick(e) {
      let index = e.currentTarget.dataset.index;
      this.setData({
        tabIndex: index,
        pageNum: 1,
        types: "",
      });
      if (index == 1) {
        this.hotListFun();
        this.communityRecordFun();
      }
      if (index == 2) {
        // this.arriveUserListFun();
        // this.customerPage();
        this.getList();
      }
    },

    momDetails(item) {
      let postId = item.postId;
      let userId = item.userId;
      uni.navigateTo({
        url:
          "/pageA/pageB/community/staffdetail?postId=" +
          postId +
          "&userId=" +
          userId,
      });
    },

    momCommunity(e) {
      console.log(e);
      let userId = e.currentTarget.dataset.userid;
      uni.navigateTo({
        url: "/pageA/pageB/community/motherspeak/momcommunity?userId" + userId,
      });
    },

    packUp(e) {
      //收起全文 动态
      let index = e.currentTarget.dataset.index;
      this.setData({
        [`userPageList[${index}].packUpShow`]:
          !this.userPageList[index].packUpShow,
        [`userPageList[${index}].moreBtns`]: !this.userPageList[index].moreBtns,
      });
    },

    packUps(e) {
      //收起全文 话题
      let index = e.currentTarget.dataset.index;
      this.setData({
        [`communityRecord[${index}].packUpShow`]:
          !this.communityRecord[index].packUpShow,
        [`communityRecord[${index}].moreBtns`]:
          !this.communityRecord[index].moreBtns,
      });
    },
    // visualizeLoadingImages() {
    //     let that = this
    //     let userPageList = that.userPageList
    //     for (let i in userPageList) {
    //         uni.createIntersectionObserver(that, {
    //             thresholds: [0],
    //         }).relativeToViewport().observe('#loadImg' + i, (ret) => {
    //             if (ret.intersectionRatio > 0) {
    //                 userPageList[i].listShow = true
    //             }
    //             that.setData({
    //                 [`userPageList[${i}].listShow`]: userPageList[i].listShow,
    //             })
    //         })
    //     }
    //     console.log('userPageList', that.userPageList)
    // },
    customerPage() {
      this.changeLoading(true);
      let data = {
        pageSize: this.pageSize,
        pageNum: this.pageNum,
        type: this.typeTab,
      };
      // if(this.data.date){
      //   data.date=this.data.date
      // }
      // if(this.data.taskNodeId){
      //   data.taskNodeId=this.data.taskNodeId
      // }
      this.$axios
        .get(this.$api.customerPage, data)
        .then((res) => {
          if (res.data.code == 200) {
            let data = res.data.rows;
            let types = data.length == 10 ? 1 : 2;
            let dtnow;
            if (this.pageNum == 1) {
              dtnow = [];
            } else {
              dtnow = this.userPageList;
            }
            data.forEach((item) => {
              if (item.content.length > 115) {
                item.packUpShow = false;
                item.moreBtns = true;
                item.moreBtn = true;
              } else {
                item.packUpShow = false;
                item.moreBtns = false;
                item.moreBtn = false;
              }
              var timearr = item.createTime
                .replace(" ", ":")
                .replace(/\:/g, "-")
                .split("-");
              item.createTimes =
                timearr[1] +
                "/" +
                timearr[2] +
                " " +
                timearr[3] +
                ":" +
                timearr[4];
            });
            this.setData({
              userPageList: dtnow.concat(data),
              types: types,
            });
            // #ifdef MP-WEIXIN
            wx.nextTick(() => {
              this.visualizeLoadingImages();
            });
            // #endif
            // #ifdef H5
            this.$nextTick(() => {
              this.visualizeLoadingImages();
            });
            // #endif
          }
        })
        .finally(() => {
          this.changeLoading(false);
        });
    },

    featuredListFun() {
      this.$axios.get(this.$api.featuredList).then((res) => {
        if (res.data.code == 200) {
          let data = res.data.data;
          this.setData({
            featuredList: data,
          });
        }
      });
    },

    next() {
      uni.navigateBack({
        delta: 1,
      });
    },

    newTab(e) {
      let index = e.currentTarget.dataset.index;
      let type = e.currentTarget.dataset.type;
      this.setData({
        newTabIndex: index,
        typeTab: type,
        pageNum: 1,
      });
      this.getList();
      // this.customerPage();
    },

    headTab(e) {
      let index = e.currentTarget.dataset.index;
      this.setData({
        tabIndex: index,
        pageNum: 1,
        types: "",
      });
      if (index == 1) {
        this.hotListFun();
        this.communityRecordFun();
      }
      if (index == 2) {
        this.arriveUserListFun();
        this.customerPage();
      }
    },

    featuredNodList() {
      //查询精选节点列表

      let data = {
        limit: 5,
      };
      this.$axios.get(this.$api.featuredNodList, data).then((res) => {
        if (res.data.code == 200) {
          this.setData({
            nodeList: res.data.data,
          });
        }
      });
    },
  },
};
</script>
<style scoped lang="less">
@import "./motherspeak.less";
</style>
