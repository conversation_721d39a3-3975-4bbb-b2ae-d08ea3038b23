<template>
  <view>
    <navigation
      :background="bgColor"
      :title="titleName"
      :isSowArrow="true"
    ></navigation>
    <view class="main">
      <view class="mainss">
        <view class="listHesds1">
          <view class="listHesds">
            <image
              @tap="goMomNote"
              lazy-load
              :src="feedPostInfo.avatar || $defaultAvatar"
              mode=""
              style="width: 80rpx; height: 80rpx; border-radius: 100rpx"
            />
            <view class="listHesds_1">
              <view class="listHesds_2">
                <view class="listHesds_3">
                  {{ feedPostInfo.nickname || $defaultName }}
                </view>
                <view class="label">
                  <view class="label-left">
                    <image
                      src="http://cdn.xiaodingdang1.com/2024/11/28/f0c7b989f9874fa8bb57e85ed07c0ca5.png"
                    >
                    </image>
                    宝妈
                  </view>
                </view>
              </view>
              <view class="listHesds_4" v-if="feedPostInfo.momId">
                年龄：{{ feedPostInfo.age }} &nbsp;宝宝：{{
                  feedPostInfo.babyWeight
                }}
              </view>
              <view class="listHesds_4" v-else>
                {{ feedPostInfo.createTimes }}
              </view>
            </view>
          </view>
          <view
            class="lengthStay"
            v-if="
              feedPostInfo.customerInfo &&
              Object.keys(feedPostInfo.customerInfo).length > 0
            "
          >
            入住第{{ feedPostInfo.customerInfo.checkInDays }}天
          </view>
        </view>
        <swiper
          :indicator-dots="indicatorDots"
          :interval="interval"
          :duration="duration"
          :autoplay="autoplay"
          indicator-color="#D9D9D9"
          indicator-active-color="#333333"
          class="swipper1"
        >
          <block v-for="(item, index) in backgroundArr" :key="index">
            <swiper-item>
              <video
                :id="`sendingVideo${index}`"
                @touchstart="onTouchStart($event, index)"
                @touchend="onTouchEnd($event, index)"
                v-if="item.type == 'video'"
                class="u-wrp-bnr"
                :src="item.url"
                @tap="preview"
                :data-index="index"
              ></video>
              <image
                lazy-load
                class="u-wrp-bnr"
                :src="item.url"
                @tap="preview"
                data-type="18"
                :data-index="index"
                :data-clubactivities="clubHomeList.clubActivities[index]"
                mode="aspectFill"
              ></image>
            </swiper-item>
          </block>
        </swiper>
        <view class="content">
          <view class="title" v-if="originContent">
            <!-- <view class="title_1">#</view> -->
            <view class="title_2">
              {{ originContent }}
            </view>
            <view class="title-devide"></view>
          </view>
          <view class="title_3" v-html="feedPostInfo.content"></view>
          <view class="issue" v-if="feedPostInfo.momId">
            <view class="issue_1">发布于{{ feedPostInfo.createTimes }}</view>
          </view>
        </view>
        <view class="comment-list">
          <comment-list-vue
            @repay="repay"
            ref="commentListRef"
            :item="feedPostInfo"
            :inputBottomHeight="inputBottomHeight"
          ></comment-list-vue>
        </view>
        <view class="dynamicState">
          <view class="titles1">
            <view class="fgx"></view>
            <view class="titles">宝妈说</view>
          </view>
          <view class="dynamic">
            <template v-if="myPageList.length > 0">
              <waterfall-card-vue
                class="waterfall-card"
                ref="list"
                :list="myPageList"
                @detail="next"
                :showTag="false"
              >
              </waterfall-card-vue>
            </template>
          </view>
        </view>
      </view>
    </view>
    <bottom-button-vue
      :item="feedPostInfo"
      :inputBottomHeight="inputBottomHeight"
      @input="inputComment"
    ></bottom-button-vue>
    <new-request-loading></new-request-loading>
  </view>
</template>

<script>
import utils from '@/utils/util.js';
import commentListVue from '@/components/commentList.vue';
import waterfallCardVue from '@/components/waterfallCard.vue';
import bottomButtonVue from '@/components/bottomButton.vue';
export default {
  components: {
    waterfallCardVue,
    commentListVue,
    bottomButtonVue,
  },
  data() {
    return {
      noClick: true,
      backgroundArr: [],
      feedPostInfo: {
        avatar: '',
        nickname: '',
        createTimes: '',
        liveDays: '',
        content: '',
        shareCount: false,
        postId: '',
        isFavorite: '',
        favoriteCount: false,
        isLike: '',
        likesCount: false,
      },
      myPageList: [],
      types: '',
      pageSize: 10,
      pageNum: 1,
      listData: [1, 2, 3, 4, 5],
      indicatorDots: true,
      autoplay: true,
      interval: 4000,
      duration: 1000,
      userId: '',
      bgColor: '#ffffff',
      titleName: '',
      clubHomeList: {
        clubActivities: '',
      },
      isZoomed: false, // 是否缩放状态
      videoContext: null,
      originContent: '',
      inputBottomHeight: 0,
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.setData({
      userId: options.userId,
      postId: options.postId,
    });
    this.getList();
    this.getfeedPostInfo();
    uni.onKeyboardHeightChange((res) => {
      if (res.height) {
        this.inputBottomHeight = res.height + 'px';
      } else {
        this.inputBottomHeight = 0;
      }
    });
  },
  onShare() {
    this.setData({
      'feedPostInfo.shareCount': ++this.feedPostInfo.shareCount,
    });
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},
  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {},
  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},
  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},
  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},
  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    if (this.types == 1) {
      this.setData({
        pageNum: this.pageNum + 1,
      });
      this.getList();
    }
  },
  onPageScroll: function (e) {
    if (e.scrollTop > 0) {
      this.setData({
        bgColor: '#ffffff',
      });
    } else if (e.scrollTop < 2) {
      this.setData({
        bgColor: '#ffffff',
      });
    }
  },
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {},

  onShareTimeline() {},
  methods: {
    goMomNote() {
      // 有momId跳转到笔记  没有跳转到主页
      let params = {
        userId: this.feedPostInfo.userId,
        momId: this.feedPostInfo.momId,
        postId: this.feedPostInfo.postId,
      };
      let url = this.feedPostInfo.momId
        ? '/pageA/pageB/community/note/notedetail'
        : '/pageA/pageB/community/motherhome';
      this.$jumpPage(url, params);
    },
    // 评论里面回复
    repay(value) {
      if (value) {
        this.feedPostInfo.commentsCount = this.feedPostInfo.commentsCount + 1;
      }
    },
    // 外面回复
    inputComment(value) {
      if (value) {
        this.feedPostInfo.commentsCount = this.feedPostInfo.commentsCount + 1;
        this.$axios
          .post(this.$api.feedPostComment, {
            comment: value,
            postId: this.feedPostInfo.postId,
          })
          .then((res) => {
            const userInfo = uni.getStorageSync('userInfo');
            const currentDate = utils.formatTime(new Date());
            const commentData = {
              comment: value,
              avatar: userInfo.avatar,
              createTime: currentDate,
              nickname: userInfo.nickname,
            };
            if (
              this.feedPostInfo.comments &&
              this.feedPostInfo.comments.length > 0
            ) {
              this.feedPostInfo.comments.push(commentData);
            } else {
              this.feedPostInfo.comments = [commentData];
            }
            console.log('this.feedPostInfo', this.feedPostInfo);
          });
      }
    },
    onTouchStart(event, index) {
      this.isZoomed = true; // 触摸开始时设置为缩放状态
    },
    onTouchEnd(event, index) {
      this.isZoomed = false; // 触摸结束时设置为非缩放状态
      if (!this.isZoomed) {
        this.pauseVideo(index); // 如果不是缩放状态，暂停视频播放
      }
    },
    pauseVideo(index) {
      this.videoContext = uni.createVideoContext(`sendingVideo${index}`, this);
      if (this.videoContext) {
        this.videoContext.pause();
      }
    },
    onShareClick() {
      this.setData({
        'feedPostInfo.shareCount': ++this.feedPostInfo.shareCount,
      });
    },
    getList() {
      let data = {
        pageSize: this.pageSize,
        pageNum: this.pageNum,
        // userId:this.data.userId
        notPostIds: this.postId,
        type: 'USER',
      };
      this.changeLoading(true);
      this.$axios
        .get(this.$api.customerPage, data)
        .then((res) => {
          if (res.data.code == 200) {
            let data = res.data.rows;
            let types = data.length == 10 ? 1 : 2;
            let dtnow;
            if (this.pageNum == 1) {
              dtnow = [];
            } else {
              dtnow = this.myPageList;
            }
            this.setData({
              myPageList: dtnow.concat(data),
              types: types,
            });
            // #ifdef MP-WEIXIN
            wx.nextTick(() => {
              this.$refs.list.visualizeLoadingImages();
            });
            // #endif
            // #ifdef H5
            this.$nextTick(() => {
              this.$refs.list.visualizeLoadingImages();
            });
            // #endif
          }
        })
        .finally(() => {
          this.changeLoading(false);
        });
    },

    getfeedPostInfo() {
      let data = {
        postId: this.postId,
      };
      this.changeLoading(true);
      this.$axios
        .get(this.$api.feedPostInfo, data)
        .then((res) => {
          if (res.data.code == 200) {
            let data = res.data.data;
            let list = [];
            if (data.videos && data.videos.length > 0) {
              data.videos.forEach((item) => {
                let data = {
                  type: 'video',
                  url: item,
                };
                list.push(data);
              });
            }
            if (data.contentPhotos && data.contentPhotos.length > 0) {
              data.contentPhotos.forEach((item) => {
                let data = {
                  type: 'image',
                  url: item,
                };
                list.push(data);
              });
            }
            var timearr = data.createTime
              .replace(' ', ':')
              .replace(/\:/g, '-')
              .split('-');
            data.createTimes =
              timearr[1] +
              '/' +
              timearr[2] +
              ' ' +
              timearr[3] +
              ':' +
              timearr[4];
            if (data.title) {
              this.originContent = data.title;
            }
            console.log(res.data.data);
            this.setData({
              backgroundArr: list,
              feedPostInfo: data,
              titleName: data.nickname + '动态',
            });
          }
        })
        .finally(() => {
          this.changeLoading(false);
        });
    },

    preview(event) {
      let maparr = this.backgroundArr;
      let index = event.currentTarget.dataset.index;
      // 既有视频又有图片用这个
      uni.previewMedia({
        sources: maparr,
        // 官方文档 : 素材展示列表 是个数组对象 一个url链接 一个type 判断这是视频还是图片
        // 接收image 和 video 所以处理数据时 最好给个type 指定 image 或者 video
        current: index,
        // 当前显示的资源序号
        autoplay: true,
      });
    },

    likesCount(e) {
      //点赞
      let { postId, type, index } = e;
      let data = {
        postId: postId,
      };
      if (type === '1') {
        const count = !this.feedPostInfo.isLike
          ? ++this.feedPostInfo.likesCount
          : --this.feedPostInfo.likesCount;
        this.setData({
          'feedPostInfo.isLike': !this.feedPostInfo.isLike,
          'feedPostInfo.likesCount': count,
        });
      } else if (type === '2') {
        const count = !this.myPageList[index].isLike
          ? ++this.myPageList[index].likesCount
          : --this.myPageList[index].likesCount;
        this.setData({
          [`myPageList[${index}].isLike`]: !this.myPageList[index].isLike,
          [`myPageList[${index}].likesCount`]: count,
        });
      }
      this.$axios.get(this.$api.feedPostLikes, data).then((res) => {
        if (res.data.code == 200) {
          if (type == 2) {
            // this.getList();
          } else if (type == 1) {
            // this.getfeedPostInfo(postId);
          }
        }
      });
    },

    favoriteCount(e) {
      //收藏
      const count = !this.feedPostInfo.isFavorite
        ? ++this.feedPostInfo.favoriteCount
        : --this.feedPostInfo.favoriteCount;
      this.setData({
        'feedPostInfo.isFavorite': !this.feedPostInfo.isFavorite,
        'feedPostInfo.favoriteCount': count,
      });
      // let postId = e.currentTarget.dataset.postid;
      let postId = e.postId;
      let data = {
        postId: postId,
      };
      this.$axios.get(this.$api.feedPostFavorite, data).then((res) => {
        if (res.data.code == 200) {
          // this.getfeedPostInfo(postId);
        }
      });
    },

    next(e) {
      let postId = e.postId;
      let userId = e.userId;
      uni.redirectTo({
        url:
          '/pageA/pageB/community/sending/detail?postId=' +
          postId +
          '&userId=' +
          userId,
      });
    },
  },
};
</script>
<style scoped>
@import './detail.css';
</style>
