.main {
	background: #f6f7fb;
}

.content {
	background: #fff;
	padding: 20rpx 24rpx;
}

.head {
	display: flex;
	justify-content: space-between;
}

.cancel {
	color: #333333;
	font-size: 28rpx;
}

.publish-upload {
	display: block;
}

.publish {
	width: 96rpx;
	text-align: center;
	height: 56rpx;
	line-height: 56rpx;
	background: #7e6dfc;
	color: #ffffff;
	border-radius: 10rpx;
	font-size: 28rpx;
}

.uploading {
	display: flex;
	flex-wrap: wrap;
	margin-bottom: 20rpx;
}

.uploadingImg {
	margin-right: 20rpx;
	position: relative;
}

.topicName {
	color: #5b6799;
	font-size: 28rpx;
	width: 100%;
}

.topic {
	display: flex;
	margin-top: 48rpx;
}


.addImg1 {
	width: 200rpx;
	height: 200rpx;
	border: 2rpx dashed #aaaaaa;
	text-align: center;
}

.addImg {
	font-size: 24rpx;
	color: #aaaaaa;
}

.textarea {
	margin-top: 20rpx;
	margin-bottom: 20rpx;
	width: 690rpx;
	font-size: 26rpx;
}

.roomNUm {
	color: #333333;
	font-size: 28rpx;
	font-weight: bold;
}

.grid-container {
	display: grid;
	margin: 20rpx 0rpx 20rpx 0rpx;
	grid-template-columns: repeat(auto-fill, calc(33.3333% - 30rpx));
	/** 平铺宽度 */
	grid-template-rows: auto;
	/** 设置高度为  */
	grid-auto-rows: auto;
	/** 当容易高度不够时，多余的组件高度将怎么分配，默认的高度由单元格内容决定 */
	justify-content: center;
	/** 水平居中  */
	grid-gap: 30rpx;
	/** 水平和垂直间距*/
	align-items: center;
}

.grid-item {
	display: flex;
	height: 64rpx;
	align-items: center;
	justify-content: center;
	border-radius: 8rpx;
	background: #3f6fff;
	color: #fff;
	font-size: 28rpx;
}

.activeRoom {
	display: flex;
	height: 64rpx;
	align-items: center;
	justify-content: center;
	border-radius: 8rpx;
	background: #3f6fff;
	color: #fff;
	font-size: 28rpx;
}

.grid-item1 {
	display: flex;
	height: 64rpx;
	align-items: center;
	justify-content: center;
	border-radius: 8rpx;
	background: #f3f4f8;
	color: #333333;
	font-size: 28rpx;
	margin-bottom: 20rpx;
}

.roomNumber {
	margin-left: 10rpx;
}

.grid-container1 {
	display: grid;
	margin: 20rpx 0rpx 20rpx 0rpx;
	grid-template-columns: repeat(auto-fill, calc(33.3333% - 30rpx));
	/** 平铺宽度 */
	grid-template-rows: auto;
	/** 设置高度为  */
	grid-auto-rows: auto;
	/** 当容易高度不够时，多余的组件高度将怎么分配，默认的高度由单元格内容决定 */
	justify-content: space-between;
	/** 水平居中  */
	/* grid-gap: 30rpx; */
	/** 水平和垂直间距*/
	align-items: center;
}

#grid-item1 {
	width: 200rpx;
	display: flex;
	height: 64rpx;
	align-items: center;
	justify-content: center;
	border-radius: 8rpx;
	background: #f3f4f8;
	color: #3f6fff;
	font-size: 28rpx;
}

.grid-item1 {
	display: flex;
	height: 64rpx;
	align-items: center;
	justify-content: center;
	border-radius: 8rpx;
	background: #f3f4f8;
	color: #333333;
	font-size: 28rpx;
}

.gridActive {
	display: flex;
	height: 64rpx;
	align-items: center;
	justify-content: center;
	border-radius: 8rpx;
	background: #e3eaff;
	color: #3f6fff;
	font-size: 28rpx;
	margin-bottom: 20rpx;
}

.templates {
	display: flex;
	justify-content: space-between;
	flex-wrap: wrap;
	padding: 20rpx 24rpx;
}

.template {
	background: #fff;
	width: 40%;
	padding: 28rpx;
	border-radius: 20rpx;
	color: #333333;
	font-size: 28rpx;
	margin-bottom: 28rpx;

}

.template-text {
	width: 100%;
	word-break: break-all;
	display: -webkit-box;
	overflow: hidden;
	text-overflow: ellipsis;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 7;
}

.activeTemplate {
	width: 40%;
	padding: 28rpx 28rpx;
	border-radius: 20rpx;
	background: #e3eaff;
	color: #3f6fff;
	font-size: 28rpx;
	margin-bottom: 28rpx;
}

.addTab {
	display: flex;
	align-items: center;
}

.tabInput input {
	width: 300rpx;
	height: 60rpx;
	border-radius: 6rpx;
	border: 1rpx solid #333333;
	padding: 0 24rpx;
}

.confirm {
	height: 60rpx;
	width: 120rpx;
	text-align: center;
	line-height: 60rpx;
	border-radius: 6rpx;
	background: #3f6fff;
	color: #fff;
	font-size: 24rpx;
	margin-left: 10rpx;
}

.addTab1 {
	margin: 12rpx 24rpx 12rpx 0;
}

.sales {
	width: 100%;
	display: flex;
	flex-wrap: wrap;
}

.sales view {
	width: 50%;
}

.publish-input /deep/.u-input {
	background-color: #3f6fff !important;
	margin: 16rpx 46rpx 20rpx 0;
	padding: 0 30rpx !important;
	border-radius: 8rpx;
}

.publish-input /deep/.u-input__input {
	font-weight: 500;
	font-size: 28rpx;
	color: #FFFFFF !important;
	line-height: 33rpx;
	min-height: 64rpx !important;
}

.publish-input /deep/.u-icon__icon {
	color: white !important;
	font-size: 18rpx !important;
}

.publish-nurse /deep/.u-input__input {
	text-align: right;
	font-size: 26rpx;
}

.publish-nurse /deep/.u-input__right-icon__item {
	margin-bottom: 4rpx;
}

.publish-nurse /deep/.u-icon__icon {
	font-size: 18rpx !important;
}

.nurse-choose {
	width: 50%;
	position: absolute;
	right: 0;
	top: 50rpx;
}

.uni-input {
	padding: 30rpx 12rpx;
	font-size: 28rpx;
	border-bottom: 1rpx solid #F3F4F8;
}
.tabsHead{
	position:sticky;
	top: 0;
	z-index: 999;
	background: #fff;
}
.tabs {
	background: #fff;
	border-radius: 20rpx;
	margin: 24rpx 24rpx;
	height: 700rpx;
	overflow-y: auto;
	position: relative;
}

.cutOff {
	width: 100%;
	height: 1rpx;
	background: #F0F0F0;
	margin: 14rpx 0;
}
.tabsContent{
	z-index: 9;
}
.tabsContent1 {
	padding: 24rpx 36rpx;
	color: #737373;
	font-size: 28rpx;
}
.maskContent{
	background: #fff;
	width: 70%;
	border-radius: 20rpx;
	margin:40% 15%;
	padding: 36rpx;
	position: fixed;
}
.maskTitle{
	color:#333333 ;
	font-size: 34rpx;
	font-weight: bold;
}
.maskContent1{
	border: 1rpx solid #E8E8E8;
	background: #FAFAFA;
	border-radius: 10rpx;
	padding: 28rpx 28rpx;
	font-size: 28rpx;
    color: #6F6F6F;
	margin: 22rpx 0;
	height: 500rpx;
	width: 90%;

}
.maskBtn{
	display: flex;
	justify-content: space-between;
}
.container {
  width: 100%;
  overflow: hidden;
}
.tab-scroll-view {
  white-space: nowrap;
}
.tab-wrapper {
  display: flex;
}
.tab-item {
  padding: 10px;
  margin-right: 10px;
  position: relative; /* 用于定位下划线 */
  cursor: pointer;
}
.tab-item.active::after {
  content: '';
  position: absolute;
  left: 25%;
  bottom: 0;
  width: 50%;
  height: 2px; /* 下划线的高度 */
  background-color: #007AFF; /* 下划线的颜色 */
}


.unfold{
	display: flex;
	justify-content: center;
align-items: center;
margin-bottom: 20rpx;
}