<template>
  <view class="btn">
    <template v-if="isActivity">
      <button
        @click="goSign"
        style="
          margin: 0 0;
          display: flex;
          padding: 3rpx 98rpx;
          justify-content: center;
          align-items: center;
          background: #ff4f61;
          border-radius: 100rpx;
        "
      >
        <u-icon name="tags" size="32" color="#ffffff"></u-icon>
        <view class="consultTitle">参加活动</view>
      </button>
    </template>
    <template v-else>
      <button @click="handleContact" class="btn2">
        <image
          src="http://cdn.xiaodingdang1.com/2024/04/24/450fd9c0bcad41eda68a19ab5228078bpng"
          mode=""
          style="width: 32rpx; height: 32rpx"
        />
        <view style="color: #333333; font-weight: 400; margin-bottom: 5rpx"
          >预约到店</view
        >
      </button>
      <view class="btn1" @tap="phoneCall">
        <image
          src="http://cdn.xiaodingdang1.com/2024/04/24/d75dc1e024ab4db7a429baf7b916066bpng"
          mode=""
          style="width: 32rpx; height: 32rpx"
        />
        <view>电话联系</view>
      </view>
    </template>
    <button
      @click="handleContact"
      style="
        margin: 0 0;
        display: flex;
        padding: 3rpx 98rpx;
        justify-content: center;
        align-items: center;
        background: #ff4f61;
        border-radius: 100rpx;
      "
    >
      <image
        src="http://cdn.xiaodingdang1.com/2024/05/30/3d5410c725194862807ec3f572db6817png"
        mode=""
        style="width: 32rpx; height: 32rpx"
      />
      <view class="consultTitle">在线咨询</view>
    </button>
  </view>
</template>

<script>
// 底部联系客服功能
export default {
  data() {
    return {};
  },
  props: {
    from: {
      type: String,
      default: '',
    },
    type: {
      type: String,
      default: '',
    },
    id: {
      type: String,
      default: '',
    },
    isActivity: {
      type: Boolean,
      default: false,
    },
    activityId: {
      type: String,
      default: '',
    },
  },
  methods: {
    goSign() {
      this.$axios.get(this.$api.getSinUp, {
        activityId: this.activityId,
      });
      uni.showToast({
        title: '报名成功～',
        icon: 'none',
        duration: 2000, //持续的时间
      });
    },
    phoneCall() {
      let servicePhone = uni.getStorageSync('servicePhone');
      uni.makePhoneCall({
        phoneNumber: servicePhone,
        success: function () {
          console.log('成功拨打电话');
        },
      });
    },

    handleContact() {
      // TODO 需要增加参数
      // type==1月子膳食菜品 type==2膳食套餐type==3产后康复type==4套餐咨询
      console.log(this.type);
      let data = {
        id: this.id,
      };
      if (this.type == 1) {
        this.$axios.get(this.$api.mealConsultItem, data).then((res) => {
          if (res.data.code == 200) {
          }
        });
      }
      if (this.type == 2) {
        this.$axios.get(this.$api.mealConsult, data).then((res) => {
          if (res.data.code == 200) {
          }
        });
      }
      if (this.type == 3) {
        this.$axios.get(this.$api.recoveryConsult, data).then((res) => {
          if (res.data.code == 200) {
          }
        });
      }
      if (this.type == 4) {
        this.$axios.get(this.$api.packageConsult, data).then((res) => {
          if (res.data.code == 200) {
          }
        });
      }
      uni.navigateTo({
        url: '/subPackages/customerService/pages/userChat',
      });
    },
  },
  created: function () {},
};
</script>
<style scoped>
.btn {
  position: fixed;
  bottom: 0;
  display: flex;
  align-items: center;
  background: #ffffff;
  width: 100%;
  padding: 20rpx 0 68rpx 0;
  justify-content: space-around;
}

.btn1 {
  text-align: center;
  color: #333333;
  font-size: 24rpx;
}

.consult {
  display: flex;
  padding: 20rpx 114rpx;
  background: -webkit-gradient(
    linear,
    left top,
    left bottom,
    color-stop(0%, #ffd9df),
    color-stop(100%, #fff5d8)
  );
  background: -webkit-linear-gradient(180deg, #ffd9df 0%, #fff5d8 100%);
  background: -o-linear-gradient(180deg, #ffd9df 0%, #fff5d8 100%);
  background: -ms-linear-gradient(180deg, #ffd9df 0%, #fff5d8 100%);
  background: linear-gradient(180deg, #ffd9df 0%, #fff5d8 100%);
  border-radius: 100rpx;
  justify-content: center;
  align-items: center;
}

.consultTitle {
  margin-left: 10rpx;
  color: #ffffff;
  font-size: 28rpx;
}

.btn2 {
  border-radius: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 200rpx;
  margin: 0 0;
  color: #333333;
  font-size: 24rpx;
  background-color: white;
  padding-top: 20rpx;
}

.btn2::after {
  border: 0;
}
</style>
