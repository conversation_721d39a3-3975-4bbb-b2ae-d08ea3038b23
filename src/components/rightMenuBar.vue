<template>
	<view class="btn">
		<button open-type="share" class="image-button">
			<image src="http://cdn.xiaodingdang1.com/2025/05/23/e4c88700d42747ba826408937b7177c7.png" mode=""
				style="width:100%;height:100%;" />
		</button>
		<button  class="image-button" @click="nextIndex()">
			<image src="http://cdn.xiaodingdang1.com/2025/05/23/3b004cce4ef54533900ef914db531cf8.png" mode=""
				style="width:100%;height:100%;" />
		</button>
		<u-back-top style="border-radius: 100rpx;" :scrollTop="scrollTop" :mode="mode" :icon-style="iconStyle"
			:custom-style="customStyle"></u-back-top>
	</view>
</template>

<script>
	// 底部联系客服功能
	export default {
		data() {
			return {
				detail:'',
			    banner:'',         
				mode: 'square',
				iconStyle: {
					fontSize: '35rpx',
					color: '#4A4A4A',
				},
				customStyle: {
					backgroundColor: '#ffffff',
					borderRadius: '100rpx',
					width: '70rpx',
					height: '70rpx',
					bottom:'230rpx',
					right:'24rpx',
                    boxShadow:'2px 2px 5px rgba(0,0,0,0.1)',					
				}
			};
		},
		props: {
			scrollTop: {
				type: String,
				default: 0
			},
		},
		methods: {
             nextIndex(){
				 uni.switchTab({
				 	url:'/pageA/home'
				 })
			 }
		},
		created: function() {}
	};
</script>
<style scoped>
	.btn {
		position: fixed;
		bottom: 310rpx;
		right: 0rpx;
	}

	.topRoof {
		background: #fff;
		border-radius: 100rpx;
		width: 80rpx;
		height: 80rpx;
		text-align: center;
		line-height: 130rpx;
	}
		
	.image-button{
		 /* 1. 去除按钮默认样式 */
		  padding: 0;
		  border: none;
		  background: none;
		  /* 2. 设置按钮尺寸 */
		  width: 120rpx;  /* 自定义宽度 */
		  height: 120rpx; /* 自定义高度 */
		  /* 3. 隐藏溢出内容 */
		  overflow: hidden;
		  /* 4. 可选：添加点击效果 */
		  cursor: pointer;
	}
	.image-button image {
	  /* 1. 确保图片填充容器 */
	  width: 100%;
	  height: 100%;
	  /* 2. 保持图片比例并裁剪填充 */
	  object-fit: cover;
	  /* 3. 消除img默认间距 */
	  display: block;
	  /* 4. 可选：添加过渡效果 */
	  transition: transform 0.3s ease;
	}
	/* 可选：悬停放大效果 */
/* 	.image-button:hover image {
	  transform: scale(1.1);
	} */
</style>