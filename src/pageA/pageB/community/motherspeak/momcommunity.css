.main {
    background: #f8f9f9;
    overflow: hidden;
}


.mainss {
    background: url('http://cdn.xiaodingdang1.com/2024/07/03/0f1684a201694f0787f62319df11bc48.png') no-repeat;
    background-size: 100% 478rpx;
}

.mainss {
    padding: 180rpx 0rpx 0rpx;
}

.next {
    display: flex;
    width: 100%;
    justify-content: center;
    z-index: 10000;
    margin-bottom: 30rpx;
    position: fixed;
    top: 0;
}

.headImg {
    position: absolute;
    left: 24rpx;
    bottom: 20rpx;
}

.headTitle {
    font-size: 34rpx;
    color: #333333;
    font-weight: bold;
    position: absolute;
    bottom: 20rpx;
}

.backContentRight {
    width: 100%;
    border-top-left-radius: 30rpx;
    padding: 24rpx 0;
}

.headNick {
    display: flex;
    align-items: center;
    padding: 0 24rpx;
}

.nickname {
    color: #333333;
    font-size: 32rpx;
    font-weight: bold;
    margin-left: 20rpx;
}

.tabList {
    display: flex;
    flex-wrap: wrap;
    margin: 26rpx 0rpx 0 0rpx;
}

.tag {
    font-size: 28rpx;
    color: #333333;
}

.tabList1 {
    background: #f1f1f1;
    padding: 0rpx 20rpx;
    border-radius: 5px 5px 5px 5px;
    color: #777777;
    font-size: 28rpx;
    margin-left: 20rpx;
    margin-bottom: 20rpx;
    height: 61rpx;
    display: flex;
    align-items: center;
}

.activeTab {
    display: flex;
    align-items: center;
    height: 61rpx;
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #ffedeb), color-stop(100%, #fff3eb));
    background: -webkit-linear-gradient(top, #ffedeb 0%, #fff3eb 100%);
    background: -o-linear-gradient(top, #ffedeb 0%, #fff3eb 100%);
    background: -ms-linear-gradient(top, #ffedeb 0%, #fff3eb 100%);
    padding: 0rpx 20rpx;
    border-radius: 5px 5px 5px 5px;
    color: #f1503d;
    font-size: 28rpx;
    margin-left: 20rpx;
    margin-bottom: 20rpx;
}

.preciousMother {
    background: #fff;
    border-radius: 20rpx;
    min-height: 316rpx;
    margin: 20rpx 20rpx;
}

.preciousMother1 {
    padding: 24rpx 24rpx;
}

.mother1 {
    display: flex;
}

.mother1_2 {
    margin-left: 10rpx;
}

.mother1Text2 {
    color: hsla(26, 100%, 50%, 1);
    font-size: 30rpx;
}

.head1_2 {
    display: flex;
    position: relative;
    margin-left: 16rpx;
}

.head1Img {
    position: absolute;
    left: -10rpx;
}

.head1_3 {
    background: #ffeecc;
    padding: 0 8rpx 0 30rpx;
    border-radius: 10rpx;
    color: #fb4105;
    font-size: 24rpx;
    height: 34rpx;
}

.mother1Text1 {
    color: hsla(0, 0%, 67%, 1);
    font-size: 24rpx;
    margin-top: 8rpx;
}

.motherImgs {
    margin: 20rpx 0 24rpx 0;
    display: flex;
    flex-wrap: wrap;
}

.motherImgs image {
    width: 220rpx;
    height: 220rpx;
    margin-left: 10rpx;
    margin-bottom: 15rpx;
}

.discuss3_1 {
    width: 220rpx;
    height: 220rpx;
}

.comment1 {
    margin: 24rpx 0;
    font-size: 24rpx;
}

.comment2 {
    padding: 6rpx 0;
}

.mmcomment {
    color: #7e6dfc;
}

.mmcomments {
    color: #777777;
    margin-left: 8rpx;
}

.discuss4 {
    width: 100%;
    height: 1rpx;
    background: hsla(0, 0%, 92%, 1);
}

.discuss5 {
    display: flex;
    justify-content: space-around;
    padding: 26rpx 0 32rpx 0;
    color: hsla(0, 0%, 47%, 1);
    font-size: 24rpx;
}


.discuss5_2 {
    margin-left: 4rpx;
}

.discuss5Active_2 {
    color: #fe5b86;
    font-size: 24rpx;
    margin-left: 4rpx;
}

.text {
    color: hsla(0, 0%, 20%, 1);
    font-size: 34rpx;
    padding: 20rpx 24rpx;
    font-weight: bold;
}

.preciousMothers {
    padding: 24rpx 24rpx;
    background: #fff;
    border-radius: 20rpx;
    margin-bottom: 20rpx;
    min-height: 316rpx;
    margin: 0 24rpx;
}

.mother1 {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.lengthStay {
    background: url('http://cdn.xiaodingdang1.com/2024/08/20/c707e0287940456787f1cef4f1dec273.png') no-repeat;
    width: 190rpx;
    height: 88rpx;
    background-size: 100% 100%;
    color: #ff4f61;
    font-size: 26rpx;
    font-weight: bold;
    text-align: center;
    line-height: 100rpx;
}

.lengthStays {
    background: url('http://cdn.xiaodingdang1.com/2024/08/20/46a44753a8844ba1880758c8fc1cbfc0.png') no-repeat;
    width: 190rpx;
    height: 88rpx;
    background-size: 100% 100%;
    color: #ff6c11;
    font-size: 26rpx;
    font-weight: bold;
    text-align: center;
    line-height: 100rpx;
}

.mother3 {
    display: flex;
}

.mother1_2 {
    margin-left: 10rpx;
}

.mother1Text2 {
    color: hsla(26, 100%, 50%, 1);
    font-size: 30rpx;
}

.head1_2 {
    display: flex;
    position: relative;
    margin-left: 16rpx;
}

.score {
    display: flex;
    margin-top: 12rpx;
    align-items: center;
    flex-wrap: wrap;
}

.score1 {
    color: #333333;
    font-size: 28rpx;
    font-weight: bold;
}

.score2 {
    padding: 4rpx 8rpx;
    color: #fff;
    background: #ff4f61;
    font-size: 24rpx;
    border-radius: 4rpx;
    margin-right: 10rpx;
}

.score3 {
    color: #777777;
    font-size: 24rpx;
    margin: 20rpx 0 0 0;
}

.img_box {
    margin-top: 20rpx;
    padding-left: 4rpx;
}

.videos {
    width: 340rpx;
    height: 340rpx;
}

.videos video {
    width: 100%;
    height: 100%;
    border-radius: 10rpx;
}

.img_box .many_img {
    display: flex;
    justify-self: start;
    flex-wrap: wrap;
}

.img_item.four {
    width: 32%;
    height: 200rpx;
    margin-bottom: 10rpx;
    border-radius: 16rpx;
    overflow: hidden;
}

.img_item.four image {
    width: 100%;
    height: 100%;
}

.img_item.four:nth-child(2) {
    margin: 0 1%;
}

.img_item.four:nth-child(5) {
    margin: 0 1%;
}

.img_item.four:nth-child(8) {
    margin: 0 1%;
}

.img_item.many {
    width: 48%;
    height: 340rpx;
    margin-bottom: 10rpx;
    border-radius: 10px;
    overflow: hidden;
    margin-right: 10rpx;
}

.img_item.many image {
    width: 100%;
    height: 100%;
}

.img_item.many:nth-child(2n) {
    margin-left: 1%;
}

.discusss5 {
    color: hsla(0, 0%, 47%, 1);
    font-size: 28rpx;
}

.custom-button {
    background: #fff;
    color: hsla(0, 0%, 47%, 1);
    font-size: 24rpx;
    margin: 0;
}

.discuss5_1 {
    display: flex;
    align-items: center;
    justify-content: center;
}

.packUp {
    color: #4d669b;
    font-size: 30rpx;
}

.mother2 {
    font-size: 30rpx;
    color: hsla(0, 0%, 20%, 1);
    margin: 16rpx 0 10rpx 0;
}

.beyond {
    font-size: 30rpx;
    color: hsla(0, 0%, 20%, 1);
    margin: 16rpx 0 10rpx 0;
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 5;
}

.roll {
    display: flex;
    padding: 0rpx 24rpx;
    overflow-x: auto;
}

.rollImg {
    width: 120rpx;
    text-align: center;
    margin-right: 42rpx;
}

.rollText {
    margin: 12rpx 0 20rpx 0;
}

.rollTextActive {
    margin: 12rpx 0 20rpx 0;
    color: #fe5b86;
    font-weight: 600;
}

.tab-h {
    width: 100%;
    box-sizing: border-box;
    font-size: 34rpx;
    white-space: nowrap;
}

.tab-item {
    display: inline-block;
    padding: 10rpx 40rpx;
    border-radius: 66rpx;
    color: #fe5b86;
    font-size: 32rpx;
    border: 1px solid #fe5b86;
    margin-right: 28rpx;
    background: #fff;
}

.border-triangle-bottom {
    padding: 10rpx 40rpx;
    position: relative;
    border-radius: 1px;
    border-radius: 66rpx;
    color: #fff;
    font-size: 32rpx;
    margin-right: 28rpx;
    background: #fe5b86;
    border: none;
}

.toLoad {
    background: #ff4f61;
    color: #ffffff;
    border-radius: 16rpx;
    font-size: 28rpx;
    width: 266rpx;
    height: 68rpx;
    text-align: center;
    line-height: 68rpx;
    margin: 0 auto;
}

.toLoads {
    background: #ff4f61;
    color: #ffffff;
    border-radius: 16rpx;
    font-size: 28rpx;
    width: 266rpx;
    height: 68rpx;
    text-align: center;
    line-height: 68rpx;
    margin: 20rpx auto;
}

.rollImgs {
    width: 120rpx;
    height: 120rpx;
    border-radius: 100rpx;
}

.imgActive {
    width: 140rpx;
    height: 140rpx;
    border-radius: 100rpx;
}

.tagName {
    padding: 4rpx 16rpx;
    background: #ff4f61;
    border-radius: 2px 2px 2px 2px;
    color: #ffffff;
    font-size: 24rpx;
    margin-left: 10rpx;
}

.ageLimit {
    color: #aaaaaa;
    font-size: 28rpx;
    font-weight: bold;
    margin: 16rpx 0;
}

.ageLimit1 {
    color: #ff6c11;
    margin: 0 26rpx 0 8rpx;
}

.ageLimit2 {
    color: #ff6c11;
    margin-left: 8rpx;
}

.ageLimit3 {
    display: flex;
    align-items: center;
    padding: 8rpx 8rpx;
    border-radius: 10rpx;
    background: #fff1ed;
}

.ageLimit4 {
    color: #ff6c11;
    font-size: 24rpx;
    margin: 0 12rpx;
    font-weight: bold;
}

.ageLimit5 {
    padding: 4rpx 8rpx;
    border-radius: 10rpx;
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #ffaa00), color-stop(100%, #f96323));
    background: -webkit-linear-gradient(180deg, #ffaa00 0%, #f96323 100%);
    background: -o-linear-gradient(180deg, #ffaa00 0%, #f96323 100%);
    background: -ms-linear-gradient(180deg, #ffaa00 0%, #f96323 100%);
    background: linear-gradient(180deg, #ffaa00 0%, #f96323 100%);
    font-size: 24rpx;
    color: #ffffff;
    font-weight: bold;
}

.refined {
    margin-left: 10rpx;
}

/* 遮罩层样式 */
.loading-mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    color: #fff;
}

/* 加载内容样式 */
.loading-content {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
    border-radius: 5px;
}