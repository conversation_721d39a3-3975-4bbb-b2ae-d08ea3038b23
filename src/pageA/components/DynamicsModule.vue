<template>
  <view class="dynamics-module" v-if="hasPostPageList">
    <!-- 标题区域 -->
    <view class="section-header" @click="handleMoreClick">
      <view class="section-header__title">商家动态</view>
      <view class="section-header__more">
        <view>更多</view>
        <u-icon name="arrow-right" color="#aaaaaa" size="26"></u-icon>
      </view>
    </view>

    <!-- 商家动态卡片 -->
    <view class="dynamic">
      <view class="dynamicTitle" @tap="handleMoreClick">
        <image
          lazy-load
          :src="postPageList.avatar || $defaultAvatar"
          mode=""
          style="width: 80rpx; height: 80rpx; border-radius: 50rpx"
        />
        <view class="dynamicTitle_1">
          <view class="dynamicTitle_2">
            {{ postPageList.clubName }}
          </view>
          <view class="dynamicTitle_3">
            {{ postPageList.hisTimeStr }}
          </view>
        </view>
      </view>
      <view
        :class="postPageList.moreBtns ? 'beyond' : 'dynamicTitle_4'"
        @tap="handleMoreClick"
      >
        {{ postPageList.content }}
      </view>
      <view
        class="packUp"
        v-if="postPageList.moreBtn"
        @tap="packUp"
        :data-index="0"
      >
        {{ postPageList.packUpShow ? '收回' : '展开' }}
      </view>
      <!-- 视频 -->
      <view
        class="img_box"
        v-if="postPageList.videos && postPageList.videos.length > 0"
      >
        <view
          class="videos"
          v-for="(res, index) in postPageList.videos"
          :key="index"
        >
          <video
            class="video"
            :src="res"
            @tap="previewUrl"
            :data-src="res"
            data-index="0"
            :controls="false"
          ></video>
        </view>
      </view>
      <!-- 图片 -->
      <view class="img_box" v-else>
        <template v-if="postPageList.contentPhotos">
          <view
            :class="postPageList.contentPhotos.length > 1 ? 'many_img' : ''"
          >
            <!-- 遍历 -->
            <!-- 在这里判断图片的数量是单站还是四张，分别给不同样式，来实现，一排是两张还是三张 -->
            <view
              :class="
                'img_item ' +
                (postPageList.contentPhotos.length == 1 ||
                postPageList.contentPhotos.length == 2 ||
                postPageList.contentPhotos.length == 4
                  ? 'many'
                  : postPageList.contentPhotos.length >= 3
                  ? 'four'
                  : '')
              "
              v-for="(res, index) in postPageList.contentPhotos"
              :key="index"
            >
              <image
                lazy-load
                class="img"
                :src="res"
                @tap="previewImage"
                :data-url="postPageList.contentPhotos"
                :data-src="res"
                :data-sources="postPageList.contentPhotos"
                :data-index="index"
                mode="aspectFill"
              />
            </view>
          </view>
        </template>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, computed, onMounted, getCurrentInstance } from 'vue';

// 响应式数据
const postPageList = reactive({
  contentPhotos: [],
  avatar: '',
  clubName: '',
  hisTimeStr: '',
  content: '',
  videos: [],
  moreBtns: false,
  moreBtn: false,
  packUpShow: false,
});

// 获取当前实例
const instance = getCurrentInstance();

// 计算属性
const hasPostPageList = computed(() => {
  return postPageList && postPageList.content;
});

// 生命周期
onMounted(() => {
  initDynamicsData();
});

// 初始化动态数据
const initDynamicsData = () => {
  postPage();
};

// 获取商家动态数据
const postPage = () => {
  const data = {
    pageSize: 1,
    pageNum: 1,
  };

  instance.proxy.$axios
    .get(instance.proxy.$api.postPage, data)
    .then((res) => {
      if (res.data.code == 200) {
        let data = res.data.rows;
        data.forEach((item) => {
          if (item.content.length > 115) {
            item.packUpShow = false;
            item.moreBtns = true;
            item.moreBtn = true;
          } else {
            item.packUpShow = false;
            item.moreBtns = false;
            item.moreBtn = false;
          }
        });
        Object.assign(postPageList, data[0] || {});
      }
    })
    .catch((error) => {
      console.error('获取商家动态数据失败:', error);
    });
};

// 展开/收起功能
const packUp = (e) => {
  const index = e.currentTarget.dataset.index;
  postPageList.packUpShow = !postPageList.packUpShow;
  postPageList.moreBtns = !postPageList.moreBtns;
};

// 图片预览
const previewImage = (e) => {
  const current = e.currentTarget.dataset.src;
  const urls = e.currentTarget.dataset.url;
  uni.previewImage({
    current: current,
    urls: urls,
  });
};

// 视频预览
const previewUrl = (e) => {
  const src = e.currentTarget.dataset.src;
  // 可以添加视频预览逻辑
  console.log('预览视频:', src);
};

// 事件处理 - 内部实现页面跳转
const handleMoreClick = () => {
  instance.proxy.$jumpPage('/pageA/dynamics/index', {}, 'switchTab');
};
</script>

<style scoped>
/* ===== 商家动态模块样式 ===== */
.dynamics-module {
  width: 100%;
  margin: 40rpx 0;
}

/* 标题区域 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 40rpx 0 20rpx 0;
}

.section-header__title {
  font-weight: bold;
  font-size: 34rpx;
  color: #333333;
}

.section-header__more {
  display: flex;
  color: #aaaaaa;
  font-size: 28rpx;
  align-items: center;
}

.section-header__arrow {
  padding-left: 8rpx;
}

.section-header__arrow image {
  width: 8rpx;
  height: 18rpx;
}

/* 商家动态卡片 - 与原型保持一致 */
.dynamic {
  background: #fff;
  border-radius: 20rpx;
  padding: 24rpx 26rpx;
  margin: 20rpx 0;
}

.dynamicTitle {
  display: flex;
  height: 80rpx;
}

.dynamicTitle_1 {
  margin-left: 12rpx;
}

.dynamicTitle_2 {
  color: #333333;
  font-size: 28rpx;
  font-weight: bold;
}

.dynamicTitle_3 {
  font-size: 24rpx;
  color: #777777;
  padding-top: 12rpx;
}

.dynamicTitle_4 {
  color: #333333;
  font-size: 28rpx;
  margin-top: 16rpx;
}

.beyond {
  margin-top: 16rpx;
  font-size: 28rpx;
  color: #333333;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 5;
}

.packUp {
  color: #4d669b;
  font-size: 28rpx;
  margin-top: 10rpx;
}

/* 图片和视频样式 */
.img_box {
  margin-top: 20rpx;
  padding-left: 4rpx;
}

.videos {
  width: 340rpx;
  height: 340rpx;
}

.videos video {
  width: 100%;
  height: 100%;
}

.img_box .many_img {
  display: flex;
  justify-self: start;
  flex-wrap: wrap;
}

.img_item.four {
  width: 32%;
  height: 200rpx;
  margin-bottom: 10rpx;
  border-radius: 16rpx;
  overflow: hidden;
}

.img_item.four image {
  width: 100%;
  height: 100%;
}

.img_item.four:nth-child(2) {
  margin: 0 1%;
}

.img_item.four:nth-child(5) {
  margin: 0 1%;
}

.img_item.four:nth-child(8) {
  margin: 0 1%;
}

.img_item.many {
  width: 48%;
  height: 300rpx;
  margin-bottom: 10rpx;
  border-radius: 10px;
  overflow: hidden;
}

.img_item.many image {
  width: 100%;
  height: 100%;
}

.img_item.many:nth-child(2) {
  margin-left: 4%;
}

.img_item.many:nth-child(4) {
  margin-left: 4%;
}

.img_item.many:nth-child(6) {
  margin-left: 4%;
}

.img_item.many:nth-child(8) {
  margin-left: 4%;
}

.img {
  width: 100%;
  height: 100%;
}
</style>
