<template>
    <view class="dynamics">
        <view class="header" v-if="activeList.length > 0">
            <u-tabs bar-width="32" :list="activeList" active-color="#FF4F61" inactive-color="#AAAAAA" font-size="32"
                :gutter="gutter" v-model="current" @change="change"></u-tabs>
        </view>
        <view class="more">
            <template v-if="pageList.length > 0">
                <view class="devide-content"></view>
                <template v-if="current == 0">
                    <view class='content-main'>
                        <waterfall-card-vue class="waterfall-card" ref="waterfallRef" :list="pageList">
                        </waterfall-card-vue>
                    </view>
                </template>
                <template v-else>
                    <view class="more-card" v-for="(item, index) in pageList" :key="index" @click="nextDetails(item.postId)">
                        <view class="pd-24">
                            <userdesc-vue :item="item" pageType="detail"></userdesc-vue>
                            <view style="margin-top: 16rpx">
                                <img-content-area-vue :listData="item" :index="index"></img-content-area-vue>
                            </view>
                        </view>
                        <!-- <view class="devide"></view> -->
                        <!-- <view class="discusss5 pd-24">
                            <functionbutton-vue :item="item"></functionbutton-vue>
                        </view> -->
                        <view class="devide-content"></view>
                    </view>
                </template>
            </template>
            <empty-vue v-if="isFinshed && pageList.length == 0"></empty-vue>
        </view>
        <u-back-top :scroll-top="scrollTop" top="800"></u-back-top>
    </view>
</template>

<script>
    import emptyVue from '@/components/empty.vue';
    import functionbuttonVue from '@/components/functionbutton.vue';
    import waterfallCardVue from "@/components/waterfallCard.vue";
    import imgContentAreaVue from "@/components/imgContentArea.vue";
    import userdescVue from '@/components/userdesc.vue';
    export default {
        components: {
            emptyVue,
            functionbuttonVue,
            waterfallCardVue,
            imgContentAreaVue,
            userdescVue
        },
        data() {
            return {
                gutter: 64,
                pageSize: 10,
                pageNum: 1,
                scrollTop: 0,
                types: 1,
                // 0:宝妈社区 1:热门话题 2:服务内容 3:服务笔记 4:我的话题
                current: 0,
                isFinshed: false,
                activeList: [],
                pageList: []
            }
        },
        onPageScroll: function(e) {
            this.scrollTop = e.scrollTop
        },
        onReachBottom() {
            if (this.types == 1 && this.current == 0) {
                this.pageNum = this.pageNum + 1
                this.getList();
            }
            if (this.types == 1 && this.current == 1) {
                this.pageNum = this.pageNum + 1
                this.getPageList()
            }
        },
        onLoad(options) {
            this.activeList = [{
                    name: '动态',
                    current: 0
                },
                {
                    name: '话题',
                    current: 1
                },
            ]
            this.gutter = 156
            this.getList()
        },
        methods: {
			nextDetails(postId){
				uni.navigateTo({
				    url: "/pageA/pageB/community/topicdetail/commentdetail?postId=" +
				        postId,
				});
			},
            detail(item) {
                uni.navigateTo({
                    url: "/pageA/pageB/community/sending?userId=" +
                        item.userId +
                        "&postId=" +
                        item.postId,
                });
            },
            change(index) {
                this.current = this.activeList[index].current
                this.pageNum = 1
                this.pageList = []
                if (this.current == 0 || this.current == 2) {
                    this.getList()
                } else {
                    this.getPageList()
                }
            },
            getPageList() {
                let data = {
                    pageSize: this.pageSize,
                    pageNum: this.pageNum,
                };
                this.isFinshed = false
                this.changeLoading(true)
                this.$axios.get(this.$api.getPostBookmark, data).then((res) => {
                    if (res.data.code == 200) {
                        let data = res.data.rows;
                        if (data != null) {
                            let types = data != null ? (data.length == 10 ? 1 : 2) : '';
                            let dtnow = this.pageNum == 1 ? [] : this.pageList;
                            data.forEach((item) => {
                                if (item.content.length > 65) {
                                    item.packUpShow = false;
                                    item.moreBtns = true;
                                    item.moreBtn = true;
                                } else {
                                    item.packUpShow = false;
                                    item.moreBtns = false;
                                    item.moreBtn = false;
                                }
                                let timearr = item.createTime.replace(' ', ':').replace(/\:/g, '-')
                                    .split('-');
                                item.createTimes = timearr[1] + '/' + timearr[2] + ' ' + timearr[3] +
                                    ':' + timearr[4];
                                if (item.imageUrl && item.imageUrl.length > 0) {
                                    item.contentPhotos = item.imageUrl
                                }
                            });
                            let dtnows = dtnow.concat(data);
                            this.setData({
                                pageList: dtnows,
                                types: types
                            });
                        }

                    }
                }).finally(() => {
                    this.isFinshed = true
                    this.changeLoading(false)
                });
            },
            getList() {
                let data = {
                    pageSize: this.pageSize,
                    pageNum: this.pageNum,
                };
                if (this.postId) {
                    data.postId = this.postId
                }
                this.isFinshed = false
                this.changeLoading(true)
                this.$axios.get(this.$api.getPostFavorite, data).then((res) => {
                    if (res.data.code == 200) {
                        let data = res.data.rows;
                        let types = data.length == 10 ? 1 : 2;
                        let dtnow = this.pageNum == 1 ? [] : this.pageList;
                        this.setData({
                            pageList: dtnow.concat(data),
                            types: types
                        });
                        this.$nextTick(() => {
                            setTimeout(() => {
                                this.$refs.waterfallRef.visualizeLoadingImages();
                            }, 400)
                        })
                    }
                }).finally(() => {
                    this.isFinshed = true
                    this.changeLoading(false)
                });
            },
        }
    }
</script>

<style lang="less" scoped>
    @import './dynamics.less';
</style>