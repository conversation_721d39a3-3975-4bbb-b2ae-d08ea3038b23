/**
 * 时间处理工具函数
 * 使用 dayjs 进行时间处理
 */
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import 'dayjs/locale/zh-cn';

// 配置 dayjs
dayjs.extend(relativeTime);
dayjs.locale('zh-cn');

/**
 * 创建兼容的 dayjs 对象
 * @param {string|number|Date} timestamp - 时间戳或日期字符串
 * @returns {dayjs.Dayjs} dayjs 对象
 */
export function createCompatibleDate(timestamp) {
  if (!timestamp) return dayjs();

  // dayjs 可以直接处理各种格式
  return dayjs(timestamp);
}

/**
 * 格式化时间显示
 * @param {string|number|Date} timestamp - 时间戳
 * @returns {string} 格式化后的时间字符串
 */
export function formatTime(timestamp) {
  if (!timestamp) return '';

  const date = createCompatibleDate(timestamp);
  const now = dayjs();
  const timeStr = date.format('HH:mm');

  if (date.isSame(now, 'day')) {
    // 今天
    return timeStr;
  } else if (date.isSame(now.subtract(1, 'day'), 'day')) {
    // 昨天
    return `昨天 ${timeStr}`;
  } else if (date.isSame(now, 'year')) {
    // 今年
    return date.format('M月D日 HH:mm');
  } else {
    // 其他年份
    return date.format('YYYY年M月D日 HH:mm');
  }
}

/**
 * 判断是否应该显示时间
 * @param {Object} message - 当前消息
 * @param {number} index - 消息索引
 * @param {Array} messages - 消息列表
 * @returns {boolean} 是否显示时间
 */
export function shouldShowTime(message, index, messages) {
  if (index === 0) return true;

  const prevMessage = messages[index - 1];
  const messageTime = message.createdTime || message.createdAt;
  const prevMessageTime = prevMessage?.createdTime || prevMessage?.createdAt;

  if (!prevMessage || !prevMessageTime || !messageTime) {
    return false;
  }

  const currentTime = createCompatibleDate(messageTime);
  const prevTime = createCompatibleDate(prevMessageTime);

  // 使用 dayjs 的 diff 方法计算时间差（分钟）
  return currentTime.diff(prevTime, 'minute') > 5;
}

/**
 * 格式化消息时间（用于消息列表显示）
 * @param {string|number|Date} timestamp - 时间戳
 * @returns {string} 格式化后的时间字符串
 */
export function formatMessageTime(timestamp) {
  if (!timestamp) return '';

  const date = createCompatibleDate(timestamp);
  const now = dayjs();

  const diffMinutes = now.diff(date, 'minute');
  const diffHours = now.diff(date, 'hour');
  const diffDays = now.diff(date, 'day');

  if (diffMinutes < 1) {
    return '刚刚';
  } else if (diffMinutes < 60) {
    return `${diffMinutes}分钟前`;
  } else if (diffHours < 24) {
    return `${diffHours}小时前`;
  } else if (diffDays < 7) {
    return `${diffDays}天前`;
  } else {
    return formatTime(timestamp);
  }
}

/**
 * 统一的消息类型判断函数
 * 根据 senderType 判断消息显示类型
 * @param {Object} message - 消息对象
 * @param {string} defaultType - 默认消息类型，默认为 'user'
 * @returns {string} 消息类型：'user' | 'service' | 'system'
 */
export function getMessageType(message, defaultType = 'user') {
  if (!message) return defaultType;

  // 优先使用 senderType 字段
  if (message.senderType === 'USER') {
    return 'user';
  } else if (message.senderType === 'AGENT') {
    return 'service';
  } else if (message.senderType === 'SYSTEM') {
    return 'system';
  }

  // 兼容旧的 action 字段（如果存在）
  if (message.action === 'USER') {
    return 'user';
  } else if (message.action === 'AGENT') {
    return 'service';
  } else if (message.action === 'SYSTEM') {
    return 'system';
  }

  return defaultType;
}
