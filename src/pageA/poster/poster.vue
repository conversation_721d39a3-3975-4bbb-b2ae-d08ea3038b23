<template>
  <view class="page-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: navbarHeight + 'px' }">
      <view class="navbar-content">
        <view class="navbar-left" @click="goBack">
          <image
            class="back-icon"
            src="/static/images/icons/icon_arrow_left.png"
          >
          </image>
        </view>
        <view class="navbar-title">{{ '海报预览' }}</view>
        <view class="navbar-right"></view>
      </view>
    </view>

    <!-- 使用 scroll-view 实现页面滚动 -->
    <scroll-view
      id="scrollView"
      class="scroll-container"
      scroll-y
      :style="{ height: scrollViewHeight + 'px' }"
    >
      <!-- snapshot 组件包裹的海报内容 -->
      <snapshot id="poster-snapshot" class="snapshot-container">
        <PosterTemplateSelector
          :type="posterType"
          :post-id="postId"
          :diary-id="diaryId"
          :uuid="uuid"
        />
      </snapshot>
    </scroll-view>

    <!-- 生成海报按钮 -->
    <view class="generate-button" @click="generatePoster">
      <image
        class="generate-button-icon"
        src="http://cdn.xiaodingdang1.com/2024/10/30/971c5c151c6b4f7ab09a4639106c7157.png"
        mode="aspectFill"
      />
      <view>生成海报</view>
    </view>

    <new-request-loading></new-request-loading>
  </view>
</template>
<script setup>
import { ref } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import PosterTemplateSelector from './components/PosterTemplateSelector.vue';

import { usePosterCapture } from './composables/usePosterCapture.js';
import { usePosterShare } from './composables/usePosterShare.js';
import { getPosterConfig } from './types/posterTypes.js';

// 使用 composables
const { capture } = usePosterCapture();
const { share } = usePosterShare();

// 海报类型和配置
const posterType = ref('dynamics');
const posterConfig = ref({});

// 页面参数
const postId = ref('');
const diaryId = ref('');
const uuid = ref('');

// scroll-view 高度计算
const scrollViewHeight = ref(0);
const navbarHeight = ref(0);  // 导航栏高度

// 页面加载时的逻辑
onLoad(async (options) => {
  console.log('options', options);
  const type = options.type || 'dynamics';
  posterType.value = type;

  try {
    // 获取海报配置
    posterConfig.value = getPosterConfig(type);

    // 设置页面参数
    postId.value = options.postId || '';
    diaryId.value = options.diaryId || '';
    uuid.value = options.uuid || '';

    console.log('海报页面初始化完成:', {
      type,
      postId: postId.value,
      diaryId: diaryId.value,
      uuid: uuid.value,
    });
  } catch (error) {
    console.error('初始化海报页面失败:', error);
    uni.showToast({
      title: '页面初始化失败',
      icon: 'none',
    });
  }

  getScrollViewHeight();
});

// 计算 scroll-view 高度
const getScrollViewHeight = () => {
  uni.getSystemInfo({
    success: function (res) {
      console.log(res);
      navbarHeight.value = res.statusBarHeight;

      const query = uni.createSelectorQuery().select('#scrollView');
      query
        .boundingClientRect((data) => {
          scrollViewHeight.value = res.windowHeight - data.top;
        })
        .exec();
    },
  });
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 使用 composables 生成海报
const generatePoster = async () => {
  try {
    console.log('开始生成海报...');

    // 截图
    const filePath = await capture({
      method: 'snapshot',
      snapshotId: 'poster-snapshot',
    });

    console.log('海报生成成功:', filePath);

    // 分享
    await share(filePath, 'menu', {
      withShareTicket: true,
      navigateBack: true,
    });

    console.log('海报分享成功');
  } catch (error) {
    console.error('生成或分享海报失败:', error);
  }
};


// 分享功能
const onShareAppMessage = () => {};
const onShareTimeline = () => {};
</script>
<style scoped lang="less">
// 页面容器样式
.page-container {
  height: 100vh;
  background-color: #f8f9fa;
}

// 自定义导航栏样式
.custom-navbar {
  z-index: 999;
  background-color: #ffffff;
  padding-top: constant(safe-area-inset-top);  /* iOS */
  padding-top: env(safe-area-inset-top);        /* Android */

  .navbar-content {
    height: 88rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .navbar-left {
      width: 80rpx;
      height: 80rpx;
      display: flex;
      align-items: center;
      justify-content: center;

      .back-icon {
        width: 16rpx;
        height: 34rpx;
      }
    }

    .navbar-title {
      font-size: 36rpx;
      font-weight: 600;
      color: #333333;
    }

    .navbar-right {
      width: 88rpx;
      height: 88rpx;
    }
  }
}

// 滚动容器样式
.scroll-container {
  background-color: #4c4c4c;
}

.snapshot-container {
  min-height: 100%;
}

// 生成按钮样式
.generate-button {
  position: fixed;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 12px 24px;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 600;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
  z-index: 999;

  &-icon {
    width: 22rpx;
    height: 22rpx;
    margin-right: 10rpx;
  }
}
</style>
