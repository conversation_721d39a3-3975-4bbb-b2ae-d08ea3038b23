/**
 * 配置文件管理器
 * 提供项目配置的增删改查功能
 */

const fs = require('fs');
const path = require('path');

class ConfigManager {
  constructor() {
    this.configPath = path.resolve(__dirname, 'ci.config.js');
    this.backupDir = path.resolve(__dirname, 'backups');
    this.ensureBackupDir();
  }

  /**
   * 确保备份目录存在
   */
  ensureBackupDir() {
    if (!fs.existsSync(this.backupDir)) {
      fs.mkdirSync(this.backupDir, { recursive: true });
    }
  }

  /**
   * 读取配置文件
   */
  readConfig() {
    try {
      // 清除 require 缓存，确保读取最新配置
      delete require.cache[this.configPath];
      const config = require(this.configPath);
      return {
        success: true,
        data: config
      };
    } catch (error) {
      return {
        success: false,
        error: `读取配置文件失败: ${error.message}`
      };
    }
  }

  /**
   * 写入配置文件
   */
  writeConfig(config) {
    try {
      // 创建备份
      this.createBackup();

      // 生成配置文件内容
      const configContent = this.generateConfigContent(config);
      
      // 写入文件
      fs.writeFileSync(this.configPath, configContent, 'utf8');
      
      return {
        success: true,
        message: '配置文件保存成功'
      };
    } catch (error) {
      return {
        success: false,
        error: `保存配置文件失败: ${error.message}`
      };
    }
  }

  /**
   * 创建配置文件备份
   */
  createBackup() {
    try {
      if (fs.existsSync(this.configPath)) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const backupPath = path.join(this.backupDir, `ci.config.${timestamp}.js`);
        fs.copyFileSync(this.configPath, backupPath);
        
        // 只保留最近 10 个备份
        this.cleanupBackups();
      }
    } catch (error) {
      console.warn('创建备份失败:', error.message);
    }
  }

  /**
   * 清理旧备份文件
   */
  cleanupBackups() {
    try {
      const files = fs.readdirSync(this.backupDir)
        .filter(file => file.startsWith('ci.config.') && file.endsWith('.js'))
        .map(file => ({
          name: file,
          path: path.join(this.backupDir, file),
          mtime: fs.statSync(path.join(this.backupDir, file)).mtime
        }))
        .sort((a, b) => b.mtime - a.mtime);

      // 删除超过 10 个的旧备份
      if (files.length > 10) {
        files.slice(10).forEach(file => {
          fs.unlinkSync(file.path);
        });
      }
    } catch (error) {
      console.warn('清理备份失败:', error.message);
    }
  }

  /**
   * 生成配置文件内容
   */
  generateConfigContent(config) {
    const { projects, defaultConfig } = config;
    
    let content = `/**
 * 小程序 CI 多项目配置
 * 支持多个 appid 项目的构建和上传
 * 
 * 此文件由配置管理器自动生成，请勿手动编辑
 * 生成时间: ${new Date().toLocaleString()}
 */

const path = require('path');

// 项目配置
const projects = ${JSON.stringify(projects, null, 2).replace(/"([^"]+)":/g, '$1:')};

// 默认配置
const defaultConfig = ${JSON.stringify(defaultConfig, null, 2).replace(/"([^"]+)":/g, '$1:')};

module.exports = {
  projects,
  defaultConfig,
  
  // 获取项目配置
  getProject: (projectName) => {
    const project = projects[projectName];
    if (!project) {
      throw new Error(\`项目 "\${projectName}" 不存在，可用项目：\${Object.keys(projects).join(', ')}\`);
    }
    return project;
  },

  // 获取所有项目名称
  getProjectNames: () => Object.keys(projects),

  // 验证项目配置
  validateProject: (projectName) => {
    const project = projects[projectName];
    if (!project) {
      return { valid: false, error: \`项目 "\${projectName}" 不存在\` };
    }

    // 检查必要的配置
    if (!project.appid) {
      return { valid: false, error: \`项目 "\${projectName}" 缺少 appid 配置\` };
    }

    if (!project.privateKeyPath) {
      return { valid: false, error: \`项目 "\${projectName}" 缺少私钥路径配置\` };
    }

    return { valid: true };
  }
};
`;

    return content;
  }

  /**
   * 获取所有项目
   */
  getAllProjects() {
    const result = this.readConfig();
    if (!result.success) {
      return result;
    }

    return {
      success: true,
      data: Object.keys(result.data.projects).map(id => ({
        id,
        ...result.data.projects[id]
      }))
    };
  }

  /**
   * 获取单个项目
   */
  getProject(projectId) {
    const result = this.readConfig();
    if (!result.success) {
      return result;
    }

    const project = result.data.projects[projectId];
    if (!project) {
      return {
        success: false,
        error: `项目 "${projectId}" 不存在`
      };
    }

    return {
      success: true,
      data: {
        id: projectId,
        ...project
      }
    };
  }

  /**
   * 创建项目
   */
  createProject(projectData) {
    const result = this.readConfig();
    if (!result.success) {
      return result;
    }

    const { id, ...projectConfig } = projectData;
    
    // 检查项目 ID 是否已存在
    if (result.data.projects[id]) {
      return {
        success: false,
        error: `项目 "${id}" 已存在`
      };
    }

    // 验证必要字段
    const validation = this.validateProjectData(projectConfig);
    if (!validation.success) {
      return validation;
    }

    // 添加项目
    result.data.projects[id] = projectConfig;

    // 保存配置
    return this.writeConfig(result.data);
  }

  /**
   * 更新项目
   */
  updateProject(projectId, projectData) {
    const result = this.readConfig();
    if (!result.success) {
      return result;
    }

    // 检查项目是否存在
    if (!result.data.projects[projectId]) {
      return {
        success: false,
        error: `项目 "${projectId}" 不存在`
      };
    }

    // 验证必要字段
    const validation = this.validateProjectData(projectData);
    if (!validation.success) {
      return validation;
    }

    // 更新项目
    result.data.projects[projectId] = {
      ...result.data.projects[projectId],
      ...projectData
    };

    // 保存配置
    return this.writeConfig(result.data);
  }

  /**
   * 删除项目
   */
  deleteProject(projectId) {
    const result = this.readConfig();
    if (!result.success) {
      return result;
    }

    // 检查项目是否存在
    if (!result.data.projects[projectId]) {
      return {
        success: false,
        error: `项目 "${projectId}" 不存在`
      };
    }

    // 删除项目
    delete result.data.projects[projectId];

    // 保存配置
    return this.writeConfig(result.data);
  }

  /**
   * 验证项目数据
   */
  validateProjectData(projectData) {
    const required = ['name', 'appid', 'projectPath', 'privateKeyPath', 'environments'];

    for (const field of required) {
      if (!projectData[field]) {
        return {
          success: false,
          error: `缺少必要字段: ${field}`
        };
      }
    }

    // 验证 appid 格式
    if (!/^wx[a-f0-9]{16}$/.test(projectData.appid)) {
      return {
        success: false,
        error: 'AppID 格式不正确，应为 wx 开头的 18 位字符串'
      };
    }

    // 验证默认租户ID格式（如果提供）
    if (projectData.defaultTenantId && !/^\d+$/.test(projectData.defaultTenantId)) {
      return {
        success: false,
        error: '默认租户ID格式不正确，应为纯数字字符串'
      };
    }

    // 验证环境配置
    if (!projectData.environments || typeof projectData.environments !== 'object') {
      return {
        success: false,
        error: '环境配置不正确'
      };
    }

    const validEnvironments = ['develop', 'trial', 'release'];
    for (const env of Object.keys(projectData.environments)) {
      if (!validEnvironments.includes(env)) {
        return {
          success: false,
          error: `不支持的环境: ${env}`
        };
      }

      // 验证环境特定的租户ID（如果提供）
      const envConfig = projectData.environments[env];
      if (envConfig.tenantId && !/^\d+$/.test(envConfig.tenantId)) {
        return {
          success: false,
          error: `环境 ${env} 的租户ID格式不正确，应为纯数字字符串`
        };
      }
    }

    return { success: true };
  }

  /**
   * 获取备份列表
   */
  getBackups() {
    try {
      const files = fs.readdirSync(this.backupDir)
        .filter(file => file.startsWith('ci.config.') && file.endsWith('.js'))
        .map(file => {
          const filePath = path.join(this.backupDir, file);
          const stats = fs.statSync(filePath);
          return {
            name: file,
            path: filePath,
            size: stats.size,
            mtime: stats.mtime
          };
        })
        .sort((a, b) => b.mtime - a.mtime);

      return {
        success: true,
        data: files
      };
    } catch (error) {
      return {
        success: false,
        error: `获取备份列表失败: ${error.message}`
      };
    }
  }

  /**
   * 恢复备份
   */
  restoreBackup(backupName) {
    try {
      const backupPath = path.join(this.backupDir, backupName);
      
      if (!fs.existsSync(backupPath)) {
        return {
          success: false,
          error: '备份文件不存在'
        };
      }

      // 创建当前配置的备份
      this.createBackup();

      // 恢复备份
      fs.copyFileSync(backupPath, this.configPath);

      return {
        success: true,
        message: '配置已恢复'
      };
    } catch (error) {
      return {
        success: false,
        error: `恢复备份失败: ${error.message}`
      };
    }
  }
}

module.exports = ConfigManager;
