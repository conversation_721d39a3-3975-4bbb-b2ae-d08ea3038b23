<template>
  <view class="headContent" @tap="handleProfileClick">
    <image
      :src="userInfo.avatar || $defaultAvatar"
      mode=""
      :data-userinfo="userInfo"
    />
    <view class="headTitle1" v-if="userInfo && userInfo.nickname">
      {{ userInfo.nickname }}
    </view>
    <view class="headTitle1" v-else>Hi 游客～</view>
    <view class="headTitle2">{{ userInfo.clubName }}</view>
  </view>
</template>

<script setup>
import { defineProps } from 'vue'

// Props
const props = defineProps({
  userInfo: {
    type: Object,
    default: () => ({
      nickname: '',
      avatar: '',
      clubName: ''
    })
  }
})

// Methods - 内部处理跳转逻辑
const handleProfileClick = () => {
  uni.navigateTo({
    url: '/pageA/pageB/mine/personal'
  })
}
</script>

<style scoped>
.headContent {
  margin: 0 auto;
  text-align: center;
}

.headContent > image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 160rpx;
  border: 8rpx solid #ffffff;
}

.headTitle1 {
  color: #333333;
  font-size: 36rpx;
  font-weight: bold;
}

.headTitle2 {
  margin-top: 8rpx;
  margin-bottom: 32rpx;
  color: #777777;
  font-size: 24rpx;
}
</style>
