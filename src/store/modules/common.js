export default {
    namespaced: true,
    state: () => ({
        currentPlayingVideo: null // 追踪视频播放状态
    }),
    getters: {},
    mutations: {
        playVideo(state, id) {
            console.log('videoooooo', state.currentPlayingVideo, id)
            if (state.currentPlayingVideo && state.currentPlayingVideo !== id) {
                // 如果已有视频在播放，则停止它
                state.currentPlayingVideo = id;
            }
            // 播放当前视频
            state.currentPlayingVideo = id;
        },
        pauseVideo(state, id) {
            if (state.currentPlayingVideo === id) {
                // 暂停当前播放的视频
                // state.currentPlayingVideo.pause();
                state.currentPlayingVideo = null;
            }
        }
    },
};