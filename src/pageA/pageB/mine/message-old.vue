<template>
    <view>
        <view class="contentTab">
            <view :class="index == currentIndex ? 'title-sel-selected' : 'title-sel'" :data-index="index"
                @tap="titleClick" v-for="(item, index) in tabList" :key="index">
                {{ item.name }}

                <hr class="line-style" />
            </view>
        </view>
        <view class="content" v-if="currentIndex == 0">
            <template v-if="communityCommentList.length > 0">
                <view class="contents" v-for="(item, index) in communityCommentList" :key="index">
                    <view class="head">
                        <image :src="item.avatar || $defaultAvatar" mode=""
                            style="width: 80rpx; height: 80rpx; border-radius: 100rpx" />
                        <view class="heads">
                            <view class="head1">
                                <view class="head1_1">
                                    {{ item.nickname || $defaultName }}
                                </view>
                                <view class="head1_2">
                                    <image
                                        src="http://cdn.xiaodingdang1.com/1/120240125/5b82ebb2-4d5c-43ae-83b8-f4621e4f3336.png"
                                        mode="" style="width: 40rpx; height: 34rpx" class="head1Img" />
                                    <view class="head1_3">高级育婴师</view>
                                </view>
                            </view>
                            <view class="head2">{{ item.hisTimeStr }}</view>
                        </view>
                    </view>

                    <view class="content1" v-if="item.type == 2">
                        <text class="content1_1">评论了我：</text>
                        <text class="content1_2">{{ item.commentContent }}</text>
                    </view>

                    <view class="content1" v-if="item.type == 3">
                        <text class="content1_1">回复我：</text>
                        <text class="content1_2">{{ item.commentContent }}</text>
                    </view>

                    <view class="content2" v-if="item.type == 2">
                        <image :src="item.postUrl || $defaultAvatar" mode=""
                            style="width: 140rpx; height: 140rpx; border-radius: 10rpx" />
                        <view class="content2_2">
                            {{ item.content }}
                        </view>
                    </view>

                    <view class="content3" v-if="item.type == 3">
                        <view class="content3_1">
                            {{ item.meComment }}
                        </view>
                    </view>
                </view>
            </template>
            <empty-vue v-if="isFinshed && communityCommentList.length == 0"></empty-vue>
        </view>
        <view class="content" v-if="currentIndex == 1">
            <template v-if="communityLikeList.length > 0">
                <view class="contents" v-for="(item, index) in communityLikeList" :key="index">
                    <view class="head">
                        <image :src="item.avatar || $defaultAvatar" mode=""
                            style="width: 80rpx; height: 80rpx; border-radius: 100rpx" />
                        <view class="heads">
                            <view class="head1">
                                <view class="head1_1">
                                    {{ item.nickname || $defaultName }}
                                </view>
                                <view class="head1_2">
                                    <image
                                        src="http://cdn.xiaodingdang1.com/1/120240125/5b82ebb2-4d5c-43ae-83b8-f4621e4f3336.png"
                                        mode="" style="width: 40rpx; height: 34rpx" class="head1Img" />
                                    <view class="head1_3">高级育婴师</view>
                                </view>
                            </view>
                            <view class="head2">{{ item.hisTimeStr }}</view>
                        </view>
                    </view>

                    <view class="content1_8">
                        <image src="http://cdn.xiaodingdang1.com/1/120240125/058cf0f5-7185-4671-961c-ee263c166795.png"
                            mode="" style="width: 24rpx; height: 24rpx" />
                        <view class="content1_9">赞了您的动态</view>
                    </view>

                    <view class="content2" v-if="item.type == 1">
                        <image :src="item.postUrl || $defaultAvatar" mode="" style="width: 140rpx; height: 140rpx" />
                        <view class="content2_2">
                            {{ item.content }}
                        </view>
                    </view>

                    <view class="content3" v-if="item.type == 4">
                        <view class="content3_1">
                            {{ item.meComment }}
                        </view>
                    </view>
                </view>
            </template>
            <empty-vue v-if="isFinshed && communityLikeList.length == 0"></empty-vue>
        </view>
        <new-request-loading></new-request-loading>
    </view>
</template>

<script>
    import emptyVue from '@/components/empty.vue';
    export default {
        components: {
            emptyVue
        },
        data() {
            return {
                communityLikeList: [],
                //查询宝妈社区动态我的消息点赞通知
                communityCommentList: [],
                //查询宝妈社区动态我的消息评论通知
                currentIndex: 0,
                tabList: [{
                        name: '评论'
                    },
                    {
                        name: '点赞'
                    }
                ],
                isFinshed: false
            };
        }
        /**
         * 生命周期函数--监听页面加载
         */
        ,
        onLoad(options) {
            if (this.currentIndex == 0) {
                this.communityComment();
            }
        },
        /**
         * 生命周期函数--监听页面初次渲染完成
         */
        onReady() {},
        /**
         * 生命周期函数--监听页面显示
         */
        onShow() {},
        /**
         * 生命周期函数--监听页面隐藏
         */
        onHide() {},
        /**
         * 生命周期函数--监听页面卸载
         */
        onUnload() {},
        /**
         * 页面相关事件处理函数--监听用户下拉动作
         */
        onPullDownRefresh() {},
        /**
         * 页面上拉触底事件的处理函数
         */
        onReachBottom() {},
        /**
         * 用户点击右上角分享
         */
        onShareAppMessage() {},

        onShareTimeline() {},
        methods: {
            //用户点击tab时调用
            titleClick: function(e) {
                console.log(e.currentTarget.dataset.index);
                this.setData({
                    //拿到当前索引并动态改变
                    currentIndex: e.currentTarget.dataset.index
                });
                if (e.currentTarget.dataset.index == 0) {
                    this.communityComment(); //查询宝妈社区动态我的消息评论通知
                }
                if (e.currentTarget.dataset.index == 1) {
                    this.communityLike(); //查询宝妈社区动态我的消息点赞通知
                }
            },

            communityLike() {
                //查询宝妈社区动态我的消息点赞通知

                let data = {
                    pageSize: 1,
                    pageNum: 1000,
                    type: 1
                };
                this.changeLoading(true)
                this.isFinshed = false
                this.$axios.get(this.$api.communityLike, data).then((res) => {
                    if (res.data.code == 200) {
                        this.setData({
                            communityLikeList: res.data.data
                        });
                    }
                }).finally(() => {
                    this.isFinshed = true
                    this.changeLoading(false)
                })
            },

            communityComment() {
                //查询宝妈社区动态我的消息评论通知

                let data = {
                    pageSize: 1,
                    pageNum: 1000,
                    type: 2
                };
                this.changeLoading(true)
                this.isFinshed = false
                this.$axios.get(this.$api.communityComment, data).then((res) => {
                    if (res.data.code == 200) {
                        this.setData({
                            communityCommentList: res.data.data
                        });
                    }
                }).finally(() => {
                    this.isFinshed = true
                    this.changeLoading(false)
                })
            }
        }
    };
</script>
<style>
    @import './message.css';
</style>