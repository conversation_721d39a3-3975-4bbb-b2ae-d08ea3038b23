<template>
    <view>
        <view class="main">
            <view class="title">我要反馈</view>
            <view class="feedbackComent">
                <view class="feedbackComent_1">
                    {{ complaintDetail.content }}
                </view>
                <view class="feedbackImg">
                    <view class="feedbackImg1" @tap="preview" :data-url="item"
                        :data-item="complaintDetail.contentPhotos"
                        v-for="(item, index) in complaintDetail.contentPhotos" :key="index">
                        <image :src="item" mode="aspectFill" />
                    </view>
                </view>
            </view>
            <view class="title" v-if="complaintDetail.status == 2">处理结果</view>
            <view class="feedbackComent" v-if="complaintDetail.status == 2">
                <view class="feedbackComent_1">
                    {{ complaintDetail.feedbackContent }}
                </view>
            </view>
        </view>
        <view class="btn">
            <view class="btn1" @tap="back">返回</view>
        </view>
    </view>
</template>

<script>
    export default {
        data() {
            return {
                complaintDetail: {
                    content: '',
                    contentPhotos: '',
                    status: 0
                }
            };
        }
        /**
         * 生命周期函数--监听页面加载
         */
        ,
        onLoad(options) {
            this.getcomplaintDetail(options.complaintId);
        },
        /**
         * 生命周期函数--监听页面初次渲染完成
         */
        onReady() {},
        /**
         * 生命周期函数--监听页面显示
         */
        onShow() {},
        /**
         * 生命周期函数--监听页面隐藏
         */
        onHide() {},
        /**
         * 生命周期函数--监听页面卸载
         */
        onUnload() {},
        /**
         * 页面相关事件处理函数--监听用户下拉动作
         */
        onPullDownRefresh() {},
        /**
         * 页面上拉触底事件的处理函数
         */
        onReachBottom() {},
        /**
         * 用户点击右上角分享
         */
        onShareAppMessage() {},

        onShareTimeline() {},
        methods: {
            getcomplaintDetail(complaintId) {
                //投诉id获取详情
                let data = {
                    complaintId: complaintId
                };
                this.$axios.get(this.$api.complaintDetail, data).then((res) => {
                    if (res.data.code == 200) {
                        let data = res.data.data;
                        this.setData({
                            complaintDetail: data
                        });
                    }
                });
            },

            back() {
                //返回
                uni.navigateBack({
                    delta: 1 // 返回的页面数，如果 delta 大于现有页面数，则返回到首页。
                });
            },

            preview(e) {
                let url = e.currentTarget.dataset.url;
                let item = e.currentTarget.dataset.item;
                uni.previewImage({
                    current: url,
                    // 当前显示图片的http链接
                    urls: item // 需要预览的图片http链接列表
                });
            }
        }
    };
</script>
<style scoped>
    @import './detail.css';
</style>