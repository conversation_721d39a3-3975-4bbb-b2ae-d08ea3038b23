<template>
  <view class="food-module" v-if="hasMealList">
    <!-- 标题区域 -->
    <view class="section-header" @click="handleMoreClick">
      <view class="section-header__title">月子膳食</view>
      <view class="section-header__more">
        <view>更多</view>
        <u-icon name="arrow-right" color="#aaaaaa" size="26"></u-icon>
      </view>
    </view>

    <!-- 月子膳食列表 -->
    <scroll-view :show-scrollbar="false" :scroll-x="true" class="food">
      <view
        style="display: inline-block"
        @tap="handleFoodClick"
        v-for="(item, index) in mealList"
        :key="index"
      >
        <view>
          <image
            lazy-load
            :src="item.photos[0]"
            mode="aspectFill"
            style="width: 200rpx; height: 200rpx; border-radius: 40rpx"
            class="food1"
          />
        </view>
        <view class="food2">
          {{ item.dishName }}
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted, getCurrentInstance } from 'vue';

// 响应式数据
const mealList = ref([]);
const isTags = ref(false);

// 获取当前实例
const instance = getCurrentInstance();

// 计算属性
const hasMealList = computed(() => {
  return mealList.value && mealList.value.length > 0;
});

// 生命周期
onMounted(() => {
  initFoodData();
});

// 初始化月子膳食数据
const initFoodData = () => {
  mealListFun();
};

// 获取月子膳食数据
const mealListFun = () => {
  const data = {
    pageSize: 100,
    pageNum: 1,
  };
  instance.proxy.$axios
    .get(instance.proxy.$api.mealList, data)
    .then((res) => {
      if (res.data.code == 200) {
        mealList.value = res.data.rows;
      }
    })
    .catch((error) => {
      console.error('获取月子膳食数据失败:', error);
    });
};

// 查询月子膳食信息
const infoMerchants = () => {
  instance.proxy.$axios
    .get(instance.proxy.$api.infoMerchants)
    .then((res) => {
      if (res.data.code == 200) {
        let data = res.data.data;
        uni.setStorageSync('mealTags', data.mealTags);
        if (res.data.data.isTags) {
          uni.navigateTo({
            url: '/pageA/pageB/community/newfoodDetails/foodDetails',
          });
        } else {
          uni.navigateTo({
            url: '/pageA/pageB/home/<USER>',
          });
        }
        isTags.value = res.data.data.isTags;
      }
    })
    .catch((error) => {
      console.error('获取月子膳食信息失败:', error);
    });
};

// 事件处理 - 内部实现页面跳转
const handleMoreClick = () => {
  infoMerchants();
};

const handleFoodClick = () => {
  infoMerchants();
};
</script>

<style scoped>
/* ===== 月子膳食模块样式 ===== */
.food-module {
  width: 100%;
  margin: 40rpx 0;
}

/* 标题区域 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 40rpx 0 20rpx 0;
}

.section-header__title {
  font-weight: bold;
  font-size: 34rpx;
  color: #333333;
}

.section-header__more {
  display: flex;
  color: #aaaaaa;
  font-size: 28rpx;
  align-items: center;
}

.section-header__arrow {
  padding-left: 8rpx;
}

.section-header__arrow image {
  width: 8rpx;
  height: 18rpx;
}

/* 月子膳食列表 - 与原有food样式一致 */
.food {
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
  white-space: nowrap;
}

.food1 {
  margin-right: 20rpx;
}

.food2 {
  color: #333333;
  font-size: 28rpx;
  margin: 0 auto;
  width: 200rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
}
</style>
