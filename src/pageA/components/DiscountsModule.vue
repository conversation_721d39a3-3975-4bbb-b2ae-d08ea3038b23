<template>
	<view class="banneimg"  @click="handleMenuClick()">
		<image src="/src/static/images/home/<USER>" mode=""></image>
	</view>
	<view class="discounts-module" v-if="hasPackagePageList">
		<!-- 标题区域 -->
		<view class="section-header" @tap="handleMoreClick">
			<view class="section-header__title">优惠套餐</view>
			<view class="section-header__more">
				<view>更多</view>
				<u-icon name="arrow-right" color="#aaaaaa" size="26"></u-icon>
			</view>
		</view>

		<!-- 套餐列表 -->
		<view class="package-content" @tap="handlePackageClick" :data-packageid="item.packageId"
			v-for="(item, index) in packagePageList" :key="index">
			<view class="package-left">
				<view class="package-left-img">
					<image mode="aspectFill" lazy-load :src="item.photos" />
				</view>
			</view>

			<view class="package-right">
				<view class="package-right-text1">
					<view class="package-right-text3">
						{{ item.packageName }}
					</view>
				</view>
				<view class="package-right-text2_1">
					<view class="package-right-text2_2">{{ item.maxArea }}㎡</view>
					<view class="package-right-text2_2">{{ item.orientation }}</view>
					<view class="package-right-text2_2">{{ item.roomType }}</view>
				</view>
				<view class="package-right-text4_1">
					<view class="package-right-text4_2">
						<text style="color: #2b3151; font-size: 24rpx; font-weight: bold">¥</text>
						<text style="
                color: #333333;
                font-size: 32rpx;
                font-weight: bold;
                margin-left: 4rpx;
              ">{{ item.packagePrices }}</text>
						<text style="color: #2b3151; font-size: 24rpx; padding-left: 8rpx">元</text>
					</view>
					<view class="package-right-text4_3_1" :data-packageid="item.packageId">
						详情了解
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
	import {
		ref,
		computed,
		onMounted,
		getCurrentInstance
	} from 'vue';

	// 响应式数据
	const packagePageList = ref([]);

	// 获取当前实例
	const instance = getCurrentInstance();

	// 计算属性
	const hasPackagePageList = computed(() => {
		return packagePageList.value && packagePageList.value.length > 0;
	});

	// 生命周期
	onMounted(() => {
		initPackageData();
	});

	// 初始化优惠套餐数据
	const initPackageData = () => {
		packagePage();
	};

	// 获取优惠套餐数据
	const packagePage = () => {
		const data = {
			pageSize: 2,
			pageNum: 1,
			auditStatus: 1,
			onlineStatus: 1,
			onHomepage: 1,
		};
		instance.proxy.$axios
			.get(instance.proxy.$api.packagePage, data)
			.then((res) => {
				if (res.data.code == 200) {
					let data = res.data.rows;
					data.forEach((item) => {
						let packagePrice1 = item.packagePrice.slice(0, 1);
						let packagePrice2 = item.packagePrice.slice(2, 10);
						item.packagePrices = packagePrice1 + '*' + packagePrice2;
					});
					packagePageList.value = data;
				}
			})
			.catch((error) => {
				console.error('获取优惠套餐数据失败:', error);
			});
	};

	// 事件处理 - 内部实现页面跳转
	const handleMoreClick = () => {
		instance.proxy.$jumpPage('/pageA/pageB/home/<USER>');
	};
	// 事件处理 - 内部实现页面跳转
	const handleMenuClick = () => {
		instance.proxy.$jumpPage('/pageA/pageB/home/<USER>');
	};

	const handlePackageClick = (e) => {
		const packageId = e.currentTarget.dataset.packageid;
		instance.proxy.$jumpPage(
			'/pageA/pageB/community/confinementDetails/confinementDetails?packageId=' + packageId,
		);
	};
</script>

<style scoped>
	/* ===== 优惠套餐模块样式 ===== */
	.discounts-module {
		width: 100%;
		margin: 40rpx 0;
	}

	/* 标题区域 */
	.section-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin: 40rpx 0 20rpx 0;
	}

	.section-header__title {
		font-weight: bold;
		font-size: 34rpx;
		color: #333333;
	}

	.section-header__more {
		display: flex;
		color: #aaaaaa;
		font-size: 28rpx;
		align-items: center;
	}

	.section-header__arrow {
		padding-left: 8rpx;
	}

	.section-header__arrow image {
		width: 8rpx;
		height: 18rpx;
	}

	/* 套餐内容 - 与原有packageContent样式一致 */
	.package-content {
		margin: 20rpx 0;
		display: flex;
	}

	.package-left {
		position: relative;
	}

	.package-left-img {
		width: 35%;
	}

	.package-left-img image {
		width: 180rpx;
		height: 180rpx;
		border-radius: 10px;
	}

	.package-right {
		margin-left: 20rpx;
		width: 100%;
	}

	.package-right-text1 {
		display: flex;
		align-items: center;
	}

	.package-right-text3 {
		font-size: 30rpx;
		margin-left: 8rpx;
		font-weight: bold;
	}

	.package-right-text2_1 {
		display: flex;
		align-items: center;
		margin-top: 16rpx;
	}

	.package-right-text2_2 {
		border-radius: 6rpx;
		color: #333333;
		margin-right: 10rpx;
		font-size: 24rpx;
		text-align: center;
		line-height: 30rpx;
		padding: 4rpx 12rpx;
		background: #efefef;
	}

	.package-right-text4_1 {
		display: flex;
		justify-content: space-between;
		align-items: flex-end;
		margin-top: 34rpx;
	}

	.package-right-text4_3_1 {
		background: -webkit-gradient(linear,
				left top,
				left bottom,
				color-stop(0%, #fc867a),
				color-stop(100%, #fe5b86));
		background: -webkit-linear-gradient(180deg, #fc867a 0%, #fe5b86 100%);
		background: -o-linear-gradient(180deg #fc867a 0%, #fe5b86 100%);
		background: -ms-linear-gradient(180deg, #fc867a 0%, #fe5b86 100%);
		background: linear-gradient(180deg, #fc867a 0%, #fe5b86 100%);
		color: #fff;
		border-radius: 6rpx;
		font-size: 22rpx;
		width: 128rpx;
		height: 50rpx;
		text-align: center;
		line-height: 50rpx;
		font-weight: bold;
	}

	.banneimg {
		width: 706rpx;
		height: 200rpx;
	}

	.banneimg image {
		width: 100%;
		height: 100%;
	}
</style>