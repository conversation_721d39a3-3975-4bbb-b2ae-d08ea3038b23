<template>
  <view class="next">
    <view class="contentTitle">我的内容</view>
    <u-grid :col="3" :border="false">
      <u-grid-item @tap="handleItemClick('dynamics')">
        <view class="grid-icon-wrapper">
          <image
            src="http://cdn.xiaodingdang1.com/2025/03/21/114703cdc02a415ea3ca4669b04e63e1.png"
            mode=""
            style="width: 48rpx; height: 48rpx"
          />
        </view>
        <view class="grid-text">发表内容</view>
      </u-grid-item>
      
      <u-grid-item @tap="handleMessageClick">
        <view class="grid-icon-wrapper">
          <image
            src="http://cdn.xiaodingdang1.com/2025/03/21/e814132c5f19490dbd99ac219fb498ef.png"
            mode=""
            style="width: 48rpx; height: 48rpx"
          />
          <u-badge
            :count="messageUnreadCount"
            :is-dot="false"
            type="error"
            v-if="messageUnreadCount > 0"
            class="badge-position"
          ></u-badge>
        </view>
        <view class="grid-text">我的消息</view>
      </u-grid-item>
      
      <u-grid-item @tap="handleItemClick('favority')">
        <view class="grid-icon-wrapper">
          <image
            src="http://cdn.xiaodingdang1.com/2025/03/21/8ee054bd620b461b8c216fc11f96c0c7.png"
            mode=""
            style="width: 48rpx; height: 48rpx"
          />
        </view>
        <view class="grid-text">我的收藏</view>
      </u-grid-item>
      
      <u-grid-item @tap="handleItemClick('praise')">
        <view class="grid-icon-wrapper">
          <image
            src="http://cdn.xiaodingdang1.com/2025/03/21/a7e828b5a3a44adf9fe43fc22f6ae79c.png"
            mode=""
            style="width: 48rpx; height: 48rpx"
          />
        </view>
        <view class="grid-text">我的点赞</view>
      </u-grid-item>
      
      <u-grid-item @tap="handleItemClick('comment')">
        <view class="grid-icon-wrapper">
          <image
            src="http://cdn.xiaodingdang1.com/2025/03/21/3b3d2c06b4a048f18643e8e1e6f4e8ac.png"
            mode=""
            style="width: 48rpx; height: 48rpx"
          />
        </view>
        <view class="grid-text">我的评论</view>
      </u-grid-item>
    </u-grid>
  </view>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue'
import subscriptionManager, { TEMPLATE_TYPES } from '@/utils/subscriptionManager.js'

// Props
const props = defineProps({
  messageUnreadCount: {
    type: Number,
    default: 0
  }
})

// Emits - 只保留需要外部处理的事件
const emit = defineEmits(['message-read'])

// Methods - 内部处理跳转逻辑
const handleItemClick = (page) => {
  uni.navigateTo({
    url: '/pageA/pageB/mine/' + page
  })
}

// 消息点击处理 - 内部处理订阅逻辑
const handleMessageClick = async () => {
  try {
    await subscriptionManager.requestSubscription(
      [TEMPLATE_TYPES.CLIENT_CONSULTATION_REMINDER],
      {
        onSuccess: (res) => {
          console.log('消息订阅成功', res)
          jumpToMessage()
        },
        onFail: (err) => {
          console.error('消息订阅失败', err)
          jumpToMessage()
        },
        onError: (error) => {
          console.error('消息订阅出错', error)
          uni.showToast({
            title: '订阅消息失败',
            icon: 'none',
          })
          jumpToMessage()
        },
      },
    )
  } catch (error) {
    console.error('消息订阅异常', error)
    jumpToMessage()
  }
}

const jumpToMessage = () => {
  uni.navigateTo({
    url: '/pageA/pageB/mine/message'
  })
  // 通知父组件重置未读数量
  emit('message-read')
}
</script>

<style scoped>
.next {
  border-radius: 14rpx;
  background-color: #ffffff;
  padding: 28rpx 30rpx;
  margin-bottom: 20rpx;
}

.contentTitle {
  font-size: 32rpx;
  color: #333333;
  font-weight: bold;
}

.grid-text {
  color: #878787;
  font-size: 24rpx;
  margin-top: 14rpx;
}

/* 统一的图标包装器样式 */
.grid-icon-wrapper {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 48rpx;
  width: 100%;
  margin-bottom: 10rpx;
}

/* 角标统一定位 */
.badge-position {
  position: absolute !important;
  right: 35rpx !important;
  top: -35rpx !important;
  z-index: 1;
}

/* 使角标呈现为完美圆形 */
:deep(.badge-position .u-badge) {
  border-radius: 50% !important;
  min-width: 36rpx !important;
  height: 36rpx !important;
  padding: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* 当数字超过一位数时保持圆形 */
:deep(.badge-position .u-badge-dot) {
  width: 36rpx !important;
  height: 36rpx !important;
  border-radius: 50% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* 确保图标底部文字对齐 */
.grid-text {
  margin-top: 10rpx;
  font-size: 26rpx;
  color: #333;
}
</style>
