.main {
  background: #eff2f5;
  min-height: 100vh;
  padding: 24rpx 24rpx;
  .coment {
    background: #ffffff;
    border-radius: 20rpx;
    padding: 40rpx 24rpx;
    margin-bottom: 20rpx;
    .list1 {
      display: flex;
      justify-content: space-between;
      color: #333333;
      font-size: 28rpx;
      .title {
        font-weight: bold;
      }
      .name {
        font-size: 28rpx;
      }
    }
    .list2 {
      .title2 {
        color: #333333;
        font-size: 28rpx;
        font-weight: bold;
        margin: 30rpx 0;
      }
      .name2 {
        width: 100%;
        height: 200rpx;
        background: #f7f7f7;
        border-radius: 20rpx;
        margin-top: 20rpx;
      }
      .chooseImg2 {
        display: grid;
        grid-template-columns: repeat(3, 1fr); /* 每行三个等宽列 */
        gap: 10px; /* 行与列之间的间隔 */
        flex-wrap: wrap;
        .chooseImg {
          width: 198rpx;
          height: 198rpx;
          margin-top: 20rpx;
          .chooseImg1 {
            width: 100%;
            height: 100%;
            border-radius: 15rpx;
          }
        }
      }
    }
  }
}
.titles {
  color: #333333;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}
.fgx {
  background: #ebebeb;
  height: 1rpx;
  width: 100%;
  margin: 26rpx 0;
}
.timeinput {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80rpx;
  color: #3f6fff;
  background: #f0f4ff;
  font-size: 28rpx;
  padding: 16rpx 20rpx;
  border-radius: 10rpx;
}
.name_1 {
  display: flex;
  align-items: center;
}
