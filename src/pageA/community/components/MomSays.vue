<template>
  <view class="mom-says" v-if="momSaysData.length > 0">
    <view class="mom-says__header">
      <view class="mom-says__title-wrapper">
        <image
          src="https://cdn.xiaodingdang1.com/2024/11/27/0ebcaf62b3034a5684bc57e7dd32129a.png"
          mode="aspectFill"
          class="mom-says__icon"
        />
        <view class="mom-says__title">宝妈说</view>
      </view>
      <view class="mom-says__badge">必看动态</view>
    </view>
    <view
      class="mom-says__item"
      @tap="handleViewDetail"
      :data-userid="item.userId"
      :data-postid="item.postId"
      v-for="(item, index) in momSaysData"
      :key="index"
    >
      <view class="mom-says__content">
        <view class="mom-says__text">
          {{ item.content }}
        </view>
        <view class="mom-says__action">看动态</view>
      </view>
      <view class="mom-says__media">
        <template v-if="item?.videos?.length">
          <video :src="item.videos[0]" mode="" class="mom-says__video" />
        </template>
        <template v-else>
          <view
            v-if="item?.contentPhotos?.length"
            class="mom-says__image"
            :style="{
              backgroundImage: `url(${item.contentPhotos[0]})`,
            }"
          >
          </view>
        </template>
      </view>
    </view>
  </view>
</template>

<script setup>
import { PAGE_ROUTES } from '../constants.js'
import { formatUrlWithParams } from '../utils.js'

const props = defineProps({
  momSaysData: {
    type: Array,
    default: () => []
  }
})

// 内部处理查看详情逻辑
const handleViewDetail = (e) => {
  const userId = e.currentTarget.dataset.userid
  const postId = e.currentTarget.dataset.postid
  const url = formatUrlWithParams(PAGE_ROUTES.MOM_INFLUENCE, { userId, postId })
  uni.navigateTo({ url })
}
</script>

<style scoped lang="less">
.mom-says {
  border-radius: 12rpx;
  padding: 12rpx;
  min-height: 510rpx;
  background: linear-gradient(180deg, #fe647b 0%, #ffcacb 100%);

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  &__title-wrapper {
    display: flex;
    align-items: center;
  }

  &__icon {
    width: 36rpx;
    height: 36rpx;
    border-radius: 100rpx;
  }

  &__title {
    font-size: 28rpx;
    color: #ffffff;
    margin-left: 10rpx;
    width: 112rpx;
    font-weight: 500;
  }

  &__badge {
    padding: 4rpx 8rpx;
    background: #ffffff;
    color: #fe667c;
    border-radius: 16rpx;
    font-weight: 500;
    font-size: 22rpx;
  }

  &__item {
    background: #fff;
    border-radius: 8rpx;
    padding: 12rpx;
    display: flex;
    justify-content: space-between;
    margin-top: 12rpx;
  }

  &__content {
    width: 60%;
  }

  &__text {
    color: #333333;
    font-size: 22rpx;
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    height: 64rpx;
  }

  &__action {
    width: 86rpx;
    height: 32rpx;
    line-height: 32rpx;
    text-align: center;
    color: #000;
    font-size: 18rpx;
    background: #f9db3b;
    border-radius: 16rpx;
    margin-top: 14rpx;

    &::after {
      content: '>';
      position: relative;
      display: inline-block;
      left: 2rpx;
      bottom: 2rpx;
      color: #111827;
    }
  }

  &__media {
    height: 108rpx;
    width: 108rpx;
    position: relative;
    overflow: hidden;
  }

  &__video {
    width: 100%;
    height: 100%;
  }

  &__image {
    width: 100%;
    height: 100%;
    border-radius: 8rpx;
    background-size: cover;
    background-position: center;
  }
}
</style>
