import { LOAD_STATUS, CONTENT_CONFIG } from './constants.js'

/**
 * 检查是否有更多数据
 * @param {number} dataLength - 当前数据长度
 * @param {number} pageSize - 页面大小
 * @returns {number} 加载状态
 */
export function checkHasMoreData(dataLength, pageSize) {
  return dataLength === pageSize ? LOAD_STATUS.HAS_MORE : LOAD_STATUS.NO_MORE
}

/**
 * 处理动态内容显示逻辑
 * @param {Object} item - 动态项目
 */
export function processContentDisplay(item) {
  if (item?.content?.length > CONTENT_CONFIG.MAX_CONTENT_LENGTH) {
    item.packUpShow = false
    item.moreBtns = true
    item.moreBtn = true
  } else {
    item.packUpShow = false
    item.moreBtns = false
    item.moreBtn = false
  }
}

/**
 * 处理点赞用户列表
 * @param {Array} likeUsers - 点赞用户列表
 */
export function processLikeUsers(likeUsers) {
  if (likeUsers && likeUsers.length > 0) {
    likeUsers.forEach((user) => (user.isLikeShow = false))
    return likeUsers
  }
  return []
}

/**
 * 格式化创建时间
 * @param {string} createTime - 创建时间
 * @returns {string} 格式化后的时间
 */
export function formatCreateTime(createTime) {
  if (createTime) {
    return createTime.slice(0, 10)
  }
  return ''
}

/**
 * 格式化URL参数
 * @param {string} baseUrl - 基础URL
 * @param {Object} params - 参数对象
 * @returns {string} 格式化后的URL
 */
export function formatUrlWithParams(baseUrl, params) {
  const queryString = Object.entries(params)
    .map(([key, value]) => `${key}=${value}`)
    .join('&')
  
  return `${baseUrl}?${queryString}`
}

/**
 * 处理点赞操作的数据更新
 * @param {Array} likeList - 点赞列表
 * @param {number} index - 索引
 * @param {Object} userInfo - 用户信息
 * @param {string} postId - 动态ID
 * @param {boolean} isLike - 是否点赞
 * @returns {Array} 更新后的点赞列表
 */
export function updateLikeList(likeList, index, userInfo, postId, isLike) {
  const likeListItem = {
    avatar: userInfo.avatar,
    nickname: userInfo.nickname,
    userId: userInfo.userId,
    postId,
  }
  
  let list = likeList[index] || []
  
  if (!isLike) {
    list.push(likeListItem)
  } else {
    const i = list.findIndex((item) => item.userId == likeListItem.userId)
    if (i !== -1) {
      list.splice(i, 1)
    }
  }
  
  return list
}


