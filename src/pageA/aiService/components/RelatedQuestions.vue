<template>
  <view class="related-questions-container" v-if="shouldShow">
    <view class="questions-card">
      <!-- 标题 -->
      <view class="questions-title">推荐问题</view>

      <!-- 问题轮播 -->
      <view class="questions-swiper-container">
        <swiper
          class="questions-swiper"
          :indicator-dots="true"
          :indicator-color="'#E3ECFF'"
          :indicator-active-color="'#406CFF'"
          :autoplay="true"
          :interval="5000"
          :circular="true"
          :duration="500"
          @change="onSwiperChange"
        >
          <swiper-item
            v-for="(pageQuestions, pageIndex) in questionPages"
            :key="pageIndex"
            class="swiper-item"
          >
            <view class="questions-list">
              <view
                v-for="(question, index) in pageQuestions"
                :key="index"
                class="question-item"
                @click="selectQuestion(question)"
              >
                <text class="question-text">{{ getQuestionText(question) }}</text>
                <view class="question-arrow">
                  <image src="/static/images/chat/arrow-right.svg" mode="aspectFit" />
                </view>
              </view>
            </view>
          </swiper-item>
        </swiper>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue';

// Props
const props = defineProps({
  questions: {
    type: Array,
    default: () => [],
  },
});

// Events
const emit = defineEmits(['select-question']);

// 数据
const currentPage = ref(0);
const questionsPerPage = 4; // 每页显示4个问题

// 计算属性
const shouldShow = computed(() => {
  return props.questions.length > 0;
});

const totalPages = computed(() => {
  return Math.ceil(props.questions.length / questionsPerPage);
});

const questionPages = computed(() => {
  const pages = [];
  for (let i = 0; i < props.questions.length; i += questionsPerPage) {
    pages.push(props.questions.slice(i, i + questionsPerPage));
  }
  return pages;
});

// 获取问题文本
const getQuestionText = (question) => {
  return question.questionText || question.content || question;
};

// 选择问题
const selectQuestion = (question) => {
  const questionText = getQuestionText(question);
  console.log('选择相关问题:', questionText);
  console.log('问题详情:', question);
  emit('select-question', questionText);
};

// swiper 变化事件
const onSwiperChange = (e) => {
  currentPage.value = e.detail.current;
};

// 暴露方法给父组件
defineExpose({
  currentPage,
  totalPages,
  nextPage: () => {
    currentPage.value = (currentPage.value + 1) % totalPages.value;
  },
  prevPage: () => {
    currentPage.value = currentPage.value > 0 ? currentPage.value - 1 : totalPages.value - 1;
  }
});
</script>

<style lang="scss" scoped>
.related-questions-container {
  margin-top: 24rpx;
}

.questions-card {
  background-color: #ffffff;
  border-radius: 28rpx;
  width: 690rpx;
  height: 533rpx;
  display: flex;
  flex-direction: column;
  padding: 28rpx;
  box-sizing: border-box;
}

.questions-title {
  font-family: PingFang SC;
  font-size: 32rpx;
  font-weight: 500;
  color: #000000;
  line-height: 46rpx;
  width: 128rpx;
  height: 46rpx;
  margin-bottom: 14rpx;
}

.questions-swiper-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.questions-swiper {
  flex: 1;
  width: 100%;

  // 自定义指示器样式
  :deep(.uni-swiper-dots) {
    bottom: 8rpx !important;
  }

  :deep(.uni-swiper-dot) {
    width: 10rpx !important;
    height: 10rpx !important;
    margin: 0 6rpx !important;
    background-color: #e3ecff !important;
    border-radius: 50% !important;
  }

  :deep(.uni-swiper-dot-active) {
    background-color: #406cff !important;
  }
}

.swiper-item {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.questions-list {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 10rpx;
  flex: 1;
}

.question-item {
  width: 100%;
  height: 88rpx;
  background-color: #f4f7fd;
  border-radius: 14rpx;
  display: flex;
  align-items: center;
  padding: 0 22rpx;
  transition: all 0.2s ease;
  cursor: pointer;
  box-sizing: border-box;
}

.question-item:active {
  background-color: #e8f0ff;
  transform: scale(0.98);
}

.question-text {
  font-family: PingFang SC;
  font-size: 28rpx;
  font-weight: 400;
  color: #333333;
  line-height: 40rpx;
  flex: 1;
  padding-right: 22rpx;
}

.question-arrow {
  width: 28rpx;
  height: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;

  image {
    width: 28rpx;
    height: 28rpx;
    filter: brightness(0) saturate(100%) invert(53%) sepia(8%) saturate(15%) hue-rotate(314deg) brightness(95%) contrast(89%);
  }
}
</style>
