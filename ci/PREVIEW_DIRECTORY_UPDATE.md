# 预览二维码目录更新说明

## 更新内容

将预览生成的二维码文件从项目根目录移动到专门的 `ci/preview` 目录中，以便更好地组织和管理预览文件。

## 修改详情

### 1. 创建预览目录结构

```
ci/preview/
├── README.md          # 目录说明文档
├── .gitignore         # 忽略二维码文件的版本控制
└── (二维码文件)        # 动态生成的预览二维码
```

### 2. 更新配置文件

#### `ci/config/config-loader.js`
- 添加 `qrcodeOutputDir` 配置项指向 `ci/preview` 目录
- 更新 `qrcodeOutputDest` 默认路径

#### `ci/config/ci.config.js`
- 更新预览配置中的二维码输出路径

### 3. 更新预览脚本

修改了以下脚本文件，确保二维码保存到新目录：

- `ci/scripts/preview.js` - 主预览脚本
- `ci/scripts/preview-existing-build.js` - 现有构建预览脚本
- `ci/scripts/test-preview-simple.js` - 简单预览测试脚本
- `ci/scripts/debug-preview.js` - 调试预览脚本

### 4. 更新服务器API

#### `ci/server/index.js`
- 更新 `/api/projects/:id/preview-qrcode` API 的文件路径
- 从 `../../preview-{id}-{environment}.jpg` 改为 `../preview/preview-{id}-{environment}.jpg`

### 5. 添加目录创建逻辑

在所有预览脚本中添加了目录存在检查和自动创建逻辑：

```javascript
// 确保预览目录存在
const previewDir = path.resolve(__dirname, '../preview');
if (!fs.existsSync(previewDir)) {
  fs.mkdirSync(previewDir, { recursive: true });
  logInfo(`创建预览目录: ${previewDir}`);
}
```

## 文件命名规则

预览二维码文件按以下格式命名：
```
preview-{项目ID}-{环境}.jpg
```

示例：
- `preview-main-develop.jpg` - main项目开发环境
- `preview-main-trial.jpg` - main项目体验环境
- `preview-xinxiyue-develop.jpg` - xinxiyue项目开发环境

## 版本控制

通过 `.gitignore` 文件确保二维码文件不会被提交到版本控制系统：

```gitignore
# 忽略所有预览二维码文件
*.jpg
*.jpeg
*.png
*.gif

# 保留 README 文件
!README.md
!.gitignore
```

## 测试工具

创建了以下测试工具来验证配置：

### `ci/scripts/test-preview-directory.js`
- 验证预览目录配置是否正确
- 检查目录权限和文件结构
- 测试目录创建功能

### `ci/scripts/test-preview-path.js`
- 创建和清理模拟预览文件
- 验证文件路径配置
- 测试API访问功能

## 使用方法

### 生成预览二维码
```bash
# 使用主预览脚本
node ci/scripts/preview.js main develop

# 使用现有构建预览脚本
node ci/scripts/preview-existing-build.js main develop
```

### 查看预览文件
```bash
# 列出预览目录内容
ls -la ci/preview/

# 查看特定项目的预览文件
ls ci/preview/preview-main-*.jpg
```

### 清理预览文件
```bash
# 删除所有二维码文件
rm ci/preview/*.jpg

# 删除特定项目的预览文件
rm ci/preview/preview-main-*.jpg
```

### API访问
```bash
# 获取预览二维码
curl "http://localhost:3001/api/projects/main/preview-qrcode?environment=develop"
```

## 验证结果

✅ 预览目录配置正确  
✅ 文件路径更新完成  
✅ API访问正常工作  
✅ 版本控制配置正确  
✅ 测试工具验证通过  

## 兼容性

- 保持了向后兼容性，现有的API接口不变
- 自动创建目录，无需手动干预
- 文件命名规则保持一致

## 优势

1. **组织性更好**: 所有预览文件集中在专门目录中
2. **版本控制友好**: 二维码文件不会污染版本控制
3. **易于管理**: 可以方便地查看、清理预览文件
4. **路径清晰**: 明确的目录结构，便于维护

更新完成后，所有预览二维码将自动保存到 `ci/preview` 目录中。
