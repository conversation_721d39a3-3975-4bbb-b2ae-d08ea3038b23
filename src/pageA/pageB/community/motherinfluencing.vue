<template>
	<view class="page-influencing">
		<navigation :background="bgColor" title="宝妈说" :isSowArrow="true"></navigation>
		<!-- <view class="top-img"></view> -->
		<view class="content">
			<view class="head-content">
				<view class="head-content1">
					<view class="head-content1_1">
						<image src="/src/static/images/chat/hi1.png" mode="" style="width: 56rpx;height: 70rpx;">
						</image>
						<image src="/src/static/images/chat/hi2.png" mode="" class="head-contentImg"></image>
					</view>
					<view class="head-content1_2">
						月子里的暖心细节，待产妈妈必看
					</view>
				</view>
				<view class="head-content2">
					产康项目效果满意度、护理服务专业细节、膳食每日营养均衡、入住环境整体舒适度，真实评价不隐瞒。
				</view>
			</view>
			<!--  <view class="content-tab">
        <u-tabs
          ref="tabs1"
          bar-width="80"
          :list="activeList"
          active-color="#373536"
          inactive-color="#8C8A8B"
          font-size="30"
          v-model="current"
          @change="change"
        ></u-tabs>
      </view> -->
			<view class="content-main">
				<template v-if="pageList.length > 0">
					<waterfall-card-vue class="waterfall-card" ref="waterfallRef" :list="pageList" :title="3"
						@detail="detail">
					</waterfall-card-vue>
				</template>
			</view>
		</view>
		<!-- <u-back-top :scroll-top="scrollTop" top="800"></u-back-top> -->
		<rightMenuBarVue :scrollTop="scrollTop"></rightMenuBarVue>
		<new-request-loading></new-request-loading>
	</view>
	<mask-dialog></mask-dialog>
</template>
<script setup>
	import rightMenuBarVue from '@/components/rightMenuBar.vue';
	import waterfallCardVue from '@/components/waterfallCard.vue';
	import {
		onShow,
		onHide,
		onLoad,
		onPageScroll,
		onReachBottom,
		onShareAppMessage,
		onShareTimeline,
	} from '@dcloudio/uni-app';
	import {
		ref,
		nextTick,
		reactive,
		getCurrentInstance,
		onMounted
	} from 'vue';
	import {
		useStore
	} from 'vuex';
	const store = useStore();
	const instance = getCurrentInstance();
	const App = instance.appContext.config.globalProperties;
	onShareAppMessage(() => {});
	onShareTimeline(() => {});
	const pageList = ref([]);
	const pageNum = ref(1);
	const pageSize = ref(10);
	const postId = ref('');
	const waterfallRef = ref(null);
	const types = ref(1);
	const bgColor = ref('');
	const current = ref(0);
	const activeList = [{
			name: '最新动态',
		},
		{
			name: '热门推荐',
		},
	];
	const scrollTop = ref(0);
	onPageScroll((e) => {
		if (e.scrollTop > 0) {
			bgColor.value = '#ffffff';
		} else if (e.scrollTop < 2) {
			bgColor.value = '';
		}
		scrollTop.value = e.scrollTop;
	});
	const orderByColumn = ref('fp.create_time');
	const change = (index) => {
		current.value = index;
		orderByColumn.value = !!index ?
			'is_featured,fp.likes_count' :
			'fp.create_time';
		pageNum.value = 1;
		pageList.value = [];
		getList();
	};
	onReachBottom(() => {
		if (types.value == 1) {
			pageNum.value = pageNum.value + 1;
			getList();
		}
	});
	onLoad((options) => {
		postId.value = options.postId;
	});
	onMounted(() => {
		getList();
	});
	const getList = () => {
		store.commit('m_user/changeLoading', true);
		let data = {
			pageSize: pageSize.value,
			pageNum: pageNum.value,
			type: 'USER',
			isAsc: 'desc',
			orderByColumn: orderByColumn.value,
		};
		if (postId.value) {
			data.postId = postId.value;
		}
		App.$axios
			.get(App.$api.customerPage, data)
			.then((res) => {
				if (res.data.code == 200) {
					let data = res.data.rows;
					types.value = data.length == 10 ? 1 : 2;
					pageList.value = pageList.value.concat(data);
				}
			})
			.finally(() => {
				store.commit('m_user/changeLoading', false);
			});
	};
	// const detail = (item) => {
	//     uni.navigateTo({
	//         url: "/pageA/pageB/community/sending?userId=" +
	//             item.userId +
	//             "&postId=" +
	//             item.postId,
	//     });
	// }
</script>
<style scoped lang="less">
	@import './motherinfluencing.less';
</style>