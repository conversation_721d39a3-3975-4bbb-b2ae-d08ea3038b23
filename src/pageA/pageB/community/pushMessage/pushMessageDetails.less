.main{
	background: #F6F6F6;
	height: 100vh;
	padding: 24rpx 0 0 0 ;
}
.collapseComent{
	padding: 20rpx 20rpx;
	background-color: #fff;
}
.collapse-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	 padding: 20rpx 0 20rpx 80rpx;
	}
	.collapse-item-title{
		display: flex;
		width: 100%;
		color: #626262;
			font-size: 28rpx;
			.collapse-item-title1{
				width: 20%;
				}
			}
	.collapse{
		background: #fff;
	}
	.black{
		height: 200rpx;
	}
	.bottomBtn{
		background: #fff;
		padding: 24rpx 24rpx 48rpx 24rpx;
		position: fixed;
		width: 100%;
bottom: 0;
		.confirm{
			background: #1FA2FF;
			border-radius: 20rpx;
			color:#FFFFFF;
			font-size: 32rpx;
			font-weight: bold;
			text-align: center;
			height: 88rpx;
			line-height: 88rpx;
		}
	}
	.people{
		display: flex;
		background: #fff;
		height: 100rpx;
		line-height: 100rpx;
		align-items: center;
		justify-content: space-between;
		padding: 0 24rpx;
		.people1{
			color: #333333;
			font-size: 32rpx;
			font-weight: bold;
		}
		.peoples1{
			color: #2979ff;
			font-size: 32rpx;
			font-weight: bold;
		}
	}
	.peopleTitle{
		color: #626262;
		font-size: 24rpx;
		padding: 24rpx 24rpx;
	}