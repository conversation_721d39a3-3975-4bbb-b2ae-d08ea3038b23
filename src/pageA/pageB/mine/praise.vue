<template>
	<view class="dynamics">
		<view class="header" v-if="activeList.length > 0">
			<u-tabs ref="tabs1" bar-width="32" :list="activeList" active-color="#FF4F61" inactive-color="#AAAAAA"
				font-size="32" :gutter="gutter" v-model="current" @change="change"></u-tabs>
		</view>
		<view class="more">
			<template v-if="pageList.length > 0">
				<view class="devide-content" v-if="activeList.length > 0"></view>
				<template v-if="current == 0">
					<view class='content-main'>
						<waterfall-card-vue class="waterfall-card" ref="waterfallRef" :list="pageList"
							@detail="momDetails">
						</waterfall-card-vue>
					</view>
				</template>
				<template v-else>
					<!-- <view class="more-card" v-for="(item, index) in pageList" :key="index">
                        <view class="pd-24">
                            <userdesc-vue :item="item" pageType="detail"></userdesc-vue>
                            <view style="margin-top: 16rpx">
                                <img-content-area-vue :listData="item" :index="index"></img-content-area-vue>
                            </view>
                        </view>
                        <view class="devide"></view>
                        <view class="discusss5 pd-24">
                            <functionbutton-vue :item="item"></functionbutton-vue>
                        </view>
                        <view class="devide-content"></view>
                    </view> -->
					<view class="discuss" v-for="(item, index) in pageList" :key="index">
						<view class="discus">
							<view class="discuss1">
								<view class="discuss1_1">
									<view class="discuss8_1">
										<image :src="item.avatar || $defaultAvatar" mode=""
											style="width: 80rpx; height: 80rpx; border-radius: 100rpx" />
										<view class="discuss1_2">
											<view class="discuss1_3">
												{{ item.nickname || $defaultName }}
											</view>
											<view class="discuss1_4">
												<text>{{ item.hisTimeStr }}</text> 
												<text class="discussName">{{ item.postIp }}</text>
											</view> 
										</view>
									</view>
									<view class="flex">
										<image
											src="http://cdn.xiaodingdang1.com/2025/03/31/01427820a81049878a07c6651d9acea0.png"
											mode="" style="width: 30rpx; height: 30rpx" />
										<view :class="item.isLike==1 ? 'active' : 'default'">
											收藏
										</view>
									</view>
								</view>
								<view class="discuss1_5" @tap="communityBookmark" :data-postid="item.postId"
									:data-index="index">
									<!-- <view v-if="!item.isBookmark">
					                                    <image
					                                        src="http://cdn.xiaodingdang1.com/2024/07/03/c5f552fafc8e40afa72ff08fb99dd398.png"
					                                        mode="" style="width: 40rpx; height: 40rpx" />
					                                </view>
					                                <view v-if="item.isBookmark">
					                                    <image
					                                        src="http://cdn.xiaodingdang1.com/2024/07/03/008774f5f0d543fa8ac91c3271e5c96d.png"
					                                        mode="" style="width: 40rpx; height: 40rpx" />
					                                </view>
					                                <view class="discussTitle">收藏</view> -->
								</view>
							</view>
							<view>
								<view class="discuss2" @tap="comment" :data-postid="item.postId">
									<text class="discuss2_1">#{{ item.topicName }}</text>
									<text class="discuss2_2">{{ item.postContent }}</text>
								</view>
								<!-- <view class="discuss3">
									<video src="{{item.videos[0]}}" class="discuss3_1" wx:if="{{item.videos[0]}}" bindtap="preview" data-src="{{item.videos[0]}}" data-index="0"/>
									<image src="{{res}}" mode="" class="discuss3_1" bindtap="previewImage" data-src="{{res}}" data-url="{{item.imageUrl}}" wx:for="{{item.imageUrl}}" wx:for-item="res" wx:for-index="i" wx:key="i"/>
								</view> -->
								<!-- 视频 -->
								<view class="img_box" v-if="item.videos && item.videos.length > 0">
									<view class="videos">
										<video class="video" :src="item.videos[0]" @tap="preview"
											:data-src="item.videos[0]" data-index="0" :ontrols="false"></video>
									</view>
								</view>
								<!-- 图片 -->
								<view class="img_box" v-else>
									<template v-if="item.postContentPhotos">
										<view id="loadImg{{index}}"
											:class="['loadImg', item.postContentPhotos.length > 1 ? 'many_img' : '', item.listShow? 'active': '']">
											<view :class="
					                                            'img_item ' +
					                                            (item.postContentPhotos.length == 1 || item.postContentPhotos.length == 2 || item.postContentPhotos.length == 4 ? 'many' : item.postContentPhotos.length >= 3 ? 'four' : '')
					                                        " v-for="(res, index1) in item.postContentPhotos" :key="index1">
												<image class="img" :src="res" @tap="previewImage"
													:data-url="item.postContentPhotos" :data-src="res"
													:data-sources="item.postContentPhotos" :data-index="index"
													mode="aspectFill"></image>
											</view>
										</view>
									</template>
								</view>
							</view>
						</view>

						<view class="discuss4"></view>
						<view class="discusss5">
							<view class="fn" :style="stylefly">
								<button class="custom-buttons" open-type="share" @click="shareCallback()">
									<view class="flex">
										<image
											src="http://cdn.xiaodingdang1.com/1/120240125/118c00f3-3132-46c7-83b3-628a1a90e2db.png"
											mode="" style="width: 30rpx; height: 30rpx; margin-right: 4rpx" />
										<view class="discuss5_2">分享</view>
									</view>
								</button>
								<view class="custom-buttons" @tap="collect(item.postId)">
									<view class="flex">
										<image
											src="http://cdn.xiaodingdang1.com/2025/03/31/c2292aa01f3a4100978f0bce7b1d175f.png"
											mode="" style="width: 30rpx; height: 30rpx" />
										<view :class="isFavorite ? 'active' : 'default'">
											评论
										</view>
									</view>
								</view>
								<view class="custom-buttons" @tap="like(item.postId)">
									<view class="flex">
										<image
											src="http://cdn.xiaodingdang1.com/2024/07/03/e915e045f143407aa4d7b4142f167389.png"
											mode="" style="width: 30rpx; height: 30rpx" />
										<view class="active">顶一下
										</view>
									</view>
								</view>
							</view>
						</view>
					</view>
				</template>
			</template>
			<empty-vue v-if="isFinshed && pageList.length == 0"></empty-vue>
		</view>
		<u-back-top :scroll-top="scrollTop" top="800"></u-back-top>
	</view>
</template>

<script>
	import emptyVue from '@/components/empty.vue';
	import functionbuttonVue from '@/components/functionbutton.vue';
	import waterfallCardVue from "@/components/waterfallCard.vue";
	import imgContentAreaVue from "@/components/imgContentArea.vue";
	import userdescVue from '@/components/userdesc.vue';
	export default {
		components: {
			emptyVue,
			functionbuttonVue,
			waterfallCardVue,
			imgContentAreaVue,
			userdescVue
		},
		data() {
			return {
				gutter: 150,
				pageSize: 10,
				pageNum: 1,
				scrollTop: 0,
				types: 1,
				// 0:宝妈社区 1:热门话题 2:服务内容 3:服务笔记 4:我的话题
				current: 0,
				isFinshed: false,
				activeList: [{
						name: '动态',
						current: 0
					},
					{
						name: '话题',
						current: 1
					},
				],
				pageList: []
			}
		},
		onPageScroll: function(e) {
			this.scrollTop = e.scrollTop
		},
		onReachBottom() {
			if (this.types == 1 && (this.current == 0)) {
				this.pageNum = this.pageNum + 1
				this.getList();
			}
			if (this.types == 1 && (this.current == 1)) {
				this.pageNum = this.pageNum + 1
				this.getPageList()
			}
		},
		onLoad(options) {
			this.getList();
		},
		methods: {
			like(postid) {
				let data = {
					postId: postid
				};
				this.setData({})
				this.$axios.put(this.$api.saveLike, data).then((res) => {
					if (res.data.code == 200) {
						// this.topicInfoFun(this.topicId); //查询话题详情
						this.getPageList()
					}
				});
			},
			collect(postId) {
				uni.navigateTo({
					url: "/pageA/pageB/community/topicdetail/commentdetail?postId=" + postId
				});
			},
			momDetails(item) {
				let params = {
					postId: item.postId,
					userId: item.userId,
				};
				let url;
				if (item.type == "USER") {
					url = "/pageA/pageB/community/sending";
				} else {
					url = "/pageA/pageB/community/staffdetail";
				}
				this.$jumpPage(url, params);
			},
			detail(item) {
				uni.navigateTo({
					url: "/pageA/pageB/community/sending?userId=" +
						item.userId +
						"&postId=" +
						item.postId,
				});
			},
			change(index) {
				this.current = this.activeList[index].current
				this.pageNum = 1
				this.pageList = []
				if (this.current == 0) {
					this.getList()
				} else {
					this.getPageList()
				}
			},
			getPageList() {
				let current = this.current
				this.isFinshed = false
				this.changeLoading(true)
				this.$axios.get(this.$api.myUpCommunity).then((res) => {
					if (res.data.code == 200) {
						let data = res.data.data;
						if (data != null) {
							// let types = data != null ? (data.length == 10 ? 1 : 2) : '';
							// let dtnow = this.pageNum == 1 ? [] : this.pageList;
							// data.forEach((item) => {
							//     if (item.content.length > 65) {
							//         item.packUpShow = false;
							//         item.moreBtns = true;
							//         item.moreBtn = true;
							//     } else {
							//         item.packUpShow = false;
							//         item.moreBtns = false;
							//         item.moreBtn = false;
							//     }
							//     var timearr = item.createTime.replace(' ', ':').replace(/\:/g, '-')
							//         .split('-');
							//     item.createTimes = timearr[1] + '/' + timearr[2] + ' ' + timearr[3] +
							//         ':' + timearr[4];
							// });
							// console.log(data)
							this.setData({
								pageList: data,
							});
						}

					}
				}).finally(() => {
					this.isFinshed = true
					this.changeLoading(false)
				});
			},
			getList() {
				this.isFinshed = false
				this.changeLoading(true)
				this.$axios.get(this.$api.myUpPost).then((res) => {
					if (res.data.code == 200) {
						let data = res.data.data;
						// let types = data.length == 10 ? 1 : 2;
						// let dtnow = this.pageNum == 1 ? [] : this.pageList;
						this.setData({
							// pageList: dtnow.concat(data),
							pageList: data,
						});
						this.$nextTick(() => {
							setTimeout(() => {
								this.$refs.waterfallRef.visualizeLoadingImages();
							}, 400)
						})
					}
				}).finally(() => {
					this.isFinshed = true
					this.changeLoading(false)
				});
			},
		}
	}
</script>

<style lang="less" scoped>
	@import './praise.less';
</style>