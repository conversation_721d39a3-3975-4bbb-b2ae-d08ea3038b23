.main {
  height: 100%;
  background: #f8f9f9;
  padding: 194rpx 24rpx 40rpx 24rpx;
  .flex {
    display: flex;
    align-items: center;
  }
  .content {
    min-height: calc(100vh - 445px);
  }
}

.bg {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  width: 100%;
  height: 400rpx;
  background-image: url('https://cdn.xiaodingdang1.com/2024/11/09/c1e8a3924cef4dc38e2765ef8cd1f36e.png');
  background-size: cover;
  // background: linear-gradient(to bottom, rgba(255,255,255,1), rgba(255,255,255,0)), url('https://cdn.xiaodingdang1.com/2024/11/09/c1e8a3924cef4dc38e2765ef8cd1f36e.png') no-repeat;
}
.top {
  background-color: white;
  border-radius: 20rpx;
  margin-top: 54rpx;
  margin-bottom: 24rpx;
  padding: 110rpx 40rpx 32rpx 40rpx;
  font-style: normal;
  text-transform: none;
  position: relative;
  z-index: 9;
  &-img {
    position: absolute;
    top: -30rpx;
    image {
      width: 128rpx;
      height: 128rpx;
      border-radius: 100%;
    }
  }
  &-header {
    margin-top: 2rpx;
    margin-bottom: 16rpx;
    display: flex;
    align-items: center;
    &-name {
      font-weight: 500;
      font-size: 40rpx;
      color: #333333;
      line-height: 47rpx;
      margin-right: 20rpx;
    }
    &-log {
      font-weight: 400;
      font-size: 26rpx;
      color: #58380c;
      line-height: 30rpx;
      text-align: center;
      padding: 2rpx 16rpx;
      background: linear-gradient(90deg, #f4e5c3 0%, #e9d29a 100%);
      border-radius: 4rpx 4rpx 4rpx 4rpx;
    }
  }
  &-subname {
    margin-bottom: 20rpx;
    font-weight: 400;
    font-size: 26rpx;
    color: #333333;
  }
  &-tag {
    display: flex;
    align-items: center;
    &-num {
      display: inline-block;
      background: #ffe8ea;
      border-radius: 6rpx 6rpx 6rpx 6rpx;
      padding: 8rpx 12rpx;
      margin-right: 12rpx;
      font-weight: 500;
      font-size: 24rpx;
      color: #ff4f61;
    }
  }
  &-state {
    display: flex;
    align-items: center;
    margin: 24rpx 0;
    &-label {
      font-weight: 400;
      font-size: 26rpx;
      color: #333333;
      line-height: 30rpx;
      margin-right: 4rpx;
    }
    &-value {
      font-weight: 500;
      font-size: 36rpx;
      color: #ff4f61;
      line-height: 42rpx;
      margin-right: 40rpx;
    }
  }
  &-desc {
    color: #777777;
    font-size: 24rpx;
    line-height: 36rpx;
    text-align: justified;
    font-style: normal;
    text-transform: none;
  }
}
.content {
}
.card {
  margin-bottom: 24rpx;
  font-style: normal;
  text-transform: none;
  background: linear-gradient(90deg, #ddedff 0%, #e2e1ff 100%);
  border-radius: 20rpx;
  position: relative;
  padding: 32rpx;
  &-img {
    position: absolute;
    top: 0;
    right: 0;
    image {
      width: 198rpx;
      height: 84rpx;
    }
  }
  &-header {
    &-img {
      display: inline-block;
      image {
        height: 80rpx;
        width: 80rpx;
        border-radius: 100%;
      }
      margin-right: 16rpx;
    }
    &-right {
      display: inline-block;
      &-name {
        font-weight: 500;
        font-size: 30rpx;
        color: #ff6c11;
        margin-right: 6rpx;
        position: relative;
        top: -8rpx;
      }
      &-log {
        display: flex;
        &-img {
          image {
            width: 42rpx;
            height: 42rpx;
            position: relative;
            z-index: 9;
          }
        }
        &-sign {
          font-weight: 500;
          font-size: 24rpx;
          color: #ffffff;
          background: linear-gradient(90deg, #7ab6fc 0%, #5e5bfe 100%);
          border-radius: 118rpx;
          position: relative;
          left: -32rpx;
          height: 42rpx;
          padding: 0rpx 24rpx 0 46rpx;
          z-index: 1;
          line-height: 40rpx;
        }
      }
      &-date {
        font-weight: 400;
        font-size: 24rpx;
        color: #aaaaaa;
        line-height: 28rpx;
      }
    }
  }
  &-tag {
    // margin-bottom: 24rpx;
    &-enum {
      border-radius: 4rpx;
      display: inline-block;
      padding: 4rpx 8rpx;
      margin-right: 28rpx;
      font-weight: 400;
      font-size: 24rpx;
      color: #ffffff;
      line-height: 28rpx;
      background: linear-gradient(90deg, #7ab6fc 0%, #5e5bfe 100%);
    }
  }
  &-content {
    margin-top: 24rpx;
    padding: 24rpx;
    background-color: white;
    border-radius: 10rpx;
    .beyond {
      margin-top: 16rpx;
      font-size: 28rpx;
      color: #333333;
      overflow: hidden;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 5;
    }
    .discuss2 {
      margin-top: 16rpx;
      font-size: 28rpx;
      color: #333333;
    }
    .packUp {
      color: #4d669b;
      font-size: 30rpx;
    }
    .discuss3_1 {
      width: 48%;
      height: 340rpx;
      margin: 20rpx 0 24rpx 0;
    }
    .discuss3_1_1 {
      width: 100%;
      height: 100%;
      border-radius: 10rpx;
    }
    .motherImgs {
      margin: 20rpx 0 24rpx 0;
      display: flex;
      flex-wrap: wrap;
    }

    .discuss3.one {
      width: 48%;
      height: 340rpx;
      margin-bottom: 10rpx;
      overflow: hidden;
    }

    .discuss3.one image {
      width: 100%;
      height: 100%;
      border-radius: 10rpx;
    }

    .discuss3.one:nth-child(2n) {
      margin-left: 1%;
    }

    .discuss3.three {
      width: 32%;
      height: 220rpx;
      margin-bottom: 10rpx;
      overflow: hidden;
    }

    .discuss3.three image {
      width: 100%;
      height: 100%;
      border-radius: 10rpx;
    }

    .discuss3.three:nth-child(2) {
      margin: 0 1%;
    }

    .discuss3.three:nth-child(5) {
      margin: 0 1%;
    }

    .discuss3.three:nth-child(8) {
      margin: 0 1%;
    }
    .discusss5 {
      color: hsla(0, 0%, 47%, 1);
      font-size: 28rpx;
      width: 100%;
    }

    .custom-button {
      background: #fff;
      color: hsla(0, 0%, 47%, 1);
      font-size: 24rpx;
      margin: 0;
    }

    .discuss5_1 {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .discuss5_2 {
      margin-left: 4rpx;
    }

    .discuss5Active_2 {
      color: #ff4f61;
      font-size: 24rpx;
      margin-left: 4rpx;
    }
    .discuss4 {
      margin-top: 16rpx;
      width: 100%;
      height: 1rpx;
      background: hsla(0, 0%, 92%, 1);
    }
    .many_img {
      display: flex;
      flex-wrap: wrap;
    }
  }
}
