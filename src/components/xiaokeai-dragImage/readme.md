## 快速上手
|  属性名 | 类型  | 说明  |
|  ----  | ----  | ----  |
| 单元格  | 单元格 |   |
| 单元格  | 单元格 |   |

## 使用方法
配置easycom规则后，自动按需引入，无需`import`组件，直接引用即可。

```html
<template>
	<xiaokeai-dragImage
		:imgList.sync="imgList"
		@moveEndList="moveEndList"
	/>
</template>
```

## 示例
```html
	<template>
		<view class="content" >
			<image class="logo" src="/static/logo.png"></image>
			<xiaokeai-dragImage
				:enableDel="enableDel"
				:imgList.sync="imgList"
				@moveEndList="moveEndList"
				@addImg="addImg"
				@delImg="delImg"
			>
				<view class="addImgBtn">添加</view>
			</xiaokeai-dragImage>
			<button type="default" @click="startDel">删除</button>
		</view>
	</template>
	
	<script>
	export default {
		data() {
			return {
				enableDel: false,  
				imgList: [{ url: '/static/logo.png' }],
			};
		},
		onLoad() {
			setTimeout(() => {
				this.imgList = [{ url: '/static/logo.png' }, { url: '/static/logo.png' }, { url: '/static/logo.png' }];
			}, 1000);
		},
		onReady() {
			// uni.getSystemInfo({
			// 	success: function (res) {
			// 		console.log(res.appName)
			// 	}
			// });
		},
		methods: {
			moveEndList(e) {
				console.log('移动结束后的新图片列表');
				console.log(e);
			},
			addImg() {
				console.log('添加图片点击事件');
				let _this = this;
				uni.chooseImage({
					count: 6, //默认9
					sizeType: ['original', 'compressed'], //可以指定是原图还是压缩图，默认二者都有
					sourceType: ['album'], //从相册选择
					success: function(res) {
						for (var i = 0; i < res.tempFilePaths.length; i++) {
							_this.imgList.push({
								url: res.tempFilePaths[i]
							});
						}
					}
				});
			},
			delImg(e) {
				console.log('删除图片点击事件');
				console.log(e);
			},
			startDel() {
				this.enableDel = !this.enableDel;
			}
		}
	};
	</script>
	
	<style>
	.content {
	}
	.addImgBtn {
		width: 100%;
		height: 100%;
		display: flex;
		color: #ffffff;
		justify-content: center;
		align-items: center;
		background-color: #999999;
	}
	</style>
	
``` 



