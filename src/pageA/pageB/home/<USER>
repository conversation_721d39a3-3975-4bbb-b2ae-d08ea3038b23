.list {
    padding-top: 170rpx;
    background-color: #F3F3F3;
    .header {
        padding-bottom: 10rpx;
        background-color: white;
        /deep/.u-tab-item {
            font-weight: normal !important;
            height: 70rpx !important;
            line-height: 70rpx !important;
        }
    }
    .list-content {
        box-sizing: border-box;
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        justify-content: start;
        min-height: calc(100vh - 280rpx);
        padding: 10rpx 6rpx 122rpx 6rpx;
    }
    .list-content::-webkit-scrollbar {
        display: none;
    }
    
    .list-content {
        scrollbar-width: none;
    }
    
    .mine {
        width: 100%;
    }
    .content {
        height: 666rpx;
        width: 50%;
        display: flex;
        justify-content: center;
        position: relative;
        flex-wrap: wrap;
        padding: 6rpx;
        margin-bottom: 4rpx;
        .img_content {
            width: 100%;
            height: 558rpx;
            text-align: center;
            position: relative;
        }
        
        .img_content>image {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        
        .operate {
            width: 100%;
            padding: 20rpx;
            background-color: white;
            .flex {
                display: flex;
                align-items: center;
            }
            .title {
                font-weight: 500;
                font-size: 24rpx;
                color: #333333;
                margin-bottom: 4rpx;
            }
            .sub-title {
                font-weight: 400;
                font-size: 20rpx;
                color: #333333;
                opacity: .5;
                display: flex;
                .devide {
                    height: 18rpx;
                    width: 1rpx;
                    margin: 0 16rpx;
                    position: relative;
                    top: 6rpx;
                    background-color: #D9D9D9;
                }
            }
            .edit {
                .text {
                    padding: 6rpx 12rpx;
                    background-color: #FFF3F2;
                    border-radius: 10rpx;
                    color: #B6664D;
                    width: 82rpx;
                    font-size: 30rpx;
                    line-height: 30rpx;
                }
            }
            .center {
                text-align: center;
            }
            .space {
                justify-content: space-between;
            }
        }
        
        .button {
            font-weight: 500;
            font-size: 28rpx;
            color: #333333;
            line-height: 33rpx;
            width: 100%;
            text-align: left;
            margin-top: 20rpx;
        }
        
        .tips {
            font-weight: 400;
            font-size: 28rpx;
            color: #aaaaaa;
            line-height: 33rpx;
            width: 100%;
            text-align: left;
            margin-top: 20rpx;
        }
        
    }
    
 
    .footer {
        position: fixed;
        bottom: 0;
        background-color: white;
        width: 100%;
        height: 110rpx;
        padding-bottom: 10rpx;
        padding-top: 17rpx;
        display: flex;
        font-weight: 500;
        font-size: 20rpx;
        color: #777777;
        .tab {
            width: 50%;
            display: flex;
            align-items: center;
            gap: 2rpx;
            flex-direction: column;
        }
        image {
            width: 40rpx;
            height: 40rpx;
        }
    }
    
    .card {
        width: 100%;
        padding: 0 16rpx 16rpx 16rpx;
       
        .card-devide {
            width: 100%;
            height: 2rpx;
            background: #F2F3F6;
        }
        .card-content {
            display: flex;
            padding: 30rpx;
             background-color: white;
            image {
                width: 125rpx;
                height: 178rpx;
                border-radius: 10rpx;
            }
            .des {
                margin-left: 22rpx;
                flex: 1;
                .title {
                    font-weight: 500;
                    font-size: 30rpx;
                    color: #333333;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    margin-bottom: 12rpx;
                }
                .sub-title {
                    font-size: 24rpx;
                    color: #333333;
                    margin-bottom: 14rpx;
                }
                .time {
                    font-weight: 500;
                    font-size: 20rpx;
                    color: #333333;    
                    font-weight: 500;
                    font-size: 20rpx;
                    color: #333333;
                    opacity: .4;
                }
                .tag {
                    margin-bottom: 14rpx;
                    display: flex;
                    
                    .elc {
                        width: 96rpx;
                        height: 34rpx;
                        line-height: 34rpx;
                        background-color: rgba(255, 79, 10, .1);
                        border-radius: 6rpx;
                        font-weight: 500;
                        font-size: 20rpx;
                        color: #FF4F61;
                        text-align: center;
                        margin-right: 20rpx;
                    }
                    .page {
                        height: 34rpx;
                        border-radius: 6rpx;
                        border: 1rpx solid #333333;
                        opacity: 0.3;
                        padding: 0 3rpx;
                        font-weight: 500;
                        font-size: 20rpx;
                        color: #333333;
                    }
                    
                }
                
            }
            
            .right {
                width: 120rpx;
                height: 48rpx;
                .right-button {
                    background-color: rgba(255, 79, 10, .1);
                    border-radius: 6rpx;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    font-weight: 500;
                    font-size: 20rpx;
                    color: #FF4F61;
                    image {
                        height: 24rpx;
                        width: 24rpx;
                        margin-right: 4rpx;
                    }
                }
            }
        }
        .card-bottom {
            background-color: white;
            padding: 24rpx;
            display: flex;
            width: 100%;
            .operate {
                image {
                    width: 38rpx;
                    height: 38rpx;
                }
                font-weight: 500;
                font-size: 22rpx;
                color: #333333;
                width: 50%;
                display: flex;
                align-items: center;
                gap: 4rpx;
                flex-direction: column;
            }
        }
    }
    .card:last-child {
        padding-bottom: 0;
    }
    .w-m {
        width: 100%;
    }
}
