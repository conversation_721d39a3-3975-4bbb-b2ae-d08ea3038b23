.note {
    max-height: 100vh;
    // max-height: calc(100vh - 174rpx);
    padding-bottom: 24rpx; 
    padding-top: 174rpx;
    background-color: #F0F0F0;
    &-fixed {
        // position: fixed;
        // top: 158rpx;
        // z-index: 99;
        width: 100%;
       
        // padding-top: 164rpx;
        
    }
}
.set-pad {
    // margin-bottom: 266rpx;
}
.top {
    // background: linear-gradient( 180deg, #F8F9F9 0%, #F0F0F0 100%);
    background-color: #F0F0F0;
    padding: 15rpx 24rpx 37rpx 24rpx;
    display: flex;
    align-items: center;
        &-img {
           width: 226rpx;
            image {
                 margin-right: 46rpx;
                 border: 6rpx solid #FFFFFF;
                 width: 180rpx;
                 height: 180rpx;
                 border-radius: 180rpx;
                 display: block;
            }
        }
        &-right {
           flex: 1;
           font-weight: 400;
           font-size: 24rpx;
           color: #333435;
           line-height: 32rpx;
          &-name {
              display: flex;
              align-items: center;
              font-weight: 500;
              font-size: 40rpx;
              color: #333333;
              margin-bottom: 10rpx;
              line-height: 47rpx;
              image {
                  margin-left: 12rpx;
                  width: 107rpx;
                  height: 56rpx;
              }
          }
           &-age {
               margin-bottom: 10rpx;
           }
           &-weight {
               margin-bottom: 10rpx;
           }
           &-time {
               margin-bottom: 10rpx;
           }
        }
    }
    .tab-line {
        height: 24rpx;
        background-color: #F0F0F0;
    }
.nurse {
    margin: 0 24rpx;
    background-color: white;
    padding: 24rpx;
    border-radius: 20rpx;
    &-title {
        font-weight: 500;
        font-size: 32rpx;
        color: #333333;
        line-height: 46rpx;
        margin-bottom: 16rpx;
    }
    &-list {
        display: flex;
         white-space: nowrap;
 
    &-enum {
        display: inline-block;
        margin-right: 20rpx;
        text-align: center;
        &-img {
            width: 140rpx;
            height: 140rpx;
            image {
                width: 100%;
                height: 100%;
                border-radius: 10rpx;
                display: block;
            }
        }
        &-name {
            font-weight: 500;
            font-size: 28rpx;
            color: #676767;
            line-height: 40rpx;
        }
        &-tag {
            background: linear-gradient(90deg, #F6E0B7 0%, #FFE7C0 100%);
            box-shadow: inset 0rpx 0rpx 4rpx 0rpx rgba(255, 255, 255, 0.25);
            font-weight: 500;
            font-size: 16rpx;
            color: #7A4E2B;
            padding: 4rpx 12rpx;
            border-radius: 40rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            width: fit-content;
            margin: 0 auto;
            image {
                width: 18rpx;
                height: 18rpx;
                margin-right: 2rpx;
            }
        }
    }
    }
   
}
.tab-day { 
     // padding-top: 24rpx;
     background: #F0F0F0;
     position: sticky;
     top: 0;
     left: 0;
     z-index: 100;
     // display: none;
}
.is-sticky {
    // display: block;
}
.tab-list {
    border-radius: 20rpx;
    background-color: white;
    padding: 30rpx 0 30rpx 30rpx;
    // margin: 0 24rpx;
}
.tab {
    display: flex;
    white-space: nowrap;
    justify-content: space-around;
    width: 100%;
    overflow-x: scroll;
    &-enum {
        display: inline-block;
        margin-right: 34rpx;
        text-align: center;
        font-weight: 500;
        font-size: 32rpx;
        line-height: 46rpx;
    }
    &-enum:not(:last-child):after {
        content: '/';
        display: inline-block;
        font-weight: 400;
        margin-left: 34rpx;
        color: #D4D2D3;
    }
    &-enum:last-child {
        // margin-right: 0;
        // padding-right: 20rpx;
    }
}
.border {
    background-color: #F0F0F0;
    width: 100%;
    padding-top: 24rpx;
    
}
.content {
    background: #F0F0F0;
    // border-radius: 20rpx;
    margin: 0 24rpx;
    // height: 100%;
    &-radius {
        border-radius: 20rpx;
        background-color: white;
    }
}

.border-top {
    margin-top: 104rpx;
}
.card {
    // background-color: white;
    border-radius: 20rpx;
    // margin-bottom: 28rpx;
    &-top {
        // border-radius: 20rpx 20rpx 0 0;
        width: 100%;
        height: 84rpx;
        width: 260rpx;
        padding-right: 20rpx;
        line-height: 60rpx;
        position: relative;
        font-weight: 400;
        font-size: 32rpx;
        color: #FFFFFF;
        text-align: center;
        background-size: cover;
        background-image: url('http://cdn.xiaodingdang1.com/2025/01/18/b0bf8fc43e534f3f85fdf8bda6b25fe9.png');
        // background-image: url('http://cdn.xiaodingdang1.com/2025/01/04/abafa35e4ce84391ad7165c7d5ad0ca9.png');
    }
    &-header {
        padding: 0 24rpx;
        &-title {
            font-weight: 500;
            font-size: 32rpx;
            color: #333333;
            line-height: 38rpx;
            margin-bottom: 8rpx;
        }
         &-title:before {
             content: '';
             display: inline-block;
             width: 6rpx;
             height: 28rpx;
             background: #A1A1A1;
             border-radius: 28rpx;
             margin-right: 18rpx;
             position: relative;
             top: 4rpx;
         }
        
        &-subtitle {
            background-color: white;
            position: relative;
            font-weight: 400;
            font-size: 28rpx;
            color: #676767;
            line-height: 38rpx;
            width: 50%;
            // height: 106rpx;
            // padding: 22rpx 0;
            height: 90rpx;
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            float: left;
            // border-bottom: .5rpx solid #EBEBEB;
             &-dot {
                position: relative;
                width: 24rpx;
                height: 24rpx;
                background-color: #f0f0f0;
                border-radius: 12rpx;
                margin: 8rpx 12rpx 8rpx 8rpx;
            }
            
             &-dot::before {
                content: "";
                width: 12rpx;
                height: 12rpx;
                background-color: #D1D1D1;
                border-radius: 6rpx;
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
            }
        }
    }
    &-line {
        margin: 0 auto;;
        width: 632rpx;
        height: 0;
        background-color: #EBEBEB;
        clear: both;
    }
    &-more {
        border-radius: 0 0 20rpx 20rpx;
        background-color: white;
        padding:24rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        font-weight: 400;
        font-size: 20rpx;
        color: #AAAAAA;
        u-icon {
            margin-left: 4rpx;
        }
    }
    &-detail {
        background-color: white;
        padding: 0 24rpx;
        &-title {
            font-weight: 500;
            font-size: 32rpx;
            color: #333333;
            line-height: 38rpx;
            margin: 32rpx 0;
            
        }
         &-title:before {
            content: '';
            display: inline-block;
            width: 6rpx;
            height: 28rpx;
            background: #A1A1A1;
            border-radius: 28rpx;
            margin-right: 18rpx;
            position: relative;
            top: 4rpx;
         }  
        &-main {
            // margin-bottom: 20rpx;
            background-color: #FAFAFA;
            padding: 20rpx 20rpx 3rpx 20rpx;
            border-radius: 20rpx;
        }
        &-main:not(:last-child)  {
              margin-bottom: 20rpx;
        }
    }
}
.pop-bottom {
    padding-bottom: 20rpx;
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    .li {
        width: 50%;
        display: flex;
        flex-direction: column;
        align-items: center;
        image {
            width: 152rpx;
            height: 152rpx;
            margin-top: 74rpx;
            margin-bottom: 29rpx;
        }
    }
    .bottom {
        width: 100%;
        margin-top: 48rpx;
        margin-bottom: 0;
        text-align: center;
        image {
            width: 40rpx;
            height: 40rpx;
        }
    }
}

.subtitle-line {
    width: 200%;
    height: 1px;
    background: #EBEBEB;
    transform: scaleY(0.5);
    position: absolute;
    bottom: 0;
}
.card-more{
	font-size: 28rpx;
}
.notAvailable{
	text-align: center;
}
.card-detail{
	border-radius: 12rpx;
}
.headPortrait{
	display: flex;
.headPortraitLeft{
	width: 80rpx;
	height: 80rpx;
	margin-right: 4rpx;
	image{
		height: 100%;
		width: 100%;
		border-radius: 80rpx;
		display: block;
	}
}
.headPortraitRight{
	display: flex;
	align-items: center;
	.headPortraitRight_1{
		color:  #FF6E00;
		    margin-right: 12rpx;
			font-size: 30rpx;
			margin-left: 16rpx;
	}
	.headPortraitRight_2{
		display: flex;
		    align-items: center;
		    padding: 4rpx 20rpx;
		    background: linear-gradient(90deg, #F6E0B7 0%, #FFE7C0 100%);
		    box-shadow: inset 0rpx 0rpx 4rpx 0rpx rgba(255, 255, 255, 0.25);
		    border-radius: 40rpx;
		    font-weight: 500;
		    font-size: 24rpx;
		    color: #7A4E2B;
	}
}
}
.headPortraitTag{
	display: flex;
	justify-content: center;
	align-items: center;
	overflow-x: scroll;
	overflow-y: hidden;
	white-space: nowrap;
	margin-top: 20rpx;
	.headPortraitTag1{
		display: inline-block;
		margin-right: 28rpx;
		border-radius: 10rpx;
		padding: 0 16rpx;
		height: 35rpx;
		line-height: 35rpx;
		font-weight: 400;
		font-size: 22rpx;
		    font-size: 22rpx;
		    background: #1FA2FF;
		    color: #E9F2FF;
		    margin-right: 16rpx;
	}
}
.btn {
		position: fixed;
		bottom: 310rpx;
		right: 0rpx;
	}

	.topRoof {
		background: #fff;
		border-radius: 100rpx;
		width: 80rpx;
		height: 80rpx;
		text-align: center;
		line-height: 130rpx;
	}
		
	.image-button{
		 /* 1. 去除按钮默认样式 */
		  padding: 0;
		  border: none;
		  background: none;
		  /* 2. 设置按钮尺寸 */
		  width: 120rpx;  /* 自定义宽度 */
		  height: 120rpx; /* 自定义高度 */
		  /* 3. 隐藏溢出内容 */
		  overflow: hidden;
		  /* 4. 可选：添加点击效果 */
		  cursor: pointer;
	}
	.image-button image {
	  /* 1. 确保图片填充容器 */
	  width: 100%;
	  height: 100%;
	  /* 2. 保持图片比例并裁剪填充 */
	  object-fit: cover;
	  /* 3. 消除img默认间距 */
	  display: block;
	  /* 4. 可选：添加过渡效果 */
	  transition: transform 0.3s ease;
	}

