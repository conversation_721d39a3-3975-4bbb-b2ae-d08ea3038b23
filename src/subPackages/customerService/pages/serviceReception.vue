<template>
  <view class="service-reception-container">
    <!-- 状态栏占位 -->
    <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>

    <!-- 顶部导航栏 -->
    <view class="reception-header">
      <view class="header-left" @click="goBack">
        <u-icon name="arrow-left" size="36" color="#333333"></u-icon>
      </view>
      <view class="header-title">
        <u-icon
          name="kefu-ermai"
          size="28"
          color="#007aff"
          :custom-style="{ marginRight: '12rpx' }"
        ></u-icon>
        <text class="title-text">客服接待</text>
      </view>
      <view class="header-right">
        <text class="status-indicator" :class="{ online: isOnline }">
          {{ isOnline ? '在线' : '离线' }}
        </text>
      </view>
    </view>

    <!-- Tab 导航栏 -->
    <view class="tab-navigation">
      <view
        class="tab-item"
        :class="{ active: currentTab === 'pending' }"
        @click="switchTab('pending')"
      >
        <text class="tab-text">待接入列表</text>
        <view v-if="pendingCount > 0" class="tab-badge">{{
          pendingCount
        }}</view>
      </view>
      <view
        class="tab-item"
        :class="{ active: currentTab === 'messages' }"
        @click="switchTab('messages')"
      >
        <text class="tab-text">消息列表</text>
        <view v-if="messageCount > 0" class="tab-badge">{{
          messageCount
        }}</view>
      </view>
    </view>

    <!-- Tab 内容区域 -->
    <view class="tab-content">
      <!-- 待接入列表 -->
      <view v-if="currentTab === 'pending'" class="tab-panel">
        <view class="panel-header">
          <text class="panel-title">待接入会话 ({{ pendingList.length }})</text>
        </view>

        <scroll-view class="session-list" scroll-y>
          <view
            v-for="session in pendingList"
            :key="session.conversationId"
            class="session-item"
            @click="enterChat(session)"
          >
            <view class="session-avatar">
              <image
                :src="session.userAvatar || defaultUserAvatar"
                class="avatar-img"
              />
              <view v-if="session.unreadCount > 0" class="unread-badge">
                {{ session.unreadCount > 99 ? '99+' : session.unreadCount }}
              </view>
            </view>

            <view class="session-content">
              <view class="session-header">
                <text class="user-name">{{ session.userName || '用户' }}</text>
                <text class="session-time">{{
                  formatTime(session.updatedTime)
                }}</text>
              </view>

              <view class="last-message">
                <text class="message-text u-line-1">{{
                  session.lastMessage
                }}</text>
              </view>

              <view class="session-tags">
                <text
                  v-if="session.priority === 'high'"
                  class="priority-tag high"
                >
                  紧急
                </text>
                <text
                  v-if="session.status === 'pending'"
                  class="status-tag pending"
                >
                  待回复
                </text>
                <text v-if="session.customerType" class="customer-tag">
                  {{ session.customerType }}
                </text>
              </view>
            </view>

            <!-- 待接入会话的接入按钮 -->
            <view class="session-actions">
              <text class="accept-btn" @click.stop="handleAcceptSession(session)">
                接入
              </text>
            </view>
          </view>

          <!-- 空状态 -->
          <view v-if="pendingList.length === 0" class="empty-state">
            <image src="/static/images/empty-chat.png" class="empty-image" />
            <text class="empty-text">暂无待接入会话</text>
          </view>
        </scroll-view>
      </view>

      <!-- 消息列表 -->
      <view v-if="currentTab === 'messages'" class="tab-panel">
        <view class="panel-header">
          <text class="panel-title">我的会话 ({{ messageList.length }})</text>
          <view class="filter-buttons">
            <text
              class="filter-btn"
              :class="{ active: messageFilter === 'all' }"
              @click="setMessageFilter('all')"
            >
              全部
            </text>
            <text
              class="filter-btn"
              :class="{ active: messageFilter === 'active' }"
              @click="setMessageFilter('active')"
            >
              进行中
            </text>
            <text
              class="filter-btn"
              :class="{ active: messageFilter === 'closed' }"
              @click="setMessageFilter('closed')"
            >
              已结束
            </text>
          </view>
        </view>

        <scroll-view class="session-list" scroll-y>
          <view
            v-for="session in filteredMessageList"
            :key="session.conversationId"
            class="session-item"
            @click="enterChat(session)"
          >
            <view class="session-avatar">
              <image
                :src="session.userAvatar || defaultUserAvatar"
                class="avatar-img"
              />
              <view v-if="session.unreadCount > 0" class="unread-badge">
                {{ session.unreadCount > 99 ? '99+' : session.unreadCount }}
              </view>
            </view>

            <view class="session-content">
              <view class="session-header">
                <text class="user-name">{{ session.userName || '用户' }}</text>
                <text class="session-time">{{
                  formatTime(session.updatedTime)
                }}</text>
              </view>

              <view class="last-message">
                <text class="message-text u-line-1">{{
                  session.lastMessage
                }}</text>
              </view>

              <view class="session-tags">
                <text
                  v-if="session.priority === 'high'"
                  class="priority-tag high"
                >
                  紧急
                </text>
                <text
                  v-if="session.status === 'active'"
                  class="status-tag active"
                >
                  进行中
                </text>
                <text
                  v-if="session.status === 'closed'"
                  class="status-tag closed"
                >
                  已结束
                </text>
                <text v-if="session.customerType" class="customer-tag">
                  {{ session.customerType }}
                </text>
              </view>
            </view>

            <view class="session-actions">
              <!-- 只有接入中的会话才显示转接按钮 -->
              <text
                v-if="session.status === 'active' || session.status === 1"
                class="action-btn"
                @click.stop="handleTransferSession(session)"
              >
                转接
              </text>
            </view>
          </view>

          <!-- 空状态 -->
          <view v-if="filteredMessageList.length === 0" class="empty-state">
            <image src="/static/images/empty-chat.png" class="empty-image" />
            <text class="empty-text">暂无会话消息</text>
          </view>
        </scroll-view>
      </view>
    </view>

    <!-- 转接面板组件 -->
    <TransferPanel
      :visible="showTransfer"
      :session="selectedSession"
      :safe-area-bottom="safeAreaBottom"
      @close="handleTransferClose"
      @transfer-success="handleTransferSuccess"
      @transfer-error="handleTransferError"
    />
  </view>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import TransferPanel from '../components/TransferPanel.vue';
import { onLoad, onShow } from '@dcloudio/uni-app';

// 响应式数据
const isOnline = ref(true);
const currentTab = ref('pending'); // 当前选中的 tab: 'pending' | 'messages'
const currentFilter = ref('all');
const messageFilter = ref('all'); // 消息列表的过滤条件: 'all' | 'active' | 'closed'
const showTransfer = ref(false);
const selectedSession = ref(null);

// 安全区域相关
const statusBarHeight = ref(0);
const safeAreaBottom = ref(0);

// 会话列表
const pendingList = ref([]); // 待接入列表
const messageList = ref([]); // 消息列表

// 计算属性
const pendingCount = computed(() => pendingList.value.length);
const messageCount = computed(
  () => messageList.value.filter((session) => session.unreadCount > 0).length,
);

// 过滤后的消息列表
const filteredMessageList = computed(() => {
  switch (messageFilter.value) {
    case 'active':
      return messageList.value.filter((session) => session.status === 'active');
    case 'closed':
      return messageList.value.filter((session) => session.status === 'closed');
    default:
      return messageList.value;
  }
});

// 默认头像
const defaultUserAvatar = '/static/images/default-user-avatar.png';

// 导入API函数
import {
  getPendingConversationList,
  getAgentConversationList,
  assignAgent,
  markConversationAsRead,
} from '../api/customerService.js';

// 页面加载
onLoad((options) => {
  console.log('服务接待页面加载', options);

  // 获取系统信息设置安全区域
  initSafeArea();

  initServiceInfo();
  // 默认加载待接入列表
  loadPendingList();
});

onShow(() => {
  // 页面显示时刷新会话列表
  refreshSessionList();
});

// 初始化安全区域
const initSafeArea = () => {
  const systemInfo = uni.getSystemInfoSync();
  statusBarHeight.value = systemInfo.statusBarHeight || 0;

  // 获取安全区域信息
  if (systemInfo.safeAreaInsets) {
    safeAreaBottom.value = systemInfo.safeAreaInsets.bottom || 0;
  } else if (systemInfo.safeArea) {
    // 兼容旧版本
    safeAreaBottom.value =
      systemInfo.screenHeight - systemInfo.safeArea.bottom || 0;
  }

  console.log('安全区域信息:', {
    statusBarHeight: statusBarHeight.value,
    safeAreaBottom: safeAreaBottom.value,
  });
};

// 初始化服务人员信息
const initServiceInfo = () => {
  // 优先从 userInfo 获取用户信息
  const userInfo = uni.getStorageSync('userInfo');
  const serviceInfo = uni.getStorageSync('serviceInfo');

  console.log('获取到的用户信息:', userInfo);
  console.log('获取到的服务人员信息:', serviceInfo);

  // 优先使用 userInfo 中的 userId
  if (userInfo && userInfo.userId) {
    currentServiceId.value = userInfo.userId;
    console.log('从 userInfo 设置当前客服ID:', currentServiceId.value);
  } else if (serviceInfo && serviceInfo.id) {
    // 如果没有 userInfo，则使用 serviceInfo
    currentServiceId.value = serviceInfo.id;
    console.log('从 serviceInfo 设置当前客服ID:', currentServiceId.value);
  } else {
    console.log('没有找到服务人员信息，将获取待接入会话列表');
  }
};

// 当前服务人员ID
const currentServiceId = ref(null);

// 加载待接入列表
const loadPendingList = async () => {
  try {
    console.log('开始加载待接入列表...');
    const res = await getPendingConversationList();
    console.log('待接入会话列表响应:', res);

    if (res.code === 200 && res.data) {
      pendingList.value = res.data.map((conv) => ({
        conversationId: conv.id,
        userName: conv.userNickname || '用户',
        userAvatar: conv.userAvatar || defaultUserAvatar,
        lastMessage: conv.lastMessageContent || '暂无消息',
        updatedTime: conv.updatedTime || Date.now(),
        unreadCount: conv.unreadCount || 0,
        status: 'pending',
        priority: conv.priority || 'normal',
        customerType: conv.customerType || '普通客户',
        agentId: conv.agentId,
        customerId: conv.customerId,
      }));
    }
  } catch (error) {
    console.error('加载待接入列表失败:', error);
    uni.showToast({
      title: '加载失败，请重试',
      icon: 'none',
    });
  }
};

// 加载消息列表
const loadMessageList = async () => {
  try {
    // 重新获取客服ID，确保是最新的
    let agentId = currentServiceId.value;

    if (!agentId) {
      // 如果没有缓存的客服ID，重新从存储中获取
      const userInfo = uni.getStorageSync('userInfo');
      const serviceInfo = uni.getStorageSync('serviceInfo');

      if (userInfo && userInfo.userId) {
        agentId = userInfo.userId;
        currentServiceId.value = agentId;
      } else if (serviceInfo && serviceInfo.id) {
        agentId = serviceInfo.id;
        currentServiceId.value = agentId;
      }
    }

    if (!agentId) {
      console.log('没有客服ID，无法加载消息列表');
      uni.showToast({
        title: '请先登录客服账号',
        icon: 'none',
      });
      return;
    }

    console.log('开始加载消息列表...', agentId);
    console.log('调用 getAgentConversationList 接口，参数:', {
      agentId: agentId,
    });

    const res = await getAgentConversationList({
      agentId: agentId,
    });

    console.log('客服会话列表响应:', res);
    console.log('响应数据详情:', {
      code: res.code,
      dataLength: res.data ? res.data.length : 0,
      data: res.data,
    });

    if (res.code === 200 && res.data) {
      messageList.value = res.data.map((conv) => ({
        conversationId: conv.id,
        userName: conv.userNickname || '用户',
        userAvatar: conv.userAvatar || defaultUserAvatar,
        lastMessage: conv.lastMessageContent || '暂无消息',
        updatedTime: conv.updatedTime || Date.now(),
        unreadCount: conv.unreadCount || 0,
        status: conv.status === 1 ? 'active' : conv.status === 2 ? 'closed' : 'active', // 1=接入中, 2=已结束
        priority: conv.priority || 'normal',
        customerType: conv.customerType || '普通客户',
        agentId: conv.agentId,
        customerId: conv.customerId,
      }));

      console.log('消息列表加载成功，共', messageList.value.length, '条会话');
    } else {
      console.log('消息列表响应异常:', res);
      messageList.value = [];
    }
  } catch (error) {
    console.error('加载消息列表失败:', error);
    uni.showToast({
      title: '加载失败，请重试',
      icon: 'none',
    });
    messageList.value = [];
  }
};

// 刷新会话列表
const refreshSessionList = async () => {
  try {
    if (currentTab.value === 'pending') {
      await loadPendingList();
    } else {
      await loadMessageList();
    }
  } catch (error) {
    console.error('刷新会话列表失败:', error);
  }
};

// 切换 tab
const switchTab = async (tab) => {
  console.log('切换 tab:', tab);
  currentTab.value = tab;

  // 显示加载提示
  uni.showLoading({
    title: tab === 'pending' ? '加载待接入列表...' : '加载消息列表...',
  });

  try {
    // 根据 tab 加载对应数据
    if (tab === 'pending') {
      await loadPendingList();
    } else {
      await loadMessageList();
    }
  } catch (error) {
    console.error('切换 tab 失败:', error);
    uni.showToast({
      title: '加载失败，请重试',
      icon: 'none',
    });
  } finally {
    uni.hideLoading();
  }
};

// 设置消息过滤器
const setMessageFilter = (filter) => {
  console.log('设置消息过滤器:', filter);
  messageFilter.value = filter;
};

// 设置过滤器
const setFilter = async (filter) => {
  console.log('切换过滤条件:', filter);
  currentFilter.value = filter;
  // 切换过滤条件时重新加载数据
  await loadSessionList();
};

// 进入聊天
const enterChat = async (session) => {
  try {
    // 如果是待接入会话，先分配客服
    if (session.status === 'pending') {
      console.log('待接入会话，开始分配客服:', session);

      // 获取当前客服信息
      const serviceInfo = uni.getStorageSync('userInfo');
      if (!serviceInfo?.userId) {
        uni.showToast({
          title: '请先登录客服账号',
          icon: 'none',
        });
        return;
      }

      // 调用分配客服API
      const res = await assignAgent({
        conversationId: session.conversationId,
        agentId: serviceInfo.userId,
      });

      console.log('分配客服API响应:', res);

      if (res.code !== 200) {
        uni.showToast({
          title: res.msg || '分配失败，请重试',
          icon: 'none',
        });
        return;
      }

      console.log('客服分配成功，进入聊天');
    }

    // 标记消息为已读（仅对消息列表中的会话）
    if (currentTab.value === 'messages' && session.conversationId) {
      try {
        console.log('标记消息为已读:', session.conversationId);

        // 使用 customerService.js 中的 API 函数
        const readRes = await markConversationAsRead({
          conversationId: session.conversationId,
          receiverType: 'AGENT'
        });

        if (readRes.code === 200) {
          console.log('消息已标记为已读');

          // 更新本地会话列表中的未读数量
          const sessionIndex = messageList.value.findIndex(
            (item) => item.conversationId === session.conversationId
          );
          if (sessionIndex !== -1) {
            const oldUnreadCount = messageList.value[sessionIndex].unreadCount || 0;
            messageList.value[sessionIndex].unreadCount = 0;

            console.log(`会话 ${session.conversationId} 未读数量从 ${oldUnreadCount} 更新为 0`);
          }

          // 刷新全局未读消息数量
          const app = getApp();
          if (app.globalData && app.globalData.updateUnreadCounts) {
            await app.globalData.updateUnreadCounts();
            console.log('已更新全局未读消息数量');
          }
        } else {
          console.warn('标记消息已读失败:', readRes);
        }
      } catch (readError) {
        console.error('标记消息已读出错:', readError);
        // 不阻止进入聊天，只记录错误
      }
    }

    // 进入聊天页面
    uni.navigateTo({
      url: `/subPackages/customerService/pages/serviceChat?conversationId=${session.conversationId}&userName=${session.userName}`,
    });
  } catch (error) {
    console.error('进入聊天失败:', error);
    uni.showToast({
      title: '操作失败，请重试',
      icon: 'none',
    });
  }
};

// 处理接入会话
const handleAcceptSession = async (session) => {
  try {
    console.log('开始接入会话:', session);

    // 调用现有的进入聊天逻辑，它会处理待接入会话的分配
    await enterChat(session);
  } catch (error) {
    console.error('接入会话失败:', error);
    uni.showToast({
      title: '接入失败，请重试',
      icon: 'none',
    });
  }
};

// 处理转接会话
const handleTransferSession = (session) => {
  selectedSession.value = session;
  showTransfer.value = true;
};

// 处理转接面板关闭
const handleTransferClose = () => {
  showTransfer.value = false;
  selectedSession.value = null;
};

// 处理转接成功
const handleTransferSuccess = async (result) => {
  console.log('转接成功:', result);

  // 关闭转接面板
  handleTransferClose();

  // 刷新会话列表
  await refreshSessionList();
};

// 处理转接失败
const handleTransferError = (error) => {
  console.error('转接失败:', error);
  // 错误提示已在组件内部处理，这里可以添加额外的错误处理逻辑
};



// 格式化时间
const formatTime = (timestamp) => {
  const date = new Date(timestamp);
  const now = new Date();
  const diff = now - date;

  if (diff < 60000) {
    // 1分钟内
    return '刚刚';
  } else if (diff < 3600000) {
    // 1小时内
    return `${Math.floor(diff / 60000)}分钟前`;
  } else if (date.toDateString() === now.toDateString()) {
    // 今天
    return date.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit',
    });
  } else {
    return date.toLocaleDateString('zh-CN', {
      month: '2-digit',
      day: '2-digit',
    });
  }
};

// 返回上一页
const goBack = () => {
  uni.navigateBack({
    delta: 1,
    fail: () => {
      uni.switchTab({
        url: '/pageA/home',
      });
    },
  });
};

// 定时刷新会话列表
onMounted(() => {
  const timer = setInterval(() => {
    if (isOnline.value) {
      refreshSessionList();
    }
  }, 30000); // 30秒刷新一次

  // 页面卸载时清除定时器
  onUnmounted(() => {
    clearInterval(timer);
  });
});
</script>

<style lang="scss" scoped>
.service-reception-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.status-bar {
  background-color: #ffffff;
  width: 100%;
}

.reception-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 32rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #e5e5e5;

  .header-left,
  .header-right {
    width: 120rpx;
    height: 60rpx;
    display: flex;
    align-items: center;
  }

  .header-left {
    justify-content: flex-start;
    border-radius: 12rpx;
    transition: background-color 0.2s ease;

    &:active {
      background-color: #f0f0f0;
    }
  }

  .header-right {
    justify-content: flex-end;
  }

  .header-title {
    display: flex;
    align-items: center;
    justify-content: center;

    .title-text {
      font-size: 34rpx;
      font-weight: 600;
      color: #333333;
    }
  }

  .status-indicator {
    font-size: 24rpx;
    padding: 6rpx 12rpx;
    border-radius: 12rpx;
    background-color: #f0f0f0;
    color: #999999;

    &.online {
      background-color: #e8f5e8;
      color: #52c41a;
    }
  }
}

// Tab 导航样式
.tab-navigation {
  display: flex;
  background-color: #ffffff;
  border-bottom: 1rpx solid #e5e5e5;

  .tab-item {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20rpx;
    position: relative;
    transition: background-color 0.2s ease;

    &:active {
      background-color: #f8f8f8;
    }

    &.active {
      .tab-text {
        color: #007aff;
        font-weight: 600;
      }

      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 40rpx;
        height: 3rpx;
        background-color: #007aff;
        border-radius: 2rpx;
      }
    }

    .tab-text {
      font-size: 26rpx;
      color: #666666;
      transition: color 0.2s ease;
    }

    .tab-badge {
      position: absolute;
      top: 16rpx;
      right: 16rpx;
      min-width: 28rpx;
      height: 28rpx;
      background-color: #ff4d4f;
      color: #ffffff;
      font-size: 20rpx;
      border-radius: 14rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0 6rpx;
    }
  }
}

.tab-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.tab-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 32rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #f0f0f0;

  .panel-title {
    font-size: 26rpx;
    font-weight: 600;
    color: #333333;
  }

  .filter-buttons {
    display: flex;
    align-items: center;

    .filter-btn {
      font-size: 24rpx;
      color: #666666;
      padding: 8rpx 16rpx;
      border-radius: 16rpx;
      margin-left: 12rpx;
      background-color: #f5f5f5;
      transition: all 0.2s ease;

      &:active {
        transform: scale(0.95);
      }

      &.active {
        background-color: #007aff;
        color: #ffffff;
      }
    }
  }
}

.session-list-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

.list-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #e5e5e5;

  .session-count {
    font-size: 26rpx;
    font-weight: 500;
    color: #333333;
  }

  .filter-buttons {
    display: flex;

    .filter-btn {
      font-size: 24rpx;
      padding: 12rpx 24rpx;
      margin-left: 16rpx;
      border-radius: 20rpx;
      background-color: #f0f0f0;
      color: #666666;

      &.active {
        background-color: #007aff;
        color: #ffffff;
      }
    }
  }
}

.session-list {
  flex: 1;
  padding: 16rpx;
  box-sizing: border-box;
}

.session-item {
  display: flex;
  align-items: center;
  background-color: #ffffff;
  gap: 20rpx;
  margin-bottom: 12rpx;
  border-radius: 12rpx;
  padding: 20rpx;
  transition: all 0.2s ease;

  &:active {
    transform: scale(0.98);
    background-color: #f8f8f8;
  }
}

.session-avatar {
  position: relative;

  .avatar-img {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
  }

  .unread-badge {
    position: absolute;
    top: -6rpx;
    right: -6rpx;
    min-width: 28rpx;
    height: 28rpx;
    background-color: #ff4d4f;
    color: #ffffff;
    font-size: 20rpx;
    border-radius: 14rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 6rpx;
  }
}

.session-content {
  flex: 1;
  width: 0;

  .session-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 6rpx;

    .user-name {
      font-size: 26rpx;
      font-weight: 600;
      color: #333333;
    }

    .session-time {
      font-size: 24rpx;
      color: #999999;
    }
  }

  .last-message {
    margin-bottom: 10rpx;

    .message-text {
      font-size: 24rpx;
      color: #666666;
      line-height: 1.3;
      display: -webkit-box;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
  }

  .session-tags {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 8rpx;

    .priority-tag,
    .status-tag,
    .customer-tag {
      font-size: 20rpx;
      padding: 3rpx 8rpx;
      border-radius: 8rpx;
    }

    .priority-tag.high {
      background-color: #fff2e8;
      color: #fa8c16;
    }

    .status-tag.pending {
      background-color: #fff1f0;
      color: #ff4d4f;
    }

    .status-tag.active {
      background-color: #e6f7ff;
      color: #1890ff;
    }

    .status-tag.closed {
      background-color: #f0f0f0;
      color: #999999;
    }

    .customer-tag {
      background-color: #f6ffed;
      color: #52c41a;
    }
  }
}

.session-actions {
  display: flex;
  flex-direction: column;
  gap: 8rpx;

  .action-btn {
    font-size: 24rpx;
    color: #007aff;
    padding: 6rpx 12rpx;
    text-align: center;
    background-color: #f0f8ff;
    border-radius: 8rpx;
    transition: all 0.2s ease;

    &:active {
      background-color: #e0f0ff;
      transform: scale(0.95);
    }
  }

  .accept-btn {
    font-size: 24rpx;
    color: #52c41a;
    padding: 6rpx 12rpx;
    text-align: center;
    background-color: #f6ffed;
    border-radius: 8rpx;
    transition: all 0.2s ease;

    &:active {
      background-color: #d9f7be;
      transform: scale(0.95);
    }
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;

  .empty-image {
    width: 160rpx;
    height: 160rpx;
    margin-bottom: 24rpx;
    opacity: 0.6;
  }

  .empty-text {
    font-size: 26rpx;
    color: #999999;
  }
}


</style>
