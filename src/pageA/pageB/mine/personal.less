.personal {
    background-color: #F3F3F3;
    height: 100vh;
    overflow: hidden;
    .footer {
        position: fixed;
        bottom: 82rpx;
        width: 100%;
        /deep/.u-btn--default {
            margin: 20rpx 16rpx;
        }
        .save /deep/.u-btn--default {
            background-color: #FF4F61;
            color: #FFFFFF;
        }
        .loginout /deep/.u-btn--default {
            color: #FF4F61;
            // border: 1rpx solid #FF4F61;
        }
    }
    .card {
        background-color: #FFFFFF;
        border-radius: 20rpx;
        margin: 20rpx;
        padding: 0 24rpx;
        overflow: hidden;
        font-size: 26rpx;
        color: #333333;
    }
    .devide {
        width: 685rpx;
        height: 1rpx;
        background: #000000;
        opacity: 0.05;
    }
}
.lbox1 {
    display: flex;
    align-items: center;
    margin: 40rpx 0;
    justify-content: space-between;
    /deep/.u-avatar {
        height: 64rpx !important;
        width: 64rpx !important;
    }
}

.lbox1 input {
   text-align: right;
   
}
.tel {
     color: #3C3C3C;
}

.lbox1 button {
    background-color: transparent;
}

.lbox1 .avatar-wrapper {
    margin: 0;
    padding: 0;
    line-height: 0;
    
}
