<template>
  <view> </view>
</template>
<script>
import { onLoad } from '@dcloudio/uni-app';

let promiseIndex = 0;
let promiseArray = [];
let imageArray = [];
export default {
  data() {
    return {};
  },
  props: {
    canvasWidth: {
      type: Number,
      default: 0,
    },
    canvasHeight: {
      type: Number,
      default: 0,
    },
  },
  methods: {
    // // 分享图片
    // sharePicture({
    //     canvas,
    //     path
    // }) {
    //     const fileData = canvas.toDataURL()
    //     const fs = uni.getFileSystemManager()
    //     let that = this
    //     fs.writeFile({
    //         filePath: path,
    //         data: fileData.replace(/^data:image\/\w+;base64,/, ""),
    //         encoding: 'base64',
    //         success(res) {
    //             uni.hideLoading()
    //             wx.showShareImageMenu({
    //                 withShareTicket: true,
    //                 path: path,
    //                 success: async res => {
    //                     console.log(res)
    //                     uni.navigateBack()
    //                 }
    //             })
    //         },
    //         complete(res) {
    //             console.log('调用结束');
    //             // uni.navigateBack()
    //         }
    //     })
    // },

    // 绘制阴影
    // drawShadow(ctx, dpr, bool) {
    //     if (bool) {
    //         ctx.shadowOffsetX = 0; // 阴影在x轴的偏移量
    //         ctx.shadowOffsetY = 0; // 阴影在y轴的偏移量
    //         ctx.shadowBlur = 14 * dpr; // 阴影的模糊程度
    //         ctx.shadowColor = 'rgba(186,186,186,0.5)'; // 阴影的颜色和透明度
    //     } else {
    //         ctx.shadowColor = 'transparent';
    //         ctx.shadowBlur = 0;
    //         ctx.shadowOffsetX = 0;
    //         ctx.shadowOffsetY = 0;
    //     }
    // },

    //rpx转换为px的工具函数
    rpxtopx(rpx) {
      let deviceWidth = wx.getSystemInfoSync().windowWidth; //获取设备屏幕宽度
      let px = (deviceWidth / 750) * Number(rpx);
      return Math.floor(px);
    },

    // 绘制矩形
    drawRect(ctx, radius, x, y, width, height) {
      ctx.save();
      if (radius) {
        console.log(x, y, width, height, radius, 'radius=====');
        // 圆角
        if (ctx.roundRect) {
          // console.log('roundRect======');
          ctx.roundRect(x, y, width, height, [radius]);
        } else {
          ctx.beginPath();
          ctx.moveTo(x + radius, y);
          ctx.arcTo(x + width, y, x + width, y + height, radius);
          ctx.arcTo(x + width, y + height, x, y + height, radius);
          ctx.arcTo(x, y + height, x, y, radius);
          ctx.arcTo(x, y, x + width, y, radius);
          ctx.closePath();
        }
        ctx.strokeStyle = 'transparent';
        ctx.stroke();
      } else {
        // 非圆角
        ctx.fillRect(x, y, width, height);
      }
      ctx.restore();
    },

    // 绘制带背景的矩形
    drawBg(ctx, x, y, w, h, r, c = '#fff') {
      if (w < 2 * r) {
        r = w / 2;
      }
      if (h < 2 * r) {
        r = h / 2;
      }

      ctx.beginPath();
      ctx.fillStyle = c;

      ctx.arc(x + r, y + r, r, Math.PI, Math.PI * 1.5);
      ctx.moveTo(x + r, y);
      ctx.lineTo(x + w - r, y);
      ctx.lineTo(x + w, y + r);

      ctx.arc(x + w - r, y + r, r, Math.PI * 1.5, Math.PI * 2);
      ctx.lineTo(x + w, y + h - r);
      ctx.lineTo(x + w - r, y + h);

      ctx.arc(x + w - r, y + h - r, r, 0, Math.PI * 0.5);
      ctx.lineTo(x + r, y + h);
      ctx.lineTo(x, y + h - r);

      ctx.arc(x + r, y + h - r, r, Math.PI * 0.5, Math.PI);
      ctx.lineTo(x, y + r);
      ctx.lineTo(x + r, y);

      ctx.fill();
      ctx.closePath();
    },

    // 绘制背景
    drawBgColor(ctx, dpr, bgColor) {
      if (bgColor) {
        ctx.fillStyle = bgColor;
      } else if (bgColor === 'gradient') {
        let gradient = ctx.createLinearGradient(0, 0, 0, 800);
        gradient.addColorStop(0, 'pink');
        gradient.addColorStop(1, '#FFFFFF');
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, 1600, 800);
      } else {
        ctx.fillStyle = 'rgba(255,255,255,0)'; // 默认透明
      }
    },

    // 绘制字体样式
    drawFont(ctx, color, size, weight, word, posX, posY) {
      ctx.fillStyle = color ? color : '#333';
      ctx.font = weight
        ? 'bold ' + size + 'px PingFang-SC'
        : size + 'px PingFang-SC';
      ctx.textBaseline = 'middle';
      ctx.fillText(word, posX, posY);
    },

    // 绘制图片圆角
    drawRoundedImage(ctx, image, x, y, w, h, r) {
      // 保存当前画布状态
      ctx.save();
      // ctx.beginPath();
      // 绘制圆角矩形的四个角
      // ctx.moveTo(x + radius, y);
      // ctx.lineTo(x + width - radius, y);
      // ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
      // ctx.lineTo(x + width, y + height - radius);
      // ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
      // ctx.lineTo(x + radius, y + height);
      // ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
      // ctx.lineTo(x, y + radius);
      // ctx.quadraticCurveTo(x, y, x + radius, y);
      // ctx.closePath();
      if (w < 2 * r) r = w / 2;
      if (h < 2 * r) r = h / 2;
      ctx.beginPath();
      ctx.moveTo(x + r, y);
      ctx.arcTo(x + w, y, x + w, y + h, r);
      ctx.arcTo(x + w, y + h, x, y + h, r);
      ctx.arcTo(x, y + h, x, y, r);
      ctx.arcTo(x, y, x + w, y, r);
      ctx.closePath();
      ctx.strokeStyle = '#FFFFFF'; // 设置绘制圆形边框的颜色
      ctx.stroke();
      // 裁剪路径
      ctx.clip();
      // 绘制图像
      this.drawImageCover(ctx, image, x, y, w, h);
      // ctx.drawImage(image, x, y, width, height);
      // 恢复画布状态
      ctx.restore();
    },

    drawImageCover(ctx, img, x, y, w, h) {
      console.log('drawImageCover', img);
      const imgRatio = img.width / img.height;
      const boxRatio = w / h;
      let sx, sy, sw, sh;
      if (imgRatio > boxRatio) {
        // 裁剪左右
        sw = img.height * boxRatio;
        sh = img.height;
        sx = (img.width - sw) / 2;
        sy = 0;
      } else {
        // 裁剪上下
        sw = img.width;
        sh = img.width / boxRatio;
        sx = 0;
        sy = (img.height - sh) / 2;
      }
      ctx.drawImage(img, sx, sy, sw, sh, x, y, w, h);
    },

    // 绘制canvas
    drawCanvas(arrayInfo) {
      promiseIndex = 0;
      promiseArray = [];
      imageArray = [];
      console.log('数据信息：', arrayInfo);
      // 0.创建canvas及ctx,并获取屏幕的dpr
      try {
        const canvas = wx.createOffscreenCanvas({
          type: '2d',
          width: this.canvasWidth,
          height: this.canvasHeight,
        });

        const ctx = canvas.getContext('2d');

        const dpr = uni.getWindowInfo().pixelRatio;

        // 1.根据数组数量创建promise数组
        for (let i = 0; i < arrayInfo.length; i++) {
          promiseArray.push(promiseIndex);
          promiseIndex++;
        }

        // 2.根据数组给promise数组赋值
        let that = this;
        for (let i = 0; i < arrayInfo.length; i++) {
          promiseArray[i] = new Promise(function (resolve, reject) {
            if (arrayInfo[i].type == 'text') {
              resolve();
            } else if (arrayInfo[i].type == 'view') {
              resolve();
            } else if (arrayInfo[i].type == 'image') {
              let img = canvas.createImage();
              img.src = arrayInfo[i].src;
              img.onload = function () {
                imageArray[i] = img;
                resolve();
              };
            }
          });
        }

        // 3.异步全部加载完成后，绘制图片
        let p = Promise.all(promiseArray);

        p.then(async () => {
          //根据数组顺序绘制图片
          for (let i = 0; i < arrayInfo.length; i++) {
            if (arrayInfo[i].type == 'text') {
              console.log('绘制文字');
              let { color, size, weight, word, posX, posY } = arrayInfo[i];
              size = that.rpxtopx(arrayInfo[i].size) * dpr;
              posX = posX * dpr;
              posY = (posY + that.rpxtopx(20)) * dpr;
              that.drawFont(ctx, color, size, weight, word, posX, posY);
            } else if (arrayInfo[i].type == 'view') {
              console.log('绘制view');
              // that.drawShadow(ctx, dpr, arrayInfo[i].shadow)
              that.drawBgColor(ctx, dpr, arrayInfo[i].bgColor);
              if (!arrayInfo[i].bgColor) {
                that.drawRect(
                  ctx,
                  arrayInfo[i].radius,
                  arrayInfo[i].posX * dpr,
                  arrayInfo[i].posY * dpr,
                  arrayInfo[i].width * dpr,
                  arrayInfo[i].height * dpr,
                );
              } else {
                that.drawBg(
                  ctx,
                  arrayInfo[i].posX * dpr,
                  arrayInfo[i].posY * dpr,
                  arrayInfo[i].width * dpr,
                  arrayInfo[i].height * dpr,
                  arrayInfo[i].radius ? Number(arrayInfo[i].radius) : 0,
                  arrayInfo[i].bgColor,
                );
              }
            } else if (arrayInfo[i].type == 'image') {
            //   console.log('绘制图片', arrayInfo);
              if (arrayInfo[i].radius) {
                console.log('imagessss-raduisssss', arrayInfo[i].radius);
                that.drawRoundedImage(
                  ctx,
                  imageArray[i],
                  arrayInfo[i].posX * dpr,
                  arrayInfo[i].posY * dpr,
                  arrayInfo[i].width * dpr,
                  arrayInfo[i].height * dpr,
                  Number(arrayInfo[i].radius),
                );
              } else {
                that.drawImageCover(
                  ctx,
                  imageArray[i],
                  arrayInfo[i].posX * dpr,
                  arrayInfo[i].posY * dpr,
                  arrayInfo[i].width * dpr,
                  arrayInfo[i].height * dpr,
                );
              }
            }
          }
          const path =
            `${wx.env.USER_DATA_PATH}/` + new Date().getTime() + `hello.png`;

          // 初始化数据
          promiseIndex = 0;
          promiseArray = [];
          imageArray = [];

          // 将生成的图片传递到父组件
          that.$emit('sharePicture', {
            canvas,
            path,
          });
        });
      } catch (e) {
        this.$emit('finshed');
      }
    },
  },
};
</script>