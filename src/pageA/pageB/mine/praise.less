.dynamics {
    width: 100%;
    .header {
        padding: 21rpx 0;
        width: 100%;
        /deep/.u-tab-item {
            font-weight: normal !important;
            height: 45rpx !important;
            line-height: 37.5rpx !important;
        }
    }
    .more {
        background-color: white;
        padding-bottom: 20rpx;
    }
    .more-card {
        // margin-bottom: 24rpx;
    }
    .pd-24 {
        padding: 24rpx;
    }
    .devide {
        width: 100%;
        height: 1rpx;
        background-color: #EBEBEB;
        margin: 24rpx 0 12rpx 0;
    }
    .devide-content {
        width: 100%;
        height: 20rpx;
        background-color: #F8F9F9;
    }
    .discusss5 {
        color: hsla(0, 0%, 47%, 1);
        font-size: 28rpx;
    }
    .content-main {
        padding: 0 12rpx 80rpx 12rpx;
        background-color: #F8F9F9  
    }
}




/*宝妈讨论*/
.discuss {
    background: #ffffff;
	padding: 24rpx 24rpx;
}

.discus {
    /* padding: 24rpx 24rpx 0 24rpx; */
}

.discuss1 {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.discuss1_1 {
	width: 100%;
    display: flex;
	justify-content: space-between; 
}
.discuss8_1{
	display: flex;
}
.discuss1_2 {
    margin-left: 16rpx;
}

.discuss1_3 {
    color: hsla(26, 100%, 50%, 1);
    font-size: 30rpx;
}

.discuss1_4 {
    font-size: 24rpx;
    color: hsla(0, 0%, 67%, 1);
    margin-top: 4rpx;
}

.discussName {
    margin-left: 24rpx;
}

.discussTitle {
    color: hsla(0, 0%, 47%, 1);
    font-size: 24rpx;
}

.discuss2 {
    margin-top: 16rpx;
    font-size: 28rpx;
}

.discuss2_1 {
    color: hsla(228, 25%, 48%, 1);
}

.discuss2_2 {
    color: hsla(0, 0%, 20%, 1);
}

.discuss3 {
    display: flex;
    flex-wrap: wrap;
    margin: 24rpx 0;
}

.discuss3_1 {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: 15rpx;
    margin-right: 10rpx;
}

.discuss1_5 {
    display: flex;
    align-items: center;
}

.discuss4 {
    width: 100%;
    height: 1rpx;
    background: hsla(0, 0%, 92%, 1);
}

.discuss5 {
    display: flex;
    justify-content: space-around;
    padding: 26rpx 0 32rpx 0;
    color: hsla(0, 0%, 47%, 1);
    font-size: 24rpx;
}

.discuss5_2 {
    margin-left: 4rpx;
}

.blank {
    height: 150rpx;
}

.dynamic {
    background: #ff4f61;
    width: 198rpx;
    height: 84rpx;
    text-align: center;
    line-height: 84rpx;
    border-radius: 110rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    position: fixed;
    bottom: 120rpx;
    left: 37%;
    color: #ffffff;
    font-size: 32rpx;
}

.avatar-list-stacked {
    margin-right: 10rpx;
}

.avatar-list-stacked .avatar {}

.avatar-list-stacked .avatar {
    margin-right: -0.8em !important;
}

.avatar {
    width: 1.5rem;
    height: 1.5rem;
    line-height: 5rem;
    display: inline-block;
    background: #2ddcd3 no-repeat center/cover;
    position: relative;
    text-align: center;
    color: #fff;
    font-weight: 600;
    vertical-align: bottom;
    font-size: 0.875rem;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    border-radius: 50%;
}

.img_box {
    margin-top: 20rpx;
    padding-left: 4rpx;
}

.videos {
    width: 340rpx;
    height: 340rpx;
}

.videos video {
    width: 100%;
    height: 100%;
}

.img_box .many_img {
    display: flex;
    justify-self: start;
    flex-wrap: wrap;
}

.img_item.four {
    width: 32%;
    height: 220rpx;
    margin-bottom: 10rpx;
    border-radius: 16rpx;
    overflow: hidden;
}

.img_item.four image {
    width: 100%;
    height: 100%;
}

.img_item.four:nth-child(2) {
    margin: 0 1%;
}

.img_item.four:nth-child(5) {
    margin: 0 1%;
}

.img_item.four:nth-child(8) {
    margin: 0 1%;
}

.img_item.many {
    width: 48%;
    height: 340rpx;
    margin-bottom: 10rpx;
    border-radius: 10px;
    overflow: hidden;
}

.img_item.many image {
    width: 100%;
    height: 100%;
}

.img_item.many:nth-child(2n) {
    margin-left: 1%;
}

.discusss5 {
    color: hsla(0, 0%, 47%, 1);
    font-size: 28rpx;
    font-weight: 500;
}

.custom-button {
    background: #fff;
    color: hsla(0, 0%, 47%, 1);
    font-size: 24rpx;
    margin: 0;
}

.discuss5_1 {
    display: flex;
    align-items: center;
    justify-content: center;
}



 .custom-buttons {
        background: #fff;
        color: hsla(0, 0%, 47%, 1);
        font-size: 24rpx;
        margin: 0;
        padding: 6rpx;
    }

    .flex {
        display: flex;
        align-items: center;
        justify-content: center;
    }


    .fn {
        display: flex;
		justify-content: space-around;
		align-items: center;
        width: 100%;
    }

    .active {
        color: #ff4f61;
        font-size: 24rpx;
        margin-left: 4rpx;
    }

    .default {
        margin-left: 4rpx;
    }