	.headimg{
		height: 562rpx;
		width: 100%;
		image{
			width: 100%;
			height: 100%;
		}
	}
.main{
	position: relative;
	.foodIntroduce5{
		background: #F8F9F9;
		position: absolute;
		top: -30rpx;
		z-index: 2;
		border-radius: 20rpx;
		text-align: left;
		font-style: normal;
		text-transform: none;
		width: 100%;
	.foodIntroduce{
		    	background: #FFFFFF;
		    		    width: 100%;
		    		    padding: 32rpx 24rpx;
		    			border-top-left-radius: 30rpx;
		    			border-top-right-radius: 30rpx;
		.foodIntroduce1{
			display: flex;
			justify-content: space-between;
			align-items: flex-end;
			.foodIntroduce1_1{
				color: #333333;
				font-size: 34rpx;
				font-weight: bold;
			}
			.foodIntroduce1_2{
				color: #AAAAAA;
				font-size: 24rpx;
				font-weight: bold;
				
			}
		}
		.foodIntroduce2{
			display: flex;
			justify-content: space-between;
			margin-top: 16rpx;
			width: 100%;
			.foodIntroduce2_1{
				// display: flex;
				// align-items: center;
				color: #777777;
				font-size: 22rpx;
				width: 80%;
				.foodIntroduce2_4{
					margin: 0 10rpx;
				}
			}
			.foodIntroduce2_2{
				width: 20%;
			}
			
		}
	}
	.foodMessage{
		padding: 24rpx 24rpx;
		background: #FFFFFF;
		display: flex;
		justify-content: space-between;
		margin-top: 20rpx;
	}
	}
	
}
.image-text-button {
  display: flex; /* 使用flex布局 */
  align-items: center; /* 垂直居中 */
  background: #FFFFFF;
  font-size: 24rpx;
  color: #777777;
}
 
.button-icon {
  margin-right: 10rpx; /* 图片与文字的间距 */
  width: 26rpx; /* 图片宽度 */
  height: 26rpx; /* 图片高度 */
}
.foodMessage1{
	display: flex;
	align-items: flex-start;
	.foodMessage1_1{
		margin-left: 24rpx;
		.foodMessage1_2{
			color: #333333;
			font-size: 28rpx;
			font-weight: bold;
		}
		.foodMessage1_3{
			width: 108rpx;
			height: 36rpx;
			text-align: center;
			line-height: 36rpx;
			color: #FFFFFF;
			background:#E53C3C ;	
			border-radius: 8rpx;
			font-size: 22rpx;
			margin-top: 16rpx;
			}
	}
}
.foodMessage2{
	width: 124rpx;
	height: 44rpx;
	text-align: center;
    line-height: 44rpx;
	border: 1rpx solid #E53C3C;
	border-radius: 46rpx;
	color: #E53C3C;
	font-size: 20rpx;
}
.foodImg{
	padding: 24rpx 24rpx;
	.foodImg1{
		image{
			width: 100%;
		}
	}
}
.tabList5{
	background: #FFFFFF;
		padding: 24rpx 24rpx;
}
.tabList {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin-top: 20rpx;
}

.tabList1 {
    width: 48.5%;
    margin-bottom: 20rpx;
}

.tabList1_1 {
    color: hsla(0, 0%, 20%, 1);
    font-size: 28rpx;
}

.tabList1_2 {
    color: hsla(0, 0%, 47%, 1);
    font-size: 24rpx;
}
.blank {
    height: 240rpx;
}
.heads {
    display: flex;
    justify-content: space-between;
}

.head1 {
    display: flex;
    align-items: center;
}

.head2 {
    display: flex;
    align-items: center;
}

#head1_1 {
    width: 10rpx;
    height: 30rpx;
    background: #ff4f61;
    border-radius: 5rpx;
	
	
}

.head1_2 {
    font-size: 34rpx;
    font-weight: bold;
    color: #333333;
    margin-left: 10rpx;
}
.foodIntroduce2_3{
	display: flex;
	width: 80%;
}