{"id": "BMXDD", "name": "宝妈小叮当", "displayName": "宝妈小叮当", "version": "3.4.5", "description": "uniapp开发", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build:mp-weixin": "cross-env NODE_ENV=production UNI_PLATFORM=mp-weixin vue-cli-service uni-build"}, "repository": "https://github.com/dcloudio/hello-uniapp.git", "keywords": ["uniapp", "BMXDD"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/dcloudio/hello-uniapp/issues"}, "homepage": "https://admin-api.xiaodingdang1.com/wx/portal/wx1df89e340decbc7a", "dcloudext": {"sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": ""}, "declaration": {"ads": "无", "data": "无", "permissions": "无"}, "npmurl": "", "type": "uniapp-template-project"}, "uni_modules": {"dependencies": [], "encrypt": [], "platforms": {"cloud": {"tcb": "y", "aliyun": "y"}, "client": {"App": {"app-vue": "y", "app-nvue": "y"}, "H5-mobile": {"Safari": "y", "Android Browser": "y", "微信浏览器(Android)": "y", "QQ浏览器(Android)": "y"}, "H5-pc": {"Chrome": "y", "IE": "y", "Edge": "y", "Firefox": "y", "Safari": "y"}, "小程序": {"微信": "y", "阿里": "y", "百度": "y", "字节跳动": "y", "QQ": "y", "京东": "y", "钉钉": "y", "快手": "y", "飞书": "y"}, "快应用": {"华为": "y", "联盟": "y"}, "Vue": {"vue2": "y", "vue3": "y"}}}}, "uni-app": {"scripts": {"mp-dingtalk": {"title": "钉钉小程序", "env": {"UNI_PLATFORM": "mp-alipay"}, "define": {"MP-DINGTALK": true}}, "hello-uniapp-demo": {"title": "hello-uniapp 演示网站", "env": {"UNI_PLATFORM": "h5"}, "define": {"H5-DEMO": true}}}}, "devDependencies": {"less": "^4.2.0", "less-loader": "^12.2.0"}, "dependencies": {}}