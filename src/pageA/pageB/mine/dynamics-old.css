.main {
    background: #f8f9f9;
}

.content {
    margin: 20rpx 24rpx 0 24rpx;
    padding: 24rpx 24rpx;
    background: #ffffff;
    border-radius: 20rpx;
}

.headContent1 {
    display: flex;
}

.headContent {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.headTitle {
    margin-left: 16rpx;
}

.headTitle1 {
    color: #ff6e00;
    font-size: 30rpx;
}

.headTitle2 {
    color: #aaaaaa;
    font-size: 24rpx;
    margin-top: 14rpx;
}

.dot {
    width: 12rpx;
    height: 12rpx;
    border-radius: 100rpx;
    background: #ebebeb;
    margin-right: 4rpx;
}

.headContent2 {
    display: flex;
}

.content1 {
    font-size: 28rpx;
    color: #333333;
    margin-top: 16rpx;
}

.content2 {
    margin-top: 20rpx;
    display: flex;
    justify-content: space-between;
}

.content3 {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 56rpx;
}

.content3_1 {
    display: flex;
}

.content3_2 {
    display: flex;
    align-items: center;
}

.content3_3 {
    display: flex;
    align-items: center;
    margin-left: 48rpx;
}

.content3_4 {
    margin-left: 8rpx;
    font-size: 28rpx;
    color: #777777;
}

.content3_5 {
    position: absolute;
    right: 0;
}

.topic1Text3topic1Text3 {
    width: 60rpx;
    height: 60rpx;
    border-radius: 100rpx;
}

.avatar-list-stacked {
    margin-right: 10rpx;
}

.avatar-list-stacked .avatar {}

.avatar-list-stacked .avatar {
    margin-right: -0.8em !important;
}

.avatar {
    width: 1.5rem;
    height: 1.5rem;
    line-height: 5rem;
    display: inline-block;
    background: #2ddcd3 no-repeat center/cover;
    position: relative;
    text-align: center;
    color: #fff;
    font-weight: 600;
    vertical-align: bottom;
    font-size: 0.875rem;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    border-radius: 50%;
}

.img_box {
    margin-top: 20rpx;
    padding-left: 4rpx;
}

.videos {
    width: 340rpx;
    height: 340rpx;
}

.videos video {
    width: 100%;
    height: 100%;
}

.img_box .many_img {
    display: flex;
    justify-self: start;
    flex-wrap: wrap;
}

.img_item.four {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: 10rpx;
    border-radius: 16rpx;
    overflow: hidden;
    margin-right: 10rpx;
}

.img_item.four image {
    width: 100%;
    height: 100%;
}

.img_item.four:nth-child(2n) {}

.img_item.many {
    width: 48%;
    height: 320rpx;
    margin-bottom: 10rpx;
    border-radius: 10px;
    overflow: hidden;
}

.img_item.many image {
    width: 100%;
    height: 100%;
}

.img_item.many:nth-child(2n) {
    margin-left: 1%;
}