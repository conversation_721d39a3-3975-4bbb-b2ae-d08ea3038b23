<template>
    <view>
        <view class="discus">
            <view class="discuss1">
                <view class="discuss1_1">
                    <image :src="communityInfo.avatar || $defaultAvatar" mode=""
                        style="width: 80rpx; height: 80rpx; border-radius: 100rpx" />
                    <view class="discuss1_2">
                        <view class="discuss1_3">
                            {{ communityInfo.nickname || $defaultName }}
                        </view>
                        <view class="discuss1_4">
                            <text>{{ communityInfo.hisTimeStr }}</text>
                            <text class="discussName">{{ communityInfo.postIp }}</text>
                        </view>
                    </view>
                </view>
                <view class="discuss1_5">
                    <view class="discuss1_5" @tap="communityBookmark" :data-postid="communityInfo.postId">
                        <view v-if="!communityInfo.isBookmark">
                            <image src="http://cdn.xiaodingdang1.com/2024/07/03/f1eea30c4652462bacb04838cf8ea389.png"
                                mode="" style="width: 30rpx; height: 30rpx" />
                        </view>
                        <view v-if="communityInfo.isBookmark">
                            <image src="http://cdn.xiaodingdang1.com/2024/07/03/008774f5f0d543fa8ac91c3271e5c96d.png"
                                mode="" style="width: 30rpx; height: 30rpx" />
                        </view>
                        <view class="discussTitle">收藏</view>
                    </view>
                    <view @tap="reply" class="reply" :data-postid="communityInfo.postId" data-type="1">
                        <view>
                            <image
                                src="http://cdn.xiaodingdang1.com/1/120240125/bac59985-fa99-4d0a-a9bd-e216fd0635c4.png"
                                mode="" style="width: 30rpx; height: 30rpx" />
                        </view>
                        <view class="discussTitle">回复</view>
                    </view>
                </view>
            </view>
            <view class="discuss2">
                <text class="discuss2_1">#{{ communityInfo.topicName }}</text>
                <text class="discuss2_2">{{ communityInfo.content }}</text>
            </view>

            <!-- 视频 -->
            <view class="img_box" v-if="communityInfo.videos && communityInfo.videos.length > 0">
                <view class="videos">
                    <video class="video" :src="communityInfo.videos[0]" @tap="preview"
                        :data-src="communityInfo.videos[0]" data-index="0" :ontrols="false"></video>
                </view>
            </view>
            <!-- 图片 -->
            <view class="img_box" v-else>
                <template v-if="communityInfo.imageUrl">
                    <view :class="communityInfo.imageUrl.length > 1 ? 'many_img' : ''">
                        <view :class="
                            'img_item ' +
                            (communityInfo.imageUrl.length == 1 || communityInfo.imageUrl.length == 2 || communityInfo.imageUrl.length == 4
                                ? 'many'
                                : communityInfo.imageUrl.length >= 3
                                ? 'four'
                                : '')
                        " v-for="(res, index) in communityInfo.imageUrl" :key="index">
                            <image class="img" :src="res" @tap="previewImage" :data-url="communityInfo.imageUrl"
                                :data-src="res" :data-sources="communityInfo.imageUrl" :data-index="index"
                                mode="aspectFill"></image>
                        </view>
                    </view>
                </template>
            </view>

            <view class="comment">
                <view class="commentTitle">评论</view>
                <view class="fgx"></view>
            </view>
            <view class="tab">
                <view :class="tabIndex == index ? 'tabActive' : 'tabList'" @tap="tab" :data-index="index"
                    v-for="(item, index) in tabList" :key="index">
                    {{ item.name }}
                </view>
            </view>
            <view class="contentList" v-for="(item, index) in listComment" :key="index">
                <view class="discuss1">
                    <view class="discuss1_1">
                        <image :src="item.avatar || $defaultAvatar" mode=""
                            style="width: 80rpx; height: 80rpx; border-radius: 100rpx" />
                        <view class="discuss1_2">
                            <view class="discuss1_3">
                                {{ item.nickname || $defaultName }}
                            </view>
                            <view class="discuss1_4">
                                <text>{{ item.createTime }}</text>
                            </view>
                        </view>
                    </view>
                    <view class="discuss1_5">
                        <view class="discuss1_6" id="discus1_6" @tap="reply" :data-postid="item.postId"
                            :data-commentid="item.commentId" data-type="2">
                            <view class="discuss1_7">
                                <image
                                    src="http://cdn.xiaodingdang1.com/1/120240125/bac59985-fa99-4d0a-a9bd-e216fd0635c4.png"
                                    mode="" style="width: 26rpx; height: 26rpx" />
                            </view>
                            <view class="discussTitle">回复</view>
                        </view>
                        <view class="discuss1_6" @tap="commentLike" :data-commentid="item.commentId">
                            <view class="discuss1_7" v-if="item.isLike">
                                <image
                                    src="http://cdn.xiaodingdang1.com/2024/07/03/e915e045f143407aa4d7b4142f167389.png"
                                    mode="" style="width: 26rpx; height: 26rpx" />
                            </view>
                            <view class="discuss1_7" v-if="!item.isLike">
                                <image
                                    src="http://cdn.xiaodingdang1.com/1/120240125/058cf0f5-7185-4671-961c-ee263c166795.png"
                                    mode="" style="width: 26rpx; height: 26rpx" />
                            </view>
                            <view class="discussTitle">{{ item.likeNum }}</view>
                        </view>
                    </view>
                </view>

                <view class="contentList1">
                    <view class="contentList1_1"></view>
                    <view class="contentList1_2">
                        {{ item.content }}
                    </view>
                </view>

                <view class="contentList1">
                    <view class="contentList1_1"></view>
                    <view class="contentList1_2" v-if="item.subCommentList.length > 0">
                        <view class="contentList1_3">
                            <view class="contentList1_6" v-if="item.commentShow == 1"
                                v-for="(res, i) in item.subCommentList" :key="i">
                                <view v-if="i <= 1">
                                    <text class="contentList1_4">{{ res.nickname || $defaultName }}：</text>
                                    <text class="contentList1_5">{{ res.content }}</text>
                                </view>
                            </view>
                            <view class="contentList1_6" v-if="item.commentShow == 2"
                                v-for="(res, i) in item.subCommentList" :key="i">
                                <text class="contentList1_4">{{ res.nickname || $defaultName }}：</text>

                                <text class="contentList1_5">{{ res.content }}</text>
                            </view>
                            <view class="contentList1_6" id="contentList1_6" @tap="examine" :data-index="index"
                                v-if="item.commentShow == 1 && item.subCommentList.length > 2">
                                <image
                                    src="http://cdn.xiaodingdang1.com/2024/07/04/ad0cea3ef7034d4d86ae504433666124.png"
                                    mode="" style="width: 30rpx; height: 30rpx" />
                                <view class="contentList1_4">查看剩余{{ item.subCommentList.length - 2 }}条评论</view>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
        </view>

        <view class="comments" :style="'bottom:' + bottomHeight + 'px;'" v-if="replyShow">
            <textarea class="textarea" :show-confirm-bar="false" :auto-height="true" :auto-focus="true"
                cursor-spacing="0" :adjust-position="false" placeholder="来说点什么吧" maxlength="1000" :value="content"
                @focus="bindfocus" @input="bindinput" @blur="bindblur"></textarea>
            <button type="primary" class="send_out" style="width: 100rpx" :disabled="content ? false : true"
                @tap="sendOut">发送</button>
        </view>
    </view>
</template>

<script>
    export default {
        data() {
            return {
                replyCommentId: '',
                replyType: '',
                replyPostId: '',
                replyShow: false,
                postId: '',
                commentShow: 1,
                listComment: [],

                communityInfo: {
                    avatar: '',
                    nickname: '',
                    hisTimeStr: '',
                    postIp: '',
                    postId: '',
                    isBookmark: '',
                    topicName: '',
                    content: '',
                    videos: '',
                    imageUrl: []
                },

                content: '',

                //文本类容
                bottomHeight: 0,

                //定义comment容器与page容器下边界之间的距离
                tabList: [{
                        name: '热度最高'
                    },
                    {
                        name: '最新评论'
                    }
                ],

                tabIndex: 0,
                res: [],
                i: ''
            };
        }
        /**
         * 生命周期函数--监听页面加载
         */
        ,
        onLoad(options) {
            this.communityInfoFun(options.postId); //查询宝妈社区动态详情
            this.listCommentFun(options.postId); //查询宝妈社区动态评论列表
            this.setData({
                postId: options.postId
            });
        },
        /**
         * 生命周期函数--监听页面初次渲染完成
         */
        onReady() {},
        /**
         * 生命周期函数--监听页面显示
         */
        onShow() {},
        /**
         * 生命周期函数--监听页面隐藏
         */
        onHide() {},
        /**
         * 生命周期函数--监听页面卸载
         */
        onUnload() {},
        /**
         * 页面相关事件处理函数--监听用户下拉动作
         */
        onPullDownRefresh() {},
        /**
         * 页面上拉触底事件的处理函数
         */
        onReachBottom() {},
        /**
         * 用户点击右上角分享
         */
        onShareAppMessage() {},

        onShareTimeline() {},
        methods: {
            tab(e) {
                let index = e.currentTarget.dataset.index;
                this.setData({
                    tabIndex: index
                });
                this.listCommentFun(this.postId);
            },

            // 获取焦点 唤起软键盘
            bindfocus(e) {
                this.setData({
                    bottomHeight: e.detail.height //将键盘的高度设置为comment容器与page容器下边界之间的距离。
                });
            },

            // 输入内容
            bindinput(e) {
                this.setData({
                    content: e.detail.value
                });
            },

            // 失去焦点
            bindblur(e) {
                this.setData({
                    bottomHeight: 0
                });
            },

            //
            sendOut() {

                if (this.content == '') {
                    uni.showToast({
                        title: '请填写回复内容',
                        icon: 'none',
                        duration: 3000 //持续的时间
                    });
                    return;
                }
                let data = {
                    postId: this.replyPostId,
                    content: this.content
                };
                if (this.replyType == 2) {
                    data.parentCommentId = this.replyCommentId;
                }
                this.$axios.put(this.$api.saveComment, data).then((res) => {
                    if (res.data.code == 200) {
                        this.communityInfoFun(this.postId); //查询宝妈社区动态详情
                        this.listCommentFun(this.postId);
                        this.setData({
                            replyShow: !this.replyShow,
                            content: ''
                        });
                    }
                });
            },

            communityInfoFun(postId) {
                //查询宝妈社区动态详情

                let data = {
                    postId: postId
                };
                this.$axios.get(this.$api.communityInfo, data).then((res) => {
                    if (res.data.code == 200) {
                        this.setData({
                            communityInfo: res.data.data
                        });
                    }
                });
            },

            listCommentFun(postId) {
                //查询宝妈社区动态评论列表

                let data = {
                    postId: postId,
                    orderType: this.tabIndex == 0 ? 2 : 1
                };
                this.$axios.get(this.$api.listComment, data).then((res) => {
                    if (res.data.code == 200) {
                        let item = res.data.data;
                        item.forEach((e) => {
                            e.commentShow = 1;
                        });
                        this.setData({
                            listComment: item
                        });
                    }
                });
            },

            examine(e) {
                //查看剩余评论
                let index = e.currentTarget.dataset.index;
                this.setData({
                    ['listComment[' + index + '].commentShow']: 2
                });
            },

            communityBookmark(e) {
                //社区动态收藏
                let postId = e.currentTarget.dataset.postid;

                let data = {
                    postId: postId
                };
                this.$axios.put(this.$api.communityBookmark, data).then((res) => {
                    if (res.data.code == 200) {
                        this.communityInfoFun(this.postId); //查询宝妈社区动态详情
                    }
                });
            },

            reply(e) {
                //回复
                let postId = e.currentTarget.dataset.postid;
                let replyType = e.currentTarget.dataset.type;
                let commentId = e.currentTarget.dataset.commentid;
                this.setData({
                    replyShow: !this.replyShow,
                    replyPostId: postId,
                    replyCommentId: commentId,
                    replyType: replyType
                });
            },

            commentLike(e) {
                //宝妈社区动态评论点赞
                let commentId = e.currentTarget.dataset.commentid;

                let data = {
                    commentId: commentId
                };
                this.$axios.put(this.$api.commentLike, data).then((res) => {
                    if (res.data.code == 200) {
                        this.listCommentFun(this.postId);
                    }
                });
            },

            previewImage: function(e) {
                var current = e.currentTarget.dataset.src;
                var url = e.currentTarget.dataset.url;
                uni.previewImage({
                    current: current,
                    // 当前显示图片的http链接
                    urls: url // 需要预览的图片http链接列表
                });
            },

            preview(event) {
                let src = event.currentTarget.dataset.src;
                let maparr = [];
                maparr.push({
                    type: 'video',
                    url: src
                });
                let index = event.currentTarget.dataset.index;
                // 既有视频又有图片用这个
                console.log(maparr);
                uni.previewMedia({
                    sources: maparr,
                    // 官方文档 : 素材展示列表 是个数组对象 一个url链接 一个type 判断这是视频还是图片
                    // 接收image 和 video 所以处理数据时 最好给个type 指定 image 或者 video
                    current: index
                    // 当前显示的资源序号
                });
            }
        }
    };
</script>
<style scoped>
    @import './commentdetail.css';
</style>