<template>
  <view class="user-chat-container">
    <!-- 状态栏占位 -->
    <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>

    <!-- 顶部导航栏 -->
    <view class="chat-header">
      <view class="header-left" @click="goBack">
        <u-icon name="arrow-left" size="36" color="#333333"></u-icon>
      </view>
      <view class="header-title">
        <u-icon
          name="kefu-ermai"
          size="32"
          color="#007aff"
          :custom-style="{ marginRight: '12rpx' }"
        ></u-icon>
        <text class="title-text">客服聊天</text>
      </view>
      <view class="header-right">
        <u-icon name="more-dot-fill" size="36" color="#333333"></u-icon>
      </view>
    </view>

    <!-- 聊天消息区域 -->
    <scroll-view
      class="chat-messages"
      :style="{ height: chatMessagesHeight + 'px' }"
      scroll-y
      :scroll-top="scrollTop"
      :refresher-enabled="true"
      :refresher-triggered="chatState.refreshing"
      @refresherrefresh="handleRefresh"
      @scrolltoupper="handleLoadMore"
      @scroll="onScroll"
    >
      <!-- 加载更多提示（顶部） -->
      <view v-if="chatState.loadingMore" class="load-more-top">
        <text>正在加载更多历史消息...</text>
      </view>

      <!-- 没有更多消息提示 -->
      <view v-if="!chatState.hasMore && !isEmpty" class="no-more">
        <text>没有更多历史消息</text>
      </view>

      <!-- 空状态 -->
      <view v-if="isEmpty" class="empty-state">
        <text>暂无聊天记录</text>
        <text>开始你们的对话吧</text>
      </view>

      <template
        v-else
        v-for="(message, index) in chatState.messages"
        :key="message.id"
      >
        <!-- 时间显示 -->
        <view
          v-if="shouldShowTime(message, index, chatState.messages)"
          class="time-display-container"
        >
          <text class="time-display-text">{{
            formatTime(message.createdTime)
          }}</text>
        </view>

        <view
          :id="`msg-${index}`"
          class="message-item"
          :class="{
            'user-message': message.type === 'user',
            'service-message': message.type === 'service',
          }"
        >
          <!-- 用户消息 -->
          <view v-if="message.type === 'user'" class="user-msg">
            <view class="message-content">
              <!-- 文本消息气泡 -->
              <view
                v-if="message.messageType === 'TEXT'"
                class="message-bubble user-bubble"
              >
                <text class="message-text">{{ message.content }}</text>
              </view>
              <!-- 图片消息（无气泡） -->
              <view
                v-if="message.messageType === 'IMAGE'"
                class="image-message"
              >
                <image
                  :src="message.content"
                  mode="widthFix"
                  class="message-image"
                  @click="previewImage(message.content)"
                  @error="onImageError"
                />
              </view>
            </view>
          </view>

          <!-- 客服消息 -->
          <view v-if="message.type === 'service'" class="service-msg">
            <view class="message-content">
              <!-- 文本消息气泡 -->
              <view
                v-if="message.messageType === 'TEXT'"
                class="message-bubble service-bubble"
              >
                <text class="message-text">{{ message.content }}</text>
              </view>
              <!-- 图片消息（无气泡） -->
              <view
                v-if="message.messageType === 'IMAGE'"
                class="image-message"
              >
                <image
                  :src="message.content"
                  mode="widthFix"
                  class="message-image"
                  @click="previewImage(message.content)"
                  @error="onImageError"
                />
              </view>
            </view>
          </view>

          <!-- 系统消息 -->
          <view v-if="message.type === 'system'" class="system-msg">
            <view class="system-content">
              <text class="system-text">{{ message.content }}</text>
            </view>
          </view>
        </view>
      </template>

      <!-- 客服正在输入提示 -->
      <view v-if="isServiceTyping" class="typing-indicator">
        <view class="typing-content">
          <text class="typing-text">客服正在输入...</text>
          <view class="typing-dots">
            <view class="dot"></view>
            <view class="dot"></view>
            <view class="dot"></view>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 输入框组件 -->
    <ChatInput
      v-model="inputText"
      :focus="inputFocus"
      @send="handleSendMessage"
      @image-upload="handleImageUpload"
      @focus="handleInputFocus"
      @blur="handleInputBlur"
      ref="chatInputRef"
    />
  </view>
</template>

<script setup>
import { ref, reactive, nextTick } from 'vue';
import { onLoad, onShow, onUnload } from '@dcloudio/uni-app';
import dayjs from 'dayjs';

// 导入组件
import ChatInput from '../components/ChatInput.vue';

// 导入API函数
import { getOrCreateConversation } from '../api/customerService.js';
import { uploadFile } from '../api/request.js';
import chatWebSocketManager from '../utils/chatWebSocketManager.js';

// 导入 composables
import { useChatPagination } from '../composables/useChatPagination.js';

// 导入工具函数
import {
  formatTime,
  shouldShowTime,
  getMessageType,
} from '../utils/timeUtils.js';

// 使用聊天分页 composable
const {
  state: chatState,
  isEmpty,
  setConversationId,
  loadFirstPage,
  onRefresh,
  onLoadMore,
  addMessage,
} = useChatPagination(null, {
  pageSize: 20,
  initialLoad: false, // 等获取到会话ID后再加载
});

// 响应式数据
const inputText = ref('');
const inputFocus = ref(false);
const scrollTop = ref(0);
const isServiceTyping = ref(false);
const isFirstLoad = ref(true); // 标记是否是首次加载

// 安全区域相关
const statusBarHeight = ref(0);
const safeAreaBottom = ref(0);

// 页面高度相关
const windowHeight = ref(0);
const chatHeaderHeight = ref(88); // 导航栏高度 88rpx
const chatInputHeight = ref(0); // 输入框区域实际高度，动态获取
const chatMessagesHeight = ref(0);

// WebSocket管理器实例

// 用户信息
const userInfo = reactive({
  id: '',
  name: '',
  avatar: '',
  phone: '',
});

// 客服信息
const serviceInfo = reactive({
  id: '',
  name: '',
  avatar: '',
});

// 会话信息
const sessionInfo = reactive({
  conversationId: '',
  tenantId: '',
  status: 'active',
});

// 默认头像
const defaultUserAvatar = '/static/images/default-user-avatar.png';
const defaultServiceAvatar = '/static/images/default-service-avatar.png';

// 组件引用
const chatInputRef = ref(null);

// 页面加载
onLoad(async (options) => {
  console.log('用户聊天页面加载', options);

  // 获取系统信息设置安全区域
  initSafeArea();

  // 计算消息列表高度
  calculateChatMessagesHeight();

  // 初始化用户信息
  initUserInfo();

  // 初始化WebSocket连接
  await initWebSocket();

  // 处理传入的参数
  if (options.conversationId) {
    sessionInfo.conversationId = options.conversationId;
    // 设置会话ID到分页管理器
    setConversationId(options.conversationId);
    console.log('使用传入的 conversationId:', options.conversationId);
    await loadChatHistory();
  } else {
    // 没有传入会话ID，创建新会话
    await initConversation();
  }

  // 处理其他可选参数
  if (options.tenantId) {
    sessionInfo.tenantId = options.tenantId;
    console.log('使用传入的 tenantId:', options.tenantId);
  }

  // 标记首次加载完成
  isFirstLoad.value = false;
});

// 初始化安全区域
const initSafeArea = () => {
  const systemInfo = uni.getSystemInfoSync();
  statusBarHeight.value = systemInfo.statusBarHeight || 0;
  windowHeight.value = systemInfo.windowHeight || 0;

  // 获取安全区域信息
  if (systemInfo.safeAreaInsets) {
    safeAreaBottom.value = systemInfo.safeAreaInsets.bottom || 0;
  } else if (systemInfo.safeArea) {
    // 兼容旧版本
    safeAreaBottom.value =
      systemInfo.screenHeight - systemInfo.safeArea.bottom || 0;
  }

  // 获取输入框容器的真实高度
  getChatInputHeight();

  console.log('安全区域信息:', {
    statusBarHeight: statusBarHeight.value,
    safeAreaBottom: safeAreaBottom.value,
    windowHeight: windowHeight.value,
  });
};

// 获取输入框容器的真实高度
const getChatInputHeight = () => {
  nextTick(() => {
    const query = uni.createSelectorQuery().in(chatInputRef.value);
    query
      .select('.chat-input-container')
      .boundingClientRect((data) => {
        if (data) {
          chatInputHeight.value = data.height;
          console.log('输入框容器真实高度:', chatInputHeight.value);

          // 由于输入框使用 fixed 定位，重新计算消息区域高度
          calculateChatMessagesHeight();
        }
      })
      .exec();
  });
};

// 计算聊天消息区域高度
const calculateChatMessagesHeight = () => {
  // 计算 scrollview 距离顶部的高度：状态栏高度 + 导航栏高度
  const topDistance =
    statusBarHeight.value + uni.upx2px(chatHeaderHeight.value);

  // 由于 ChatInput 组件使用 fixed 定位，脱离文档流
  // 消息列表可以占满整个可用空间，只需要减去顶部距离
  // 底部会被 fixed 的输入框覆盖，但这是期望的行为

  // scrollview 高度 = 窗口高度 - 顶部距离
  chatMessagesHeight.value = windowHeight.value - topDistance;

  console.log('高度计算 (ChatInput fixed定位):', {
    windowHeight: windowHeight.value,
    statusBarHeight: statusBarHeight.value,
    topDistance,
    chatMessagesHeight: chatMessagesHeight.value,
    note: 'ChatInput使用fixed定位，消息列表占满可用空间',
  });
};

// 初始化用户信息
const initUserInfo = () => {
  // 从缓存或全局状态获取用户信息
  const user = uni.getStorageSync('userInfo');
  if (user) {
    Object.assign(userInfo, user);
  }
};

// 初始化会话
const initConversation = async () => {
  try {
    const res = await getOrCreateConversation();
    if (res.code === 200 && res.data) {
      console.log('会话信息:', res.data);
      sessionInfo.conversationId = res.data.id;
      sessionInfo.tenantId =
        res.data.tenantId || uni.getStorageSync('tenantId') || '000000';

      // 设置会话ID到分页管理器
      setConversationId(res.data.id);
      await loadChatHistory();
    }
  } catch (error) {
    console.error('初始化会话失败:', error);
    uni.showToast({
      title: '连接失败，请重试',
      icon: 'none',
    });
  }
};

// 加载聊天历史
const loadChatHistory = async () => {
  try {
    if (!sessionInfo.conversationId) {
      console.warn('没有会话ID，无法加载聊天历史');
      return;
    }
    console.log('开始加载聊天历史...', sessionInfo.conversationId);

    // 使用分页 composable 加载第一页消息
    const result = await loadFirstPage();

    if (result.success) {
      console.log('聊天历史加载成功:', chatState.messages.length, '条消息');
      console.log('消息列表:', chatState.messages);
      console.log('isEmpty状态:', isEmpty);
      scrollToBottom();
    } else {
      console.warn('获取聊天历史失败:', result.error);
    }
  } catch (error) {
    console.error('加载聊天历史失败:', error);
    uni.showToast({
      title: '加载聊天记录失败',
      icon: 'none',
    });
  }
};

// 下拉刷新处理
const handleRefresh = async () => {
  await onRefresh();
  // 刷新后滚动到底部
  nextTick(() => {
    scrollToBottom();
  });
};

// 滚动到顶部加载更多历史消息
const handleLoadMore = async () => {
  await onLoadMore();
};

// 处理发送消息（来自组件事件）
const handleSendMessage = async (content) => {
  if (!content) return;

  // 添加用户消息到列表
  const userMessage = {
    id: dayjs().valueOf().toString(),
    type: 'user',
    messageType: 'TEXT',
    content: content,
    createdTime: dayjs().valueOf(),
  };

  addMessage(userMessage);

  // 滚动到底部
  await nextTick();
  scrollToBottom();

  try {
    // 使用新的WebSocket管理器发送用户消息
    const success = chatWebSocketManager.sendMessage(
      content,
      sessionInfo.conversationId,
      'send_user_message',
    );
    if (!success) {
      throw new Error('WebSocket未连接');
    }
  } catch (error) {
    console.error('发送消息失败:', error);
    uni.showToast({
      title: '发送失败，请重试',
      icon: 'none',
    });
  }
};

// 处理输入框聚焦（来自组件事件）
const handleInputFocus = () => {
  setTimeout(() => {
    scrollToBottom();
  }, 300);
};

// 处理输入框失焦（来自组件事件）
const handleInputBlur = () => {
  // 可以在这里添加失焦时的逻辑
};

// 滚动到底部
const scrollToBottom = () => {
  nextTick(() => {
    // 获取聊天消息容器的滚动高度
    const query = uni.createSelectorQuery();
    query
      .select('.chat-messages')
      .scrollOffset((res) => {
        if (res) {
          // 设置 scrollTop 为最大滚动高度，实现滚动到底部
          scrollTop.value = res.scrollHeight;
        }
      })
      .exec();
  });
};

// 返回上一页
const goBack = () => {
  // 离开会话
  if (sessionInfo.conversationId) {
    chatWebSocketManager.close();
  }
  uni.navigateBack({
    delta: 1,
    fail: () => {
      uni.switchTab({
        url: '/pageA/home',
      });
    },
  });
};

// 处理图片上传（来自ChatInput组件）
const handleImageUpload = async (filePath) => {
  console.log('开始处理图片上传:', filePath);

  // 显示上传进度
  uni.showLoading({
    title: '上传中...',
    mask: true,
  });

  try {
    // 上传图片
    const imageUrl = await uploadFile({
      filePath: filePath,
    });

    console.log('图片上传成功:', imageUrl);

    // 发送图片消息
    await sendImageMessage(imageUrl);

    uni.hideLoading();
    uni.showToast({
      title: '发送成功',
      icon: 'success',
      duration: 1500,
    });
  } catch (error) {
    console.error('图片上传失败:', error);
    uni.hideLoading();
    uni.showToast({
      title: '上传失败，请重试',
      icon: 'none',
      duration: 2000,
    });
  }
};

// 发送图片消息
const sendImageMessage = async (imageUrl) => {
  if (!imageUrl) return;

  const message = {
    id: dayjs().valueOf(),
    type: 'user',
    messageType: 'IMAGE',
    content: imageUrl,
    createdTime: dayjs().toISOString(),
    status: 'sending',
  };

  // 添加到消息列表
  addMessage(message);

  // 滚动到底部
  nextTick(() => {
    scrollToBottom();
  });

  try {
    // 通过WebSocket发送图片消息
    if (chatWebSocketManager.isConnected()) {
      const success = chatWebSocketManager.sendImageMessage(
        imageUrl,
        sessionInfo.conversationId,
        false, // 用户发送的消息
      );

      if (success) {
        // 更新消息状态为已发送
        message.status = 'sent';
      } else {
        throw new Error('发送图片消息失败');
      }
    } else {
      throw new Error('WebSocket连接已断开');
    }
  } catch (error) {
    console.error('发送图片消息失败:', error);
    // 更新消息状态为失败
    message.status = 'failed';
    uni.showToast({
      title: '发送失败',
      icon: 'none',
      duration: 2000,
    });
  }
};

// 图片预览
const previewImage = (imageUrl) => {
  uni.previewImage({
    urls: [imageUrl],
    current: imageUrl,
  });
};

// 图片加载错误处理
const onImageError = (e) => {
  console.error('图片加载失败:', e);
  uni.showToast({
    title: '图片加载失败',
    icon: 'none',
    duration: 1500,
  });
};

// 滚动事件
const onScroll = (e) => {
  // scrollTop.value = e.detail.scrollTop;
};

// 删除重复的 onLoad 函数

onUnload(() => {
  // 关闭WebSocket连接
  chatWebSocketManager.close();
});

// 初始化WebSocket连接
const initWebSocket = async () => {
  try {
    // 初始化WebSocket管理器
    const success = await chatWebSocketManager.init({
      userType: 'USER',
    });

    if (!success) {
      console.error('WebSocket初始化失败');
      return;
    }

    // 注册事件监听器
    setupWebSocketEventListeners();

    console.log('WebSocket初始化成功');
  } catch (error) {
    console.error('WebSocket初始化异常:', error);
  }
};

// 设置WebSocket事件监听器
const setupWebSocketEventListeners = () => {
  // 先清理所有现有的监听器，避免重复监听
  chatWebSocketManager.messageHandlers.clear();

  // 连接成功
  chatWebSocketManager.on('connected', () => {
    console.log('WebSocket连接成功');
  });

  // 绑定成功
  chatWebSocketManager.on('bindSuccess', () => {
    console.log('WebSocket绑定成功');
    uni.showToast({
      title: '连接成功',
      icon: 'success',
      duration: 1000,
    });
  });

  // 收到聊天消息
  chatWebSocketManager.on('chatMessage', (message) => {
    console.log('收到聊天消息:', message);

    // 使用统一的消息类型判断函数
    const userType = getMessageType(message, 'service'); // 默认为客服消息

    const formattedMessage = {
      id: message.id || message.messageId || dayjs().valueOf().toString(),
      type: userType, // 消息发送者类型：user/service/system
      userType: userType, // 用户类型字段，与type保持一致
      content: message.content,
      createdTime: message.createdTime || message.timestamp || dayjs().valueOf(),
      messageType: (
        message.format ||
        message.messageType ||
        'TEXT'
      ).toUpperCase(), // 消息内容类型：TEXT/IMAGE
      sender: message.sender,
      receiver: message.receiver,
      action: message.action,
    };

    addMessage(formattedMessage);

    // 滚动到底部
    nextTick(() => {
      scrollToBottom();
    });
  });

  // 连接断开
  chatWebSocketManager.on('disconnected', () => {
    console.log('WebSocket连接断开');
    uni.showToast({
      title: '连接断开',
      icon: 'none',
      duration: 1000,
    });
  });

  // 连接错误
  chatWebSocketManager.on('error', (error) => {
    console.error('WebSocket连接错误:', error);
    uni.showToast({
      title: '连接错误',
      icon: 'none',
    });
  });
};
</script>

<style lang="scss" scoped>
.user-chat-container {
  height: 100vh;
  background: #f5f5f5;
}

.status-bar {
  background: #ffffff;
  width: 100%;
}

.chat-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 32rpx;
  background: #ffffff;
  border-bottom: 1rpx solid #e5e5e5;

  .header-left,
  .header-right {
    width: 80rpx;
    height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 12rpx;
    transition: background-color 0.2s ease;

    &:active {
      background-color: #f0f0f0;
    }
  }

  .header-title {
    display: flex;
    align-items: center;
    justify-content: center;

    .title-text {
      font-size: 34rpx;
      font-weight: 600;
      color: #333333;
    }
  }
}

.chat-messages {
  padding: 20rpx;
  padding-bottom: 200rpx; // 增加底部padding，避免被fixed输入框遮挡
  overflow: hidden;
  box-sizing: border-box;
  background: transparent;
}

.message-item {
  margin-bottom: 24rpx;

  &.user-message {
    display: flex;
    justify-content: flex-end;
  }

  &.service-message {
    display: flex;
    justify-content: flex-start;
  }
}

.user-msg,
.service-msg {
  display: flex;
  align-items: flex-end;
  max-width: 80%;
  gap: 16rpx;
}

.user-msg {
  .message-content {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
  }
}

.service-msg {
  .message-content {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
  }
}

.avatar {
  width: 72rpx;
  height: 72rpx;
  border-radius: 50%;
  flex-shrink: 0;
}

.message-bubble {
  padding: 20rpx 24rpx;
  border-radius: 20rpx;
  margin-bottom: 8rpx;
  max-width: 100%;
  word-wrap: break-word;
  position: relative;

  &.user-bubble {
    background: #007aff;
    color: #ffffff;
    border-radius: 20rpx 20rpx 8rpx 20rpx;
  }

  &.service-bubble {
    background: #ffffff;
    color: #333333;
    border: 1rpx solid #e5e5e5;
    border-radius: 20rpx 20rpx 20rpx 8rpx;
  }
}

.message-text {
  font-size: 32rpx;
  line-height: 1.4;
  font-weight: 400;
}

.image-message {
  .message-image {
    width: auto;
    max-width: 400rpx;
    max-height: 400rpx;
    min-width: 200rpx;
    min-height: 200rpx;
    border-radius: 12rpx;
    background-color: #f5f5f5;
  }
}

.time-display-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10rpx 0;
}

.time-display-text {
  font-size: 24rpx;
  color: #999999;
}

.system-msg {
  display: flex;
  justify-content: center;
  margin-bottom: 24rpx;

  .system-content {
    background: rgba(0, 0, 0, 0.05);
    padding: 12rpx 24rpx;
    border-radius: 16rpx;

    .system-text {
      font-size: 24rpx;
      color: #666666;
    }
  }
}

.load-more-top,
.no-more,
.loading-more {
  text-align: center;
  padding: 20rpx;

  text {
    font-size: 24rpx;
    color: #999999;
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400rpx;

  text {
    font-size: 28rpx;
    color: #999999;
    margin-bottom: 16rpx;
  }
}

.typing-indicator {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 24rpx;

  .typing-content {
    display: flex;
    align-items: center;
    background: #ffffff;
    padding: 16rpx 20rpx;
    border-radius: 20rpx;
    border: 1rpx solid #e5e5e5;

    .typing-text {
      font-size: 24rpx;
      color: #666666;
      margin-right: 16rpx;
    }

    .typing-dots {
      display: flex;
      align-items: center;

      .dot {
        width: 8rpx;
        height: 8rpx;
        background: #007aff;
        border-radius: 50%;
        margin-right: 6rpx;
        animation: typing 1.4s infinite ease-in-out;

        &:nth-child(1) {
          animation-delay: -0.32s;
        }

        &:nth-child(2) {
          animation-delay: -0.16s;
        }

        &:last-child {
          margin-right: 0;
        }
      }
    }
  }
}

@keyframes typing {
  0%,
  80%,
  100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}
</style>
