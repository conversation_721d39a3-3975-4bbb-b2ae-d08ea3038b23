import { ref } from 'vue';

/**
 * 海报分享功能 Composable
 * 支持微信分享菜单、保存到相册等功能
 */
export function usePosterShare() {
  const isSharing = ref(false);
  const shareError = ref(null);

  /**
   * 显示微信分享图片菜单
   * @param {string} filePath - 图片文件路径
   * @param {Object} options - 分享选项
   * @returns {Promise<void>}
   */
  const showShareImageMenu = async (filePath, options = {}) => {
    if (!filePath) {
      throw new Error('图片路径不能为空');
    }

    const defaultOptions = {
      withShareTicket: true,
      navigateBack: true,
      ...options,
    };

    isSharing.value = true;
    shareError.value = null;

    try {
      console.log('调用分享菜单，文件路径:', filePath);

      // #ifdef MP-WEIXIN
      return new Promise((resolve, reject) => {
        wx.showShareImageMenu({
          withShareTicket: defaultOptions.withShareTicket,
          path: filePath,
          success: async (res) => {
            console.log('分享成功:', res);
            if (defaultOptions.navigateBack) {
              uni.navigateBack();
            }
            resolve(res);
          },
          fail: (err) => {
            console.error('分享取消:', err);
            reject(new Error('分享取消'));
          },
        });
      });
      // #endif

      // #ifndef MP-WEIXIN
      // 非微信小程序环境，保存到相册
      return await saveToAlbum(filePath, {
        navigateBack: defaultOptions.navigateBack,
      });
      // #endif
    } catch (error) {
      console.error('分享过程出错:', error);
      shareError.value = error.message;
      throw error;
    } finally {
      isSharing.value = false;
    }
  };

  /**
   * 保存图片到相册
   * @param {string} filePath - 图片文件路径
   * @param {Object} options - 保存选项
   * @returns {Promise<void>}
   */
  const saveToAlbum = async (filePath, options = {}) => {
    if (!filePath) {
      throw new Error('图片路径不能为空');
    }

    const defaultOptions = {
      navigateBack: true,
      showToast: true,
      ...options,
    };

    isSharing.value = true;
    shareError.value = null;

    try {
      return new Promise((resolve, reject) => {
        uni.saveImageToPhotosAlbum({
          filePath: filePath,
          success: () => {
            if (defaultOptions.showToast) {
              uni.showToast({
                title: '保存成功',
                icon: 'success',
              });
            }
            if (defaultOptions.navigateBack) {
              uni.navigateBack();
            }
            resolve();
          },
          fail: (error) => {
            console.error('保存到相册失败:', error);

            if (error.errMsg && error.errMsg.includes('auth deny')) {
              uni.showModal({
                title: '提示',
                content: '需要您授权保存图片到相册',
                showCancel: false,
                confirmText: '确定',
              });
              reject(new Error('需要授权保存图片到相册'));
            } else {
              reject(new Error('保存失败'));
            }
          },
        });
      });
    } catch (error) {
      console.error('保存过程出错:', error);
      shareError.value = error.message;
      throw error;
    } finally {
      isSharing.value = false;
    }
  };

  /**
   * 分享到朋友圈（微信小程序）
   * @param {string} filePath - 图片文件路径
   * @param {Object} options - 分享选项
   * @returns {Promise<void>}
   */
  const shareToMoments = async (filePath, options = {}) => {
    if (!filePath) {
      throw new Error('图片路径不能为空');
    }

    const defaultOptions = {
      navigateBack: true,
      ...options,
    };

    isSharing.value = true;
    shareError.value = null;

    try {
      // #ifdef MP-WEIXIN
      // 微信小程序分享到朋友圈需要先保存图片，然后提示用户手动分享
      return new Promise((resolve, reject) => {
        wx.saveImageToPhotosAlbum({
          filePath: filePath,
          success: () => {
            if (defaultOptions.navigateBack) {
              uni.navigateBack();
            }
            uni.showModal({
              title: '提示',
              content: '图片已保存到相册，请手动分享到朋友圈',
              showCancel: false,
              confirmText: '确定',
            });
            resolve();
          },
          fail: (error) => {
            console.error('保存到相册失败:', error);
            if (error.errMsg && error.errMsg.includes('auth deny')) {
              uni.showModal({
                title: '提示',
                content: '需要您授权保存图片到相册',
                showCancel: false,
                confirmText: '确定',
              });
              reject(new Error('需要授权保存图片到相册'));
            } else {
              reject(new Error('保存失败'));
            }
          },
        });
      });
      // #endif

      // #ifndef MP-WEIXIN
      uni.showToast({
        title: '该功能仅支持微信小程序',
        icon: 'none',
      });
      throw new Error('该功能仅支持微信小程序');
      // #endif
    } catch (error) {
      console.error('分享到朋友圈过程出错:', error);
      shareError.value = error.message;
      throw error;
    } finally {
      isSharing.value = false;
    }
  };

  /**
   * 通用分享方法
   * @param {string} filePath - 图片文件路径
   * @param {string} type - 分享类型: 'menu' | 'album' | 'moments'
   * @param {Object} options - 分享选项
   * @returns {Promise<void>}
   */
  const share = async (filePath, type = 'menu', options = {}) => {
    try {
      switch (type) {
        case 'menu':
          return await showShareImageMenu(filePath, options);
        case 'album':
          return await saveToAlbum(filePath, options);
        case 'moments':
          return await shareToMoments(filePath, options);
        default:
          throw new Error(`不支持的分享类型: ${type}`);
      }
    } catch (error) {
      // 显示错误提示
      if (
        error.message !== '需要授权保存图片到相册' &&
        error.message !== '该功能仅支持微信小程序'
      ) {
        uni.showToast({
          title: error.message,
          icon: 'none',
        });
      }
      throw error;
    }
  };

  return {
    // 状态
    isSharing,
    shareError,

    // 方法
    share,
    showShareImageMenu,
    saveToAlbum,
    shareToMoments,
  };
}
