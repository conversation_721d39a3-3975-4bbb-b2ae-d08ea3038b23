<template>
    <view class="page-motherhome">
        <navigation :background="bgColor" title="宝妈主页" :isSowArrow="true"></navigation>
        <view class="top">
            <view class="top-img">
                <image :src="motherInfo.avatar || $defaultAvatar"></image>
            </view>
            <view class="top-content">
                <view class="top-flex">
                    <view class="top-content-name">{{ motherInfo.nickName }}</view>
                    <view class="top-content-mother">
                        <image src="http://cdn.xiaodingdang1.com/2024/09/09/164e1a0c11f44833add17d077b6545cd.png">
                        </image>
                    </view>
                </view>
                <view class="top-flex">
                    <view class="top-content-label">服务人员:</view>
                    <scroll-view :scroll-x="true" class="top-content-tag">
                        <view @tap="employeeDetail(item)" class="top-content-tag-enum"
                            v-for="(item, index) in motherInfo.employeeInfoList" :key="index">
                            {{ item.name }}
                        </view>
                    </scroll-view>
                </view>
                <view class="top-flex">
                    <view class="top-content-label">宝妈入住:</view>
                    <view class="top-content-date">{{ showTime }}</view>
                </view>
            </view>
        </view>
        <view class="tag">
            <view class="tag-main" v-if="motherInfo.tagInfoList && motherInfo.tagInfoList.length > 0">
                <view class="tag-content">
                    <view class="tag-content-title">标签：</view>
                    <view
                        :class="['tag-content-enum', motherInfo.tagInfoList && tagListSelected.length == motherInfo.tagInfoList.length? 'tag-content-active': '']"
                        @tap='selectAll'>全部</view>
                    <view v-for="(item, index) in motherInfo.tagInfoList" :key="index" @tap="onSelected(item)"
                        :class="['tag-content-enum', item.id && tagListSelected.includes(item.id)?'tag-content-active': '']">
                        {{ item.name }}
                    </view>
                </view>
                <scroll-view :scroll-x="true" class="tag-week">
                    <view v-for="(item, index) in weekList" :key="index"
                        :class="['tag-week-enum', selectedWeek == item.weekId? 'tag-week-active': '']"
                        @tap="onWeekSelected(item)">
                        {{ item.weekName }}
                    </view>
                </scroll-view>
            </view>

        </view>
        <view class="content" v-if="showList.length > 0">
            <view v-for="(i, index) in showList" :key="index">
                <template v-if="pageList[i] && pageList[i].length > 0">
                    <view class="title">
                        <image :src="weekTitleImg[i]"></image>
                    </view>
                    <waterfall-card-vue class="waterfall-card" :ref="el => getRef(el, index)" :list="pageList[i]"
                        @detail="detail">
                    </waterfall-card-vue>
                </template>
            </view>
        </view>
        <u-back-top :scroll-top="scrollTop" top="800"></u-back-top>
        <new-request-loading></new-request-loading>
        <empty-vue v-if="isEmpty"></empty-vue>
    </view>
</template>
<script setup>
    import emptyVue from '@/components/empty.vue';
    import waterfallCardVue from "@/components/waterfallCard.vue"
    import {
        onShow,
        onHide,
        onLoad,
        onPageScroll,
        onReachBottom,
		onShareAppMessage,
		onShareTimeline
    } from "@dcloudio/uni-app";
    import {
        ref,
        nextTick,
        reactive,
        getCurrentInstance,
        onMounted
    } from "vue";
    import {
        useStore
    } from "vuex";
    const store = useStore();
    const instance = getCurrentInstance();
    const App = instance.appContext.config.globalProperties
    onShareAppMessage(() => {
    })
    onShareTimeline(() => {

    })
    const pageList = ref([])
    const postId = ref('')
    const waterfallRefList = ref([])
    const types = ref(1)
    const bgColor = ref('')
    const scrollTop = ref(0)
    const userId = ref('')
    const motherInfo = reactive({})
    const taskNodeIdList = ref([])
    const tagListSelected = ref([])
    const orderByColumn = ref('is_featured,fp.likes_count')
    const selectedWeek = ref('')
    const week = [{
        weekName: '第一周',
        weekId: 1
    }, {
        weekName: '第二周',
        weekId: 2
    }, {
        weekName: '第三周',
        weekId: 3
    }, {
        weekName: '第四周',
        weekId: 4
    }, {
        weekName: '第五周',
        weekId: 5
    }, {
        weekName: '第六周',
        weekId: 6
    }]
    const weekList = ref([])
    const weekTitleImg = [
        'http://cdn.xiaodingdang1.com/2024/12/21/64acdf702c0e429abe82bd59a964b117.png',
        'http://cdn.xiaodingdang1.com/2024/12/21/0251fe09fc514486bd96af1e2bc2cb38.png',
        'http://cdn.xiaodingdang1.com/2024/12/21/204ec6bc95fc4af2bd0db95cca05c3c6.png',
        'http://cdn.xiaodingdang1.com/2024/12/21/d7a0608a9b7a45b1947387f8245543d0.png',
        'http://cdn.xiaodingdang1.com/2024/12/21/3ddfb442307e4ed7a44b81de646a15f5.png',
        'http://cdn.xiaodingdang1.com/2024/12/21/5250bdfe649149ddb64bd9cc1bbadb25.png',
    ]
    const showList = ref([])
    const showListCopy = ref([])
    const isEmpty = ref(false)
    let showTime = null
    onPageScroll((e) => {
        if (e.scrollTop > 0) {
            bgColor.value = '#ffffff'
        } else if (e.scrollTop < 2) {
            bgColor.value = ''
        }
        scrollTop.value = e.scrollTop
    })
    const getRef = ((el, index) => {
        if (el) {
            waterfallRefList.value[index] = el
        }
    })
    const selectAll = () => {
        if (tagListSelected.value.length == motherInfo.tagInfoList.length) {
            tagListSelected.value = []
        } else {
            tagListSelected.value = []
            motherInfo.tagInfoList.forEach((item) => {
                tagListSelected.value.push(item.id)
            })
        }
        selectedWeek.value = ''
        getMatherList()
    }
    const onWeekSelected = (item) => {
        console.log('onWeekSelectedonWeekSelected', item)
        if (selectedWeek.value == item.weekId) {
            selectedWeek.value = ''
            showList.value = showListCopy.value
        } else {
            selectedWeek.value = item.weekId
            showList.value = []
            showList.value[item.weekId - 1] = item.weekId - 1
        }
        showLazyImg()
    }
    const onSelected = (item) => {
        if (item.id) {
            if (tagListSelected.value.includes(item.id)) {
                let index = tagListSelected.value.indexOf(item.id)
                tagListSelected.value.splice(index, 1)
            } else {
                tagListSelected.value.push(item.id)
            }
            selectedWeek.value = ''
            getMatherList()
        }
    }
    onLoad((options) => {
        postId.value = options.postId
        userId.value = options.userId
    })
    onMounted(async () => {
        await getMatherInfo()
        // await getMatherList()
    })
    const getMatherInfo = async () => {
        const res = await App.$axios.get(App.$api.getMotherInfo, {
            userId: userId.value
        })
        if (res.data.code == 200) {
            const data = res.data.data
            console.log('dataaaaaaa', data)
            if (data && data.checkInTime) {
                showTime = data.checkInTime.slice(0, 10)
            }
            Object.assign(motherInfo, data)
            await getMatherList()
        }
    }
    const getMatherList = async () => {
        isEmpty.value = false
        const params = {
            userId: userId.value
        }
        if (motherInfo.value) {
            params.checkInTime = motherInfo.value.checkInTime
        }
        if (selectedWeek.value) {
            params.weekNum = selectedWeek.value
        }
        if (tagListSelected.value.length > 0) {
            params.taskNodeIdList = tagListSelected.value.join(', ')
        }
        // 对应的周数据
        const weekMap = new Map([
            ['第1周', 0],
            ['第2周', 1],
            ['第3周', 2],
            ['第4周', 3],
            ['第5周', 4],
            ['第6周', 5]
        ])
        store.commit("m_user/changeLoading", true);
        const res = await App.$axios.get(App.$api.getMotherList, params)
        if (res.data.code == 200) {
            try {
                const data = res.data?.data
                weekList.value = []
                showList.value = []
                pageList.value = []
                showListCopy.value = []
                data.forEach((item) => {
                    const index = weekMap.get(item.week)
                    weekList.value.push(week[index])
                    showList.value.push(index)
                    showListCopy.value.push(index)
                    pageList.value[index] = item.posts
                })
                if (data.length == 0) {
                    isEmpty.value = true
                }
            } catch (e) {
                isEmpty.value = true
            }
            showLazyImg()
        } else {
            isEmpty.value = true
        }
        store.commit("m_user/changeLoading", false);
    }
    const showLazyImg = () => {
        setTimeout(() => {
            nextTick(() => {
                waterfallRefList.value.forEach((item) => {
                    item.visualizeLoadingImages()
                })
            })
        }, 800)
    }
    const detail = (item) => {
        let params = {
            postId: item.postId,
            userId: item.userId,
        };
        let url;
        if (item.type == "USER") {
            url = "/pageA/pageB/community/sending";
        } else {
            url = "/pageA/pageB/community/staffdetail";
        }
        App.$jumpPage(url, params);
    }
    const employeeDetail = (item) => {
        // App.$jumpPage('/pageA/pageB/home/<USER>/detail', {
        //     staffId: item.userId
        // });
    }
</script>
<style scoped lang='less'>
    @import './motherhome.less';
</style>