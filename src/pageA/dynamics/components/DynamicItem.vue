<template>
  <view class="dynamic-item">
    <!-- 用户信息头部 -->
    <view class="dynamic-item__header">
      <view class="dynamic-item__user-info">
        <image
          :src="dynamicData.avatar || $defaultAvatar"
          class="dynamic-item__avatar"
          mode="aspectFill"
        />
        <view class="dynamic-item__user-details">
          <view class="dynamic-item__username">{{ dynamicData.nickname || $defaultName }}</view>
          <view class="dynamic-item__publish-time">{{ dynamicData.createTime }}</view>
        </view>
      </view>
    </view>

    <!-- 动态内容区域 -->
    <ContentArea
      :list-data="dynamicData"
      :item-index="index"
    />

    <!-- 底部操作区域 -->
    <view class="dynamic-item__footer">
      <view class="dynamic-item__relative-time">
        {{ dynamicData.hisTimeStr }}
      </view>
      <view
        class="dynamic-item__actions-trigger"
        @tap="handleActionsToggle"
        :data-index="index"
        :data-postid="dynamicData.postId"
      >
        <image
          :src="ICONS.MORE_ACTIONS"
          class="dynamic-item__actions-icon"
        />
      </view>

      <!-- 快捷操作菜单 -->
      <view class="dynamic-item__quick-actions" v-if="dynamicData.show">
        <!-- 点赞操作 -->
        <view class="dynamic-item__action-item" @tap="() => handleLikeAction(dynamicData.postId, index)">
          <image
            :src="dynamicData.isLike ? likeActiveIcon : likeIcon"
            class="dynamic-item__action-icon"
          />
          <view class="dynamic-item__action-label">赞</view>
        </view>

        <!-- 海报功能 -->
        <template v-if="showPoster">
          <view class="dynamic-item__action-divider"></view>
          <view
            class="dynamic-item__action-item"
            @tap="() => handlePosterAction(dynamicData.postId, index)"
          >
            <image
              :src="ICONS.POSTER"
              class="dynamic-item__action-icon"
            />
            <view class="dynamic-item__action-label">海报</view>
          </view>
        </template>

        <!-- 消息推送功能 -->
        <template v-if="showPushMessage">
          <view class="dynamic-item__action-divider"></view>
          <view
            class="dynamic-item__action-item"
            @tap="() => handlePushMessageAction(dynamicData.postId, index)"
          >
            <image
              :src="ICONS.PUSH_MESSAGE"
              class="dynamic-item__action-icon"
            />
            <view class="dynamic-item__action-label">消息推送</view>
          </view>
        </template>
      </view>
    </view>

    <!-- 点赞用户列表 -->
    <LikeUsersList
      v-if="likeUsers && likeUsers.length > 0"
      :like-users="likeUsers"
      :item-index="index"
    />

    <!-- 分割线 -->
    <view class="dynamic-item__separator"></view>
  </view>
</template>

<script setup>
import { PAGE_ROUTES, SUBSCRIBE_TEMPLATE_IDS } from '../constants.js'
import { formatUrlWithParams } from '../utils.js'
import { ICONS } from '../assets.js'
import ContentArea from './ContentArea.vue'
import LikeUsersList from './LikeUsersList.vue'

// Props 定义
const props = defineProps({
  // 动态数据
  dynamicData: {
    type: Object,
    required: true,
    validator: (value) => {
      return value && (typeof value.postId === 'string' || typeof value.postId === 'number')
    }
  },
  // 项目索引
  index: {
    type: Number,
    required: true,
    validator: (value) => value >= 0
  },
  // 点赞用户列表
  likeUsers: {
    type: Array,
    default: () => []
  },
  // 是否显示海报功能
  showPoster: {
    type: Boolean,
    default: false
  },
  // 是否显示消息推送功能
  showPushMessage: {
    type: Boolean,
    default: false
  }
})

// 事件定义
const emit = defineEmits([
  'like',
  'more-actions',
  'update-dynamic'
])

// 图标常量
const likeIcon = ICONS.LIKE_DEFAULT
const likeActiveIcon = ICONS.LIKE_ACTIVE

// 事件处理函数

/**
 * 处理操作菜单切换
 * @param {Event} event - 点击事件
 */
const handleActionsToggle = (event) => {
  emit('more-actions', event)
}

/**
 * 处理点赞操作
 * @param {string|number} postId - 动态ID
 * @param {number} index - 项目索引
 */
const handleLikeAction = (postId, index) => {
  emit('like', postId, index)
  // 关闭操作菜单
  emit('update-dynamic', index, 'show', false)
}

/**
 * 处理海报生成
 * @param {string|number} postId - 动态ID
 * @param {number} index - 项目索引
 */
const handlePosterAction = (postId, index) => {
  const url = formatUrlWithParams(PAGE_ROUTES.POSTER, {
    type: 'dynamics',
    postId
  })
  uni.navigateTo({ url })
  // 关闭操作菜单
  emit('update-dynamic', index, 'show', false)
}

/**
 * 处理消息推送
 * @param {string|number} postId - 动态ID
 * @param {number} index - 项目索引
 */
const handlePushMessageAction = (postId, index) => {
  // 请求订阅消息权限
  uni.requestSubscribeMessage({
    tmplIds: SUBSCRIBE_TEMPLATE_IDS,
    success() {
      console.log('订阅消息授权成功')
      // 订阅成功后跳转
      const url = formatUrlWithParams(PAGE_ROUTES.PUSH_MESSAGE, {
        receivingType: 0,
        postId
      })
      uni.navigateTo({ url })
    },
    fail(error) {
      console.warn('订阅消息授权失败:', error)
      // 订阅失败也允许跳转
      const url = formatUrlWithParams(PAGE_ROUTES.PUSH_MESSAGE, {
        receivingType: 0,
        postId
      })
      uni.navigateTo({ url })
    },
  })

  // 关闭操作菜单
  emit('update-dynamic', index, 'show', false)
}


</script>

<style scoped lang="less">
.dynamic-item {
  margin: 24rpx;

  &__header {
    margin-bottom: 8rpx;
  }

  &__user-info {
    font-size: 30rpx;
    color: #5b6799;
    font-weight: bold;
    display: flex;
    align-items: center;
  }

  &__avatar {
    margin-right: 10rpx;
    width: 85rpx;
    height: 85rpx;
    border-radius: 2rpx;
  }

  &__user-details {
    flex: 1;
    margin-top: 8rpx;
  }

  &__username {
    font-weight: 500;
    font-size: 30rpx;
    color: #5b6799;
  }

  &__publish-time {
    font-weight: 400;
    font-size: 24rpx;
    color: #777777;
  }

  &__footer {
    margin-bottom: 6rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
  }

  &__relative-time {
    font-size: 24rpx;
    color: #777777;
    font-weight: 400;
  }

  &__actions-trigger {
    padding: 8rpx;
    cursor: pointer;
  }

  &__actions-icon {
    width: 42rpx;
    height: 22rpx;
    display: block;
  }

  &__separator {
    width: 100%;
    height: 1rpx;
    background: #ebebeb;
    margin: 24rpx 0;
  }

  &__quick-actions {
    display: flex;
    background: #4c4c4c;
    height: 72rpx;
    align-items: center;
    color: #ffffff;
    font-size: 26rpx;
    border-radius: 10rpx;
    position: absolute;
    top: -18rpx;
    right: 50rpx;
    z-index: 1000;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  }

  &__action-item {
    display: flex;
    align-items: center;
    padding: 0 20rpx;
    justify-content: center;
    cursor: pointer;
    transition: opacity 0.2s ease;

    &:active {
      opacity: 0.7;
    }
  }

  &__action-icon {
    width: 26rpx;
    height: 26rpx;
    flex-shrink: 0;
  }

  &__action-label {
    margin-left: 6rpx;
    font-size: 24rpx;
    white-space: nowrap;
  }

  &__action-divider {
    width: 1rpx;
    height: 40rpx;
    background: rgba(255, 255, 255, 0.3);
    flex-shrink: 0;
  }
}
</style>
