<template>
  <view class="mom-says-list-page">
    <!-- 页面内容 -->
    <view class="page-content">
      <!-- AI助理消息 -->
      <view class="ai-message-container">
        <view class="ai-message-card">
          <view class="ai-avatar-section">
            <view class="ai-avatar-container">
              <image src="/static/images/chat/hi.png" mode="scaleToFill" />
            </view>
            <view class="ai-greeting">您好，我是你的智能AI助理</view>
          </view>
          <view class="ai-message-content">
            <view class="ai-result-text"
              >根据您的需求，我们已完成宝妈说检索，共收集{{
                momSaysList.length
              }}个宝妈说内容。以下为整理结果。</view
            >
          </view>
        </view>
      </view>

      <!-- 宝妈说列表 -->
      <view class="mom-says-list">
        <view
          v-for="(momItem, index) in momSaysList"
          :key="momItem.postId || index"
          class="mom-item"
          @click="goToMomDetail(momItem)"
        >
          <!-- 宝妈信息头部 -->
          <view class="mom-header">
            <view class="mom-avatar-container">
              <image
                :src="momItem.avatar || '/static/images/default-avatar.svg'"
                class="mom-avatar"
                mode="aspectFill"
                @error="handleAvatarError"
              />
            </view>
            <view class="mom-info">
              <view class="mom-name-section">
                <text class="mom-name">{{ momItem.nickname || '宝妈' }}</text>
                <view class="mom-tag">
                  <image
                    src="/static/images/chat/feeder.png"
                    class="tag-icon"
                  />
                  <text class="tag-text">宝妈</text>
                </view>
              </view>
              <view class="mom-details"
                >年龄：{{ momItem.age || '26' }} 宝宝：{{
                  momItem.babyWeight || '6斤6两'
                }}</view
              >
            </view>
            <view class="status-tag">
              <text class="status-text"
                >入住第{{ momItem.checkInDays || '19' }}天</text
              >
              <image src="/static/images/chat/rainbow.png" class="status-bg" />
            </view>
          </view>

          <!-- 评价图片 -->
          <view
            v-if="momItem.contentPhotos && momItem.contentPhotos.length > 0"
            class="mom-images"
          >
            <swiper
              class="images-swiper"
              :indicator-dots="true"
              :indicator-color="'rgba(255, 255, 255, 0.5)'"
              :indicator-active-color="'#454545'"
              :autoplay="false"
              :circular="false"
              :duration="300"
            >
              <swiper-item
                v-for="(img, imgIndex) in momItem.contentPhotos"
                :key="imgIndex"
                class="swiper-item"
              >
                <image
                  :src="img"
                  class="mom-image"
                  mode="aspectFill"
                  @error="handleImageError"
                  @click.stop="previewImage(img, momItem.contentPhotos)"
                />
              </swiper-item>
            </swiper>
          </view>

          <!-- 评价内容 -->
          <view class="mom-content">
            <text class="mom-text">{{
              momItem.content ||
              '小区好多宝妈都在这个月子中心住过，最后选了这家看到宋阿姨第一眼就觉得很舒服，很踏实的感觉，遇到个满意的月嫂真的不容易!照顾我以后，阿姨怕我休息不好，白天让我把奶吸出来，晚上她起来喂宝宝，宋阿姨细心又唠叨，会自觉以宝宝为主，懂得根据我的身体情况来照顾我，一个月以来宋阿姨都是尽心尽力，很辛苦!也真的很实在，很专业。如果有二胎我还会来。'
            }}</text>
          </view>
        </view>
      </view>

      <!-- 加载状态 -->
      <view v-if="isLoading" class="loading-container">
        <view class="loading-text">加载中...</view>
      </view>

      <!-- 空状态 -->
      <view
        v-if="!isLoading && momSaysList.length === 0"
        class="empty-container"
      >
        <u-empty></u-empty>
      </view>

      <!-- 没有更多数据 -->
      <view v-if="!hasMore && momSaysList.length > 0" class="no-more-container">
        <view class="no-more-text">没有更多数据了</view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue';
import { onLoad, onShow } from '@dcloudio/uni-app';
import { navigateToPage, decodePageTitle } from '@/utils/urlUtils.js';

// 页面参数
const pageParams = ref({});

// 数据状态
const momSaysList = ref([]);
const isLoading = ref(false);
const hasMore = ref(false);

// 页面加载
onLoad((options) => {
  pageParams.value = options || {};

  // 解码URL参数
  if (pageParams.value.userMessage) {
    pageParams.value.userMessage = decodePageTitle(
      pageParams.value.userMessage,
    );
  }

  console.log('宝妈说列表页面参数:', pageParams.value);

  // 加载组件传递的数据
  loadDataFromComponent();
});

// 页面显示
onShow(() => {
  // 可以在这里刷新数据
});

// 从组件传递的数据中加载宝妈说列表
const loadDataFromComponent = () => {
  try {
    const momSaysData = uni.getStorageSync('momSaysListData');

    if (momSaysData && momSaysData.list) {
      console.log('从组件获取宝妈说数据:', momSaysData);

      // 检查数据新鲜度（5分钟内有效）
      const isDataFresh =
        momSaysData.timestamp &&
        Date.now() - momSaysData.timestamp < 5 * 60 * 1000;

      if (isDataFresh) {
        // 使用组件传递的数据
        momSaysList.value = momSaysData.list;

        // 更新页面参数
        if (momSaysData.conversationId) {
          pageParams.value.conversationId = momSaysData.conversationId;
        }
        if (momSaysData.userMessage) {
          pageParams.value.userMessage = momSaysData.userMessage;
        }

        console.log(
          '成功加载组件传递的宝妈说数据，共',
          momSaysData.list.length,
          '条',
        );

        // 清除存储的数据，避免重复使用
        uni.removeStorageSync('momSaysListData');
        return;
      } else {
        console.log('组件传递的数据已过期');
        uni.removeStorageSync('momSaysListData');
      }
    } else {
      console.log('未找到组件传递的数据');
    }
  } catch (error) {
    console.error('加载组件数据失败:', error);
  }

  // 如果没有组件数据，显示空状态
  momSaysList.value = [];
};

// 跳转到宝妈说详情
const goToMomDetail = (momItem) => {
  console.log('跳转到宝妈说详情:', momItem);

  const params = {
    userId: momItem.userId,
    postId: momItem.postId,
  };

  navigateToPage('/pageA/pageB/community/sending/detail', params);
};

// 预览图片
const previewImage = (current, urls) => {
  uni.previewImage({
    current: current,
    urls: urls,
  });
};

// 处理头像加载失败
const handleAvatarError = (e) => {
  console.log('宝妈头像加载失败:', e);
  // 可以设置默认头像
};

// 处理图片加载失败
const handleImageError = (e) => {
  console.log('宝妈评价图片加载失败:', e);
};

// 处理视频加载失败（保留以备后续使用）
// const handleVideoError = (e) => {
//   console.log('宝妈评价视频加载失败:', e);
// };

// 格式化时间（保留以备后续使用）
// const formatTime = (timeStr) => {
//   if (!timeStr) return '';
//
//   const date = new Date(timeStr);
//   const now = new Date();
//   const diff = now - date;
//
//   // 一天内显示相对时间
//   if (diff < 24 * 60 * 60 * 1000) {
//     const hours = Math.floor(diff / (60 * 60 * 1000));
//     if (hours < 1) {
//       const minutes = Math.floor(diff / (60 * 1000));
//       return minutes < 1 ? '刚刚' : `${minutes}分钟前`;
//     }
//     return `${hours}小时前`;
//   }
//
//   // 超过一天显示日期
//   const month = date.getMonth() + 1;
//   const day = date.getDate();
//   return `${month}月${day}日`;
// };
</script>

<style lang="scss" scoped>
.mom-says-list-page {
  background-color: #eff0f6;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.page-content {
  flex: 1;
}

// AI助理消息样式
.ai-message-container {
  padding: 24rpx;
}

.ai-message-card {
  background-color: #ffffff;
  border-radius: 28rpx;
  display: flex;
  flex-direction: column;
  padding: 28rpx;
}

.ai-avatar-section {
  display: flex;
  gap: 16rpx;
  align-items: flex-end;
}

.ai-avatar-container {
  width: 72rpx;
  height: 85rpx;
  box-shadow: inset 0 0 10rpx rgba(255, 255, 255, 0.23);
  image {
    width: 100%;
    height: 100%;
  }
}

.ai-avatar-icon {
  width: 35rpx;
  height: 37rpx;
  background: linear-gradient(135deg, #d9d9d9 0%, #d9d9d9 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: inset 0 -1rpx 4rpx rgba(195, 233, 245, 1);
}

.ai-text {
  font-family: PingFang SC;
  font-weight: 600;
  font-size: 20rpx;
  line-height: 1.4;
  background: linear-gradient(135deg, #ffab75 0%, #ff6b44 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: inset 0 0 1rpx rgba(255, 255, 255, 0.25);
}

.ai-message-content {
  flex: 1;
}

.ai-greeting {
  font-family: PingFang SC;
  font-weight: 500;
  font-size: 32rpx;
  color: #000000;
  margin: 0 0 16rpx;
}

.ai-result-text {
  font-family: PingFang SC;
  font-weight: 400;
  font-size: 28rpx;
  line-height: 1.57;
  color: #7c7c7c;
}

// 宝妈说列表样式
.mom-says-list {
  padding: 0 24rpx;
}

.mom-item {
  background-color: #ffffff;
  border-radius: 20rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
}

.mom-header {
  padding: 24rpx;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
}

.mom-avatar-container {
  width: 68rpx;
  height: 68rpx;
  border-radius: 50%;
  overflow: hidden;
  background-color: #f5f5f5;
  flex-shrink: 0;
}

.mom-avatar {
  width: 100%;
  height: 100%;
}

.mom-info {
  flex: 1;
  margin-left: 22rpx;
}

.mom-name-section {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 8rpx;
}

.mom-name {
  font-family: PingFang SC;
  font-weight: 500;
  font-size: 26rpx;
  line-height: 1.4;
  color: #333333;
}

.mom-tag {
  position: relative;
  display: flex;
  padding: 4.5rpx 16.5rpx 6rpx 25.5rpx;
  justify-content: center;
  align-items: center;
  gap: 15rpx;
  border-radius: 6rpx;
  background: linear-gradient(0deg, #ffeef0 0%, #ffeef0 100%),
    linear-gradient(264deg, #f03d6a 0%, #f05279 98.02%);
}

.tag-icon {
  width: 44rpx;
  height: 44rpx;
  position: absolute;
  left: 0;
  top: 0;
  transform: translate(-22rpx, -11rpx);
}

.tag-text {
  font-family: Alimama ShuHeiTi;
  font-weight: 700;
  font-size: 18rpx;
  line-height: 1;
  color: #ff4f61;
}

.mom-details {
  font-family: PingFang SC;
  font-weight: 400;
  font-size: 20rpx;
  line-height: 1.4;
  color: #aaaaaa;
}

.status-tag {
  position: relative;
  flex-shrink: 0;
  display: inline-flex;
  padding: 14rpx 16rpx;
  justify-content: center;
  align-items: center;
  gap: 8px;
  border-radius: 76rpx;
  background: #ffeef0;
}

.status-text {
  font-family: Alimama ShuHeiTi;
  font-weight: 700;
  font-size: 20rpx;
  color: #ff4f61;
}

.status-bg {
  width: 94rpx;
  height: 68rpx;
  position: absolute;
  right: 0;
  top: 0;
  transform: translate(12rpx, -10rpx);
}

.mom-content {
  padding: 24rpx;
}

.mom-text {
  font-family: PingFang SC;
  font-weight: 400;
  font-size: 30rpx;
  line-height: 1.5;
  color: #333333;
  text-align: justify;
}

.mom-images {
  width: 100%;
  height: 702rpx;
}

.images-swiper {
  width: 100%;
  height: 100%;

  // 自定义指示器样式
  :deep(.uni-swiper-dots) {
    bottom: 24rpx !important;
  }

  :deep(.uni-swiper-dot) {
    width: 10rpx !important;
    height: 10rpx !important;
    margin: 0 5rpx !important;
    background-color: rgba(255, 255, 255, 0.5) !important;
    border-radius: 50% !important;
  }

  :deep(.uni-swiper-dot-active) {
    background-color: #454545 !important;
  }
}

.swiper-item {
  width: 100%;
  height: 100%;
}

.mom-image {
  width: 100%;
  height: 100%;
}

.loading-container,
.empty-container,
.no-more-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 32rpx;
}

.loading-text,
.empty-text,
.no-more-text {
  font-size: 28rpx;
  color: #999999;
  margin-top: 16rpx;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  opacity: 0.6;
}
</style>
