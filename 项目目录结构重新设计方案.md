# 艾尔母婴商家管理平台 - 项目目录结构重新设计方案

## 项目概述

**项目名称**: 艾尔母婴商家管理平台 (uni-mother-club)  
**技术栈**: Vue 3 + Uni-app + Vuex + Vite  
**平台**: 微信小程序  
**主要功能**: 月子中心商家管理、AI智能助理、客服系统、会所服务展示

## 当前项目分析

### 核心功能模块
1. **首页模块** - 会所信息展示、服务介绍、品牌宣传 (tabbar页面)
2. **AI智能助理** - 智能问答、推荐系统、聊天功能 (tabbar页面)
3. **会所服务** - 社区动态、服务笔记、套餐展示 (tabbar页面)
4. **会所动态** - 动态发布、浏览、互动功能 (tabbar页面)
5. **用户中心** - 个人信息、消息管理、收藏点赞 (tabbar页面)
6. **客服系统** - 实时聊天、会话管理、消息推送 (分包功能)

### 技术特点
- 采用 Vue 3 Composition API
- 使用 uni-app 跨平台框架
- 集成 WebSocket 实时通信
- 支持 SSE 流式数据传输
- 模块化组件设计

## 重新设计的目录结构

```
aier-merchant-uniapp/
├── docs/                           # 📚 项目文档
│   ├── README.md                   # 项目说明
│   ├── API.md                      # API接口文档
│   ├── DEPLOYMENT.md               # 部署指南
│   └── CHANGELOG.md                # 更新日志
│
├── src/                            # 📁 源代码目录
│   ├── app/                        # 🏠 应用核心
│   │   ├── App.vue                 # 应用入口组件
│   │   ├── main.js                 # 应用入口文件
│   │   ├── pages.json              # 页面路由配置
│   │   ├── manifest.json           # 应用配置清单
│   │   └── uni.scss                # 全局样式变量
│   │
│   ├── pages/                      # 📄 主包页面
│   │   ├── home/                   # 首页模块
│   │   │   ├── index.vue           # 首页主页面
│   │   │   └── components/         # 首页组件
│   │   │       ├── TenantInfo.vue  # 会所信息
│   │   │       ├── TenantMenu.vue  # 会所菜单
│   │   │       ├── ActivityModule.vue # 活动模块
│   │   │       └── ...
│   │   │
│   │   ├── ai-service/             # AI智能助理模块 (tabbar页面)
│   │   │   ├── index.vue           # AI聊天主页
│   │   │   └── components/         # AI服务组件
│   │   │       ├── ChatInput.vue   # 聊天输入框
│   │   │       ├── ChatSidebar.vue # 聊天侧边栏
│   │   │       ├── MomSays.vue     # 宝妈说组件
│   │   │       ├── QuickQuestions.vue # 快速问题
│   │   │       ├── RelatedQuestions.vue # 相关问题
│   │   │       ├── AiMessageRenderer.vue # AI消息渲染
│   │   │       └── ...
│   │   │
│   │   ├── community/              # 会所服务模块
│   │   │   ├── index.vue           # 服务主页
│   │   │   └── components/
│   │   │
│   │   ├── dynamics/               # 会所动态模块
│   │   │   ├── index.vue           # 动态主页
│   │   │   └── components/
│   │   │
│   │   ├── profile/                # 用户中心模块
│   │   │   ├── index.vue           # 个人中心主页
│   │   │   ├── message.vue         # 我的消息
│   │   │   ├── favorites.vue       # 我的收藏
│   │   │   └── components/
│   │   │
│   │   ├── auth/                   # 认证模块
│   │   │   ├── login.vue           # 登录页面
│   │   │   └── components/
│   │   │
│   │   └── common/                 # 通用页面
│   │       ├── blank.vue           # 空白页
│   │       └── error.vue           # 错误页
│   │
│   ├── subPackages/                # 📦 分包模块
│   │   ├── ai-service-ext/         # AI智能助理扩展分包
│   │   │   ├── pages/              # AI服务扩展页面
│   │   │   │   ├── mom-says-list.vue # 宝妈说列表
│   │   │   │   └── related-cases-list.vue # 相关案例列表
│   │   │   ├── components/         # AI服务扩展组件
│   │   │   │   ├── MomSaysCard.vue # 宝妈说卡片
│   │   │   │   └── CaseCard.vue    # 案例卡片
│   │   │   └── utils/              # AI服务扩展工具
│   │   │       └── listUtils.js
│   │   │
│   │   ├── customer-service/       # 客服系统分包
│   │   │   ├── pages/              # 客服页面
│   │   │   │   ├── reception.vue   # 客服接待台
│   │   │   │   └── chat.vue        # 客服聊天页
│   │   │   ├── components/         # 客服组件
│   │   │   │   ├── ChatInput.vue   # 聊天输入
│   │   │   │   ├── TransferPanel.vue # 转接面板
│   │   │   │   └── ...
│   │   │   ├── api/                # 客服API
│   │   │   │   ├── customerService.js
│   │   │   │   └── request.js
│   │   │   ├── composables/        # 客服组合式函数
│   │   │   │   └── useChatPagination.js
│   │   │   ├── utils/              # 客服工具函数
│   │   │   │   ├── chatWebSocketManager.js
│   │   │   │   └── timeUtils.js
│   │   │   └── config/             # 客服配置
│   │   │       └── constants.js
│   │   │
│   │   ├── business/               # 业务功能分包
│   │   │   ├── pages/              # 业务页面
│   │   │   │   ├── room/           # 套房相关
│   │   │   │   ├── food/           # 膳食相关
│   │   │   │   ├── team/           # 团队相关
│   │   │   │   └── package/        # 套餐相关
│   │   │   └── components/         # 业务组件
│   │   │
│   │   ├── poster/                 # 海报功能分包
│   │   │   ├── pages/              # 海报页面
│   │   │   │   ├── create.vue      # 海报创建
│   │   │   │   ├── edit.vue        # 海报编辑
│   │   │   │   ├── preview.vue     # 海报预览
│   │   │   │   └── gallery.vue     # 海报画廊
│   │   │   ├── components/         # 海报组件
│   │   │   │   ├── PosterCanvas.vue # 海报画布
│   │   │   │   ├── TemplateSelector.vue # 模板选择器
│   │   │   │   ├── TextEditor.vue  # 文本编辑器
│   │   │   │   ├── ImageUploader.vue # 图片上传器
│   │   │   │   └── StylePanel.vue  # 样式面板
│   │   │   ├── api/                # 海报API
│   │   │   │   └── poster.js
│   │   │   ├── utils/              # 海报工具
│   │   │   │   ├── canvas.js       # 画布工具
│   │   │   │   ├── template.js     # 模板工具
│   │   │   │   └── export.js       # 导出工具
│   │   │   └── config/             # 海报配置
│   │   │       ├── templates.js    # 模板配置
│   │   │       └── constants.js    # 常量配置
│   │   │
│   │   ├── invitation/             # 请帖功能分包
│   │   │   ├── pages/              # 请帖页面
│   │   │   │   ├── create.vue      # 请帖创建
│   │   │   │   ├── edit.vue        # 请帖编辑
│   │   │   │   ├── preview.vue     # 请帖预览
│   │   │   │   ├── list.vue        # 请帖列表
│   │   │   │   └── share.vue       # 请帖分享
│   │   │   ├── components/         # 请帖组件
│   │   │   │   ├── InvitationCard.vue # 请帖卡片
│   │   │   │   ├── TemplateGrid.vue # 模板网格
│   │   │   │   ├── BabyInfoForm.vue # 宝宝信息表单
│   │   │   │   ├── PhotoUploader.vue # 照片上传器
│   │   │   │   └── SharePanel.vue  # 分享面板
│   │   │   ├── api/                # 请帖API
│   │   │   │   └── invitation.js
│   │   │   ├── utils/              # 请帖工具
│   │   │   │   ├── template.js     # 模板工具
│   │   │   │   ├── generator.js    # 生成工具
│   │   │   │   └── share.js        # 分享工具
│   │   │   └── config/             # 请帖配置
│   │   │       ├── templates.js    # 模板配置
│   │   │       └── constants.js    # 常量配置
│   │   │
│   │   └── rehabilitation/         # 产后康复分包
│   │       ├── pages/              # 康复页面
│   │       │   ├── invite.vue      # 康复邀请
│   │       │   ├── projects.vue    # 康复项目
│   │       │   └── booking.vue     # 预约康复
│   │       └── components/         # 康复组件
│   │           ├── ProjectCard.vue # 项目卡片
│   │           └── BookingForm.vue # 预约表单
│   │
│   ├── components/                 # 🧩 全局组件
│   │   ├── ui/                     # 基础UI组件
│   │   │   ├── Button/             # 按钮组件
│   │   │   ├── Input/              # 输入框组件
│   │   │   ├── Modal/              # 弹窗组件
│   │   │   └── ...
│   │   ├── business/               # 业务组件
│   │   │   ├── CustomTabBar.vue    # 自定义标签栏
│   │   │   ├── Navigation.vue      # 导航组件
│   │   │   └── ...
│   │   └── layout/                 # 布局组件
│   │       ├── PageContainer.vue   # 页面容器
│   │       └── SafeArea.vue        # 安全区域
│   │
│   ├── api/                        # 🌐 API接口层
│   │   ├── modules/                # 模块化API
│   │   │   ├── auth.js             # 认证相关
│   │   │   ├── club.js             # 会所相关
│   │   │   ├── user.js             # 用户相关
│   │   │   ├── aiService.js        # AI服务相关
│   │   │   ├── poster.js           # 海报相关
│   │   │   ├── invitation.js       # 请帖相关
│   │   │   ├── rehabilitation.js   # 康复相关
│   │   │   └── ...
│   │   ├── request.js              # 请求封装
│   │   └── endpoints.js            # 接口地址配置
│   │
│   ├── store/                      # 🗄️ 状态管理
│   │   ├── modules/                # 模块化状态
│   │   │   ├── user.js             # 用户状态
│   │   │   ├── app.js              # 应用状态
│   │   │   ├── aiChat.js           # AI聊天状态
│   │   │   ├── customerService.js  # 客服状态
│   │   │   ├── poster.js           # 海报状态
│   │   │   ├── invitation.js       # 请帖状态
│   │   │   ├── rehabilitation.js   # 康复状态
│   │   │   └── ...
│   │   ├── index.js                # Store入口
│   │   └── plugins/                # Store插件
│   │
│   ├── utils/                      # 🛠️ 工具函数
│   │   ├── common/                 # 通用工具
│   │   │   ├── format.js           # 格式化工具
│   │   │   ├── validate.js         # 验证工具
│   │   │   └── storage.js          # 存储工具
│   │   ├── business/               # 业务工具
│   │   │   ├── auth.js             # 认证工具
│   │   │   ├── permission.js       # 权限工具
│   │   │   ├── navigation.js       # 导航工具
│   │   │   ├── messageRenderer.js  # 消息渲染工具
│   │   │   ├── sse.js              # SSE流式传输工具
│   │   │   ├── canvas.js           # 画布工具
│   │   │   ├── invitation.js       # 请帖工具
│   │   │   └── template.js         # 模板工具
│   │   └── platform/               # 平台相关
│   │       ├── wechat.js           # 微信相关
│   │       └── uni.js              # uni-app相关
│   │
│   ├── styles/                     # 🎨 样式文件
│   │   ├── base/                   # 基础样式
│   │   │   ├── reset.scss          # 重置样式
│   │   │   ├── variables.scss      # 样式变量
│   │   │   └── mixins.scss         # 样式混入
│   │   ├── components/             # 组件样式
│   │   ├── pages/                  # 页面样式
│   │   └── themes/                 # 主题样式
│   │       └── default.scss        # 默认主题
│   │
│   ├── assets/                     # 📁 静态资源
│   │   ├── images/                 # 图片资源
│   │   │   ├── icons/              # 图标
│   │   │   ├── backgrounds/        # 背景图
│   │   │   └── common/             # 通用图片
│   │   ├── fonts/                  # 字体文件
│   │   └── data/                   # 静态数据
│   │
│   ├── config/                     # ⚙️ 配置文件
│   │   ├── env.js                  # 环境配置
│   │   ├── constants.js            # 常量配置
│   │   └── app.js                  # 应用配置
│   │
│   └── plugins/                    # 🔌 插件目录
│       ├── uni-modules/            # uni-app插件
│       └── custom/                 # 自定义插件
│
├── tests/                          # 🧪 测试文件
│   ├── unit/                       # 单元测试
│   ├── integration/                # 集成测试
│   └── e2e/                        # 端到端测试
│
├── scripts/                        # 📜 构建脚本
│   ├── build.js                    # 构建脚本
│   └── deploy.js                   # 部署脚本
│
├── .env.example                    # 环境变量示例
├── .gitignore                      # Git忽略文件
├── package.json                    # 项目依赖配置
├── vite.config.js                  # Vite配置
├── project.config.json             # 小程序项目配置
└── README.md                       # 项目说明文档
```

## 重新设计的核心优势

### 1. 模块化架构
- **清晰的功能分离**: 每个功能模块独立管理
- **tabbar页面主包化**: AI智能助理等tabbar页面放在主包，确保快速加载
- **分包策略优化**: 扩展功能和客服系统合理分包，提升性能
- **组件复用性**: 全局组件与业务组件分离

### 2. 可维护性提升
- **统一的代码组织**: 相同类型文件集中管理
- **明确的职责划分**: API、状态、工具、样式各司其职
- **标准化命名**: 采用语义化的文件和目录命名

### 3. 开发效率优化
- **快速定位**: 通过目录结构快速找到相关文件
- **团队协作**: 清晰的结构便于多人协作开发
- **扩展性强**: 新功能可以按照既定模式快速添加

### 4. 性能优化
- **按需加载**: 分包设计支持按需加载
- **资源管理**: 静态资源分类管理，便于优化
- **代码分割**: 合理的模块划分支持代码分割

## 迁移建议

### 阶段一：基础结构调整
1. 创建新的目录结构
2. 迁移核心配置文件
3. 重新组织全局组件

### 阶段二：页面模块迁移
1. 按功能模块逐步迁移页面
2. 调整路由配置
3. 更新组件引用路径

### 阶段三：API和状态管理重构
1. 重新组织API接口
2. 优化状态管理结构
3. 统一工具函数管理

### 阶段四：样式和资源整理
1. 重新组织样式文件
2. 优化静态资源管理
3. 建立主题系统

## 注意事项

1. **渐进式迁移**: 建议分阶段进行，避免一次性大改动
2. **路径更新**: 迁移过程中需要更新所有import路径
3. **测试验证**: 每个阶段完成后进行充分测试
4. **文档同步**: 及时更新相关文档和注释

## 重要调整说明

### AI智能助理模块位置调整
**原设计问题**: 最初将AI智能助理放在分包中
**调整原因**: AI智能助理作为tabbar页面，必须放在主包中才能正常工作
**最终方案**:
- AI智能助理主页面放在 `pages/ai-service/` 目录
- AI相关的核心组件也放在主包中
- 仅将AI的扩展功能（如宝妈说列表、相关案例列表）放在分包 `subPackages/ai-service-ext/`

### 分包策略优化
1. **主包内容**:
   - 所有tabbar页面（首页、AI助理、会所服务、动态、用户中心）
   - 核心组件和API
   - 基础工具函数

2. **分包内容**:
   - AI扩展功能（宝妈说列表、相关案例列表等）
   - 客服系统（实时聊天、会话管理等）
   - 海报功能（海报创建、编辑、预览等）
   - 请帖功能（请帖创建、编辑、分享等）
   - 产后康复（康复项目、预约等）
   - 其他业务扩展功能

### 新增分包模块说明

#### 🎨 **海报功能分包** (`subPackages/poster/`)
- **功能范围**: 海报创建、编辑、预览、模板管理
- **核心组件**: PosterCanvas、TemplateSelector、TextEditor、ImageUploader
- **技术特点**: 画布操作、模板系统、图片处理、导出功能

#### 💌 **请帖功能分包** (`subPackages/invitation/`)
- **功能范围**: 请帖创建、编辑、预览、分享、列表管理
- **核心组件**: InvitationCard、TemplateGrid、BabyInfoForm、PhotoUploader
- **技术特点**: 模板定制、信息表单、照片上传、分享机制

#### 🏥 **产后康复分包** (`subPackages/rehabilitation/`)
- **功能范围**: 康复项目展示、预约管理、邀请功能
- **核心组件**: ProjectCard、BookingForm
- **技术特点**: 项目管理、预约系统、服务展示

这个重新设计的目录结构将大大提升项目的可维护性、可扩展性和开发效率，同时确保tabbar页面的正常运行，每个分包都有明确的业务边界和技术职责，为项目的长期发展奠定良好基础。
