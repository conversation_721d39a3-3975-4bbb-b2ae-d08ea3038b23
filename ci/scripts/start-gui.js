#!/usr/bin/env node

/**
 * 启动图形化管理界面
 */

const { spawn } = require('child_process');
const path = require('path');

// 颜色输出
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

async function startServer() {
  try {
    log('🚀 启动小程序 CI 图形化管理界面', 'cyan');
    log('='.repeat(50), 'cyan');
    
    // 启动服务器
    const serverPath = path.resolve(__dirname, '../server/index.js');
    const serverProcess = spawn('node', [serverPath], {
      stdio: 'inherit',
      cwd: path.resolve(__dirname, '../..')
    });

    // 等待服务器启动
    setTimeout(() => {
      logSuccess('服务器启动成功！');
      logInfo('Web 界面: http://localhost:3001');
      logInfo('按 Ctrl+C 停止服务器');
      
      // 自动打开浏览器
      if (process.argv.includes('--open') || process.argv.includes('-o')) {
        logInfo('正在打开浏览器...');
        import('open').then(({ default: open }) => {
          open('http://localhost:3001').catch(() => {
            logInfo('请手动打开浏览器访问: http://localhost:3001');
          });
        }).catch(() => {
          logInfo('请手动打开浏览器访问: http://localhost:3001');
        });
      } else {
        logInfo('使用 --open 参数可自动打开浏览器');
      }
    }, 2000);

    // 处理进程退出
    process.on('SIGINT', () => {
      log('\n正在停止服务器...', 'yellow');
      serverProcess.kill('SIGINT');
      process.exit(0);
    });

    process.on('SIGTERM', () => {
      serverProcess.kill('SIGTERM');
      process.exit(0);
    });

    serverProcess.on('error', (error) => {
      logError(`服务器启动失败: ${error.message}`);
      process.exit(1);
    });

    serverProcess.on('exit', (code) => {
      if (code !== 0) {
        logError(`服务器异常退出，退出码: ${code}`);
        process.exit(code);
      }
    });

  } catch (error) {
    logError(`启动失败: ${error.message}`);
    process.exit(1);
  }
}

// 显示帮助信息
function showHelp() {
  log('小程序 CI 图形化管理界面', 'cyan');
  log('');
  log('使用方法:', 'yellow');
  log('  node scripts/start-gui.js [选项]', 'white');
  log('  pnpm run ci:gui [选项]', 'white');
  log('');
  log('选项:', 'yellow');
  log('  --open, -o    启动后自动打开浏览器', 'white');
  log('  --help, -h    显示帮助信息', 'white');
  log('');
  log('功能特性:', 'yellow');
  log('  📊 项目状态监控', 'white');
  log('  📁 私钥文件管理', 'white');
  log('  🔨 一键构建项目', 'white');
  log('  📤 图形化上传', 'white');
  log('  👁️  预览二维码生成', 'white');
  log('  📋 实时日志显示', 'white');
  log('');
  log('示例:', 'yellow');
  log('  node scripts/start-gui.js --open', 'white');
  log('  pnpm run ci:gui --open', 'white');
}

// 检查依赖
function checkDependencies() {
  try {
    require('express');
    require('cors');
    require('multer');
    require('ws');
    require('chokidar');
    return true;
  } catch (error) {
    logError('缺少必要依赖，请运行: pnpm install');
    logError(`缺少依赖: ${error.message}`);
    return false;
  }
}

// 主函数
function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--help') || args.includes('-h')) {
    showHelp();
    return;
  }

  if (!checkDependencies()) {
    process.exit(1);
  }

  startServer();
}

// 运行主函数
main();
