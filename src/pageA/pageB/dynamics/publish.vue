<template>
	<view class="main">
		<view class="content">
			<view class="head">
				<view class="cancel"></view>
				<view class="publish" @tap="publish">发表</view>
			</view>
			<input v-if="roleIf == 2" class="uni-input" placeholder-class="input-placeholder"
				placeholder="填写标题会有更多赞哦(选填)" v-model="title" />
			<textarea :value="content" maxlength="1000" placeholder-class="input-placeholder" placeholder="这一刻你想说些什么？" class="textarea"
				@input="searchInput" />
			<upload-vue class="publish-upload" @clickFile="clickAlbumImg"></upload-vue>
			<!-- <view class="sales" v-if="roleIf == 4"> -->
			<view class="sales" v-if="roleIf == 2">
				<!-- 销售特殊处理 -->
				<view>
					<!-- <text style="color: #ff0000">*</text> -->
					<text class="roomNUm">角色</text>
				</view>
				<view>
					<!-- <text style="color: #ff0000">*</text> -->
					<text class="roomNUm">房间号</text>
				</view>
				<view>
					<u-input placeholder="" class="publish-input" v-model="roleName" type="select" :border="border"
						@click="showRole = true" />
				</view>
				<view>
					<u-input placeholder="" class="publish-input" v-model="roomName" type="select" :border="border"
						@click="showRoom = true" />
				</view>
				<u-select v-model="showRole" :list="roleList" mode="single-column" @confirm="confirmRole"></u-select>
				<u-select v-model="showRoom" :list="roomList" mode="single-column" @confirm="confirmRoom"></u-select>
			</view>
			<!-- <template v-else> -->
				<!-- <template v-if="roleIf != 3"> -->
					<!-- <view v-if="queryRoomsList.length == 1" style="position:relative">
						<view>
							<text style="color: #ff0000">*</text>
							<text class="roomNUm">房间号</text>
						</view>
						<view class="grid-container">
							<view @tap="roomTab" :data-index="index" :data-id="item.roomId"
								:class="roomIndex == index ? 'activeRoom' : 'grid-item1'"
								v-for="(item, index) in queryRoomsList" :key="index">
								<image
									:src="
                                    roomIndex == index
                                        ? 'http://cdn.xiaodingdang1.com/2024/10/30/344d0049b93249af8dcd2d0ee9f16d25.png'
                                        : 'http://cdn.xiaodingdang1.com/2024/11/19/362cc1ef39d341cbba6255663337c084.png'"
									mode="" style="width: 20rpx; height: 25rpx" />
								<view class="roomNumber">
									{{ item.roomNumber }}
								</view>
							</view>
						</view>
						<view v-if="addBtnShow" class="nurse-choose">
							特殊处理护士
							<u-input placeholder="特殊情况选择其他房间" class="publish-nurse" v-model="nurseName" type="select"
								:border="border" @click="showNurseRoom = true" />
							<u-select v-model="showNurseRoom" :list="allRoomList" mode="single-column"
								@confirm="confirmNurse"></u-select>
						</view>
					</view> -->
			<!-- 		<view v-if="queryRoomsList.length > 1">
						<view>
							<text style="color: #ff0000">*</text>
							<text class="roomNUm">房间号1</text>
						</view>
						<view class="grid-container">
							<view :class="roomIndex == index ? 'activeRoom' : 'grid-item1'" @tap="roomTab"
								:data-index="index" :data-id="item.roomId" v-for="(item, index) in queryRoomsList"
								:key="index">
								<image :src="
                                    roomIndex == index
                                        ? 'http://cdn.xiaodingdang1.com/2024/10/30/344d0049b93249af8dcd2d0ee9f16d25.png'
                                        : 'http://cdn.xiaodingdang1.com/2024/11/19/362cc1ef39d341cbba6255663337c084.png'
                                " mode="" style="width: 20rpx; height: 25rpx" />

								<view class="roomNumber">
									{{ item.roomNumber }}
								</view>
							</view>
						</view>
					</view> -->
				<!-- </template> -->
			<!-- </template> -->
			<!-- 会所除外 -->
			<view v-if="roleIf == 2">
				<text style="color: #ff0000">*</text>
				<text class="roomNUm">标签</text>
			</view>
			<view>
				<view class="grid-container1" v-if="roleIf == 2">
					<view v-if="unfoldShow" :class="tabIndexs == index ? 'gridActive' : 'grid-item1'" @tap="tab"
						:data-id="item.taskNodeId" :data-index="index" :data-name="item.nodeName"
						v-for="(item, index) in tabList" :key="index">
						{{ item.nodeName }}
					</view>
					<view v-if="!unfoldShow"  @tap="tab"
						:data-id="item.taskNodeId" :data-index="index" :data-name="item.nodeName"
						v-for="(item, index) in tabList" :key="index">
						<view v-if="index<=8" :class="tabIndexs == index ? 'gridActive' : 'grid-item1'">
							{{ item.nodeName }}
						</view>
					</view>
				</view>
				<view class="unfold" v-if="tabList.length>9&&roleIf == 2" @click="unfold" >
								<view style="color: #3f6fff;">
									{{unfoldShow?'收起':"展开"}}
								</view>
								<image :src="unfoldShow?'http://cdn.xiaodingdang1.com/2025/04/08/de07df2be20242fd852ae84d3b0c30b5.png':'http://cdn.xiaodingdang1.com/2025/04/08/aca3e630588846e381feea6bac9dc10f.png'" mode="" style="width:30rpx;height: 20rpx;margin-left: 10rpx;"></image>
								</view>
				<view class="addTab1" v-if="addBtnShow && roleIf == 2">
					<view class="addTab" v-if="addTabShow">
						<view class="tabInput">
							<input type="text" placeholder="请输入标签" :value="tabName" @input="tabInput" />
						</view>
						<view class="confirm" @tap="confirm">确定</view>
					</view>
					<view id="grid-item1" @tap="addTab" v-if="!addTabShow">
						<image src="http://cdn.xiaodingdang1.com/2024/11/12/9a6cf0e15120476688b74ad0ec69a522.png"
							mode="" style="width: 32rpx; height: 32rpx" />
						<view>新增标签</view>
					</view>
				</view>
				
			</view>
		</view>
		<!-- 		<view class="templates" v-if="addBtnShows && templateList.length > 0">
			<view :class="templateIndex == index ? 'activeTemplate' : 'template'" @tap="template"
				:data-content="item.content" :data-index="index" v-for="(item, index) in templateList" :key="index">
				<view class="template-text">
					{{ item.content }}
				</view>
			</view>
		</view> -->
		<new-request-loading></new-request-loading>
		<view class="tabs" v-if="roleIf == 2">
			<view class="tabsHead" v-if="addBtnShows && tabsList.length > 0">
				<!-- <u-tabs :list="tabsList" :is-scroll="true" :current="current" @change="change"></u-tabs> -->
				 <view class="container">
									     <scroll-view scroll-x="true" class="tab-scroll-view">
									       <view class="tab-wrapper">
									         <view v-for="(tab, index) in tabsList" :key="index" class="tab-item" :class="{ 'active': current === index }" @click="change(index)">
									           {{ tab.name }}
									         </view>
									       </view>
									     </scroll-view>
									   </view>
				<view class="cutOff"></view>
			</view>
			<view class="tabsContent" v-for="item,index in templateList" :key="index" @click="tabsContent(item)"
				v-if="addBtnShows && templateList.length > 0">
				<view class="tabsContent1">
					{{ item.content }}
				</view>
				<view class="cutOff"></view>
			</view>

		</view>
		<u-mask :show="show" @click="show = false">
			<view class="maskContent">
				<view class="maskTitle">
					模板详情
				</view>
				<scroll-view scroll-y="true" class="maskContent1">
					{{tabcontent}}
				</scroll-view>
				<view class="maskBtn">
					<u-button size="medium" @click="show = false">取消</u-button>
					<u-button size="medium" type="primary" @click="employ">使用</u-button>
				</view>
			</view>
		</u-mask>
	</view>
</template>

<script>
	import uploadVue from '@/components/upload.vue';
	export default {
		components: {
			uploadVue,
		},
		data() {
			return {
				unfoldShow:false,
				tabsList: [],
				show: false,
				addBtnShow: false,
				addBtnShows: false,
				tabName: '',
				addTabShow: false,
				roomId: '',
				roomIndex: -1,
				queryRoomsList: [],
				templateIndex: -1,
				templateList: [],
				tabList: [],
				tabIndexs: -1,
				tabIndex: -1,
				videosList: [],
				videosShow: true,
				photosShow: true,
				videos: [],
				content: '',
				//动态内容
				contentPhotos: [],
				//动态图片
				imgUrl: [],
				topicInfo: '',
				taskNodeId: '',
				roleIf: '',
				// 销售发动态
				roleList: [],
				roomList: [],
				showRole: false,
				showRoom: false,
				roleValue: '',
				roomValue: '',
				roleName: '',
				roomName: '',
				// 护士选择所有房间
				showNurseRoom: false,
				nurseName: '',
				allRoomList: [],
				title: '',
				list: [],
				current: 0,
				contentStyle: '',
				labelName: '',
				tabcontent:''
			};
		}
		/**
		 * 生命周期函数--监听页面加载
		 */
		,
		onLoad(options) {
			let roles = uni.getStorageSync('roles');
			console.log( options.roleIf);
			this.setData({
				roleIf: options.roleIf,
				//1宝妈USER 2.员工(宝妈社区)STAFF 3.会所 CLUB 4.销售 
				addBtnShow: roles.includes('NURSE'),
				addBtnShows: !roles.includes('CUSTOMER')
			});
			this.getlistByType();
			this.getqueryRooms();
			// if (this.roleIf == 4) {
				this.getRoleList()
				this.getRoomList()
			// }
		},
		/**
		 * 生命周期函数--监听页面初次渲染完成
		 */
		onReady() {},
		/**
		 * 生命周期函数--监听页面显示
		 */
		onShow() {},
		/**
		 * 生命周期函数--监听页面隐藏
		 */
		onHide() {},
		/**
		 * 生命周期函数--监听页面卸载
		 */
		onUnload() {},
		/**
		 * 页面相关事件处理函数--监听用户下拉动作
		 */
		onPullDownRefresh() {},
		/**
		 * 页面上拉触底事件的处理函数
		 */
		onReachBottom() {},
		/**
		 * 用户点击右上角分享
		 */
		onShareAppMessage() {},

		onShareTimeline() {},
		methods: {
			unfold(){
				this.unfoldShow=!this.unfoldShow
			},
			tabsContent(item) {
				this.show = true
				this.tabcontent=item.content
			},
			change(index) {
				this.current = index;
				this.contentStyle = this.tabsList[index].label
				this.getlistByLabel();
			},
			confirmNurse(e) {
				this.nurseName = e[0].label
				this.roomId = e[0].value
				this.roomIndex = -1
			},
			confirmRole(e) {
				this.roleName = e[0].label
				this.roleValue = e[0].value
				this.getRoomList()
				this.roomValue = ''
				this.roomName = '全部'
			},
			confirmRoom(e) {
				this.roomName = e[0].label
				this.roomValue = e[0].value
			},
			getRoleList() {
				// 目前只有宝妈，护理，厨师角色有房间
				const hasRoomList = ['NURSE', 'POSTPARTUM', 'CHEF']
				this.$axios.get(this.$api.getRoleList).then((res) => {
					const data = res?.data?.data
					if (data) {
						data.forEach((item) => {
							const list = {
								value: item.code,
								label: item.name
							}
							if (hasRoomList.includes(item.code)) {
								this.roleList.push(list)
							}
						})
						this.roleList.unshift({
							value: '',
							label: '母婴顾问'
						})
						this.roleValue = ''
						this.roleName = '母婴顾问'
					}
				})
			},
			getRoomList() {
				this.roomList = []
				this.$axios.get(this.$api.getRoomList, {
					roleCode: this.roleValue
				}).then((res) => {
					const data = res?.data?.data
					if (data) {
						data.forEach((item) => {
							const list = {
								value: item.roomId,
								label: item.roomNumber
							}
							this.roomList.push(list)
						})
					}
					this.roomList.unshift({
						value: '',
						label: '全部'
					})
					this.roomValue = ''
					this.roomName = '全部'
				})
			},
			clickAlbumImg(value) {
				console.log('clickAlbumImg', value)
				// value: [{type: '',url: ''}]
				let imgUrl = []
				let videos = []
				if (value && value.length > 0) {
					value.forEach((item) => {
						item.type == 'video' ? videos.push(item.url) : imgUrl.push(item.url)
					})
					this.setData({
						imgUrl,
						videos
					});
				} else {
					this.setData({
						imgUrl: [],
						videos: []
					});
				}

			},

			async publish() {
				//发布
				if (this.taskNodeId == '' && this.roleIf == 2) {
					uni.showToast({
						title: '请选择标签',
						icon: 'none',
						duration: 3000 //持续的时间
					});
					return;
				}
				if (this.content == '') {
					uni.showToast({
						title: '请填写发布内容',
						icon: 'none',
						duration: 3000 //持续的时间
					});
					return;
				}
				// if (this.queryRoomsList.length > 1 && this.roomId == '') {
				//     uni.showToast({
				//         title: '请选择房号',
				//         icon: 'none',
				//         duration: 3000 //持续的时间
				//     });
				//     return;
				// }
				let data = {
					content: this.content,
					taskNodeId: this.taskNodeId,
					title: this.title
				};
				if (this.imgUrl.length > 0) {
					  let imgsList=[]
						  this.imgUrl.forEach(item=>{
							  imgsList.push(item.url)
						  })
					data.contentPhotos = imgsList;
				}
				if (this.videos.length > 0) {
					data.videos = this.videos;
				}
				if (this.roleIf) {
					//1宝妈USER 2.员工(宝妈社区)STAFF 3.会所 CLUB
					data.type = this.roleIf == 1 ? 'USER' : this.roleIf == 2 ? 'STAFF' : this.roleIf == 3 ? 'CLUB' :
						'';
				}
				if (this.roomId) {
					data.roomId = this.roomId;
				}
				if(this.roomValue){
					data.roomId = this.roomValue
				}
				if(this.roleValue){
					data.roleCode = this.roleValue
				}
				this.changeLoading(true)
				let res = null
				// if (this.roleIf == 4) {
				// 	data.roomId = this.roomValue
				// 	data.roleCode = this.roleValue
				// 	res = await this.$axios.put(this.$api.pushSalesPublish, data)
				// } else {
				// 	res = await this.$axios.put(this.$api.getpublish, data)
				// }
				res = await this.$axios.put(this.$api.getpublish, data)
				if (res?.data?.code == 200) {
					setTimeout(() => {
						uni.$emit('publishsuccesss');
						uni.navigateBack({
							delta: 1
						});
					}, 200);
				}
			},

			searchInput(e) {
				//输入框获取值
				this.setData({
					content: e.detail.value
				});
			},

			cancel() {
				//取消
				uni.navigateBack({
					delta: 1
				});
			},

			tab(e) {
				console.log('this.roleIfthis.roleIf', this.roleIf)
				let index = e.currentTarget.dataset.index;
				let name = e.currentTarget.dataset.name;
				this.labelName = name
				if (this.roleIf == 1) {
					let id = e.currentTarget.dataset.id;
					this.setData({
						tabIndexs: index,
						taskNodeId: id,
						current:0,
					    contentStyle:'' 
					});
				}
				if (this.roleIf == 2) {
					let id = e.currentTarget.dataset.id;
					this.setData({
						tabIndexs: index,
						taskNodeId: id,
						current:0,
						contentStyle:'' 
					});
					this.getlistByLabel();
				}
				if (this.roleIf == 4) {
					let id = e.currentTarget.dataset.id;
					this.setData({
						tabIndexs: index,
						taskNodeId: id,
						current:0,
						contentStyle:'' 
					});
					this.getlistByLabel();
				}
				this.getTemplateStyle()
			},

			getlistByType() {
				//通过类型与角色查询标签列表
				// let data = {
				//     nurseRole: uni.getStorageSync('roles')[0]
				// };
				this.$axios.get(this.$api.getAllTag).then((res) => {
					if (res.data.code == 200) {
						this.setData({
							tabList: res.data.data
						});
					}
				});
			},
			getTemplateStyle() {
				//获取所有模板风格
				this.tabsList=[]
				this.$axios.get(this.$api.templateStyle).then((res) => {
					if (res.data.code == 200) {
						const data = res.data.data
						if (data) {
							data.forEach((item) => {
								const list = {
									id: item.id,
									name: item.name,
									label:item.name,
								}
								this.tabsList.push(list)
							})
						}
						this.tabsList.unshift({
							id: '',
							name: '全部',
							label: ''
						})
					}
				});
			},
			getlistByLabel() {
				//通过标签名称查询模板
				let data = {
					labelName: this.labelName
				};
				if (this.contentStyle) {
					data.contentStyle = this.contentStyle
				}
				this.$axios.get(this.$api.listByLabel, data).then((res) => {
					if (res.data.code == 200) {
						this.setData({
							templateList: res.data.data
						});
					}
				});
			},

			template(e) {
				//选择模板
				let content = e.currentTarget.dataset.content;
				let index = e.currentTarget.dataset.index;
				this.setData({
					content: content,
					templateIndex: index
				});
			},
           employ(){
			   //选择模板
			   this.setData({
			   	content: this.tabcontent,
				show:false
			   });
		   },
			getqueryRooms() {
				//查询房间号
				this.$axios.get(this.$api.queryRooms).then((res) => {
					if (res?.data?.code == 200) {
						this.setData({
							queryRoomsList: res.data.data
						});
						if (this.queryRoomsList.length == 1) {
							this.roomId = this.queryRoomsList[0]?.roomId
							this.roomIndex = 0
						}
						// 护士发动态需要所有房间号
						if (this.addBtnShow && this.queryRoomsList.length == 1) {
							this.$axios.get(this.$api.getAllRoom).then((res) => {
								if (res?.data?.code == 200) {
									const data = res.data.data
									data.forEach((item) => {
										if (item.roomId != this.queryRoomsList[0]?.roomId) {
											const list = {
												value: item.roomId,
												label: item.roomNumber
											}
											this.allRoomList.push(list)
										}
									})
								}
							})
						}
					}
				});
			},

			roomTab(e) {
				//选择房间号
				let index = e.currentTarget.dataset.index;
				let roomId = e.currentTarget.dataset.id;
				this.setData({
					roomIndex: index,
					roomId: roomId,
					nurseName: ''
				});
			},

			addTab() {
				//新增标签
				this.setData({
					addTabShow: !this.addTabShow
				});
			},

			confirm() {
				//新增确定
				if (this.tabName == '') {
					uni.showToast({
						title: '请填写标签',
						icon: 'none',
						duration: 2000 //持续的时间
					});
					return;
				}
				this.getTaskNodeSave();
			},

			tabInput(e) {
				this.setData({
					tabName: e.detail.value
				});
			},

			getTaskNodeSave() {
				//新增节点
				let data = {
					nurseType: 'HLFW',
					nurseRole: 'NURSE',
					nodeName: this.tabName
				};
				this.$axios.post(this.$api.taskNodeSave, data).then((res) => {
					if (res.data.code == 200) {
						this.setData({
							addTabShow: !this.addTabShow
						});
						this.getlistByType();
					}
				});
			}
		}
	};
</script>
<style scoped>
	@import './publish.css';
</style>
<style>
	.input-placeholder {
		color: #BCBCBC;
	}
</style>