<template>
	<navigation background="#fff" :title="title" :isSowArrow="true"></navigation>
	<view class="main">
		<view class="list" v-for="item,index in listData" :key="index" @tap="nextList(item)">
			<view class="title">
				<view class="title_1">
					<image src="http://cdn.xiaodingdang1.com/2025/03/21/e4023bdc195b4f75905506fd572490d9.png" mode=""
						style="width: 24rpx;height: 24rpx;" />
				</view>
				<view class="title_2">
					操作人员：{{item.authorName}}
				</view>
			</view>
			<view class="list1">
				<view>
					<view class="list_1">
						<view v-if="type!=8&&type!=9">
							<text class="list_2"> 宝宝名称：</text><text class="list_3">{{item.babyName}}</text>
						</view>
						<view v-if="type==8||type==9">
							<text class="list_2"> 宝妈名称：</text><text class="list_3">{{item.momName}}</text>
						</view>
						<view>
							<text class="list_2">入住时间：</text><text class="list_3">{{item.startTime}}</text>
						</view>
					</view>
					<view class="list_1">
						<view>
							<text class="list_2"> 创建时间：</text><text class="list_3">{{item.createTime}}</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		<empty-vue v-if="isFinshed && listData.length == 0"></empty-vue>
	</view>
</template>
<script>
	import emptyVue from '@/components/empty.vue';
	export default {
		components: {
			emptyVue
		},
		data() {
			return {
				isFinshed: false,
				bgColor: '#ffffff',
				title: "",
				listData: [],
				type: ''
			};
		},
		methods: {
			nextList(item) {
				if(this.type==8){
					uni.navigateTo({
						url: '/pageA/pageB/community/nurse/nursedetails?title=' + this.title+'&id='+item.id
					});
				}else if(this.type==9){
					uni.navigateTo({
						url: '/pageA/pageB/community/nurse/recorddetails?title=' + this.title+'&id='+item.id
					});
				}else if(this.type==2){
					uni.navigateTo({
						url: '/pageA/pageB/community/nurse/babynursedetails?title=' + this.title+'&id='+item.id+'&babyId='+item.babyId
					});
				}else if(this.type==3){
					uni.navigateTo({
						url: '/pageA/pageB/community/nurse/babyrecorddetails?title=' + this.title+'&id='+item.id
					});
				}else if(this.type==4){
					uni.navigateTo({
						url: '/pageA/pageB/community/nurse/babymilkdetails?title=' + this.title+'&id='+item.id
					});
				}else if(this.type==5){
					uni.navigateTo({
						url: '/pageA/pageB/community/nurse/babydiaperdetails?title=' + this.title+'&id='+item.id
					});
				}else if(this.type==6){
					uni.navigateTo({
						url: '/pageA/pageB/community/nurse/babydatedetails?title=' + this.title+'&id='+item.id
					});
				}else if(this.type==7){
					uni.navigateTo({
						url: '/pageA/pageB/community/nurse/babypharmacydetails?title=' + this.title+'&id='+item.id
					});
				}
				
			},
			getList() {
				//护理列表
				this.changeLoading(true)
				this.isFinshed = false
				let type = this.type
				let api = type == 1 ? this.$api.getMyDetail : type == 2 ? this.$api.careMyList : type == 3 ? this.$api
					.routineMyList : type == 4 ? this.$api.feedingMyList : type == 5 ? this.$api.diaperMyList : type == 6 ?
					this.$api.bathMyList : type == 7 ? this.$api.medicationMyList : type == 8 ? this.$api
					.motherCareMyList : type == 9 ? this.$api.motherRoundsMyList : ''
				console.log(api); -
				this.$axios.get(api).then((res) => {
					if (res.data.code == 200) {
						this.setData({
							listData: res.data.data
						});
					}
				}).finally(() => {
					this.isFinshed = true
					this.changeLoading(false)
				})
			}
		},
		/**
		 * 生命周期函数--监听页面加载
		 */
		onLoad: function(options) {
			this.title = options.title
			this.type = options.type
			this.getList()
		},
		/**
		 * 生命周期函数--监听页面初次渲染完成
		 */
		onReady: function() {},
		/**
		 * 生命周期函数--监听页面显示
		 */
		onShow: function() {},
		/**
		 * 生命周期函数--监听页面隐藏
		 */
		onHide: function() {},
		/**
		 * 生命周期函数--监听页面卸载
		 */
		onUnload: function() {},
		/**
		 * 页面相关事件处理函数--监听用户下拉动作
		 */
		onPullDownRefresh: function() {},
		/**
		 * 页面上拉触底事件的处理函数
		 */
		onReachBottom: function() {},
		/**
		 * 用户点击右上角分享、
		 */
		onShareAppMessage() {},

		onShareTimeline() {},
	}
</script>

<style lang="less" scoped>
	@import './nurselist.less';
</style>