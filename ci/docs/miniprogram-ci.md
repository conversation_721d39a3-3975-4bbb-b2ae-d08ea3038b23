# 小程序多项目 CI 使用说明

本项目支持使用 miniprogram-ci 将小程序上传到多个不同的 appid 项目。

## 功能特性

- ✅ 支持多个 appid 项目管理
- ✅ 支持不同环境（开发、体验、正式）
- ✅ 自动构建和上传
- ✅ 预览二维码生成
- ✅ 版本号自动生成
- ✅ 项目配置管理
- ✅ 私钥文件安全管理
- 🆕 **图形化管理界面**
- 🆕 **实时日志显示**
- 🆕 **拖拽上传私钥文件**
- 🆕 **WebSocket 实时通信**
- 🆕 **项目状态监控**

## 快速开始

### 方式一：图形化界面（推荐）

```bash
# 启动图形化管理界面
pnpm run ci:gui --open

# 或者使用脚本
node scripts/start-gui.js --open
```

访问 http://localhost:3001 即可使用图形化界面进行：
- 📁 拖拽上传私钥文件
- 🔨 一键构建项目
- 📤 可视化上传配置
- 👁️ 预览二维码生成
- 📋 实时日志查看

### 方式二：命令行操作

### 1. 配置私钥文件

1. 登录微信公众平台：https://mp.weixin.qq.com
2. 进入对应的小程序账号
3. 导航到：开发 -> 开发管理 -> 开发设置
4. 在"小程序代码上传"部分，点击"生成"按钮生成私钥
5. 下载私钥文件并放置到 `private/` 目录下

```bash
# 私钥文件命名规范
private/
├── main.key    # 主项目私钥
├── test.key    # 测试项目私钥
└── demo.key    # 演示项目私钥
```

### 2. 检查项目配置

```bash
# 检查所有项目配置
node scripts/manage.js check

# 查看项目列表
node scripts/manage.js list

# 查看特定项目信息
node scripts/manage.js info main
```

### 3. 上传小程序

```bash
# 使用 npm scripts（推荐）
pnpm run upload:main:dev      # 上传主项目到开发环境
pnpm run upload:main:trial    # 上传主项目到体验环境
pnpm run upload:main:release  # 上传主项目到正式环境

# 直接使用脚本
node scripts/upload.js main develop                    # 基本用法
node scripts/upload.js main trial 1.0.0 "新功能上线"   # 指定版本和描述
node scripts/upload.js test develop                    # 上传到测试项目
```

### 4. 生成预览二维码

```bash
# 使用 npm scripts（推荐）
pnpm run preview:main:dev     # 预览主项目开发环境
pnpm run preview:test:dev     # 预览测试项目

# 直接使用脚本
node scripts/preview.js main develop                           # 基本用法
node scripts/preview.js main develop pages/detail/detail       # 指定页面
node scripts/preview.js main develop pages/detail/detail id=123 # 指定页面和参数
```

## 项目配置

### 配置文件结构

项目配置位于 `ci.config.js` 文件中：

```javascript
const projects = {
  main: {
    name: '艾尔母婴商家管理平台',
    appid: 'wx1df89e340decbc7a',
    projectPath: path.resolve(__dirname, 'dist/build/mp-weixin'),
    privateKeyPath: path.resolve(__dirname, 'private/main.key'),
    ignores: ['node_modules/**/*'],
    environments: {
      develop: {
        name: '开发版',
        desc: '开发环境版本',
        buildCommand: 'pnpm run build:mp-weixin --mode test'
      },
      // ... 其他环境
    }
  },
  // ... 其他项目
};
```

### 添加新项目

1. 在 `ci.config.js` 的 `projects` 对象中添加新项目配置
2. 将对应的私钥文件放置到 `private/` 目录
3. 在 `package.json` 中添加对应的 npm scripts

```bash
# 生成项目配置模板
node scripts/manage.js template
```

## 命令参考

### 上传命令

```bash
node scripts/upload.js <项目名> <环境> [版本号] [描述]
```

参数说明：
- `项目名`: main, test, demo 等
- `环境`: develop, trial, release
- `版本号`: 可选，不传则自动生成
- `描述`: 可选，版本描述信息

### 预览命令

```bash
node scripts/preview.js <项目名> <环境> [页面路径] [页面参数]
```

参数说明：
- `项目名`: main, test, demo 等
- `环境`: develop, trial, release
- `页面路径`: 可选，如 pages/index/index
- `页面参数`: 可选，如 id=123&name=test

### 管理命令

```bash
node scripts/manage.js <命令>
```

可用命令：
- `list`: 列出所有项目配置
- `check`: 检查项目配置和私钥文件
- `info <项目名>`: 显示项目详细信息
- `template`: 生成项目配置模板

## NPM Scripts

项目已预配置了常用的 npm scripts：

```json
{
  "scripts": {
    // 上传命令
    "upload:main:dev": "node scripts/upload.js main develop",
    "upload:main:trial": "node scripts/upload.js main trial",
    "upload:main:release": "node scripts/upload.js main release",
    "upload:test:dev": "node scripts/upload.js test develop",
    "upload:test:trial": "node scripts/upload.js test trial",
    "upload:demo:dev": "node scripts/upload.js demo develop",
    "upload:demo:trial": "node scripts/upload.js demo trial",
    
    // 预览命令
    "preview:main:dev": "node scripts/preview.js main develop",
    "preview:main:trial": "node scripts/preview.js main trial",
    "preview:test:dev": "node scripts/preview.js test develop",
    "preview:demo:dev": "node scripts/preview.js demo develop"
  }
}
```

## 版本号规则

### 自动生成版本号

格式：`年.月.日.时分`

示例：`2024.12.20.1430`

### 手动指定版本号

```bash
node scripts/upload.js main develop 1.0.0 "版本描述"
```

## 安全注意事项

1. **私钥文件安全**：
   - 私钥文件已添加到 `.gitignore`，不会被提交到版本控制
   - 请妥善保管私钥文件，避免泄露
   - 建议定期更换私钥文件

2. **文件权限**：
   ```bash
   chmod 600 private/*.key
   ```

3. **环境隔离**：
   - 不同环境使用不同的构建配置
   - 确保测试环境不会影响生产环境

## 故障排除

### 常见问题

1. **私钥文件不存在**
   ```
   ❌ 私钥文件不存在: /path/to/private/main.key
   ```
   解决方案：从微信公众平台下载私钥文件并放置到正确位置

2. **构建失败**
   ```
   ❌ 项目构建失败
   ```
   解决方案：检查构建命令是否正确，确保依赖已安装

3. **上传失败**
   ```
   ❌ 上传失败: appid 不匹配
   ```
   解决方案：检查 appid 配置是否正确，私钥是否对应正确的小程序

### 调试模式

在脚本中添加调试信息：

```bash
DEBUG=1 node scripts/upload.js main develop
```

## 更新日志

- v1.0.0: 初始版本，支持多项目上传和预览
- 支持自动版本号生成
- 支持项目配置管理
- 支持私钥文件安全管理
