<template>
  <view class="detail">
    <web-view v-if="isShow" :src="src"> </web-view>
    <cover-view class="footer">
      <cover-view class="footer-info">
        <cover-view>{{ detail?.viewNumber || 0 }}人浏览</cover-view>
        <cover-view>{{ pageTotal || 0 }}图</cover-view>
      </cover-view>
      <cover-view class="footer-operate">
        <button open-type="share" class="share-btn">
          <cover-view class="footer-operate-item">
            <cover-image
              class="operate-icon"
              src="http://cdn.xiaodingdang1.com/2025/03/19/19746bc5f04247a3a1728c45f71ff36b.png"
              alt=""
            ></cover-image>
            <cover-view class="share-text">分享</cover-view>
          </cover-view>
        </button>
        <cover-view class="footer-operate-item" @click="handleFavorite">
          <cover-image
            class="operate-icon favorite-icon"
            :src="
              isFavorite
                ? 'https://cdn.xiaodingdang1.com/2024/12/31/2899047a49fd47fe97e0ec71c5d21635.png'
                : 'https://cdn.xiaodingdang1.com/2025/03/19/4dcfaf8d693046468ff56c379f3db702.png'
            "
            alt=""
          ></cover-image>
          <cover-view class="share-text" :class="{ active: isFavorite }">
            {{ isFavorite ? '已收藏' : '收藏' }}
          </cover-view>
        </cover-view>
      </cover-view>

      <cover-view class="footer-btn" @click="handleEdit">
        <cover-view>{{ type == 'user' ? '编辑' : '立即制作' }}</cover-view>
      </cover-view>
    </cover-view>
  </view>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { onLoad, onShareAppMessage, onShow, onHide } from '@dcloudio/uni-app';
import utils from '@/utils/util';
import {
  getRehabilitationDetailApi,
  getUserFavoritesApi,
  addUserFavoritesApi,
} from './api/rehabilitation';

const src = ref('');
const pageTotal = ref(0);

const isShow = ref(false);
onShow(() => {
  isShow.value = true;
});
onHide(() => {
  isShow.value = false;
});

const state = reactive({
  id: '',
  tenantId: '',
  userId: '',
  type: '',
});

// 获取请帖详情
const detail = ref({});
const getDetail = async () => {
  const res = await getRehabilitationDetailApi(state.id);
  detail.value = res.data;
  pageTotal.value = detail.value.pageNum;
};

// 获取用户是否收藏
const isFavorite = ref(false);
const getIsFavorite = async () => {
  const res = await getUserFavoritesApi(state.id);
  isFavorite.value = res.data;
};

// 收藏
const handleFavorite = async () => {
  const res = await addUserFavoritesApi({
    templateId: state.id,
  });
  console.log(res);

  isFavorite.value = !isFavorite.value;
};

onLoad((options) => {
  const userInfo = uni.getStorageSync('userInfo') || {};
  const { tenantId, userId } = userInfo;

  if (options?.id) {
    state.id = options.id;
    state.tenantId = tenantId;
    state.userId = userId;
    state.type = options.type;

    getDetail();
    getIsFavorite();
  }

  src.value = `https://h5.xiaodingdang1.com/wap.html?${utils.queryParams({
    type: 'template',
    id: options.id,
    tenantId,
    preview: true,
  })}`;
});

const handleEdit = () => {
  uni.navigateTo({
    url: `/pageA/pageB/home/<USER>/edit?${utils.queryParams({
      ...state,
    })}`,
  });
};

onShareAppMessage(() => {
  return {
    title: detail.value.title,
    path: `pageA/pageB/home/<USER>/detail?${utils.queryParams({
      ...state,
    })}`,
    imageUrl:
      'http://cdn.xiaodingdang1.com/2025/03/20/325053002d784109a28711387132373c.png',
    success: function (res) {
      console.log('success:' + JSON.stringify(res));
    },
    fail: function (err) {
      console.log('fail:' + JSON.stringify(err));
    },
  };
});
</script>
<style scoped lang="less">
cover-view {
  box-sizing: border-box;
}
.detail {
  height: 100%;
  width: 100%;
}

.footer {
  position: fixed;
  z-index: 999;
  width: 100vw;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 32rpx 24rpx;
  // 安全区域
  padding-bottom: calc(env(safe-area-inset-bottom) + 24rpx);

  background-color: white;
  box-shadow: 0 -3px 6px rgba(0, 0, 0, 0.5);
  .footer-info {
    display: flex;
    flex-direction: column;
    font-size: 20rpx;
    color: #666;
    line-height: 1.5;
  }
  .footer-operate {
    margin: 0 32rpx;
    display: flex;
    align-items: center;
    gap: 32rpx;

    .footer-operate-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 80rpx;
      font-size: 20rpx;
      color: #333333;
      line-height: 1 !important;

      .operate-icon {
        width: 32rpx;
        height: 32rpx;
      }

      .favorite-icon {
        width: 34rpx;
        height: 34rpx;
      }

      .share-text {
        line-height: 1.5;
      }

      .active {
        color: #ff4f61;
      }
    }
  }

  .share-btn {
    background-color: transparent !important;
  }

  .footer-btn {
    width: 360rpx;
    height: 72rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #ff4f61;
    border-radius: 100rpx 100rpx 100rpx 100rpx;

    font-size: 28rpx;
    color: #ffffff;
  }
}
</style>
