.contentTab {
    display: flex;
    padding: 24rpx 20rpx;
    margin-top: 10rpx;
    justify-content: space-around;
}

.title-sel-selected {
    color: #ff4f61;
    font-size: 32rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    font-weight: bold;
    margin-right: 36rpx;
}

.title-sel-selected .line-style {
    background: #ff4f61;
    height: 6rpx;
    width: 40rpx;
    position: relative;
    margin-top: 10rpx;
    border-radius: 20rpx;
}

.title-sel {
    color: #aaaaaa;
    font-size: 32rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    font-weight: bold;
    height: 56rpx;
    margin-right: 36rpx;
}

/*评论*/
.content {
    padding: 20rpx 24rpx;
}

.head {
    display: flex;
}

.heads {
    margin-left: 16rpx;
}

.head1 {
    display: flex;
}

.head1_1 {
    color: #ff6e00;
    font-size: 30rpx;
}

.head1_2 {
    display: flex;
    position: relative;
    margin-left: 16rpx;
}

.head1_3 {
    background: #ffeecc;
    padding: 0 8rpx 0 30rpx;
    border-radius: 10rpx;
    color: #fb4105;
    font-size: 24rpx;
    height: 34rpx;
}

.head1Img {
    position: absolute;
    left: -10rpx;
}

.head2 {
    color: #aaaaaa;
    font-size: 24rpx;
    margin-top: 15rpx;
}

.content1 {
    margin-top: 20rpx;
    color: #333333;
    font-size: 28rpx;
}

.content1_1 {
    font-weight: bold;
}

.content2 {
    background: #f6f6f6;
    display: flex;
    padding: 24rpx 24rpx;
    align-items: center;
    margin-top: 16rpx;
    border-radius: 20rpx;
}

.content2_2 {
    width: 494rpx;
    margin-left: 20rpx;
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    color: #777777;
    font-size: 28rpx;
}

.content3 {
    background: #f6f6f6;
    padding: 48rpx 24rpx;
    margin-top: 16rpx;
    border-radius: 20rpx;
    color: #777777;
    font-size: 28rpx;
}

.content3_1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
}

.content1_8 {
    display: flex;
    align-items: center;
    margin-top: 20rpx;
}

.content1_9 {
    font-size: 28rpx;
    color: #333333;
    margin-left: 8rpx;
}

.contents {
    margin-bottom: 36rpx;
}