<template>
	<view class="container">
		<map id="map" :latitude="centerLatitude" :longitude="centerLongitude" :markers="markers" scale="14"></map>
		<view class="container1" @click="handleLocationClick">
			<view class="container1_1">
				{{clubName}}
			</view>
			<view class="container2" >
				<image src="http://cdn.xiaodingdang1.com/2025/06/07/3d632413ec0446f888f3a5ad488ee14a.png" mode=""
					style="width: 24rpx;height: 24rpx;"></image>
				<view class="container2_1">
					{{address}}
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				clubName: '',
				address: '',
				centerLatitude: '',
				centerLongitude: '',
				markers: [{
					id: 1,
					latitude: '',
					longitude: '',
					title: '茶山馨苑',
					width: 30,
					height: 30,
				}, ],
			}
		},
		/**
		 * 生命周期函数--监听页面加载
		 */
		onLoad(options) {
			let item = JSON.parse(options.item)
			this.clubName = item.clubName
			this.address = item.address
			this.getgeocodes(item.address)
		},
		methods: {
			handleLocationClick(){
				
				let lng = this.centerLongitude;
				let lat = this.centerLatitude;
				let locationAddress=this.address
				uni.getLocation({
				  type: 'gcj02', //定位类型 wgs84, gcj02
				  success: function (res) {
				    uni.openLocation({
				      //当前经纬度
				      latitude: Number(lat),
				      longitude: Number(lng),
				      //缩放级别默认28
				      scale: 18,
				      //位置名
				      address: locationAddress,
				      success(res) {
				        console.log(res);
				      },
				      fail(err) {
				        console.log(err);
				      },
				    });
				  },
				  fail(err) {
				    console.log(err);
				  },
				});
			},
			getgeocodes(address) {
				//获取用户浏览记录-会所记录
				let data = {
					address: address,
				}
				this.$axios.get(this.$api.geocodes, data).then((res) => {
					if (res.data.code == 200) {
						let location = res.data.data.location.split(',');
					console.log(location);
						this.markers[0].latitude = location[1]
						this.markers[0].longitude = location[0]
						this.centerLatitude = location[1]
						this.centerLongitude = location[0]
					}
				});
			},
		}
	}
</script>

<style lang="less" scoped>
	@import './location.less';
</style>