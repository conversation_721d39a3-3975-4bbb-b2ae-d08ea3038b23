<template>
  <view class="upload">
    <view
      class="upload-list"
      v-for="(item, index) in imgList"
      :key="index"
      v-if="fileName == 'mp4'"
    >
      <view class="upload-video">
        <video
          :src="item"
          mode=""
          @tap="preview(index)"
          style="width: 100%; height: 100%"
        />
      </view>
      <view class="upload-delete" @tap.prevent="delVideoImage(index)">
        <icon type="clear" size="18"> </icon>
      </view>
    </view>
    <!-- 	<view class="upload-list" v-for="(item,index) in imgList" :key="index" v-else>
			<image class="upload-img" @tap="preview(index)" :src="item" mode=""></image>
			<view class="upload-delete" @tap.prevent="delVideoImage(index)">
				<icon type="clear" size="18">
				</icon>
			</view>
		</view> -->
    <view class="uploading" v-else>
      <!--      <view class="uploadingImg" v-for="(item, index) in imgUrl" :key="index">
		          <image :src="item" mode="" style="width: 198rpx; height: 198rpx; border: 1rpx solid #c2bfbf"
		              @tap="previewImage" :data-src="item" :data-url="imgUrl" />
		
		          <icon class="imgCancel" type="clear" size="25" :data-index="index" @tap.stop.prevent="deleteImg"></icon>
		      </view> -->
      <view @touchmove.stop.prevent="moveHandle" class="moveWrap" >
        <movable-area
          class="movarea"
          ref="areaBox"
          id="areaBox"
        >
          <view class="imgBox">
            <view
              :id="'img' + idx"
              class="imgItem"
              v-for="(item, idx) in imgList"
              :key="idx"
              :style="{
                transition: addJump ? ' all 0.5s' : '',
                opacity: idx === selectIdx ? '0' : '1',
                width: imgSize + 'rpx',
                height: imgSize + 'rpx',
                borderRadius: imgRadius + 'rpx',
                left:
                  hoverImgIdx === 'img' + idx ? curHoverBoxMove.x + 'rpx' : '',
                top:
                  hoverImgIdx === 'img' + idx ? curHoverBoxMove.y + 'rpx' : '',
              }"
            >
              <image
                :style="{ borderRadius: imgRadius + 'rpx' }"
                :ref="'img' + idx"
                :src="item.url"
                mode="aspectFill"
                @touchstart="tstr(idx, $event)"
                @touchmove="tsmo"
                @touchend="toend"
              >
              </image>
              <icon
                class="imgCancel"
                type="clear"
                size="25"
                :data-index="index"
                @tap.stop.prevent="delVideoImage(index)"
              ></icon>
            </view>
            <!-- 	<view class="addImg1" @tap="chooseImg" v-if="imgList.length < 6">
							<image src="http://cdn.xiaodingdang1.com/2024/05/07/866cb0bda2354e2e99cbe6c6f66572e7png"
								mode="" style="width: 84rpx; height: 84rpx; margin-top: 40rpx" />
							<view class="addImg">添加图</view>
						</view> -->
            <view class="upload-add" @click="chooseVideoImage">
              <image
                src="http://cdn.xiaodingdang1.com/2024/12/03/1922c494570546a3aef3ed4dec1348e6.png"
              ></image>
            </view>
          </view>
          <movable-view
            v-if="moveSrc"
            :animation="false"
            class="moveV"
            :x="x"
            :y="y"
            direction="all"
            @change="onChange"
            :style="{
              width: imgSize + 'rpx',
              height: imgSize + 'rpx',
              padding: imgPadding + 'rpx',
            }"
          >
            <image
              :style="{ borderRadius: imgRadius + 'rpx' }"
              :src="moveSrc"
              mode="aspectFill"
            ></image>
          </movable-view>
        </movable-area>
      </view>
    </view>
    <view class="upload-add" @click="chooseVideoImage" v-if="fileName == 'mp4'">
      <image
        src="http://cdn.xiaodingdang1.com/2024/12/03/1922c494570546a3aef3ed4dec1348e6.png"
      ></image>
    </view>
  </view>
</template>

<script>
// 上传通用组件 6张图片或者1个视频
export default {
  name: "upload",
  props: {
    list: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      areaBoxInfo: {},
      x: 0,
      y: 0,
      selectIdx: null,
      moveSrc: "",
      areaBoxTop: 0,
      hoverImgIdx: "",
      inBoxXY: {},
      curHoverBoxMove: {
        x: 20,
        y: 20,
      },
      imgSize: 200,
      rowNum: 1,
      screenWidth: 0,
      imgList: this.list,
      contentPhotos: [],
      fileName: "",
      fileIndex: 0,
      previewList: [],
    };
  },
  watch: {
    hoverImgIdx(e) {
      let idx = this.selectIdx;
      let hoverIdx = parseInt(e.split("img")[1]);
      if (this.imgList[idx]) {
        let selectRow = this.imgList[idx].y / uni.upx2px(this.imgSize);
        let selectColum = this.imgList[idx].x / uni.upx2px(this.imgSize);
        let hoverRow = this.imgList[hoverIdx].y / uni.upx2px(this.imgSize);
        let hoverColum = this.imgList[hoverIdx].x / uni.upx2px(this.imgSize);
        let left = -(this.imgSize * (hoverColum - selectColum));
        let top = -(this.imgSize * (hoverRow - selectRow));
        this.curHoverBoxMove = {
          x: left,
          y: top,
        };
      }
    },
    imgList(e) {
      this.imgList = e;
      this.rowNum = Math.ceil((e.length + 1) / this.colNum);
      let _this = this;
      this.$nextTick(function () {
        _this.getDomInfo("areaBox", (info) => {
          _this.areaBoxInfo = info;
          // 设置区域内所有图片的左上角坐标
          _this.imgList.forEach((item, idx) => {
            _this.getDomInfo("img" + idx, (res) => {
              item.x = res.left - info.left;
            });
            _this.getDomInfo("img" + idx, (res) => {
              item.y = res.top - info.top;
            });
          });
        });
      });
    },
  },
  methods: {
    getDomInfo(id, callBack) {
      const query = uni.createSelectorQuery().in(this);
      // console.log(query)
      query
        .select("#" + id)
        .boundingClientRect()
        .exec(function (res) {
          callBack(res[0]);
        });
    },
    tstr(e, s) {
      const _this = this;
      _this.addJump = true;
      _this.getDomInfo("areaBox", (info) => {
        _this.areaBoxInfo = info;
        // 设置区域内所有图片的左上角坐标
        _this.imgList.forEach((item, idx) => {
          _this.getDomInfo("img" + idx, (res) => {
            item.x = res.left - info.left;
          });
          _this.getDomInfo("img" + idx, (res) => {
            item.y = res.top - info.top;
          });
        });
      });
      //获取拖拽区域的上边距和下边距
      let areaBoxTop = this.areaBoxInfo.top;
      let areaBoxLeft = this.areaBoxInfo.left;
      // console.log(this.areaBoxInfo)

      // 设置可移动方块的初始位置为当前所选中图片的位置坐标
      this.x = this.imgList[e].x;
      this.y = this.imgList[e].y;
      //显示可移动方块
      this.moveSrc = this.imgList[e].url;
      //保存当前所选择的图片索引
      this.selectIdx = e;
      var x = s.changedTouches[0].clientX - areaBoxLeft;
      var y = s.changedTouches[0].clientY - areaBoxTop;
      // 保存鼠标在图片内的坐标
      this.inBoxXY = {
        x: x - this.imgList[e].x,
        y: y - this.imgList[e].y,
      };
    },
    tsmo(e) {
      const _this = this;
      let areaBoxTop = this.areaBoxInfo.top;
      let areaBoxLeft = this.areaBoxInfo.left;
      let imgSize = this.imgSize;
      //重置为以拖拽盒子左上角为坐标原点
      var x = e.changedTouches[0].clientX - areaBoxLeft;
      var y = e.changedTouches[0].clientY - areaBoxTop;
      this.x = x - this.inBoxXY.x;
      this.y = y - this.inBoxXY.y;

      this.imgList.forEach((item, idx) => {
        if (
          x > item.x &&
          x < item.x + imgSize &&
          y > item.y &&
          y < item.y + imgSize
        ) {
          _this.hoverImgIdx = "img" + idx;
        }
      });
    },
    toend(e) {
      const _this = this;
      _this.addJump = false;
      // 移动结束隐藏可移动方块
      let beforeIdx = _this.selectIdx;
      let afterIdx = parseInt(_this.hoverImgIdx.split("img")[1]);
      if (_this.hoverImgIdx !== "" && beforeIdx !== afterIdx) {
        _this.imgList[beforeIdx].url = _this.imgList[afterIdx].url;
        _this.imgList[afterIdx].url = _this.moveSrc;
        this.$emit("moveEndList", this.imgList);
      }
      this.moveSrc = "";
      this.hoverImgIdx = "";
      this.selectIdx = null;
    },
    chooseVideoImage() {
      let that = this;
      uni.showActionSheet({
        itemList: ["图片", "视频"],
        success: async (res) => {
          // console.log(res);
          if (res.tapIndex == 0) {
            if (this.imgList.length == 0) {
              await this.chooseImages();
            } else {
              (await this.clickImageGiveVideo(res.tapIndex))
                ? null
                : this.chooseImages();
            }
          } else {
            if (this.imgList.length == 0) {
              await this.chooseVideo();
            } else {
              (await this.clickImageGiveVideo(res.tapIndex))
                ? this.chooseVideo()
                : null;
            }
          }
        },
      });
    },

    clickImageGiveVideo(tapIndex) {
      if (this.imgList.length != 0) {
        let isVideo = false;
        this.imgList.forEach((item, index) => {
          let url = item.url;
          this.fileName = url.substring(url.lastIndexOf(".") + 1);
          console.log("this.fileName____________>", this.fileName);
          if (this.fileName == "mp4") {
            isVideo = true;
            if (tapIndex == 0) {
              uni.showToast({
                title: "只能选择图片或视频",
                icon: "none",
              });
            } else {
              uni.showToast({
                title: "只能上传一个视频",
                icon: "none",
              });
              isVideo = false;
            }
          } else {
            isVideo = false;
            if (tapIndex === 1) {
              uni.showToast({
                title: "只能选择图片或视频",
                icon: "none",
              });
            }
          }
        });
        return isVideo;
      }
    },

    delVideoImage(index) {
      this.imgList.splice(index, 1);
      this.contentPhotos.splice(index, 1);
      this.previewList.splice(index, 1);
      this.$emit("clickFile", this.previewList);
    },
    preview(index) {
      // 既有视频又有图片用这个
      uni.previewMedia({
        sources: this.previewList,
        // 官方文档 : 素材展示列表 是个数组对象 一个url链接 一个type 判断这是视频还是图片
        // 接收image 和 video 所以处理数据时 最好给个type 指定 image 或者 video
        current: index,
      });
    },
    chooseImages: function (e) {
      var that = this;
      var contentPhotos = that.contentPhotos;
      if (contentPhotos.length >= 6) {
        uni.showToast({
          icon: "none",
          title: "最多上传6张图片",
        });
        return false;
      }
      uni.chooseImage({
        // count: 1, // 默认9
        sizeType: ["compressed"],
        // 可以指定是原图还是压缩图，默认二者都有
        sourceType: ["album", "camera"],
        // 可以指定来源是相册还是相机，默认二者都有
        success: function (res) {
          var tempFilePaths = res.tempFilePaths;
          var contentPhotos = that.contentPhotos;
          let picNum = tempFilePaths.length;
          uni.showLoading({
            title: "图片上传中",
          });
          for (var i = 0; i < tempFilePaths.length; i++) {
            if (contentPhotos.length >= 6) {
              that.setData({
                contentPhotos: contentPhotos,
              });
              return false;
            } else {
              contentPhotos.push(tempFilePaths[i]);
              uni.uploadFile({
                filePath: tempFilePaths[i],
                name: "file",
                url: that.$api.uploadOSS,
                formData: {},
                header: {
                  "Content-Type": "multipart/form-data",
                  Authorization: "Bearer " + uni.getStorageSync("token"),
                  "Accept-Encoding": "gzip",
                  Clientid: "428a8310cd442757ae699df5d894f051",
                },
                success: function (rests) {
                  let data = JSON.parse(rests.data);
                  if (data.code == 200) {
                    let datas = that.imgList;
                    datas.push({
                      url: data.data.url,
                    });
                    let preview = [];
                    datas.forEach((url) => {
                      preview.push({
                        type: "image",
                        url,
                      });
                    });
                    that.$emit("clickFile", preview);
                    console.log(datas);
                    that.setData({
                      imgList: datas,
                      previewList: preview,
                      fileName: "image",
                    });
                  }
                  uni.hideLoading();
                },
              });
            }
          }
          that.setData({
            contentPhotos: contentPhotos,
          });
        },
      });
    },
    chooseVideo() {
      var that = this;
      uni.chooseVideo({
        maxDuration: 30,
        count: 1,
        mediaType: ["video", "image"],
        sourceType: ["album", "camera"],
        camera: "back",
        success(res) {
          uni.showLoading({
            title: "视频上传中",
          });
          var tempFiles = res.tempFilePath;
          let videos = [];
          uni.uploadFile({
            filePath: tempFiles,
            name: "file",
            url: that.$api.uploadOSS,
            header: {
              "Content-Type": "multipart/form-data",
              Authorization: "Bearer " + uni.getStorageSync("token"),
              "Accept-Encoding": "gzip",
              Clientid: "428a8310cd442757ae699df5d894f051",
            },
            success: function (rests) {
              let data = JSON.parse(rests.data);
              if (data.code == 200) {
                uni.hideLoading();
                let videoList = [];
                let preview = [];
                if (data.code == 200) {
                  videoList.push(data?.data?.url);
                  videoList.forEach((item, index) => {
                    let url = item;
                    that.fileName = url.substring(url.lastIndexOf(".") + 1);
                    console.log(index);
                    that.fileIndex = index;
                    preview.push({
                      type: "video",
                      url,
                    });
                  });
                  that.$emit("clickFile", preview);
                  that.setData({
                    imgList: videoList,
                    previewList: preview,
                  });
                }
              }
            },
          });
        },
      });
      that.fileName = "";
    },
  },
};
</script>

<style lang="less" scoped>
.upload {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  width: 100%;
  &-list {
    margin-right: 35rpx;
    margin-bottom: 20rpx;
    position: relative;
  }

  &-video {
    height: 200rpx;
    width: 200rpx;
  }

  &-img {
    height: 200rpx;
    width: 200rpx;
  }

  &-delete {
    position: absolute;
    top: -22rpx;
    right: -22rpx;
  }

  &-add {
    image {
      height: 200rpx;
      width: 200rpx;
    }
    z-index: 1;
    margin-bottom: 20rpx;
  }
}

.head {
  display: flex;
  justify-content: space-between;
}

.cancel {
  color: #333333;
  font-size: 28rpx;
}

.publish {
  width: 96rpx;
  text-align: center;
  height: 56rpx;
  line-height: 56rpx;
  background: #7e6dfc;
  color: #ffffff;
  border-radius: 10rpx;
  font-size: 28rpx;
}

.uploading {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 20rpx;
}

.uploadingImg {
  margin-right: 20rpx;
  position: relative;
}

.topicName {
  color: #5b6799;
  font-size: 28rpx;
  width: 100%;
}

.topic {
  display: flex;
  margin-top: 48rpx;
}

.imgCancel {
  position: absolute;
  top: 0;
  right: 0;
  background: #fff;
  border-radius: 50rpx;
}

.addImg1 {
  width: 200rpx;
  height: 200rpx;
  border: 2rpx dashed #aaaaaa;
  text-align: center;
  margin-right: 20rpx;
  z-index: 999;
}

.addImg {
  font-size: 24rpx;
  color: #aaaaaa;
}

.textarea {
  margin-top: 20rpx;
  width: 100%;
}
.moveWrap {
  width: 100%;
}
.movarea {
  width: 100%;
  height: fit-content;
  display: flex;
  flex-direction: row;
  margin-bottom: 50rpx;
}
.moveV {
  opacity: 0.6;
  z-index: 999;
  box-sizing: border-box;
}
.select {
  opacity: 0;
}

.addImg {
  font-size: 24rpx;
  color: #aaaaaa;
}

.imgBox {
  width: 100%;
  height: fit-content;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
}
.imgItem {
  position: relative;
  box-sizing: border-box;
  left: 0;
  top: 0;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
}
.imgCancel {
  position: absolute;
  top: 0;
  right: 0;
  background: #fff;
  border-radius: 50rpx;
}
.imgItem image {
  width: 100%;
  height: 100%;
  vertical-align: top;
}
.upload-list {
  display: flex;
}
</style>
