<template>
  <view
    class="chat-input-container"
    :style="{
      paddingBottom: inputBottomDistance + 'px',
    }"
  >
    <view class="input-wrapper">
      <view class="input-tools">
        <view class="tool-item" @click="handleImageClick">
          <u-icon name="camera" size="40" color="#4ecdc4"></u-icon>
        </view>
      </view>
      <input
        v-model="inputValue"
        class="message-input"
        placeholder="请输入消息..."
        :focus="inputFocus"
        :adjust-position="false"
        @focus="handleInputFocus"
        @blur="handleInputBlur"
        @confirm="handleSendMessage"
        confirm-type="send"
      />
      <view
        class="send-btn"
        :class="{ active: inputValue.trim() }"
        @click="handleSendMessage"
      >
        <u-icon
          v-if="inputValue.trim()"
          name="arrow-right"
          size="32"
          color="#ffffff"
        ></u-icon>
        <text v-else class="send-text">发送</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, watch, computed, onMounted, onUnmounted } from 'vue';

// Props
const props = defineProps({
  modelValue: {
    type: String,
    default: '',
  },
  focus: {
    type: Boolean,
    default: false,
  },
});

// Emits
const emit = defineEmits([
  'update:modelValue',
  'update:focus',
  'send',
  'image-upload', // 图片上传事件，返回文件路径
  'focus',
  'blur',
]);

// 响应式数据
const inputValue = ref(props.modelValue);
const inputFocus = ref(props.focus);

// 键盘高度管理
const keyboardHeight = ref(0);
// 安全区域底部高度
const safeAreaBottom = ref(0);

// 监听 props 变化
watch(
  () => props.modelValue,
  (newValue) => {
    inputValue.value = newValue;
  },
);

watch(
  () => props.focus,
  (newValue) => {
    inputFocus.value = newValue;
  },
);

// 监听输入值变化，同步到父组件
watch(inputValue, (newValue) => {
  emit('update:modelValue', newValue);
});

// 监听焦点状态变化，同步到父组件
watch(inputFocus, (newValue) => {
  emit('update:focus', newValue);
});

// 事件处理
const handleSendMessage = () => {
  const content = inputValue.value.trim();
  if (!content) return;

  emit('send', content);
  inputValue.value = '';
};

const handleInputFocus = () => {
  inputFocus.value = true;
  emit('focus');
};

const handleInputBlur = () => {
  inputFocus.value = false;
  emit('blur');
};

const handleImageClick = () => {
  uni.chooseImage({
    count: 1,
    sizeType: ['compressed'],
    sourceType: ['album', 'camera'],
    success: (res) => {
      const filePath = res.tempFilePaths[0];
      console.log('选择图片:', filePath);

      // 只负责选择图片，将文件路径传递给父组件处理
      emit('image-upload', filePath);
    },
    fail: (error) => {
      console.error('选择图片失败:', error);
    },
  });
};

// 计算输入框底部距离
const inputBottomDistance = computed(() => {
  if (keyboardHeight.value > 0) {
    // 有键盘时，使用键盘高度
    return keyboardHeight.value + 10;
  } else {
    // 无键盘时，使用安全区域底部距离
    return safeAreaBottom.value || 10;
  }
});

// 键盘监听
const setupKeyboardListener = () => {
  uni.onKeyboardHeightChange((res) => {
    console.log('键盘高度变化:', res.height);
    keyboardHeight.value = res.height;
  });
};

const removeKeyboardListener = () => {
  uni.offKeyboardHeightChange();
};

// 获取安全区域信息
const getSafeAreaInfo = () => {
  const systemInfo = uni.getSystemInfoSync();
  safeAreaBottom.value = systemInfo.safeAreaInsets?.bottom || 0;
  console.log('ChatInput获取安全区域底部高度:', safeAreaBottom.value);
};

// 生命周期
onMounted(() => {
  getSafeAreaInfo();
  setupKeyboardListener();
});

onUnmounted(() => {
  removeKeyboardListener();
});

// 暴露方法给父组件
defineExpose({
  insertText: (text) => {
    inputValue.value += text;
  },
  clearInput: () => {
    inputValue.value = '';
  },
  focusInput: () => {
    inputFocus.value = true;
  },
  blurInput: () => {
    inputFocus.value = false;
  },
});
</script>

<style lang="scss" scoped>
.chat-input-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100vw;
  background: #ffffff;
  border-top: 1rpx solid #e5e5e5;
  padding: 20rpx 24rpx;
  transition: all 0.2s ease;
  z-index: 999;

  .input-wrapper {
    display: flex;
    align-items: center;
    background: #f5f5f5;
    border-radius: 24rpx;
    padding: 8rpx 16rpx;
    border: 1rpx solid #e5e5e5;
    transition: all 0.2s ease;

    &:focus-within {
      border-color: #007aff;
      background: #ffffff;
    }

    .input-tools {
      display: flex;
      align-items: center;
      margin-right: 16rpx;

      .tool-item {
        width: 64rpx;
        height: 64rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 8rpx;
        border-radius: 50%;
        background: #ffffff;
        border: 1rpx solid #e5e5e5;
        transition: all 0.2s ease;

        &:active {
          transform: scale(0.95);
          background: #f0f0f0;
        }

        &:last-child {
          margin-right: 0;
        }
      }
    }

    .message-input {
      flex: 1;
      font-size: 32rpx;
      line-height: 1.4;
      background-color: transparent;
      border: none;
      outline: none;
      color: #333333;
      padding: 12rpx 0;

      &::placeholder {
        color: #999999;
      }
    }

    .send-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      min-width: 100rpx;
      height: 64rpx;
      border-radius: 20rpx;
      margin-left: 16rpx;
      transition: all 0.2s ease;

      .send-text {
        font-size: 26rpx;
        color: #999999;
      }

      &.active {
        background: #007aff;
        color: #ffffff;

        &:active {
          transform: scale(0.95);
          background: #0056cc;
        }
      }

      &:not(.active) {
        background: #f0f0f0;
        border: 1rpx solid #e5e5e5;

        &:active {
          transform: scale(0.95);
          background: #e5e5e5;
        }
      }
    }
  }
}
</style>
