#!/usr/bin/env node

/**
 * 调试版预览脚本
 * 用于诊断页面路径问题
 */

const ci = require('miniprogram-ci');
const path = require('path');
const fs = require('fs');
const { projects, defaultConfig, getProject, validateProject } = require('../config/config-loader.js');

// 命令行参数解析
const args = process.argv.slice(2);

// 解析命令行参数
function parseArgs(args) {
  const result = {
    projectName: null,
    environment: null,
    pagePath: null,
    searchQuery: null
  };

  let positionalIndex = 0;
  let i = 0;
  
  while (i < args.length) {
    const arg = args[i];
    
    if (arg === '--page' && i + 1 < args.length) {
      result.pagePath = args[i + 1];
      i += 2;
    } else if (arg === '--query' && i + 1 < args.length) {
      result.searchQuery = args[i + 1];
      i += 2;
    } else if (arg === '--help' || arg === '-h') {
      result.showHelp = true;
      i++;
    } else if (!arg.startsWith('--')) {
      // 位置参数
      if (positionalIndex === 0) {
        result.projectName = arg;
      } else if (positionalIndex === 1) {
        result.environment = arg;
      }
      positionalIndex++;
      i++;
    } else {
      i++;
    }
  }

  // 设置默认值
  result.projectName = result.projectName || 'main';
  result.environment = result.environment || 'develop';

  return result;
}

const parsedArgs = parseArgs(args);
const projectName = parsedArgs.projectName;
const environment = parsedArgs.environment;
const pagePath = parsedArgs.pagePath;
const searchQuery = parsedArgs.searchQuery;

// 颜色输出
function logInfo(message) {
  console.log(`ℹ️  ${message}`);
}

function logError(message) {
  console.log(`❌ ${message}`);
}

function logSuccess(message) {
  console.log(`✅ ${message}`);
}

function logDebug(message) {
  console.log(`🔍 ${message}`);
}

/**
 * 验证页面路径是否存在
 */
function validatePagePath(pagePath) {
  if (!pagePath) {
    return { valid: false, reason: '页面路径为空' };
  }

  // 检查pages.json中是否存在该页面
  const pagesJsonPath = path.resolve(__dirname, '../../src/pages.json');
  if (!fs.existsSync(pagesJsonPath)) {
    return { valid: false, reason: 'pages.json文件不存在' };
  }

  try {
    const pagesConfig = JSON.parse(fs.readFileSync(pagesJsonPath, 'utf8'));
    
    // 检查主包页面
    const mainPages = pagesConfig.pages || [];
    const isMainPage = mainPages.some(page => page.path === pagePath);
    
    if (isMainPage) {
      return { valid: true, type: 'main', reason: '主包页面' };
    }

    // 检查分包页面
    const subPackages = pagesConfig.subPackages || pagesConfig.subpackages || [];
    for (const subPackage of subPackages) {
      const subPages = subPackage.pages || [];
      const fullPath = subPages.some(page => `${subPackage.root}/${page.path}` === pagePath);
      if (fullPath) {
        return { valid: true, type: 'subpackage', reason: `分包页面 (${subPackage.root})` };
      }
    }

    return { valid: false, reason: '页面在pages.json中不存在' };
  } catch (error) {
    return { valid: false, reason: `解析pages.json失败: ${error.message}` };
  }
}

/**
 * 调试预览生成
 */
async function debugPreview() {
  try {
    logInfo('🔍 调试预览生成');
    logInfo('='.repeat(50));

    // 显示解析的参数
    logDebug(`项目名称: ${projectName}`);
    logDebug(`环境: ${environment}`);
    logDebug(`页面路径: ${pagePath || '未指定'}`);
    logDebug(`查询参数: ${searchQuery || '未指定'}`);

    // 验证项目配置
    const validation = validateProject(projectName);
    if (!validation.valid) {
      logError(validation.error);
      return;
    }

    const project = getProject(projectName);
    const envConfig = project.environments[environment];
    
    if (!envConfig) {
      logError(`环境 "${environment}" 不存在`);
      return;
    }

    // 确定最终使用的页面路径
    const finalPagePath = pagePath || defaultConfig.preview.pagePath;
    logDebug(`最终页面路径: ${finalPagePath}`);

    // 验证页面路径
    const pageValidation = validatePagePath(finalPagePath);
    logDebug(`页面路径验证: ${pageValidation.valid ? '✅' : '❌'} ${pageValidation.reason}`);

    if (!pageValidation.valid) {
      logError(`页面路径无效: ${pageValidation.reason}`);
      logInfo('建议检查pages.json中的页面配置');
      return;
    }

    // 检查构建产物
    const projectPath = path.resolve(__dirname, '../../dist/build/mp-weixin');
    if (!fs.existsSync(projectPath)) {
      logError('构建产物不存在，请先运行构建');
      return;
    }

    // 检查私钥文件
    if (!fs.existsSync(project.privateKeyPath)) {
      logError(`私钥文件不存在: ${project.privateKeyPath}`);
      return;
    }

    logInfo('开始生成预览...');

    // 创建项目实例
    const miniProgram = new ci.Project({
      appid: project.appid,
      type: 'miniProgram',
      projectPath: projectPath,
      privateKeyPath: project.privateKeyPath,
      ignores: project.ignores || defaultConfig.ignores
    });

    // 构建预览选项
    const previewOptions = {
      project: miniProgram,
      version: '0.0.1',
      desc: `${project.name} - ${envConfig.name} 调试预览`,
      robot: defaultConfig.preview.robot,
      setting: {
        es6: true,
        minifyJS: true,
        minifyWXML: true,
        minifyWXSS: true,
        autoPrefixWXSS: true
      },
      qrcodeFormat: 'image',
      qrcodeOutputDest: path.resolve(__dirname, '../preview/debug-preview.jpg'),
      pagePath: finalPagePath,
      scene: defaultConfig.preview.scene,
      onProgressUpdate: (progress) => {
        console.log(`进度: ${JSON.stringify(progress)}`);
      }
    };

    // 添加查询参数
    if (searchQuery) {
      previewOptions.searchQuery = searchQuery;
    }

    logDebug('预览选项:');
    logDebug(`  页面路径: ${previewOptions.pagePath}`);
    logDebug(`  场景值: ${previewOptions.scene}`);
    logDebug(`  查询参数: ${previewOptions.searchQuery || '无'}`);

    // 确保temp目录存在
    const tempDir = path.dirname(previewOptions.qrcodeOutputDest);
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    const result = await ci.preview(previewOptions);
    
    logSuccess('预览生成成功！');
    logInfo(`二维码保存到: ${previewOptions.qrcodeOutputDest}`);
    
    // 显示详细信息
    if (result.subPackageInfo) {
      logInfo('分包信息:');
      result.subPackageInfo.forEach(pkg => {
        console.log(`  ${pkg.name}: ${pkg.size}KB`);
      });
    }

    // 验证二维码是否真的跳转到指定页面
    logInfo('');
    logInfo('🎯 预览验证建议:');
    logInfo('1. 扫描二维码后检查是否跳转到正确页面');
    logInfo(`2. 预期页面: ${finalPagePath}`);
    if (searchQuery) {
      logInfo(`3. 预期参数: ${searchQuery}`);
    }

  } catch (error) {
    logError(`预览生成失败: ${error.message}`);
    
    if (error.message.includes('exceed max limit')) {
      logError('包大小超出限制，请优化包大小');
    } else if (error.message.includes('page not found')) {
      logError('页面未找到，请检查页面路径是否正确');
    }
  }
}

// 显示帮助信息
function showHelp() {
  console.log('调试版预览脚本');
  console.log('');
  console.log('使用方法:');
  console.log('  node ci/scripts/debug-preview.js [项目名] [环境] [选项]');
  console.log('');
  console.log('选项:');
  console.log('  --page <路径>    指定预览页面路径');
  console.log('  --query <参数>   页面查询参数');
  console.log('  --help, -h       显示帮助信息');
  console.log('');
  console.log('示例:');
  console.log('  node ci/scripts/debug-preview.js main trial --page pageA/home');
  console.log('');
}

// 主函数
async function main() {
  if (parsedArgs.showHelp) {
    showHelp();
    return;
  }

  await debugPreview();
}

// 运行主函数
if (require.main === module) {
  main().catch(error => {
    logError(`执行失败: ${error.message}`);
    process.exit(1);
  });
}
