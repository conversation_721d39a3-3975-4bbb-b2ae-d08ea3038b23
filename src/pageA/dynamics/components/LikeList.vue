<template>
  <view class="like-list">
    <view class="like-list__content">
      <view class="like-list__icon">
        <image
          lazy-load
          :src="ICONS.LIKE_LIST"
          class="like-list__like-icon"
        />
      </view>
      
      <!-- 少于15个用户时的显示 -->
      <view 
        class="like-list__avatars"
        v-if="likeUsers.length < 15"
      >
        <image
          class="like-list__avatar"
          :class="{ 'like-list__avatar--stacked': index > 0 }"
          :style="index > 0 ? `left: ${index * 36}rpx; z-index: ${index}` : ''"
          lazy-load
          :src="user.avatar || $defaultAvatar"
          v-for="(user, index) in likeUsers"
          :key="index"
        />
      </view>
      
      <!-- 超过14个用户时的显示 -->
      <view 
        class="like-list__avatars"
        v-if="likeUsers.length > 14"
      >
        <template v-if="likeUsers.isLikeShow">
          <image
            class="like-list__avatar"
            lazy-load
            :src="user.avatar || $defaultAvatar"
            v-for="(user, index) in likeUsers"
            :key="index"
          />
        </template>
        <template v-else>
          <image
            class="like-list__avatar like-list__avatar--stacked"
            :style="index > 0 ? `left: ${index * 36}rpx; z-index: ${index}` : ''"
            lazy-load
            :src="user.avatar || $defaultAvatar"
            v-for="(user, index) in likeUsers.slice(-14)"
            :key="index"
          />
        </template>
        
        <view class="like-list__count" v-if="likeUsers.length > 0">
          <view
            class="like-list__toggle"
            @tap="handleLikeShow"
            :data-index="index"
          >
            <image
              :class="{ 'like-list__arrow--right': !likeUsers.isLikeShow }"
              :src="ICONS.ARROW"
              class="like-list__arrow"
            />
            <view class="like-list__count-text">
              <template v-if="!likeUsers.isLikeShow">
                {{ likeUsers.length }}人
              </template>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ICONS } from '../assets.js'

const props = defineProps({
  likeUsers: {
    type: Array,
    required: true
  },
  index: {
    type: Number,
    required: true
  }
})

const emit = defineEmits(['like-show'])

const handleLikeShow = (e) => {
  emit('like-show', e)
}
</script>

<style scoped lang="less">
.like-list {
  background: #f7f7f7;
  border-radius: 20rpx;
  padding: 20rpx;

  &__content {
    display: flex;
    align-items: center;
  }

  &__icon {
    line-height: 68rpx;
  }

  &__like-icon {
    width: 28rpx;
    height: 28rpx;
  }

  &__avatars {
    position: relative;
    padding-left: 8rpx;
    flex: 1;
  }

  &__avatar {
    width: 68rpx;
    height: 68rpx;
    border-radius: 68rpx;

    &--stacked {
      position: absolute;
    }
  }

  &__count {
    position: absolute;
    left: 560rpx;
    bottom: 0;
  }

  &__toggle {
    display: flex;
    align-items: center;
    font-size: 24rpx;
    color: #777777;
    position: absolute;
  }

  &__arrow {
    width: 42rpx;
    height: 22rpx;
    position: absolute;
    left: 0;
    top: -30rpx;

    &--right {
      left: 52rpx !important;
    }
  }

  &__count-text {
    position: absolute;
    left: 35rpx;
    top: -40rpx;
    width: max-content;
  }
}
</style>
