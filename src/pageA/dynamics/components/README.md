# Dynamics 页面组件

本目录包含了会所动态页面拆分出来的各个组件，采用了优化的命名规范和代码结构。

## 文件结构

```
src/pageA/dynamics/
├── components/           # 组件目录
│   ├── ClubHeader.vue   # 会所头部组件
│   ├── DynamicItem.vue  # 动态项目组件（已优化）
│   ├── DynamicsList.vue # 动态列表组件

│   ├── ContentArea.vue  # 内容区域组件（新增）
│   ├── LikeUsersList.vue # 点赞用户列表组件（重构）
│   ├── FloatingButtons.vue # 浮动按钮组件
│   └── README.md        # 组件说明文档
├── constants.js         # 常量定义
├── utils.js            # 工具函数
├── assets.js           # 图标和资源管理
└── index.vue           # 主页面（待重构）
```

## 组件列表

### 1. ClubHeader.vue
**功能**: 会所头部展示组件

**Props**:
- `clubInfo`: 会所信息对象

**内部功能**:
- 展示会所背景图片
- 显示会所名称和标签

**样式类名**:
- `.club-header`: 容器
- `.club-header__overlay`: 遮罩层
- `.club-header__title`: 标题
- `.club-header__tags`: 标签容器

### 2. DynamicItem.vue（已优化）
**功能**: 单个动态项目组件

**Props**:
- `dynamicData`: 动态数据对象（保持原有命名）
- `index`: 项目索引（保持原有命名）
- `likeUsers`: 点赞用户列表（保持原有命名）
- `showPoster`: 是否显示海报功能（保持原有命名）
- `showPushMessage`: 是否显示消息推送功能（保持原有命名）

**优化内容**:
- ✅ 移除懒加载逻辑，提升性能
- ✅ 保持参数命名一致性，避免破坏性变更
- ✅ 重构组件结构，职责更清晰
- ✅ 添加详细的 JSDoc 注释
- ✅ 改进样式设计，增强用户体验
- ✅ 拆分子组件，提高复用性
- ✅ 修复 postId 类型验证，支持 string|number
- ✅ 移除评论相关逻辑，简化组件结构

**Events**:
- `like`: 点赞操作事件
- `more-actions`: 操作菜单切换事件
- `update-dynamic`: 更新动态状态事件

**内部处理的功能**:
- ✅ 内容详情跳转（不再向外emit）
- ✅ 点赞列表展开/收起（子组件内部处理）

### 3. ContentArea.vue（重构）
**功能**: 动态内容展示组件（基于原型 imgContentArea）

**Props**:
- `listData`: 列表数据对象（与原型保持一致）
- `itemIndex`: 懒加载索引（与原型保持一致）
- `showContent`: 是否控制展开内容（与原型保持一致）
- `pageType`: 页面类型，支持 'note' 模式（与原型保持一致）

**核心功能**:
- ✅ 文本内容展示，支持 HTML 渲染
- ✅ 图片直接显示和预览功能
- ✅ 视频播放和预览功能
- ✅ 内容展开/收起功能
- ✅ 图片质量优化（OSS 处理）
- ✅ 内部处理详情跳转（不向外emit）
- ❌ 已移除懒加载逻辑，提升性能

**数据字段**:
- `content`: 文本内容
- `contentPhotos`: 图片数组
- `videos`: 视频数组
- `moreBtns`: 展开状态
- `packUpShow`: 收起状态

### 4. LikeUsersList.vue（重构）
**功能**: 点赞用户列表组件

**Props**:
- `likeUsers`: 点赞用户数组
- `itemIndex`: 项目索引
- `maxDisplayCount`: 最大显示数量

**优化内容**:
- ✅ 支持展开/收起功能（内部处理）
- ✅ 头像堆叠显示效果
- ✅ 响应式设计
- ✅ 不向外emit事件，完全自包含

### 5. DynamicsList.vue
**功能**: 动态列表容器组件

**Props**:
- `dynamicsList`: 动态列表数据
- `likeList`: 点赞列表数据
- `showPoster`: 是否显示海报功能
- `showPushMessage`: 是否显示消息推送功能

**内部功能**:
- 管理动态列表的状态更新
- 处理点赞操作的API调用
- 协调各个子组件的交互
- 提供关闭所有操作菜单的方法（滚动时自动关闭）

**Events**:
- `update-dynamic`: 更新动态数据
- `update-like-list`: 更新点赞列表
- `hide-shadow`: 隐藏遮罩

### 6. FloatingButtons.vue
**功能**: 浮动按钮组件

**Props**:
- `scrollTop`: 滚动位置

**内部功能**:
- 分享功能（内部处理）
- 回到顶部功能（内部处理）

## 优化内容

### 命名优化
- **组件命名**: 使用语义化的组件名，如 `DynamicItem`、`ActionMenu`、`LikeList` 等
- **CSS类名**: 采用 BEM 命名规范，如 `.dynamic-item__header`、`.action-menu__item` 等
- **函数命名**: 使用动词开头的命名，如 `handleLike`、`handlePoster` 等

### 代码结构优化
- **常量提取**: 将配置项和常量提取到 `constants.js` 文件
- **工具函数**: 将通用逻辑提取到 `utils.js` 文件
- **资源管理**: 将图标和静态资源统一管理到 `assets.js` 文件
- **组件拆分**: 将复杂页面拆分为多个职责单一的组件

### 逻辑优化
- **事件处理**: 将事件处理逻辑内置到组件中，减少父子组件耦合
- **状态管理**: 优化状态变量的命名和使用
- **API调用**: 统一API调用逻辑和错误处理
- **用户体验**: 滚动时自动关闭操作菜单，提升交互体验

## 使用方式

```vue
<template>
  <view>
    <!-- 会所头部 -->
    <ClubHeader :clubInfo="clubInfo" />
    
    <!-- 动态列表 -->
    <DynamicsList
      :dynamicsList="postPageList"
      :likeList="likeList"
      :showPoster="posterIf"
      :showPushMessage="pushMessageIf"
      @update-dynamic="updateDynamic"
      @update-like-list="updateLikeList"
      @show-comment-input="showCommentInput"
      @hide-shadow="hideShadow"
    />
    
    <!-- 评论输入 -->
    <CommentInput
      :visible="showInput"
      :postId="replyPostId"
      @close="closeCommentInput"
      @success="onCommentSuccess"
    />
    
    <!-- 浮动按钮 -->
    <FloatingButtons :scrollTop="scrollTop" />
    
    <!-- 遮罩层 -->
    <view class="shadow" v-show="shadowIf" @tap="hideShadow"></view>
  </view>
</template>
```

## 设计优势

### 1. 组件化程度高
- 每个组件职责单一，便于维护和测试
- 组件可以独立复用

### 2. 内部逻辑封装
- 组件内部处理自己的业务逻辑
- 减少了父子组件之间的事件传递

### 3. 代码可读性强
- 语义化的命名让代码意图更清晰
- BEM命名规范提高CSS可维护性

### 4. 易于扩展
- 常量和工具函数便于功能扩展
- 组件化结构支持渐进式重构
 