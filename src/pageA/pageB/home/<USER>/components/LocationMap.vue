<template>
  <view class="form-item map-preview-item" v-if="latitude && longitude">
    <view class="label">{{ title }}</view>
    <view class="map-container">
      <map
        class="map"
        :latitude="Number(latitude)"
        :longitude="Number(longitude)"
        :markers="mapMarkers"
        :scale="scale"
        show-location
      ></map>
      <view class="map-address">
        <uni-icons type="location-filled" size="16" color="#f56c6c"></uni-icons>
        <text class="address-text">{{ address }}</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { computed } from 'vue';

// 接收属性
const props = defineProps({
  // 地图标题
  title: {
    type: String,
    default: '地图预览'
  },
  // 纬度
  latitude: {
    type: [Number, String],
    default: ''
  },
  // 经度
  longitude: {
    type: [Number, String],
    default: ''
  },
  // 地址文本
  address: {
    type: String,
    default: ''
  },
  // 地图缩放级别
  scale: {
    type: Number,
    default: 16
  }
});

// 计算地图标记点
const mapMarkers = computed(() => {
  if (!props.latitude || !props.longitude) return [];
  
  return [{
    id: 1,
    latitude: Number(props.latitude),
    longitude: Number(props.longitude),
    title: props.address,
    // 使用系统默认图标
    width: 24,
    height: 24,
    callout: {
      content: props.address || '已选择位置',
      color: '#333333',
      fontSize: 12,
      borderRadius: 4,
      padding: 5,
      bgColor: '#ffffff',
      display: 'ALWAYS'
    }
  }];
});
</script>

<style lang="less" scoped>
.map-preview-item {
  background-color: #fff;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 0;

  .label {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 10px;
  }

  .map-container {
    position: relative;
    height: 180px;
    border-radius: 8px;
    overflow: hidden;

    .map {
      width: 100%;
      height: 100%;
    }

    .map-address {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      background-color: rgba(0, 0, 0, 0.5);
      padding: 5px 10px;
      border-radius: 0 0 8px 8px;
      display: flex;
      align-items: center;

      .address-text {
        color: #fff;
        font-size: 14px;
        margin-left: 5px;
      }
    }
  }
}
</style> 