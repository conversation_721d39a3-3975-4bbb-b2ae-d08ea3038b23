<template>
  <view class="related-cases-list-page">
    <!-- AI助理消息 -->
    <view class="ai-message-container">
      <view class="ai-message-card">
        <view class="ai-avatar-section">
          <view class="ai-avatar-container">
            <image src="/static/images/chat/hi.png" mode="scaleToFill" />
          </view>
          <view class="ai-greeting">您好，我是你的智能AI助理</view>
        </view>
        <view class="ai-message-content">
          <view class="ai-result-text"
            >根据您的需求，我们已完成相关案例检索，共收集{{
              casesList.length
            }}个典型案例。以下为整理结果。</view
          >
        </view>
      </view>
    </view>

    <!-- 案例列表 -->
    <view class="cases-list">
      <view
        v-for="(caseItem, index) in casesList"
        :key="caseItem.diaryId || index"
        class="case-item"
        @click="goToCaseDetail(caseItem)"
      >
        <!-- 员工头像和姓名 -->
        <view class="case-header">
          <view class="user-avatar">
            <image
              :src="caseItem.staffPhotos || '/static/images/tabbar/mine.png'"
              class="avatar-img"
            />
          </view>
          <view class="user-name">{{ caseItem.staffName || '员工' }}</view>
          <!-- 标签 -->

          <view class="case-tag">
            <image src="/static/images/chat/top.png" mode="scaleToFill" />
            {{ caseItem.staffPost || '孕产医师' }}
          </view>
        </view>

        <!-- 案例内容 -->
        <view class="case-content">
          <text class="case-text u-line-4">{{
            caseItem.content || '案例内容'
          }}</text>
        </view>

        <!-- 案例图片 -->
        <view
          v-if="caseItem.imgs && caseItem.imgs.length > 0"
          class="case-images"
        >
          <view
            v-for="(img, imgIndex) in caseItem.imgs.slice(0, 4)"
            :key="imgIndex"
            class="case-image-container"
            :class="{
              'has-overlay': imgIndex === 3 && caseItem.imgs.length > 4,
            }"
          >
            <image
              :src="img"
              class="case-image"
              mode="aspectFill"
              @error="handleImageError"
            />
            <!-- 显示更多图片数量 -->
            <view
              v-if="imgIndex === 3 && caseItem.imgs.length > 4"
              class="more-images-indicator"
            >
              +{{ caseItem.imgs.length - 4 }}
            </view>
          </view>
        </view>

        <!-- 用户信息卡片 -->
        <view
          class="user-info-card"
          v-if="caseItem.mom"
          @click.stop="goToMomDetail(caseItem.mom)"
        >
          <view class="user-info-avatar">
            <image :src="caseItem.mom.avatar" class="info-avatar-img" />
          </view>
          <view class="user-info-content">
            <view class="user-nickname">
              {{ caseItem.mom.name || '' }}
              <view class="user-tag">宝妈</view>
            </view>
            <view class="user-details"
              >宝宝：{{ caseItem.mom.babyWeight || '' }} 入住时间：{{
                formatDate(caseItem.mom.serviceStartDate)
              }}
            </view>
          </view>

          <view class="arrow-right">
            <u-icon name="arrow-right" color="#444" size="26"></u-icon>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载状态 -->
    <view v-if="isLoading" class="loading-container">
      <view class="loading-text">加载中...</view>
    </view>

    <!-- 空状态 -->
    <view v-if="!isLoading && casesList.length === 0" class="empty-container">
      <image src="/static/images/empty.png" class="empty-image" />
      <view class="empty-text">暂无相关案例</view>
    </view>

    <!-- 底部安全区域 -->
    <view class="bottom-safe-area"></view>
  </view>
</template>

<script setup>
import { ref } from 'vue';
import { onLoad, onShow } from '@dcloudio/uni-app';
import { navigateToPage, decodePageTitle } from '@/utils/urlUtils.js';

// 页面参数
const pageParams = ref({});

// 数据状态
const casesList = ref([]);
const isLoading = ref(false);

// 页面加载
onLoad((options) => {
  pageParams.value = options || {};

  // 解码URL参数
  if (pageParams.value.userMessage) {
    pageParams.value.userMessage = decodePageTitle(
      pageParams.value.userMessage,
    );
  }

  console.log('相关案例列表页面参数:', pageParams.value);

  // 加载组件传递的数据
  loadDataFromComponent();
});

// 页面显示
onShow(() => {
  // 可以在这里刷新数据
});

// 从组件传递的数据中加载相关案例列表
const loadDataFromComponent = () => {
  try {
    const relatedCasesData = uni.getStorageSync('relatedCasesListData');

    if (relatedCasesData && relatedCasesData.list) {
      console.log('从组件获取相关案例数据:', relatedCasesData);

      // 直接使用API返回的数据格式
      casesList.value = relatedCasesData.list;

      // 更新页面参数
      if (relatedCasesData.conversationId) {
        pageParams.value.conversationId = relatedCasesData.conversationId;
      }
      if (relatedCasesData.userMessage) {
        pageParams.value.userMessage = relatedCasesData.userMessage;
      }

      console.log(
        '成功加载组件传递的相关案例数据，共',
        relatedCasesData.list.length,
        '条',
      );
    } else {
      // 如果没有组件数据，显示空状态
      casesList.value = [];
      console.log('未找到组件传递的数据');
    }
  } catch (error) {
    console.error('加载组件数据失败:', error);
    // 如果没有组件数据，显示空状态
    casesList.value = [];
  }
};

// 跳转到案例详情
const goToCaseDetail = (caseItem) => {
  console.log('跳转到案例详情:', caseItem);

  const params = {
    diaryId: caseItem.diaryId,
  };

  console.log('跳转到案例详情页:', params);
  navigateToPage('/pageA/pageB/community/note/onedetail', params);
};

// 跳转到宝妈说详情
const goToMomDetail = (momItem) => {
  console.log('跳转到宝妈说详情:', momItem);

  const params = {
    momId: momItem.momId,
  };

  navigateToPage('/pageA/pageB/community/note/notedetail', params);
};

// 处理图片加载失败
const handleImageError = (e) => {
  console.log('案例图片加载失败:', e);
};

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return '';
  const date = new Date(dateStr);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};
</script>

<style lang="scss" scoped>
.related-cases-list-page {
  min-height: 100vh;
  background-color: #eff0f6;
}

// AI助理消息样式
.ai-message-container {
  padding: 24rpx 24rpx 0;
}

.ai-message-card {
  background-color: #ffffff;
  border-radius: 28rpx;
  display: flex;
  flex-direction: column;
  padding: 28rpx;
}

.ai-avatar-section {
  display: flex;
  gap: 16rpx;
  align-items: flex-end;
}

.ai-avatar-container {
  width: 72rpx;
  height: 85rpx;
  box-shadow: inset 0 0 10rpx rgba(255, 255, 255, 0.23);
  image {
    width: 100%;
    height: 100%;
  }
}

.ai-avatar-icon {
  width: 35rpx;
  height: 37rpx;
  background: linear-gradient(135deg, #d9d9d9 0%, #d9d9d9 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: inset 0 -1rpx 4rpx rgba(195, 233, 245, 1);
}

.ai-text {
  font-family: PingFang SC;
  font-weight: 600;
  font-size: 20rpx;
  line-height: 1.4;
  background: linear-gradient(135deg, #ffab75 0%, #ff6b44 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: inset 0 0 1rpx rgba(255, 255, 255, 0.25);
}

.ai-message-content {
  flex: 1;
}

.ai-greeting {
  font-family: PingFang SC;
  font-weight: 500;
  font-size: 32rpx;
  color: #000000;
  margin: 0 0 16rpx;
}

.ai-result-text {
  font-family: PingFang SC;
  font-weight: 400;
  font-size: 28rpx;
  line-height: 1.57;
  color: #7c7c7c;
}

// 案例列表样式
.cases-list {
  padding: 24rpx;
  text-align: center;
}

.case-item {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 24rpx;
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

/* 案例头部 */
.case-header {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  overflow: hidden;
  background: #d9d9d9;
}

.avatar-img {
  width: 100%;
  height: 100%;
}

.user-name {
  font-family: PingFang SC;
  font-weight: 600;
  font-size: 34rpx;
  color: #333333;
}

/* 案例内容 */
.case-content {
}

.case-text {
  font-family: PingFang SC;
  font-weight: 400;
  font-size: 28rpx;
  color: #484848;
  text-align: left;
}

/* 案例图片 */
.case-images {
  display: flex;
  gap: 12rpx;
  position: relative;
}

.case-image-container {
  width: 170rpx;
  height: 170rpx;
  border-radius: 20rpx;
  overflow: hidden;
  position: relative;
}

.case-image-container.has-overlay::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 20rpx;
}

.case-image {
  width: 100%;
  height: 100%;
}

.more-images-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-family: PingFang SC;
  font-weight: 500;
  font-size: 32rpx;
  color: #ffffff;
  line-height: 1.38;
  z-index: 1;
}

/* 用户信息卡片 */
.user-info-card {
  background: #f9f9f9;
  border-radius: 10rpx;
  padding: 22rpx;
  display: flex;
  align-items: center;
  gap: 20rpx;
  position: relative;
}

.user-info-content {
  flex: 1;
}

.user-nickname {
  display: flex;
  align-items: center;
  font-weight: 500;
  font-size: 24rpx;
  color: #333333;
  margin-bottom: 8rpx;
  gap: 10rpx;
  .user-tag {
    display: flex;
    padding: 3rpx 12rpx;
    justify-content: center;
    align-items: center;
    border-radius: 50rpx;
    background: linear-gradient(0deg, #ffeef0 0%, #ffeef0 100%),
      linear-gradient(264deg, #f03d6a 0%, #f05279 98.02%);
    color: #ff4f61;
    font-size: 18rpx;
    font-weight: 500;
  }
}

.user-details {
  text-align: left;
  font-weight: 400;
  font-size: 18rpx;
  color: #a7a7a7;
  margin-bottom: 8rpx;
}

.user-tag {
  display: inline-block;
  padding: 6rpx 22rpx;
  background: linear-gradient(0deg, #f03d6a 0%, #f05279 100%);
  border-radius: 50rpx;
  font-family: PingFang SC;
  font-weight: 500;
  font-size: 18rpx;
  color: #ff4f61;
  line-height: 1.4;
}

.user-info-avatar {
  width: 70rpx;
  height: 70rpx;
  border-radius: 50%;
  overflow: hidden;
  background: #ffffff;
}

.info-avatar-img {
  width: 100%;
  height: 100%;
}

.arrow-right {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 标签容器 */
.case-tag {
  display: flex;
  align-items: center;
  gap: 6rpx;
  padding: 8rpx 30rpx;
  background: linear-gradient(0deg, #f6e0b7 0%, #ffe7c0 100%);
  border-radius: 40rpx;
  font-family: PingFang SC;
  font-weight: 500;
  font-size: 16rpx;
  color: #7a4e2b;
  box-shadow: inset 0 0 4rpx rgba(255, 255, 255, 0.25);
  image {
    width: 16rpx;
    height: 12.8rpx;
  }
}

.loading-container {
  padding: 40rpx;
  text-align: center;
}

.loading-text {
  font-size: 28rpx;
  color: #999999;
}

.empty-container {
  padding: 120rpx 40rpx;
  text-align: center;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 32rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999999;
}

.bottom-safe-area {
  height: 50rpx;
  background: #ffffff;
}
</style>
