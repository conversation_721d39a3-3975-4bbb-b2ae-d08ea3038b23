<template>
  <view v-if="Object.keys(item).length > 0">
    <!-- 文本内容 -->
    <view v-if="showContent" @tap="onDetail">
      <view v-if="pageType == 'note'" class="note">
        {{ item.content }}
      </view>
      <view style="white-space: pre-wrap" v-else v-html="content"></view>
    </view>
    <view v-else @tap="onDetail">
      <view
        :class="item.moreBtns ? 'beyond' : ''"
        :data-postid="item.postId"
        v-html="content"
      ></view>
      <view class="packUp" v-if="item.moreBtn" @tap.stop="packUp">
        {{ item.packUpShow ? '收回' : '展开' }}
      </view>
    </view>

    <!-- 视频 -->
    <view class="img_box" v-if="item.videos && item.videos.length > 0">
      <view class="videos" v-for="(res, index1) in item.videos" :key="index1">
        <video
          :id="`myVideo${itemIndex}`"
          @touchstart="onTouchStart"
          @touchend="onTouchEnd"
          class="video"
          :src="res"
          @tap="previewVideo"
          :data-url="item.video"
          :data-src="res"
          :data-index="index1"
          :controls="true"
        ></video>
      </view>
    </view>

    <!-- 图片 -->
    <view class="img_box" v-else>
      <template v-if="item.contentPhotos && item.contentPhotos.length > 0">
        <view
          :class="[
            'loadImg',
            item.contentPhotos.length > 1 ? 'many_img' : '',
            'active'
          ]"
        >
          <view
            class="img_item many"
            v-for="(res, index1) in item.contentPhotos"
            :key="index1"
          >
            <image
              class="img"
              :src="res + '?x-oss-process=image/quality,q_60'"
              @tap="previewImage"
              :data-url="item.contentPhotos"
              :data-src="res"
              :data-sources="item.contentPhotos"
              :data-index="index1"
              mode="aspectFill"
            ></image>
          </view>
        </view>
      </template>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'

const props = defineProps({
  // 列表数据
  listData: {
    type: Object,
    default: () => ({})
  },
  // 懒加载id
  itemIndex: {
    type: Number,
    default: 0
  },
  // 是否控制展开内容
  showContent: {
    type: Boolean,
    default: true
  },
  // 服务笔记展示数据需要特殊处理
  pageType: {
    type: String,
    default: 'static'
  }
})

// 移除事件定义，内部处理跳转

// 响应式数据
const videoContext = ref(null)
const isZoomed = ref(false) // 是否缩放状态

// 计算属性
const item = computed(() => {
  const data = { ...props.listData }
  if (props.pageType === 'note') {
    // 展示图片
    if (data.contentPhotos && data.contentPhotos.length > 2) {
      data.contentPhotos = data.contentPhotos.slice(0, 2)
    }
  }
  return data
})

const content = computed(() => {
  return item.value.content || ''
})

// 生命周期
onMounted(() => {
  videoContext.value = uni.createVideoContext(`myVideo${props.itemIndex}`)
})

// 方法
const onTouchStart = () => {
  isZoomed.value = true // 触摸开始时设置为缩放状态
}

const onTouchEnd = () => {
  isZoomed.value = false // 触摸结束时设置为非缩放状态
  if (!isZoomed.value) {
    pauseVideo() // 如果不是缩放状态，暂停视频播放
  }
}

const pauseVideo = () => {
  if (videoContext.value) {
    videoContext.value.pause()
  }
}



const packUp = () => {
  item.value.packUpShow = !item.value.packUpShow
  item.value.moreBtns = !item.value.moreBtns
}

const onDetail = () => {
  // 内部处理详情跳转
  const url = `/pageA/pageB/dynamics/detail?postId=${item.value.postId}`
  uni.navigateTo({ url })
}

const previewVideo = (event) => {
  preview('video', event)
}

const previewImage = (event) => {
  preview('image', event)
}

const preview = (type, event) => {
  const url = event.currentTarget.dataset.url
  const src = event.currentTarget.dataset.src
  const index = event.currentTarget.dataset.index
  console.log('previewwww', url, src, index)

  let maparr = []
  if (type === 'video') {
    maparr.push({
      type,
      url: src,
    })
  } else {
    url.forEach((item) => {
      maparr.push({
        type,
        url: item,
      })
    })
  }

  // 既有视频又有图片用这个
  uni.previewMedia({
    sources: maparr,
    current: index,
    autoplay: true,
  })
}
</script>

<style lang="less" scoped>
.beyond {
  font-size: 28rpx;
  color: #333333;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 5;
}

.packUp {
  color: #4d669b;
  font-size: 30rpx;
}

.img_box {
  margin-top: 20rpx;
  padding-left: 4rpx;
}

.videos {
  width: 48%;
  height: 280rpx;
}

.videos video {
  width: 100%;
  height: 100%;
  border-radius: 10rpx;
  display: block;
}

.img_box .many_img {
  display: flex;
  flex-wrap: wrap;
}

.img_item.many {
  width: 49%;
  height: 280rpx;
  margin-bottom: 10rpx;
  border-radius: 10px;
  overflow: hidden;
}

.img_item.many image {
  width: 100%;
  height: 100%;
}

.img_item.many:nth-child(2n) {
  margin-left: 1%;
}

.loadImg.active {
  transition: all 0.5s ease-in-out;
  opacity: 1;
}

.note {
  height: 80rpx;
  font-weight: 400;
  font-size: 23rpx;
  color: #333333;
  line-height: 40rpx;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  flex-wrap: wrap;
  white-space: pre-wrap;
}
</style>
