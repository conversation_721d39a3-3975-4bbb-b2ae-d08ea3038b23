<template>
  <view class="poster-container">
    <view
      class="poster-wrapper"
      style="
        background-image: url('http://cdn.xiaodingdang1.com/2025/01/20/0046e46896cb4f3bb334e003cedb4037.png');
      "
    >
      <view class="poster-main">
        <!-- 顶部信息 -->
        <view class="poster-header">
          <image
            class="poster-header__logo"
            :src="data.logo || clubLogo || $defaultAvatar"
          />
          <view class="poster-header__info">
            <text class="poster-header__title">{{ clubName }}</text>
            <text class="poster-header__subtitle">{{
              data.posterSlogan || defaultLogan
            }}</text>
          </view>
        </view>

        <!-- 宝妈信息区域 -->
        <view class="poster-mom-info">
          <view class="poster-mom-info__avatar">
            <image :src="data.avatar || $defaultAvatar" />
          </view>
          <view class="poster-mom-info__details">
            <view class="flex">
              <text>宝妈：</text>
              <text>{{ data.momName }}</text>
            </view>
            <view class="flex" v-if="data.age">
              <text>年龄：</text>
              <text>{{ data.age }}</text>
            </view>
            <view class="flex" v-if="data.babyWeight">
              <text>宝宝：</text>
              <text>{{ data.babyWeight }}</text>
            </view>
            <view class="flex" v-if="data.serviceStartDate">
              <text style="white-space: nowrap">入住时间：</text>
              <text>{{
                dayjs(data.serviceStartDate).format('YYYY-MM-DD')
              }}</text>
            </view>
          </view>
        </view>

        <!-- 内容区域 -->
        <view class="poster-content">
          <view class="poster-card">
            <view
              class="poster-card__header"
              style="
                background-image: url('http://cdn.xiaodingdang1.com/2025/01/20/3aa0331c80f54166a71fb26dc9b07ac7.png');
              "
            >
              <!-- 员工信息 -->
              <view class="poster-user">
                <view class="poster-user__avatar">
                  <image :src="data.staffPhotos || $defaultAvatar" />
                </view>
                <view class="poster-user__name">
                  <text>{{ data.staffName }}</text>
                </view>
                <view class="poster-user__badge" v-if="data.staffPost">
                  <image
                    src="http://cdn.xiaodingdang1.com/2024/12/31/7ab70df46904400aae76508164f5a0ac.png"
                  />
                  <view class="poster-user__badge-text">
                    <text>{{ data.staffPost }}</text>
                  </view>
                </view>
              </view>

              <!-- 标签 -->
              <view class="poster-tag" v-if="data.nodeName">
                <view class="poster-tag__item">
                  <text>{{ data.nodeName }}</text>
                </view>
              </view>

              <!-- 内容文本 -->
              <view class="poster-text" v-if="data.content">
                <text>{{ data.content }}</text>
              </view>
            </view>

            <!-- 内容图片 -->
            <template
              v-if="data.contentPhotos && data.contentPhotos.length > 0"
            >
              <view
                v-for="(img, index) in data.contentPhotos"
                :key="index"
                class="poster-image"
              >
                <image mode="aspectFill" :src="img" />
              </view>
            </template>

            <!-- 底部信息 -->
            <view class="poster-footer">
              <view class="poster-footer__left poster-footer__left--padding">
                <view class="poster-footer__comment">
                  <text>{{ data.commentText || '' }}</text>
                </view>
                <view class="poster-footer__line"></view>
                <view class="poster-footer__subtitle">
                  <text>扫码即刻开启</text>
                </view>
                <view class="poster-footer__title">
                  <text>{{ str1 }}</text>
                </view>
                <view class="poster-footer__title">
                  <text>{{ str2 }}</text>
                </view>
              </view>
              <view
                class="poster-footer__right poster-footer__right--center"
                v-if="qrCode"
              >
                <image class="poster-footer__qr" :src="qrCode" />
                <view class="poster-footer__title">
                  <text>宝妈小叮当</text>
                </view>
                <view class="poster-footer__title">
                  <text>专注母婴系统开发</text>
                </view>
              </view>
            </view>
          </view>

          <!-- 装饰图片 -->
          <view class="poster-decoration">
            <image
              src="http://cdn.xiaodingdang1.com/2025/01/20/3fe0ddf2cb4c4c88a9a9592050ec6ac3.png"
            />
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance } from 'vue';
import dayjs from 'dayjs';

// Props 定义
const props = defineProps({
  // 海报类型
  type: {
    type: String,
    default: 'create',
  },
  // 日记ID
  diaryId: {
    type: String,
    required: true,
  },
  // uuid
  uuid: {
    type: String,
    required: true,
  },
});

// 响应式数据
const instance = getCurrentInstance();
const isLoading = ref(false);
const data = ref({});
const clubName = ref(uni.getStorageSync('clubName'));
const clubLogo = ref(uni.getStorageSync('clubInfo')?.logo);
const defaultLogan = ref('专业呵护新起点');
const str1 = ref('不用来回体验');
const str2 = ref('在线上就能查看合适再选择');
const qrCode = ref('');

/**
 * 获取日记详情信息
 */
const getDiaryDetail = async () => {
  if (!props.uuid) {
    throw new Error('uuid不能为空');
  }

  isLoading.value = true;

  try {
    const res = await instance.proxy.$axios.get(
      instance.proxy.$api.getDiaryDetail,
      { diaryId: props.diaryId },
    );

    if (res.data.code === 200) {
      const diaryData = res.data.data;
      data.value = diaryData;

      data.value.commentText =
        data.value.userCount > 0
          ? `该用户还有${data.value.userCount}条精彩评论`
          : '该用户还有1条精彩评论';

      // 处理海报标语
      if (data.value.chefPosterSlogan) {
        const arr = data.value.chefPosterSlogan.split('，');
        str1.value = arr[0] || '不用来回体验';
        str2.value = arr[1] || '在线上就能查看合适再选择';
      }

      console.log('获取日记详情成功:', diaryData);
      return diaryData;
    } else {
      throw new Error(res.data.msg || '获取日记详情失败');
    }
  } catch (error) {
    console.error('获取日记详情失败:', error);
    throw error;
  } finally {
    isLoading.value = false;
  }
};

/**
 * 获取二维码
 */
const getQrCode = async () => {
  try {
    const res = await instance.proxy.$axios.get(instance.proxy.$api.getQrCode, {
      pathUrl: 'pageA/pageB/community/note/notedetail',
      uuid: props.uuid,
      diaryId: '',
    });

    if (res.data.code === 200) {
      qrCode.value = res.data.msg;
      console.log('获取二维码成功:', res.data.msg);
    } else {
      throw new Error(res.data.msg || '获取二维码失败');
    }
  } catch (error) {
    console.error('获取二维码失败:', error);
    throw error;
  }
};

/**
 * 初始化数据
 */
const initData = async () => {
  try {
    await Promise.all([getDiaryDetail(), getQrCode()]);
    console.log('创建海报数据初始化完成');
  } catch (error) {
    console.error('创建海报数据初始化失败:', error);
    uni.showToast({
      title: '数据加载失败',
      icon: 'none',
    });
  }
};

// 组件挂载时初始化数据
onMounted(() => {
  initData();
});
</script>

<style lang="scss" scoped>
@import './poster.scss';

/* 动态海报特有样式 */
</style>
