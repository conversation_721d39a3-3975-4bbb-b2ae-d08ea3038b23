<template>
    <view>
        <movable-area>
            <movable-view
                @touchend="onTouchend"
                @touchmove="onTouchMove"
                @touchstart="addAnimation"
                :animation="animation"
                :style="'width:' + elementWidth + 'px;height:' + elementHeight + 'px'"
                :x="x"
                :y="y"
                direction="all"
            >
                <view class="float-box">
                    <slot></slot>
                </view>
            </movable-view>
        </movable-area>
    </view>
</template>

<script>
export default {
    data() {
        return {
            x: 999,
            y: 999,
            windowWidth: uni.getSystemInfoSync().windowWidth,
            windowHeight: uni.getSystemInfoSync().windowHeight,
            elementWidth: 0,
            elementHeight: 0,
            animation: false,
            isMoved: false // 是否拖动
        };
    },
    /**
     * 组件的属性列表
     */
    props: {
        position: {
            type: Object,
            default: () => ({
                left: 0,
                right: 0,
                bottom: 0,
                top: 0
            }) // left、top、right、bottom
        },
        left: {
            type: Number,
            default: 0
        },
        right: {
            type: Number,
            default: 0
        },
        top: {
            type: Number,
            default: 0
        },
        bottom: {
            type: Number,
            default: 0
        },
        hMargin: {
            // 拖动后的水平方向最小边距（左右边距）
            type: Number,
            default: 10
        },
        vMargin: {
            // 拖动后的垂直方向最小边距(上下边距)
            type: Number,
            default: 10
        }
    },
    mounted() {
        // 处理小程序 attached 生命周期
        this.attached();
    },
    /**
     * 组件的方法列表
     */
    methods: {
        attached() {
            // 初始化位置
            uni.createSelectorQuery()
                .in(this)
                .select('.float-box')
                .boundingClientRect()
                .exec((res) => {
                    console.log(233, res);
                    this.elementWidth = res[0].width;
                    this.elementHeight = res[0].height;
                    if (this.position.left || this.left) {
                        this.x = this.position.left || this.left;
                    }
                    if (this.position.right || this.right) {
                        this.x = this.windowWidth - this.elementWidth - (this.position.right ? this.position.right : this.right);
                    }
                    if (this.position.top || this.top) {
                        this.y = this.position.top || this.top;
                    }
                    if (this.position.bottom || this.bottom) {
                        this.y = this.windowHeight - this.elementHeight - (this.position.bottom ? this.position.bottom : this.bottom);
                    }
                    this.setData({
                        elementWidth: this.elementWidth,
                        elementHeight: this.elementHeight,
                        x: this.x,
                        y: this.y
                    });
                });
        },

        onTouchend(e) {
            console.log(this.isMoved, this.x, e.changedTouches[0].clientX, this.elementWidth);
            if (!this.isMoved) {
                return;
            }
            const currentX = e.changedTouches[0].clientX;
            let currentY = e.changedTouches[0].clientY;
            if (currentY <= this.vMargin) {
                currentY = this.vMargin + this.elementHeight / 2;
            }
            if (currentY >= this.windowHeight - this.vMargin) {
                currentY = this.windowHeight - this.vMargin - this.elementHeight / 2;
            }
            if (currentX + this.elementWidth / 2 > this.windowWidth / 2) {
                this.setData({
                    x: this.windowWidth - this.hMargin - this.elementWidth,
                    y: currentY - this.elementHeight / 2
                });
            }
            if (currentX + this.elementWidth / 2 <= this.windowWidth / 2) {
                this.setData({
                    x: this.hMargin,
                    y: currentY - this.elementHeight / 2
                });
            }
        },

        addAnimation() {
            this.isMoved = false;
            if (!this.animation) {
                this.setData({
                    animation: true
                });
            }
        },

        onTouchMove() {
            this.isMoved = true;
        }
    },
    created: function () {}
};
</script>
<style>
@import './movableArea.css';
</style>
