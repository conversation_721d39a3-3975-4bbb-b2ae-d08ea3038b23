<template>
  <view class="page-container">
    <!-- 请帖内容编辑区 -->
    <view class="share-card">
      <view class="edit-tip">请帖编辑(可直接编辑封面和文字)</view>

      <!-- 请帖标题 -->
      <view class="invitation-title">
        <input
          type="text"
          v-model="reqData.title"
          class="title-input"
          placeholder="请输入请帖标题"
        />
        <view class="count">{{ titleLength }}/36</view>
      </view>

      <!-- 请帖预览区域 -->
      <view class="preview-area">
        <view class="cover-wrapper" @click="changeCover">
          <image
            class="cover-image"
            :src="reqData.coverImageUrl"
            mode="aspectFill"
          ></image>
          <view class="change-cover">更换封面</view>
        </view>
        <view class="preview-content">
          <textarea
            class="content-textarea"
            v-model="reqData.text"
            :maxlength="36"
            placeholder="请输入邀请内容"
          ></textarea>
          <view class="count">{{ contentLength }}/36</view>
        </view>
      </view>

      <!-- 分享区域 -->
      <view class="share-section">
        <view class="divider">
          <view class="divider-line"></view>
          <text class="divider-text">分享至</text>
          <view class="divider-line"></view>
        </view>

        <view class="share-buttons">
          <button open-type="share" class="share-button">
            <image
              class="share-icon"
              src="./static/images/share_wechat.png"
              mode="aspectFit"
            ></image>
            <text class="share-text">微信好友</text>
          </button>

          <view @click="showShareGuide" class="share-button">
            <image
              class="share-icon"
              src="./static/images/share_icon.png"
              mode="aspectFit"
            ></image>
            <text class="share-text">分享链接</text>
          </view>

          <view @click="showShareGuide" class="share-button">
            <image
              class="share-icon"
              src="./static/images/share_moments.png"
              mode="aspectFit"
            ></image>
            <text class="share-text">朋友圈</text>
          </view>

          <view class="share-button" @click="copyLink">
            <image
              class="share-icon"
              src="./static/images/share_link.png"
              mode="aspectFit"
            ></image>
            <text class="share-text">复制链接</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 分享指引弹窗 -->
    <view class="share-guide-modal" v-if="showGuide">
      <view class="guide-content">
        <image
          class="guide-bg"
          src="./static/images/share_guide.png"
          mode="aspectFill"
        ></image>
        <button
          open-type="contact"
          :send-message-title="reqData.title"
          :send-message-path="backPath"
          :send-message-img="reqData.coverImageUrl"
          :show-message-card="true"
          class="share-now-btn"
          @click="handleShare"
        >
          立即分享
        </button>
      </view>

      <view class="close-icon" @click="closeGuide">
        <uni-icons type="closeempty" size="24" color="#fff"></uni-icons>
      </view>
    </view>
  </view>
</template>

<script setup>
import utils from '@/utils/util';
import { onLoad, onShareAppMessage } from '@dcloudio/uni-app';
import dayjs from 'dayjs';
import { computed, reactive, ref } from 'vue';
import { getInvitationApi, saveInvitationApi } from './api/rehabilitation';
import { uploadFile } from './api/request';

const isCustomShare = ref(false);

const reqData = reactive({
  title: '', // 分享标题
  text: '', // 分享内容
  coverImageUrl: '', // 分享封面图片
});

const cardId = ref('');
const showGuide = ref(false);

const getInvitation = async () => {
  const res = await getInvitationApi(cardId.value);
  Object.assign(reqData, res.data);
  if (!res?.data?.title) {
    reqData.title = `${res?.data?.name || ''}宝宝的生日邀请`;
  }
  if (!res?.data?.text) {
    let eventTime = res?.data?.eventTime;
    let date = dayjs(eventTime).format('MM月DD日');
    reqData.text = `我们将在${date}举行宝宝生日，诚恳邀请您的到来。`;
  }
};

const state = ref({});

// 获取当前页面路径和参数
onLoad(async (options) => {
  state.value = options;
  if (options.cardId) {
    cardId.value = options.cardId;
    await getInvitation();
  }
});

onShareAppMessage((res) => {
  console.log('分享参数:', res);
  return {
    title: reqData.title || '生日邀请',
    path: `pageA/pageB/home/<USER>/preview?${utils.queryParams({
      cardId: cardId.value,
      ...state.value,
    })}`,
    imageUrl:
      reqData.coverImageUrl ||
      'http://cdn.xiaodingdang1.com/2025/03/20/325053002d784109a28711387132373c.png',
  };
});

const backPath = computed(() => {
  const page = getCurrentPages()[0];
  const path = page.route;
  const { tenantId, userId } = uni.getStorageSync('userInfo') || {};
  return `${path}?${utils.queryParams({
    tenantId,
    userId,
    cardId: cardId.value,
    source: 'share',
  })}`;
});

const sharePath = computed(() => {
  const { tenantId, userId } = uni.getStorageSync('userInfo') || {};
  return `https://h5.xiaodingdang1.com/wap.html?${utils.queryParams({
    cardId: cardId.value,
    tenantId,
    userId,
  })}`;
});

// 计算字数
const titleLength = computed(() => reqData.title.length);
const contentLength = computed(() => reqData.text.length);

// 更换封面
const changeCover = () => {
  uni.chooseImage({
    count: 1,
    success: async (res) => {
      const url = await uploadFile({ filePath: res.tempFilePaths[0] });
      reqData.coverImageUrl = url;
    },
  });
};

// 复制链接
const copyLink = () => {
  // 这里应该是生成实际的分享链接
  const shareLink = sharePath.value;

  uni.setClipboardData({
    data: shareLink,
    success: function () {
      uni.showToast({
        title: '链接已复制',
        icon: 'success',
      });
    },
  });
};

// 显示分享指引弹窗
const showShareGuide = () => {
  showGuide.value = true;
};

// 关闭分享指引弹窗
const closeGuide = () => {
  showGuide.value = false;
};

const handleShare = async () => {
  await saveInvitationApi({
    cardId: cardId.value,
    title: reqData.title,
    text: reqData.text,
    coverImageUrl: reqData.coverImageUrl,
    shareUrl: sharePath.value,
  });
};
</script>

<style lang="less" scoped>
.page-container {
  width: 100%;
  height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
}

.share-card {
  margin: 16rpx;
  padding: 24rpx;
  background-color: #fff;
  border-radius: 10rpx;
}

.edit-tip {
  font-size: 24rpx;
  color: #666;
}

.invitation-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f2f3f6;
  padding: 20rpx;
  border-radius: 8rpx;
  margin-top: 20rpx;

  .title-input {
    flex: 1;
    height: 20px;
    font-size: 16px;
    font-weight: 500;
    border: none;
  }

  .count {
    font-size: 12px;
    color: #999;
  }
}

.preview-area {
  display: flex;
  overflow: hidden;
  margin-top: 20rpx;
  column-gap: 20rpx;

  .cover-wrapper {
    position: relative;
    width: 170rpx;
    height: 170rpx;
    border-radius: 8rpx;
    background-color: #f2f3f6;

    .cover-image {
      width: 100%;
      height: 100%;
      border-radius: 8rpx;
    }

    .change-cover {
      position: absolute;
      bottom: 10px;
      right: 50%;
      transform: translateX(50%);
      background-color: rgba(0, 0, 0, 0.5);
      color: #fff;
      font-size: 24rpx;
      padding: 4rpx 8rpx;
      border-radius: 8rpx;
      white-space: nowrap;
    }
  }

  .preview-content {
    flex: 1;
    width: 0;
    padding: 20rpx;
    position: relative;
    background-color: #f2f3f6;
    height: 170rpx;
    border-radius: 8rpx;

    .content-textarea {
      width: 100%;
      height: 100%;
      font-size: 24rpx;
    }

    .count {
      position: absolute;
      right: 15px;
      bottom: 15px;
      font-size: 12px;
      color: #999;
    }
  }
}

.share-section {
  margin: 40rpx 0;
  .divider {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;

    .divider-line {
      width: 60px;
      height: 1px;
      background-color: #ddd;
    }

    .divider-text {
      margin: 0 10px;
      font-size: 14px;
      color: #999;
    }
  }

  .share-buttons {
    display: flex;
    justify-content: space-around;
    margin-bottom: 40rpx;

    .share-button {
      display: flex;
      flex-direction: column;
      align-items: center;
      background-color: transparent;
      border: none;
      outline: none;
      margin: 0;
      padding: 0;
      line-height: 1;

      .share-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
      }

      .share-text {
        margin-top: 16rpx;
        font-size: 24rpx;
        color: #333;
      }
    }
  }
}

/* 分享指引弹窗样式 */
.share-guide-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 999;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  .guide-content {
    width: 600rpx;
    height: 600rpx;
    background-color: #fff;
    border-radius: 30rpx;
    overflow: hidden;
    display: flex;
    align-items: flex-end;
    position: relative;
    padding: 40rpx;

    .guide-bg {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }
  }

  .share-now-btn {
    width: 100%;
    height: 90rpx;
    line-height: 90rpx;
    border-radius: 16rpx;
    background-color: #f05654;
    color: #fff;
    font-size: 32rpx;
    font-weight: bold;
    text-align: center;
  }

  .close-icon {
    border-radius: 50%;
    border: 1px solid #fff;
    margin-top: 60rpx;
    width: 56rpx;
    height: 56rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>
