#!/usr/bin/env node

/**
 * 租户ID构建演示脚本
 * 演示如何为不同租户构建小程序
 */

const BuildConfigInjector = require('./build-config-injector.js');
const { projects } = require('../config/config-loader.js');
const fs = require('fs');
const path = require('path');

// 颜色输出
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

/**
 * 演示租户ID注入过程
 */
async function demoTenantInjection() {
  log('🎭 租户ID构建演示', 'cyan');
  log('='.repeat(60));
  
  log('\n📋 当前项目配置:', 'yellow');
  
  // 显示所有项目的租户ID配置
  Object.entries(projects).forEach(([projectName, project]) => {
    log(`\n🏢 项目: ${project.name} (${projectName})`, 'blue');
    console.log(`   AppID: ${project.appid}`);
    console.log(`   默认租户ID: ${project.defaultTenantId || '未设置'}`);

    Object.entries(project.environments).forEach(([envName, envConfig]) => {
      const tenantId = envConfig.tenantId || project.defaultTenantId || '194338';
      console.log(`   ${envName}: 租户ID ${tenantId}`);
    });
  });

  log('\n🔧 演示配置注入过程:', 'yellow');
  
  // 演示main项目的develop环境
  const projectName = 'main';
  const environment = 'develop';
  const project = projects[projectName];
  const envConfig = project.environments[environment];
  const tenantId = envConfig.tenantId || project.defaultTenantId || '194338';

  log(`\n正在为项目 "${project.name}" 的 "${environment}" 环境注入配置...`, 'blue');
  console.log(`租户ID: ${tenantId}`);
  console.log(`AppID: ${project.appid}`);

  const injector = new BuildConfigInjector();
  
  try {
    // 显示注入前的配置
    await showCurrentConfig('注入前');
    
    // 执行注入
    injector.inject({
      tenantId,
      appid: project.appid,
      projectName,
      environment
    });

    // 显示注入后的配置
    await showCurrentConfig('注入后');
    
    // 恢复配置
    log('\n🔄 恢复原始配置...', 'yellow');
    injector.restoreBackups();
    
    await showCurrentConfig('恢复后');
    
    logSuccess('\n🎉 演示完成！');
    
  } catch (error) {
    logError(`演示失败: ${error.message}`);
    injector.restoreBackups();
  }
}

/**
 * 显示当前配置
 */
async function showCurrentConfig(stage) {
  log(`\n📄 ${stage}的配置:`, 'cyan');
  
  const rootPath = path.resolve(__dirname, '../..');
  const configPath = path.join(rootPath, 'src/utils/config.js');
  const appVuePath = path.join(rootPath, 'src/App.vue');

  // 读取config.js中的租户ID
  if (fs.existsSync(configPath)) {
    const configContent = fs.readFileSync(configPath, 'utf8');
    const tenantIdMatch = configContent.match(/TENANT_ID:\s*['"]([^'"]*)['"]/);
    if (tenantIdMatch) {
      console.log(`   config.js 租户ID: ${tenantIdMatch[1]}`);
    }
  }

  // 读取App.vue中的默认租户ID
  if (fs.existsSync(appVuePath)) {
    const appVueContent = fs.readFileSync(appVuePath, 'utf8');
    const defaultTenantMatch = appVueContent.match(/:\s*["']([^"']*?)["'];/);
    if (defaultTenantMatch) {
      console.log(`   App.vue 默认租户ID: ${defaultTenantMatch[1]}`);
    }
  }
}

/**
 * 显示使用说明
 */
function showUsageGuide() {
  log('\n📚 使用指南:', 'yellow');
  log('='.repeat(60));
  
  log('\n1. 配置项目租户ID:', 'blue');
  log('   编辑 ci/config/projects.json 文件', 'white');
  log('   设置 defaultTenantId 和环境特定的 tenantId', 'white');
  
  log('\n2. 构建项目:', 'blue');
  log('   node ci/scripts/upload.js main develop', 'white');
  log('   系统会自动注入对应的租户ID', 'white');
  
  log('\n3. 验证配置:', 'blue');
  log('   pnpm run ci:test-tenant', 'white');
  log('   测试所有项目的租户ID注入功能', 'white');
  
  log('\n4. 管理项目:', 'blue');
  log('   pnpm run ci:manage list', 'white');
  log('   查看所有项目的配置信息', 'white');
}

/**
 * 显示帮助信息
 */
function showHelp() {
  log('租户ID构建演示脚本', 'cyan');
  log('');
  log('使用方法:', 'yellow');
  log('  node ci/scripts/demo-tenant-build.js [选项]', 'white');
  log('');
  log('选项:', 'yellow');
  log('  --demo    - 运行演示', 'white');
  log('  --guide   - 显示使用指南', 'white');
  log('  --help    - 显示帮助信息', 'white');
  log('');
}

// 主函数
async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--help') || args.includes('-h')) {
    showHelp();
    return;
  }

  if (args.includes('--guide')) {
    showUsageGuide();
    return;
  }

  if (args.includes('--demo') || args.length === 0) {
    await demoTenantInjection();
    showUsageGuide();
    return;
  }

  showHelp();
}

// 运行主函数
if (require.main === module) {
  main().catch(error => {
    logError(`脚本执行失败: ${error.message}`);
    process.exit(1);
  });
}

module.exports = {
  demoTenantInjection,
  showUsageGuide
};
