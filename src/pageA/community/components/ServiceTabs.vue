<template>
  <scroll-view
    :show-scrollbar="false"
    :scroll-x="true"
    class="horizontal-tabs"
    :scroll-this="scrollPosition"
  >
    <view
      :class="['horizontal-tab', isAllSelected ? 'horizontal-tab--active' : '']"
      @tap="handleSelectAll()"
    >
      全部
    </view>
    <view
      :class="[
        'horizontal-tab',
        selectedTaskNodeIds.includes(item.taskNodeId) ? 'horizontal-tab--active' : '',
      ]"
      @tap="handleTabClick(item.taskNodeId)"
      v-for="(item, index) in tagList"
      :key="index"
    >
      {{ item.nodeName }}
    </view>
  </scroll-view>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue'

const props = defineProps({
  tagList: {
    type: Array,
    default: () => []
  },
  selectedTaskNodeIds: {
    type: Array,
    default: () => []
  },
  isAllSelected: {
    type: Boolean,
    default: false
  },
  scrollPosition: {
    type: Number,
    default: 0
  }
})

const emit = defineEmits(['tab-click', 'select-all'])

const handleTabClick = (taskNodeId) => {
  emit('tab-click', taskNodeId)
}

const handleSelectAll = () => {
  emit('select-all')
}
</script>

<style scoped lang="less">
.horizontal-tabs {
  width: 100vw;
  box-sizing: border-box;
  white-space: nowrap;
  display: flex;
  align-items: center;
  position: relative;
  z-index: 9;
  padding: 18rpx;
  background: #ffffff;
}

.horizontal-tab {
  display: inline-block;
  background-color: #f6f6f6;
  border-radius: 66rpx;
  padding: 8rpx 20rpx;
  font-weight: 400;
  font-size: 24rpx;
  color: #848484;
  line-height: 40rpx;

  &:not(:last-child) {
    margin-right: 20rpx;
  }

  &--active {
    background: #ff4f61;
    color: #ffffff;
    font-size: 26rpx;
  }
}
</style>
