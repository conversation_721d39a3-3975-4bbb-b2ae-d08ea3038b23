<template>
  <view class="card">
    <view class="card-content">
      <image mode="widthFix" :src="item.backGroundImage"> </image>
      <view class="des">
        <view class="title">{{ item.cardSetting?.title || item.title }}</view>
        <view class="sub-title">宝宝</view>
        <view class="tag">
          <view class="elc">电子请帖</view>
          <view class="page">{{ item.total }}张</view>
        </view>
        <view class="time">{{ item.updateTime }}</view>
      </view>
      <view class="right" @click.capture="handleShare">
        <button open-type="share" class="right-button">
          <image
            src="http://cdn.xiaodingdang1.com/2025/03/21/086a375ba05e41a88eeb2cbb6f2cc0aa.png"
          >
          </image>
          分享
        </button>
      </view>
    </view>
    <view class="card-devide"></view>
    <view class="card-bottom">
      <view class="operate" @click="handleEdit">
        <image
          src="http://cdn.xiaodingdang1.com/2025/03/21/1db7bb61a6994224ab7232b68581d8ef.png"
        >
        </image>
        编辑
      </view>
      <view class="operate" @click="showDeleteModal">
        <image
          src="http://cdn.xiaodingdang1.com/2025/03/21/29c80d32031d4507b9a6810da5c6fd1a.png"
        >
        </image>
        删除
      </view>
    </view>
  </view>

  <!-- 删除确认模态框 -->
  <u-modal
    :show-confirm-button="true"
    :show-cancel-button="true"
    v-model="showModal"
    @confirm="confirmDelete"
    @cancel="cancelDelete"
    :show-title="false"
    content="确认是否删除这张请帖？"
  >
  </u-modal>
</template>

<script setup>
import { ref, getCurrentInstance } from 'vue';
import utils from '@/utils/util';

// 获取当前实例
const instance = getCurrentInstance();

// 响应式数据
const showModal = ref(false);

// 接收属性
const props = defineProps({
  item: {
    type: Object,
    required: true,
  },
});

// 定义事件
const emit = defineEmits(['refresh']);

// 处理分享
const handleShare = () => {
  const userInfo = uni.getStorageSync('userInfo') || {};
  const { tenantId, userId } = userInfo;
  let src = encodeURIComponent(
    props.item.src + `&isShare=1&tenantId=${tenantId}&userId=${userId}`,
  );
  const path = `/pageA/pageB/home/<USER>/detail?src=${src}&title=${props.item.title}&id=${props.item.id}&type=${props.item.templateType}`;

  // 设置分享数据到全局，供微信分享使用
  const shareData = {
    title: props.item.title,
    imageUrl: props.item.backGroundImage,
    path,
  };

  // 将分享数据存储到全局，供页面级的 onShareAppMessage 使用
  uni.setStorageSync('currentShareData', shareData);

  console.log('分享数据已设置:', shareData);
};

// 处理编辑
const handleEdit = () => {
  const { templateId, templateType } = props.item;
  const queryPath = utils.queryParams({
    id: templateId,
    type: templateType,
  });
  const url = `/pageA/pageB/home/<USER>/edit?${queryPath}`;

  uni.navigateTo({
    url,
  });
};

// 显示删除模态框
const showDeleteModal = () => {
  showModal.value = true;
};

// 确认删除
const confirmDelete = () => {
  instance.proxy.$axios
    .get(instance.proxy.$api.delRehabiliteItem, {
      id: props.item.id,
    })
    .then(() => {
      showModal.value = false;
      // 通知父组件刷新数据
      emit('refresh');
    })
    .catch((error) => {
      console.error('删除请帖失败:', error);
      showModal.value = false;
    });
  console.log('delId', props.item.id);
};

// 取消删除
const cancelDelete = () => {
  showModal.value = false;
};
</script>

<style scoped lang="less">
.card {
  width: 100%;

  .card-devide {
    width: 100%;
    height: 2rpx;
    background: #f2f3f6;
  }

  .card-content {
    display: flex;
    padding: 30rpx;
    background-color: white;

    image {
      width: 125rpx;
      height: 178rpx;
      border-radius: 10rpx;
    }

    .des {
      margin-left: 22rpx;
      flex: 1;

      .title {
        font-weight: 500;
        font-size: 30rpx;
        color: #333333;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        margin-bottom: 12rpx;
      }

      .sub-title {
        font-size: 24rpx;
        color: #333333;
        margin-bottom: 14rpx;
      }

      .time {
        font-weight: 500;
        font-size: 20rpx;
        color: #333333;
        opacity: 0.4;
      }

      .tag {
        margin-bottom: 14rpx;
        display: flex;

        .elc {
          width: 96rpx;
          height: 34rpx;
          line-height: 34rpx;
          background-color: rgba(255, 79, 10, 0.1);
          border-radius: 6rpx;
          font-weight: 500;
          font-size: 20rpx;
          color: #ff4f61;
          text-align: center;
          margin-right: 20rpx;
        }

        .page {
          height: 34rpx;
          border-radius: 6rpx;
          border: 1rpx solid #333333;
          opacity: 0.3;
          padding: 0 3rpx;
          font-weight: 500;
          font-size: 20rpx;
          color: #333333;
        }
      }
    }

    .right {
      width: 120rpx;
      height: 48rpx;

      .right-button {
        background-color: rgba(255, 79, 10, 0.1);
        border-radius: 6rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        font-weight: 500;
        font-size: 20rpx;
        color: #ff4f61;

        image {
          height: 24rpx;
          width: 24rpx;
          margin-right: 4rpx;
        }
      }
    }
  }

  .card-bottom {
    background-color: white;
    padding: 24rpx;
    display: flex;
    width: 100%;

    .operate {
      image {
        width: 38rpx;
        height: 38rpx;
      }
      font-weight: 500;
      font-size: 22rpx;
      color: #333333;
      width: 50%;
      display: flex;
      align-items: center;
      gap: 4rpx;
      flex-direction: column;
    }
  }
}

.card:last-child {
  padding-bottom: 0;
}
</style>
