<template>
    <view class="main" v-if="feedPostInfoList">
        <view class="mains">
            <view class="dynamicTitle">
                <view class="dynamicTitle1">
                    <view class="dynamicTitle1_1">
                        <image lazy-load :src="feedPostInfoList.avatar || $defaultAvatar" mode="" />
                        <view class="dynamicTitle-right">
                            <view>{{ feedPostInfoList.nickname || $defaultName }}</view>
                            <view>{{ feedPostInfoList.createTime }}</view>
                        </view>
                    </view>
                    <img-content-area-vue :showContent="true" :listData="feedPostInfoList"
                        :index="0"></img-content-area-vue>
                    <!--  <view class="dynamicTitle1_2">
                        {{ feedPostInfoList.content }}
                    </view> -->

                    <!-- 视频 -->
                    <!--  <view class="img_box" v-if="feedPostInfoList.videos && feedPostInfoList.videos.length > 0">
                        <view class="videos" v-for="(res, index) in feedPostInfoList.videos" :key="index">
                            <video class="video" :src="res" @tap="preview" :data-src="res" :data-index="index"
                                :controls="false"></video>
                        </view>
                    </view> -->
                    <!-- 图片 -->
                    <!-- <view class="img_box" v-else>
                        <template v-if="feedPostInfoList.contentPhotos && feedPostInfoList.contentPhotos.length > 0 ">
                            <view class="many_img">
                                <view class="img_item many" v-for="(res, index) in feedPostInfoList.contentPhotos" :key="index">
                                    <image class="img" :src="res" @tap="previewImage"
                                        :data-url="feedPostInfoList.contentPhotos" :data-src="res"
                                        :data-sources="feedPostInfoList.contentPhotos" :data-index="index"
                                        mode="aspectFill"></image>
                                </view>
                            </view>
                        </template>
                    </view> -->
                    <view class="dynamicTitle1_3">
                        <view class="dynamicTitle1_3_1">
                            {{ feedPostInfoList.hisTimeStr }}
                        </view>
                        <view>
                            <!-- <image
                                src="http://cdn.xiaodingdang1.com/1/120240115/2db46e59-414e-48cc-85a4-fc8559a13a9c.png"
                                mode="" style="width: 42rpx; height: 22rpx" /> -->
                        </view>
                    </view>
                </view>
            </view>
        </view>
        <!-- <view class="fgx"></view> -->
		<mask-dialog></mask-dialog>
    </view>
</template>

<script>
    import imgContentAreaVue from "@/components/imgContentArea.vue";
    export default {
        components: {
            imgContentAreaVue
        },
        data() {
            return {
                packUpShow: false,
                moreBtns: false,
                moreBtn: false,

                feedPostInfoList: {
                    avatar: '',
                    nickname: '',
                    content: '',
                    videos: '',
                    contentPhotos: [],
                    hisTimeStr: ''
                },

                isHeightMode: false,
                height: '',
                width: '',

                mediaList: [{
                        url: 'http://*************:9000/tools/files/1746798009883627522/download',
                        type: 1
                    },
                    {
                        url: '../../../images/icon/WeChat_20240111141223.mp4',
                        type: 2
                    },
                    {
                        url: 'http://*************:9000/tools/files/1746797843919212545/download'
                    },
                    {
                        url: 'http://*************:9000/tools/files/1746797843919212545/download'
                    },
                    {
                        url: 'https://lupic.cdn.bcebos.com/20220708/3087013573_14_600_429.jpg'
                    }
                ],

                res: [],
				postId:''
            };
        }
        /**
         * 生命周期函数--监听页面加载
         */
        ,
        onLoad(options) {
			this.setData({
				postId:options.postId
			})
			if (options.scene) {
				let params = {};
				let pairs = options.scene.split('%26');
				for (let i = 0; i < pairs.length; i++) {
					let pair = pairs[i].split('-');
					params[pair[0]] = pair[1];
				}
				this.postId = params.uuid
			}
            this.feedPostInfo(); //查询动态详情
        },
        /**
         * 生命周期函数--监听页面初次渲染完成
         */
        onReady() {},
        /**
         * 生命周期函数--监听页面显示
         */
        onShow() {},
        /**
         * 生命周期函数--监听页面隐藏
         */
        onHide() {},
        /**
         * 生命周期函数--监听页面卸载
         */
        onUnload() {},
        /**
         * 页面相关事件处理函数--监听用户下拉动作
         */
        onPullDownRefresh() {},
        /**
         * 页面上拉触底事件的处理函数
         */
        onReachBottom() {},
        /**
         * 用户点击右上角分享
         */
        onShareAppMessage() {},

        onShareTimeline() {},
        methods: {
            // 预览图片
            previewImage: function(e) {
                console.log(e);
                var current = e.currentTarget.dataset.src;
                var url = e.currentTarget.dataset.url;
                uni.previewImage({
                    current: current,
                    // 当前显示图片的http链接
                    urls: url // 需要预览的图片http链接列表
                });
            },

            // 视频加载完成，改变视频的宽高
            videometa(e) {
                console.log(e);
                var that = this;
                //获取系统信息
                uni.getSystemInfo({
                    success(res) {
                        //视频的高
                        var height = e.detail.height;

                        //视频的宽
                        var width = e.detail.width;

                        //算出视频的比例
                        var proportion = height / width;

                        //res.windowWidth为手机屏幕的宽。
                        var windowWidth = res.windowWidth;

                        //算出当前宽度下高度的数值
                        height = proportion * windowWidth;
                        that.setData({
                            height,
                            width: windowWidth
                        });
                    }
                });
            },

            // 图片加载后判断图片宽高比例
            oneImageLoad(e) {
                const {
                    width,
                    height
                } = e.detail;
                if (height >= width) {
                    this.setData({
                        isHeightMode: true
                    });
                }
            },

            feedPostInfo() {
                //查询动态详情
                let left = this;
                let data = {
                    postId: this.postId
                };
                this.$axios.get(this.$api.getClubInfo, data).then((res) => {
                    if (res.data.code == 200) {
                        if (res.data.data.content.length > 100) {
                            left.setData({
                                moreBtns: true,
                                moreBtn: true
                            });
                        }
                        left.setData({
                            feedPostInfoList: res.data.data
                        });
                    }
                });
            },

            preview(event) {
                let src = event.currentTarget.dataset.src;
                let maparr = [];
                maparr.push({
                    type: 'video',
                    url: src
                });
                let index = event.currentTarget.dataset.index;
                // 既有视频又有图片用这个
                console.log(maparr);
                uni.previewMedia({
                    sources: maparr,
                    // 官方文档 : 素材展示列表 是个数组对象 一个url链接 一个type 判断这是视频还是图片
                    // 接收image 和 video 所以处理数据时 最好给个type 指定 image 或者 video
                    current: index
                    // 当前显示的资源序号
                });
            },

            packUp(e) {
                //收起全文
                this.setData({
                    packUpShow: !this.packUpShow,
                    moreBtns: !this.moreBtns
                });
            }
        }
    };
</script>
<style scoped>
    @import './dynamicsdetail.css';
</style>