.main {
    background: #f5f6fa;
    height: 100vh;
}


.next {
    display: flex;
    position: fixed;
    top: 120rpx;
    width: 100%;
}

.headImg {
    width: 38%;
}

.headTitle {
    font-size: 34rpx;
    color: #ffffff;
    font-weight: bold;
}

.headTitle1 {
    display: flex;
    align-items: center;
    margin-top: 56rpx;
}

.headTitle1_1 {
    color: #ffffff;
    font-size: 32rpx;
    font-weight: bold;
    margin-left: 16rpx;
}

.prosonal {
    background: url('http://cdn.xiaodingdang1.com/2024/07/04/4f95b10957644cb0ae03814f62d91fd5.png') no-repeat;
    background-size: 100% 662rpx;
}

.prosonal {
    padding: 216rpx 24rpx 24rpx;
}

.message {
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #ffffff;
}

.message1 {
    display: flex;
    align-items: center;
}

.message1_2 {
    margin-left: 16rpx;
    color: 28rpx;
}

.message1_3 {
    font-size: 32rpx;
    padding: 8rpx 40rpx;
    border: 2rpx solid #ffffff;
    border-radius: 98rpx;
}

.message1_4 {
    margin-top: 8rpx;
}

.sign {
    background: #ffffff;
    border-radius: 20rpx;
    margin-top: 30rpx;
    padding: 24rpx 24rpx 0 24rpx;
}

.title {
    color: #333333;
    font-size: 32rpx;
    font-weight: bold;
}

.sign1 {
    margin-top: 20rpx;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
}

.signAtive {
    background: #ff4f61;
    color: #ffffff;
    border-radius: 20rpx;
    width: 140rpx;
    text-align: center;
    font-size: 28rpx;
    font-weight: bold;
    margin-bottom: 30rpx;
    padding: 20rpx 0;
}

.signImg {
    margin-top: 10rpx;
}

.sign1_1 {
    background: #f5f6fa;
    border-radius: 20rpx;
    width: 140rpx;
    height: 150rpx;
    line-height: 150rpx;
    text-align: center;
    color: #333333;
    font-size: 28rpx;
    font-weight: bold;
    margin-bottom: 30rpx;
}

.sifnBtn {
    margin: 20rpx auto;
    text-align: center;
    background: #ff4f61;
    width: 376rpx;
    height: 88rpx;
    font-size: 34rpx;
    color: #ffffff;
    line-height: 88rpx;
    border-radius: 108rpx;
}

.sign1_2 {
    width: 312rpx;
    height: 150rpx;
    background: #f5f6fa;
    border-radius: 20rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.signAtive1 {
    width: 312rpx;
    height: 150rpx;
    background: #ff4f61;
    border-radius: 20rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #ffffff;
}

.sign1_3 {
    width: 50%;
    text-align: center;
}

.sign1_4 {
    width: 50%;
}

.sign1_3_1 {
    font-size: 28rpx;
    color: #333333;
    font-weight: bold;
}

.sign1_3_1_1 {
    font-size: 28rpx;
    color: #ffffff;
    font-weight: bold;
}

.sign1_3_2 {
    font-size: 24rpx;
    color: #777777;
    margin-top: 10rpx;
}

.sign1_3_2_1 {
    font-size: 24rpx;
    color: #ffffff;
    margin-top: 10rpx;
}

.popUp {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background-color: rgba(0, 0, 0, 0.2);
    z-index: 999;
}

.popUp1 {
    width: 630rpx;
    height: 705rpx;
    border-radius: 60rpx;
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #fff5db), color-stop(100%, #fefcf7));
    background: -webkit-linear-gradient(180deg, #fff5db 0%, #fefcf7 100%);
    background: -o-linear-gradient(180deg, #fff5db 0%, #fefcf7 100%);
    background: -ms-linear-gradient(180deg, #fff5db 0%, #fefcf7 100%);
    background: linear-gradient(180deg, #fff5db 0%, #fefcf7 100%);
    position: absolute;
    z-index: 100;
    top: 478rpx;
    left: 60rpx;
    text-align: center;
}

.popTitle1 {
    color: #333333;
    font-size: 44rpx;
    font-weight: bold;
    margin-top: 80rpx;
}

.popTitle2_1 {
    color: #aaaaaa;
    font-size: 28rpx;
    margin-top: 8rpx;
}

.popImg {
    position: absolute;
    top: -160rpx;
    left: 20%;
}

.price {
    display: flex;
    margin-top: 16rpx;
    align-items: center;
    justify-content: center;
}

.price1 {
    color: #333333;
    font-size: 28rpx;
}

.price2 {
    color: #ff7a00;
    font-size: 42rpx;
    font-weight: bold;
}

.getBtn {
    width: 424rpx;
    height: 80rpx;
    text-align: center;
    line-height: 80rpx;
    color: #ffffff;
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #908ff0), color-stop(100%, #b391ff));
    background: -webkit-linear-gradient(180deg, #908ff0 0%, #b391ff 100%);
    background: -o-linear-gradient(180deg, #908ff0 0%, #b391ff 100%);
    background: -ms-linear-gradient(180deg, #908ff0 0%, #b391ff 100%);
    background: linear-gradient(180deg, #908ff0 0%, #b391ff 100%);
    border-radius: 50rpx;
    margin: 48rpx auto;
}

.popUps {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background-color: rgba(0, 0, 0, 0.2);
    z-index: 999;
}

.popUp2 {
    width: 480rpx;
    height: 408rpx;
    border-radius: 60rpx;
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #ffccd1), color-stop(100%, #ffffff));
    background: -webkit-linear-gradient(180deg, #ffccd1 0%, #ffffff 100%);
    background: -o-linear-gradient(180deg, #ffccd1 0%, #ffffff 100%);
    background: -ms-linear-gradient(180deg, #ffccd1 0%, #ffffff 100%);
    background: linear-gradient(180deg, #ffccd1 0%, #ffffff 100%);
    position: absolute;
    z-index: 100;
    top: 478rpx;
    left: 124rpx;
    text-align: center;
}

.getBtn1 {
    width: 260rpx;
    height: 74rpx;
    text-align: center;
    line-height: 74rpx;
    color: #ffffff;
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #ff4f61), color-stop(100%, #ff001a));
    background: -webkit-linear-gradient(180deg, #ff4f61 0%, #ff001a 100%);
    background: -o-linear-gradient(180deg, #ff4f61 0%, #ff001a 100%);
    background: -ms-linear-gradient(180deg, #ff4f61 0%, #ff001a 100%);
    background: linear-gradient(180deg, #ff4f61 0%, #ff001a 100%);
    border-radius: 50rpx;
    margin: 48rpx auto;
    font-weight: bold;
    font-size: 36rpx;
}

.popTitle2 {
    font-size: 44rpx;
    font-weight: bold;
    margin-top: 148rpx;
    color: #ff4f61;
}