#!/usr/bin/env node

/**
 * 测试预览目录配置
 * 验证预览二维码是否正确保存到 ci/preview 目录
 */

const fs = require('fs');
const path = require('path');
const { defaultConfig } = require('../config/config-loader.js');

// 颜色输出
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

/**
 * 测试预览目录配置
 */
function testPreviewDirectory() {
  log('🧪 预览目录配置测试', 'cyan');
  log('='.repeat(50));
  
  let hasIssues = false;
  
  // 检查配置
  logInfo('检查预览配置...');
  console.log(`默认二维码输出路径: ${defaultConfig.preview.qrcodeOutputDest}`);
  
  if (defaultConfig.preview.qrcodeOutputDir) {
    console.log(`二维码输出目录: ${defaultConfig.preview.qrcodeOutputDir}`);
  }
  
  // 检查预览目录是否存在
  const previewDir = path.resolve(__dirname, '../preview');
  logInfo(`\n检查预览目录: ${previewDir}`);
  
  if (fs.existsSync(previewDir)) {
    logSuccess('预览目录存在');
    
    // 检查目录权限
    try {
      fs.accessSync(previewDir, fs.constants.W_OK);
      logSuccess('预览目录可写');
    } catch (error) {
      logError('预览目录不可写');
      hasIssues = true;
    }
    
    // 列出现有文件
    const files = fs.readdirSync(previewDir);
    if (files.length > 0) {
      logInfo(`\n现有文件 (${files.length}个):`);
      files.forEach(file => {
        const filePath = path.join(previewDir, file);
        const stats = fs.statSync(filePath);
        const size = (stats.size / 1024).toFixed(2);
        const mtime = stats.mtime.toLocaleString();
        console.log(`  ${file} (${size}KB, ${mtime})`);
      });
    } else {
      logInfo('\n预览目录为空');
    }
  } else {
    logWarning('预览目录不存在，将在首次使用时自动创建');
  }
  
  // 检查 .gitignore 文件
  const gitignorePath = path.join(previewDir, '.gitignore');
  logInfo(`\n检查 .gitignore 文件: ${gitignorePath}`);
  
  if (fs.existsSync(gitignorePath)) {
    logSuccess('.gitignore 文件存在');
    const content = fs.readFileSync(gitignorePath, 'utf8');
    if (content.includes('*.jpg')) {
      logSuccess('.gitignore 正确配置了图片文件忽略规则');
    } else {
      logWarning('.gitignore 可能缺少图片文件忽略规则');
    }
  } else {
    logWarning('.gitignore 文件不存在');
    hasIssues = true;
  }
  
  // 测试目录创建
  logInfo('\n测试目录创建...');
  const testDir = path.join(previewDir, 'test');
  
  try {
    if (!fs.existsSync(testDir)) {
      fs.mkdirSync(testDir, { recursive: true });
      logSuccess('测试目录创建成功');
      
      // 清理测试目录
      fs.rmdirSync(testDir);
      logSuccess('测试目录清理完成');
    }
  } catch (error) {
    logError(`目录创建测试失败: ${error.message}`);
    hasIssues = true;
  }
  
  log('\n📊 测试结果:', 'cyan');
  if (hasIssues) {
    logWarning('发现配置问题，请检查上述警告和错误');
    return false;
  } else {
    logSuccess('预览目录配置正确！');
    return true;
  }
}

/**
 * 显示预览目录使用说明
 */
function showUsageInfo() {
  log('\n📋 预览目录使用说明:', 'cyan');
  log('1. 预览二维码将保存到 ci/preview/ 目录', 'yellow');
  log('2. 文件命名格式: preview-{项目ID}-{环境}.jpg', 'yellow');
  log('3. 二维码文件不会提交到版本控制系统', 'yellow');
  log('4. 每次生成预览时会自动覆盖同名文件', 'yellow');
  
  log('\n💡 相关命令:', 'cyan');
  log('# 生成预览二维码', 'green');
  log('node ci/scripts/preview.js main develop', 'green');
  log('', 'green');
  log('# 查看预览目录内容', 'green');
  log('ls -la ci/preview/', 'green');
  log('', 'green');
  log('# 清理预览文件', 'green');
  log('rm ci/preview/*.jpg', 'green');
}

/**
 * 主函数
 */
function main() {
  const success = testPreviewDirectory();
  showUsageInfo();
  
  if (!success) {
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = {
  testPreviewDirectory,
  showUsageInfo
};
