import { reactive, computed } from 'vue';
import { getConversationMessages } from '../api/customerService.js';
import { formatTime, getMessageType } from '../utils/timeUtils.js';

/**
 * 处理消息数据，将 senderType 转换为 type 字段
 * @param {Object} message - 原始消息对象
 * @returns {Object} 处理后的消息对象
 */
function processMessage(message) {
  return {
    ...message,
    type: getMessageType(message, 'user'), // 添加 type 字段用于模板显示
    time: formatTime(message.createdTime), // 格式化时间
  };
}

/**
 * 聊天消息分页管理 Composable（简化版）
 * 专门用于聊天页面的下拉刷新和往上滚动加载更多历史消息场景
 * @param {number} conversationId - 会话ID（可选，可后续通过 setConversationId 设置）
 * @param {Object} options - 配置选项
 */
export function useChatPagination(conversationId = null, options = {}) {
  const {
    pageSize = 20,
    initialLoad = true,
  } = options;

  // 响应式状态
  const state = reactive({
    messages: [],
    currentPage: 1,
    pageSize,
    total: 0,
    loading: false,
    refreshing: false,
    loadingMore: false,
    hasMore: true,
    error: null,
    conversationId,
  });

  // 计算属性
  const isEmpty = computed(() => state.messages.length === 0 && !state.loading);
  const canLoadMore = computed(() => state.hasMore && !state.loading && !state.loadingMore);

  /**
   * 设置会话ID
   * @param {number} newConversationId - 新的会话ID
   */
  const setConversationId = (newConversationId) => {
    state.conversationId = newConversationId;
  };

  /**
   * 重置状态
   */
  const reset = () => {
    state.messages = [];
    state.currentPage = 1;
    state.total = 0;
    state.hasMore = true;
    state.error = null;
  };

  /**
   * 请求消息数据
   * @param {number} page - 页码
   * @param {boolean} isRefresh - 是否为刷新操作
   * @param {boolean} isLoadMore - 是否为加载更多操作
   */
  const fetchMessages = async (page = 1, isRefresh = false, isLoadMore = false) => {
    if (!state.conversationId) {
      console.warn('useChatPagination: conversationId 不能为空');
      return { success: false, error: 'conversationId 不能为空' };
    }

    try {
      // 设置加载状态
      if (isRefresh) {
        state.refreshing = true;
      } else if (isLoadMore) {
        state.loadingMore = true;
      } else {
        state.loading = true;
      }

      state.error = null;

      const params = {
        conversationId: state.conversationId,
        page,
        size: state.pageSize,
      };

      console.log('获取聊天消息:', params);

      const response = await getConversationMessages(params);

      if (response.code === 200) {
        const { data } = response;
        const { records = [], total = 0, totalPages = 0, currentPage = page } = data;

        // 处理消息数据，添加 type 字段和格式化时间
        const processedMessages = records.map(processMessage);

        // API 返回的是降序（最新在前），需要反转为升序（最新在后）
        const reversedMessages = processedMessages.reverse();

        // 更新分页信息
        state.currentPage = currentPage;
        state.total = total;
        state.hasMore = currentPage < totalPages;

        if (isRefresh || page === 1) {
          // 刷新或首次加载：替换消息列表
          state.messages = reversedMessages;
        } else {
          // 加载更多：追加到消息列表前面（因为是历史消息）
          state.messages = [...reversedMessages, ...state.messages];
        }

        console.log('消息获取成功:', {
          page: currentPage,
          totalPages,
          total,
          newMessages: reversedMessages.length,
          totalMessages: state.messages.length,
          hasMore: state.hasMore,
        });

        return {
          success: true,
          data: reversedMessages,
          hasMore: state.hasMore,
        };
      } else {
        const errorMsg = response.message || '获取消息失败';
        state.error = errorMsg;
        console.error('获取消息失败:', response);
        return {
          success: false,
          error: errorMsg,
        };
      }
    } catch (error) {
      const errorMsg = error.message || '网络请求失败';
      state.error = errorMsg;
      console.error('获取消息异常:', error);
      return {
        success: false,
        error: errorMsg,
      };
    } finally {
      // 清除加载状态
      state.loading = false;
      state.refreshing = false;
      state.loadingMore = false;
    }
  };

  /**
   * 初始化加载第一页
   */
  const loadFirstPage = () => {
    reset();
    return fetchMessages(1);
  };

  /**
   * 下拉刷新
   */
  const onRefresh = async () => {
    console.log('下拉刷新聊天消息');
    reset();
    const result = await fetchMessages(1, true);
    
    // 显示刷新结果
    if (result.success) {
      uni.showToast({
        title: '刷新成功',
        icon: 'success',
        duration: 1500,
      });
    } else {
      uni.showToast({
        title: result.error || '刷新失败',
        icon: 'error',
        duration: 2000,
      });
    }
    
    return result;
  };

  /**
   * 滚动到顶部加载更多历史消息
   */
  const onLoadMore = async () => {
    if (!canLoadMore.value) {
      console.log('无法加载更多历史消息:', {
        hasMore: state.hasMore,
        loading: state.loading,
        loadingMore: state.loadingMore,
      });
      return { success: false, error: '无法加载更多' };
    }

    console.log('加载更多历史消息');
    const nextPage = state.currentPage + 1;
    const result = await fetchMessages(nextPage, false, true);

    // 显示加载结果
    if (!result.success) {
      uni.showToast({
        title: result.error || '加载失败',
        icon: 'error',
        duration: 2000,
      });
    }

    return result;
  };

  /**
   * 添加新消息（用于实时消息）
   * @param {Object} message - 新消息对象
   * @param {boolean} prepend - 是否添加到开头，默认false（添加到末尾）
   */
  const addMessage = (message, prepend = false) => {
    if (prepend) {
      state.messages.unshift(message);
    } else {
      state.messages.push(message);
    }
    state.total += 1;
  };

  /**
   * 更新消息
   * @param {string|number} messageId - 消息ID
   * @param {Object} updates - 更新的字段
   */
  const updateMessage = (messageId, updates) => {
    const index = state.messages.findIndex(msg => msg.id === messageId);
    if (index !== -1) {
      state.messages[index] = { ...state.messages[index], ...updates };
      return true;
    }
    return false;
  };

  /**
   * 删除消息
   * @param {string|number} messageId - 消息ID
   */
  const removeMessage = (messageId) => {
    const index = state.messages.findIndex(msg => msg.id === messageId);
    if (index !== -1) {
      state.messages.splice(index, 1);
      state.total = Math.max(0, state.total - 1);
      return true;
    }
    return false;
  };

  /**
   * 查找消息
   * @param {string|number} messageId - 消息ID
   */
  const findMessage = (messageId) => {
    return state.messages.find(msg => msg.id === messageId);
  };

  // 初始化加载
  if (initialLoad && state.conversationId) {
    loadFirstPage();
  }

  return {
    // 响应式状态
    state,
    
    // 计算属性
    isEmpty,
    canLoadMore,
    
    // 核心方法
    setConversationId,
    loadFirstPage,
    onRefresh,
    onLoadMore,
    
    // 消息操作方法
    addMessage,
    updateMessage,
    removeMessage,
    findMessage,
    
    // 工具方法
    reset,
  };
}
