.main {
    min-height: 100vh;
    .bg {
        background-color: #f6f6f6;
        width: 100%;
        position: relative;
       .content {
           background-image: url('https://cdn.xiaodingdang1.com/2024/11/09/c1e8a3924cef4dc38e2765ef8cd1f36e.png');
           background-size: cover;
           // background: linear-gradient(to bottom, rgba(255,255,255,1), rgba(255,255,255,0)), url('https://cdn.xiaodingdang1.com/2024/11/09/c1e8a3924cef4dc38e2765ef8cd1f36e.png') no-repeat;
           overflow-x: hidden;
            padding: 184rpx 24rpx 28rpx 24rpx;
            ::-webkit-scrollbar {
                display: none;
            }
          .top-container::-webkit-scrollbar {
              display: none;
          }
          .top-container::after {
              content: '';
              display: block;
              width: 28rpx;
          }
           .top-container {
                overflow-x: scroll;
                overflow-y: hidden;
                white-space: nowrap;
                .top {
                    height: 564rpx; 
                    background-color: white;
                    border-radius: 20rpx
                }
                .top1 {
                    // width: 792rpx;
                    // margin-right: 30rpx;
                }
                .top2 {
                    width: 668rpx;
                    display: inline-block;
                    margin-right: 28rpx;
                }
                .top2:last-child{
                    margin-right: 0;
                }
           }
           .top-container > .top2:last-child::after {
             display: none
           }
       }
    }
    .invite {
        background-color: #f6f6f6;
        &-title {
            margin-bottom: 24rpx;
            padding-left: 30rpx;
            display: flex;
            align-items: center;
            &-dot {
                margin-right: 20rpx;
                width: 8rpx;
                height: 30rpx;
                background: #FF4F61;
                border-radius: 40rpx 40rpx 40rpx 40rpx;
            }
        }
        &-card {
            overflow: auto;
            white-space: nowrap;
            &-list {
                border-radius: 20rpx 20rpx 20rpx 20rpx;
                margin: 0 24rpx 24rpx 24rpx;
                background: #fff;
            }
        }
    }
}

    
