<template>
	<view>
		<view class="content">
			<template v-if="communityCommentList.length > 0">
				<view class="contents" v-for="(item, index) in communityCommentList" :key="index" >
					<view class="head">
						<image :src="avatar || $defaultAvatar" mode=""
							style="width: 80rpx; height: 80rpx; border-radius: 100rpx" />                                                  
						<view class="heads">
							<view class="head1">
								<view class="head1_1">
									{{ nickname || $defaultName }}
								</view>
								<!--     <view class="head1_2">
                                    <image
                                        src="http://cdn.xiaodingdang1.com/1/120240125/5b82ebb2-4d5c-43ae-83b8-f4621e4f3336.png"
                                        mode="" style="width: 40rpx; height: 34rpx" class="head1Img" />
                                    <view class="head1_3">高级育婴师</view>
                                </view> -->
							</view>
							<view class="head2">{{ item.hisTimeStr }}</view>
						</view>
					</view>
					<view class="content1">
						<text class="content1_2">{{ item.content }}</text>
					</view>
					<!-- <view class="content1" v-if="item.type == 2">
                        <text class="content1_1">评论了我：</text>
                        <text class="content1_2">{{ item.commentContent }}</text>
                    </view> -->

					<!--    <view class="content1" v-if="item.type == 3">
                        <text class="content1_1">回复我：</text>
                        <text class="content1_2">{{ item.commentContent }}</text>
                    </view> -->

					 <view class="content2" @tap="next(item.postId)">
                        <image :src="item.postContentPhotos[0] || $defaultAvatar" mode=""
                            style="width: 140rpx; height: 140rpx; border-radius: 10rpx" />
                        <view class="content2_2">
                            {{ item.postContent }}
                        </view>
                    </view>

				<!-- 	<view class="content3" v-if="item.type == 3">
						<view class="content3_1">
							{{ item.meComment }}
						</view>
					</view> -->
				</view>
			</template>
			<empty-vue v-if="isFinshed && communityCommentList.length == 0"></empty-vue>
		</view>
		<new-request-loading></new-request-loading>
	</view>
</template>

<script>
	import emptyVue from '@/components/empty.vue';
	import dayjs from 'dayjs';
	import 'dayjs/locale/zh-cn'; // 导入中文语言包

	// 设置dayjs为中文
	dayjs.locale('zh-cn');

	export default {
		components: {
			emptyVue
		},
		data() {
			return {
				avatar:'',
				nickname:'',
				communityLikeList: [],
				//查询宝妈社区动态我的消息点赞通知
				communityCommentList: [],
				//查询宝妈社区动态我的消息评论通知
				messageList: [], // 消息列表
				currentIndex: 0,
				tabList: [{
						name: '评论',
						unreadCount: 0
					},
					{
						name: '点赞',
						unreadCount: 0
					},
					{
						name: '客服',
						unreadCount: 0
					}
				],
				isFinshed: false
			};
		}
		/**
		 * 生命周期函数--监听页面加载
		 */
		,
		onLoad(options) {
			let userInfo=uni.getStorageSync('userInfo')
			this.communityComment();
			this.avatar=userInfo.avatar
			this.nickname=userInfo.nickname
		},
		/**
		 * 生命周期函数--监听页面初次渲染完成
		 */
		onReady() {},
		/**
		 * 生命周期函数--监听页面显示
		 */
		onShow() {},
		/**
		 * 生命周期函数--监听页面隐藏
		 */
		onHide() {},
		/**
		 * 生命周期函数--监听页面卸载
		 */
		onUnload() {},
		/**
		 * 页面相关事件处理函数--监听用户下拉动作
		 */
		onPullDownRefresh() {},
		/**
		 * 页面上拉触底事件的处理函数
		 */
		onReachBottom() {},
		/**
		 * 用户点击右上角分享
		 */
		onShareAppMessage() {},

		onShareTimeline() {},
		methods: {
			next(postId){
				console.log(postId);
				uni.navigateTo({
				    url: '/pageA/pageB/community/staffdetail?postId='+postId 
				});
			},
			communityComment() {
				//查询宝妈社区动态我的消息评论通知

				// let data = {
				//     pageSize: 1, 
				//     pageNum: 1000,
				//     type: 2
				// };
				this.changeLoading(true)
				this.isFinshed = false
				this.$axios.get(this.$api.myComment).then((res) => {
					if (res.data.code == 200) {
						this.setData({
							communityCommentList: res.data.data
						});
						// 标记评论消息为已读
						// if (this.tabList[0].unreadCount > 0) {
						//     this.markCommentAsRead();
						// }
					}
				}).finally(() => {
					this.isFinshed = true
					this.changeLoading(false)
				})
			},
			// 格式化时间显示 (改用dayjs)
			formatTime(timeStr) {
				if (!timeStr) return '';

				const messageDate = dayjs(timeStr);
				const now = dayjs();

				// 同一天显示时间
				if (messageDate.format('YYYY-MM-DD') === now.format('YYYY-MM-DD')) {
					return messageDate.format('HH:mm');
				}

				// 一周内显示星期几
				const daysDiff = now.diff(messageDate, 'day');
				if (daysDiff < 7) {
					return messageDate.format('ddd'); // 自动显示为周一、周二等
				}

				// 其他情况显示日期
				return messageDate.format('M月D日');
			},

			// 获取状态文本
			getStatusText(status) {
				const statusMap = {
					0: '已结束',
					1: '进行中',
					2: '排队中'
				};
				return statusMap[status] || '未知状态';
			},

			// 获取状态样式类
			getStatusClass(status) {
				const classMap = {
					0: 'status-ended',
					1: 'status-active',
					2: 'status-queue'
				};
				return classMap[status] || '';
			},

			// 进入聊天页面
			enterChat(item) {
				uni.navigateTo({
					url: `/subPackages/customerService/pages/userChat`
				});
			},

			getUnreadCounts() {
				// TODO 获取所有未读消息数量
				// this.$axios.get(this.$axios.getUnreadCounts).then((res) => {
				//     if (res.data.code == 200) {
				//         let counts = res.data.data;
				//         this.setData({
				//             tabList: [
				//                 {
				//                     name: '评论',
				//                     unreadCount: counts.commentCount || 0
				//                 },
				//                 {
				//                     name: '点赞',
				//                     unreadCount: counts.likeCount || 0
				//                 },
				//                 {
				//                     name: '消息',
				//                     unreadCount: counts.messageCount || 0
				//                 }
				//             ]
				//         });
				//     }
				// });
			},

			markLikeAsRead() {
				this.$axios.post(this.$api.readLikes).then((res) => {
					if (res.data.code == 200) {
						this.tabList[1].unreadCount = 0;
						this.setData({
							tabList: this.tabList
						});
					}
				});
			},

			markCommentAsRead() {
				this.$axios.post(this.$api.readComments).then((res) => {
					if (res.data.code == 200) {
						this.tabList[0].unreadCount = 0;
						this.setData({
							tabList: this.tabList
						});
					}
				});
			}
		}
	};
</script>
<style lang="less" scoped>
	@import './message.less';

	.head1_1 {
		font-size: 30rpx;
		color: #333333;
		font-weight: bold;
	}

	.badge {
		position: absolute;
		top: -6rpx;
		right: -6rpx;
		min-width: 30rpx;
		height: 30rpx;
		border-radius: 15rpx;
		background-color: #ff4d4f;
		color: #ffffff;
		font-size: 20rpx;
		text-align: center;
		line-height: 30rpx;
		padding: 0 6rpx;
	}

	.title-sel,
	.title-sel-selected {
		position: relative;
	}

	/* 微信风格聊天列表样式 */
	.chat-item {
		display: flex;
		padding: 20rpx 30rpx;
		position: relative;
		background-color: #ffffff;
		transition: all 0.2s;
		border-bottom: 1rpx solid #f5f5f5;

		&:active {
			background-color: rgba(0, 0, 0, 0.05);
		}
	}

	.chat-avatar {
		position: relative;
		margin-right: 20rpx;

		image {
			width: 96rpx;
			height: 96rpx;
			border-radius: 8rpx;
			background-color: #f5f5f7;
		}
	}

	.chat-content {
		flex: 1;
		display: flex;
		flex-direction: column;
		justify-content: center;
		overflow: hidden;
	}

	.chat-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 12rpx;
	}

	.chat-name {
		font-size: 32rpx;
		color: #333333;
		font-weight: 500;
	}

	.chat-time {
		font-size: 24rpx;
		color: #999999;
	}

	.chat-footer {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.chat-message {
		flex: 1;
		font-size: 28rpx;
		color: #888888;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		max-width: 450rpx;
	}

	.unread-badge {
		position: absolute;
		top: -10rpx;
		right: -10rpx;
		min-width: 36rpx;
		height: 36rpx;
		border-radius: 18rpx;
		background-color: #ff4d4f;
		color: #ffffff;
		font-size: 22rpx;
		text-align: center;
		line-height: 36rpx;
		padding: 0 6rpx;
		box-shadow: 0 2rpx 4rpx rgba(250, 81, 81, 0.2);
	}

	.status-tag {
		font-size: 20rpx;
		padding: 2rpx 12rpx;
		border-radius: 10rpx;
		margin-left: 10rpx;
	}

	.status-ended {
		background-color: #f5f5f5;
		color: #999999;
	}

	.status-active {
		background-color: #e6f7ff;
		color: #1890ff;
	}

	.status-queue {
		background-color: #fff7e6;
		color: #fa8c16;
	}
</style>