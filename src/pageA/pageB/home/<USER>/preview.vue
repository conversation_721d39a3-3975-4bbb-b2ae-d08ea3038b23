<template>
  <view class="page-container">
    <web-view :src="webViewUrl"></web-view>
  </view>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { onLoad, onShareAppMessage } from '@dcloudio/uni-app';
import utils from '@/utils/util';
import { getInvitationApi } from './api/rehabilitation';     

const state = reactive({});
const webViewUrl = ref('');

onLoad((options) => {
  const { tenantId, userId, cardId } = options;
  Object.assign(state, {
    cardId,
    tenantId,
    userId,
  });

  generateWebView(cardId);
  getInvitation(cardId);
});

const invitationData = ref({});
const getInvitation = async (cardId) => {
  const res = await getInvitationApi(cardId);
  invitationData.value = res.data;
};

onShareAppMessage((res) => {
  console.log('分享参数:', res);
  return {
    title: invitationData.value.title || '生日邀请',
    path: `pageA/pageB/home/<USER>/preview?${utils.queryParams({
      ...state,
    })}`,
    imageUrl: invitationData.value.coverImageUrl,
  };
});

const generateWebView = (cardId) => {
  webViewUrl.value = `https://h5.xiaodingdang1.com/wap.html?${utils.queryParams(
    {
      cardId,
      tenantId: state.tenantId,
      userId: state.userId,
      source: 'miniProgram',
    },
  )}#wechat_redirect`;
  console.log('webViewUrl:', webViewUrl.value);
};
</script>
<style scoped lang="less">
.page-container {
  height: 100%;
  width: 100%;
  overflow: hidden;
}
</style>
