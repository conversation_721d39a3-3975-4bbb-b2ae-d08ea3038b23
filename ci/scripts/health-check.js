#!/usr/bin/env node

/**
 * CI系统健康检查脚本
 * 快速验证CI系统的基本功能是否正常
 */

const fs = require('fs');
const path = require('path');
const { projects, getProjectNames } = require('../config/config-loader.js');

// 颜色输出
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

/**
 * 检查配置文件
 */
function checkConfigFiles() {
  log('📄 检查配置文件', 'blue');
  
  const configFiles = [
    { path: 'ci/config/projects.json', name: 'JSON配置文件' },
    { path: 'ci/config/ci.config.js', name: 'CI配置文件' },
    { path: 'ci/config/config-loader.js', name: '配置加载器' }
  ];

  let allGood = true;
  
  configFiles.forEach(file => {
    const fullPath = path.resolve(__dirname, '../..', file.path);
    if (fs.existsSync(fullPath)) {
      logSuccess(`${file.name} 存在`);
    } else {
      logError(`${file.name} 不存在: ${file.path}`);
      allGood = false;
    }
  });

  return allGood;
}

/**
 * 检查项目配置
 */
function checkProjects() {
  log('\n🏢 检查项目配置', 'blue');
  
  const projectNames = getProjectNames();
  logInfo(`找到 ${projectNames.length} 个项目: ${projectNames.join(', ')}`);

  let allGood = true;

  projectNames.forEach(name => {
    const project = projects[name];
    log(`\n检查项目: ${name}`, 'yellow');
    
    // 检查基本配置
    if (!project.appid) {
      logError('缺少AppID');
      allGood = false;
    } else {
      logSuccess(`AppID: ${project.appid}`);
    }

    if (!project.defaultTenantId) {
      logWarning('未设置默认租户ID');
    } else {
      logSuccess(`默认租户ID: ${project.defaultTenantId}`);
    }

    // 检查私钥文件
    if (project.privateKeyPath) {
      const keyPath = path.resolve(__dirname, '../..', project.privateKeyPath);
      if (fs.existsSync(keyPath)) {
        logSuccess('私钥文件存在');
      } else {
        logWarning('私钥文件不存在');
      }
    } else {
      logError('未配置私钥路径');
      allGood = false;
    }

    // 检查环境配置
    const envCount = Object.keys(project.environments || {}).length;
    if (envCount > 0) {
      logSuccess(`配置了 ${envCount} 个环境`);
    } else {
      logError('未配置任何环境');
      allGood = false;
    }
  });

  return allGood;
}

/**
 * 检查脚本文件
 */
function checkScripts() {
  log('\n🔧 检查脚本文件', 'blue');
  
  const scripts = [
    'ci/scripts/upload.js',
    'ci/scripts/preview.js',
    'ci/scripts/manage.js',
    'ci/scripts/build-config-injector.js'
  ];

  let allGood = true;

  scripts.forEach(script => {
    const scriptPath = path.resolve(__dirname, '../..', script);
    if (fs.existsSync(scriptPath)) {
      logSuccess(`${path.basename(script)} 存在`);
    } else {
      logError(`${path.basename(script)} 不存在`);
      allGood = false;
    }
  });

  return allGood;
}

/**
 * 检查依赖
 */
function checkDependencies() {
  log('\n📦 检查依赖', 'blue');
  
  try {
    require('miniprogram-ci');
    logSuccess('miniprogram-ci 已安装');
  } catch (error) {
    logError('miniprogram-ci 未安装');
    return false;
  }

  return true;
}

/**
 * 运行健康检查
 */
function runHealthCheck() {
  log('🏥 CI系统健康检查', 'blue');
  log('='.repeat(40));

  const checks = [
    { name: '配置文件', fn: checkConfigFiles },
    { name: '项目配置', fn: checkProjects },
    { name: '脚本文件', fn: checkScripts },
    { name: '依赖检查', fn: checkDependencies }
  ];

  let passed = 0;
  const results = [];

  checks.forEach(check => {
    const result = check.fn();
    results.push({ name: check.name, passed: result });
    if (result) passed++;
  });

  // 显示总结
  log('\n📊 检查结果', 'blue');
  log('='.repeat(40));

  results.forEach(result => {
    const status = result.passed ? '✅ 正常' : '❌ 异常';
    console.log(`  ${result.name}: ${status}`);
  });

  log('');
  if (passed === checks.length) {
    logSuccess(`🎉 系统健康状态良好 (${passed}/${checks.length})`);
    logInfo('所有CI功能可以正常使用');
  } else {
    logWarning(`⚠️ 发现问题 (${passed}/${checks.length} 正常)`);
    logInfo('请检查上述异常项目');
  }

  return passed === checks.length;
}

/**
 * 显示快速修复建议
 */
function showQuickFixes() {
  log('\n🔨 快速修复建议', 'yellow');
  log('='.repeat(40));
  
  console.log('如果发现问题，可以尝试以下修复方法：');
  console.log('');
  console.log('1. 重新生成配置文件:');
  console.log('   pnpm run ci:regenerate');
  console.log('');
  console.log('2. 运行完整测试:');
  console.log('   pnpm run ci:test-full');
  console.log('');
  console.log('3. 检查项目配置:');
  console.log('   pnpm run ci:manage check');
  console.log('');
  console.log('4. 验证私钥路径:');
  console.log('   pnpm run ci:manage validate');
  console.log('');
}

/**
 * 显示帮助信息
 */
function showHelp() {
  log('CI系统健康检查脚本', 'blue');
  log('');
  log('使用方法:', 'yellow');
  log('  node ci/scripts/health-check.js [选项]', 'white');
  log('');
  log('选项:', 'yellow');
  log('  --help, -h    显示帮助信息', 'white');
  log('  --fix         显示修复建议', 'white');
  log('');
  log('功能说明:', 'yellow');
  log('  - 检查配置文件完整性', 'white');
  log('  - 验证项目配置正确性', 'white');
  log('  - 确认脚本文件存在', 'white');
  log('  - 检查必要依赖', 'white');
  log('');
}

// 主函数
function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--help') || args.includes('-h')) {
    showHelp();
    return;
  }

  const healthy = runHealthCheck();
  
  if (args.includes('--fix') || !healthy) {
    showQuickFixes();
  }

  process.exit(healthy ? 0 : 1);
}

// 运行主函数
if (require.main === module) {
  main();
}

module.exports = {
  runHealthCheck
};
