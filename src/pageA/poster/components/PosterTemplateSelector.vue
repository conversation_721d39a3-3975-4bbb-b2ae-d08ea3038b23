<template>

	<!-- 动态海报模板 -->
	<template v-if="type === 'dynamics'">
		<DynamicsPosterTemplate :type="type" :post-id="postId" :uuid="uuid" />
	</template>

	<!-- 员工海报模板 -->
	<template v-else-if="type === 'staff'">
		<StaffPosterTemplate :type="type" :post-id="postId" :uuid="uuid" />
	</template>

	<!-- 创建海报模板 -->
	<template v-else-if="type === 'create'">
		<CreatePosterTemplate :type="type" :diary-id="diaryId" :uuid="uuid" />
	</template>

	<!-- 发送海报模板 -->
	<template v-else-if="type === 'sending'">
		<SendingPosterTemplate :type="type" :post-id="postId" :uuid="uuid" />
	</template>
	<!-- 今日餐单海报模板 -->
	<template v-else-if="type === 'menu'">
		<menuPosterTemplate :type="type" :post-id="postId" :uuid="uuid" />
	</template>
	<!-- 默认模板（动态海报） -->
	<template v-else>
		<DynamicsPosterTemplate :type="'dynamics'" :post-id="postId" :uuid="uuid" />
	</template>
</template>

<script setup>
	import DynamicsPosterTemplate from './DynamicsPosterTemplate.vue';
	import StaffPosterTemplate from './StaffPosterTemplate.vue';
	import CreatePosterTemplate from './CreatePosterTemplate.vue';
	import SendingPosterTemplate from './SendingPosterTemplate.vue';
	import menuPosterTemplate from './menuPosterTemplate.vue';

	// Props 定义
	const props = defineProps({
		// 海报类型
		type: {
			type: String,
			required: true,
			validator: (value) => ['menu', 'dynamics', 'staff', 'create', 'sending'].includes(value)
		},
		// 动态ID（用于动态海报和员工海报）
		postId: {
			type: String,
			default: ''
		},
		// 日记ID（用于创建海报）
		diaryId: {
			type: String,
			default: ''
		},
		// uuid
		uuid: {
			type: String,
			default: ''
		}
	});
</script>

<style scoped>
	/* 模板选择器本身不需要样式，样式由具体的模板组件提供 */
</style>