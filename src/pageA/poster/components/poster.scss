/* 海报公共样式文件 */

/* ==================== 基础容器样式 ==================== */
.poster-container {
  width: 100%;
  background-color: #4c4c4c;
}

.poster-wrapper {
  height: 100%;
  width: 100%;
  background-size: cover;
  padding-bottom: 40rpx;
}

.poster-main {
  margin: 0 20rpx;
}

.poster-content {
  padding: 40rpx 20rpx;
  width: 100%;
  position: relative;
  border-radius: 40rpx;
}

/* ==================== 顶部信息样式 ==================== */
.poster-header {
  padding-left: 50rpx;
  height: 250rpx;
  display: flex;
  align-items: center;
}

.poster-header__logo {
  width: 100rpx;
  height: 100rpx;
  border-radius: 100rpx;
  margin-right: 10rpx;
}

.poster-header__info {
  text {
    display: block;
  }
}

.poster-header__title {
  font-size: 40rpx;
  color: black;
  font-weight: 600;
}

.poster-header__subtitle {
  font-size: 26rpx;
  color: #33333369;
}

/* ==================== 宝妈信息样式 ==================== */
.poster-mom-info {
  padding: 0 60rpx;
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
}

.poster-mom-info__avatar {
  width: 200rpx;
  height: 200rpx;
  background-size: cover;
  background-image: url('http://cdn.xiaodingdang1.com/2025/01/20/0b86ed83621045cea5a084d6f56de44d.png');
  margin-right: 30rpx;
  position: relative;
}

.poster-mom-info__avatar image {
  position: absolute;
  left: 26rpx;
  top: 24rpx;
  width: 152rpx;
  height: 152rpx;
  border-radius: 152rpx;
}

.poster-mom-info__details {
  flex: 1;
  font-size: 26rpx;
  font-weight: 400;
  color: #333333;
  line-height: 40rpx;
}

/* ==================== 卡片样式 ==================== */
.poster-card {
  background: white;
  border-radius: 40rpx;
  overflow: hidden;
  margin-bottom: 30rpx;
}

.poster-card__header {
  padding: 20rpx 30rpx;
  border-radius: 40rpx 40rpx 0 0;
  background-size: cover;
}

/* ==================== 用户信息样式 ==================== */
.poster-user {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.poster-user__avatar {
  margin-right: 20rpx;
}

.poster-user__avatar image {
  width: 80rpx;
  height: 80rpx;
  border-radius: 80rpx;
}

.poster-user__name {
  font-size: 36rpx;
  color: #ff6e00;
  font-weight: 600;
}

.poster-user__badge {
  margin-left: 12rpx;
  margin-bottom: 4rpx;
  display: flex;
  align-items: center;
  height: 44rpx;
  padding: 0 20rpx;
  background: linear-gradient(90deg, #f6e0b7 0%, #ffe7c0 100%);
  border-radius: 40rpx;
  font-weight: 500;
  font-size: 26rpx;
}

.poster-user__badge image {
  height: 26rpx;
  width: 26rpx;
  display: inline-block;
  margin-right: 8rpx;
}

.poster-user__badge-text {
  font-weight: 500;
  font-size: 26rpx;
  color: #7a4e2b;
}

/* ==================== 标签样式 ==================== */
.poster-tag {
  margin-bottom: 12rpx;
}

.poster-tag__item {
  position: relative;
  width: fit-content;
  display: inline-block;
  border-radius: 10rpx;
  height: 40rpx;
  padding: 0 16rpx;
  font-weight: 400;
  font-size: 26rpx;
  background: #1fa2ff;
  color: #e9f2ff;
}

.poster-tag__item::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* ==================== 内容样式 ==================== */
.poster-text {
  font-weight: 400;
  font-size: 26rpx;
  color: #333333;
  line-height: 40rpx;
  flex-wrap: wrap;
  white-space: break-spaces;
  margin: 8rpx 0;
}

.poster-image {
  width: 100%;
  height: 616rpx;
  padding: 0 30rpx 10rpx 30rpx;
}

.poster-image image {
  width: 100%;
  height: 100%;
  border-radius: 10rpx;
}

/* ==================== 底部信息样式 ==================== */
.poster-footer {
  padding: 40rpx 0;
  display: flex;
  align-items: flex-end;
}

.poster-footer--flex {
  display: flex;
  align-items: flex-start;
}

.poster-footer__left,
.poster-footer__right {
  flex: 1;
  padding: 40rpx 0;
}

.poster-footer__left--padding {
  padding-left: 30rpx;
}

.poster-footer__right--center {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.poster-footer__comment {
  font-size: 24rpx;
  color: #333333;
}

.poster-footer__line {
  width: 20rpx;
  height: 2rpx;
  background-color: #333333;
  margin: 20rpx 0;
}

.poster-footer__subtitle {
  font-size: 30rpx;
  color: #333333;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.poster-footer__title {
  font-size: 24rpx;
  color: #33333369;
  line-height: 35rpx;
}

.poster-footer__qr {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 10rpx;
}

/* ==================== 装饰元素样式 ==================== */
.poster-decoration {
  position: absolute;
  top: -10rpx;
  right: 50rpx;
  z-index: 2;
}

.poster-decoration image {
  height: 80rpx;
  width: 80rpx;
}

/* ==================== 工具类样式 ==================== */
.flex {
  display: flex;
  align-items: center;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.text-center {
  text-align: center;
}

/* ==================== 响应式和适配 ==================== */
@media screen and (max-width: 750rpx) {
  .poster-mom-info {
    padding: 0 40rpx;
  }

  .poster-header {
    padding-left: 30rpx;
  }
}
