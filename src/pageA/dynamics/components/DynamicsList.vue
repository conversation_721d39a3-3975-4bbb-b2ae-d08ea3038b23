<template>
  <view class="dynamics-list">
    <view 
      class="dynamics-list__item"
      v-for="(item, index) in dynamicsList" 
      :key="index"
      :id="`dynamic-item-${index}`"
    >
      <DynamicItem
        :dynamicData="item"
        :index="index"
        :likeUsers="likeList[index]"
        :showPoster="showPoster"
        :showPushMessage="showPushMessage"
        @more-actions="handleMoreActions"
        @like="handleLike"
        @update-dynamic="handleUpdateDynamic"
      />
    </view>
  </view>
</template>

<script setup>
import { getCurrentInstance } from 'vue'
import { RESPONSE_CODES } from '../constants.js'
import { updateLikeList } from '../utils.js'
import DynamicItem from './DynamicItem.vue'

const { proxy } = getCurrentInstance()

const props = defineProps({
  dynamicsList: {
    type: Array,
    default: () => []
  },
  likeList: {
    type: Array,
    default: () => []
  },
  showPoster: {
    type: <PERSON>olean,
    default: false
  },
  showPushMessage: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits([
  'update-dynamic', 
  'update-like-list', 
  'show-comment-input',
  'hide-shadow'
])

// 处理更多操作
const handleMoreActions = (e) => {
  const postId = e.currentTarget.dataset.postid
  const index = e.currentTarget.dataset.index
  
  // 更新显示状态
  emit('update-dynamic', index, 'show', true)
  emit('show-comment-input', postId, index)
}

// 处理点赞
const handleLike = async (postId, index) => {
  try {
    const data = { postId }
    const userInfo = uni.getStorageSync('userInfo') || {}
    const currentIsLike = props.dynamicsList[index].isLike
    
    // 更新点赞列表
    const newLikeList = updateLikeList(
      props.likeList, 
      index, 
      userInfo, 
      postId, 
      currentIsLike
    )
    
    // 更新状态
    emit('update-dynamic', index, 'isLike', !currentIsLike)
    emit('update-dynamic', index, 'show', false)
    emit('update-like-list', index, newLikeList)
    emit('hide-shadow')
    
    // 调用API
    const res = await proxy.$axios.get(proxy.$api.feedPostLikes, data)
    if (res.data.code !== RESPONSE_CODES.SUCCESS) {
      // 如果API调用失败，回滚状态
      emit('update-dynamic', index, 'isLike', currentIsLike)
      emit('update-like-list', index, props.likeList[index])
    }
  } catch (error) {
    console.error('点赞操作失败:', error)
  }
}

// 处理动态更新（从子组件传递上来）
const handleUpdateDynamic = (index, field, value) => {
  emit('update-dynamic', index, field, value)
}

// 关闭所有打开的 ActionMenu
const closeAllActionMenus = () => {
  props.dynamicsList.forEach((item, index) => {
    if (item.show) {
      emit('update-dynamic', index, 'show', false)
    }
  })
  emit('hide-shadow')
}

// 暴露方法给父组件
defineExpose({
  closeAllActionMenus
})
</script>

<style scoped lang="less">
.dynamics-list {
  border-top-left-radius: 20rpx;
  border-top-right-radius: 20rpx;
  background: #fff;
  padding-bottom: calc(40rpx + 150rpx + env(safe-area-inset-bottom));

  &__item {
    margin-bottom: 0;
  }
}
</style>
