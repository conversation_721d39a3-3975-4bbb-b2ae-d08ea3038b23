# 租户ID配置修复说明

## 问题描述

在项目编辑界面中，缺少租户ID字段的设置，导致：
1. 无法在编辑项目时设置默认租户ID
2. 无法为特定环境设置专用租户ID
3. 环境显示信息不完整，看不到当前使用的租户ID

## 修复内容

### 1. 项目编辑表单增强

**文件**: `ci/server/public/index.html`

#### 添加的字段：
- **默认租户ID字段**: 在项目基本信息中添加 `defaultTenantId` 输入框
- **环境租户ID字段**: 在每个环境配置中添加 `tenantId` 输入框

#### 表单验证：
- 默认租户ID为必填字段
- 租户ID必须为纯数字格式
- 环境租户ID为可选字段

### 2. 项目信息显示优化

#### 项目卡片显示：
- 在项目名称和AppID下方显示默认租户ID
- 使用醒目的蓝色字体突出显示

#### 环境选择器优化：
- 环境选项显示格式：`环境名称 (租户ID: 具体ID)`
- 自动计算有效租户ID（优先级：环境专用 > 项目默认 > 系统默认）

### 3. 数据结构更新

#### 项目表单对象：
```javascript
const projectForm = reactive({
    id: '',
    name: '',
    appid: '',
    projectPath: '',
    privateKeyPath: '',
    defaultTenantId: '',  // 新增
    ignoresText: '',
    environments: {}
});
```

#### 环境配置结构：
```javascript
environments: {
    develop: {
        name: '开发版',
        desc: '开发环境版本',
        buildCommand: 'pnpm run build:mp-weixin --mode test',
        tenantId: ''  // 新增
    }
}
```

### 4. 样式优化

添加了租户ID相关的CSS样式：
- `.project-tenant`: 租户ID容器样式
- `.tenant-label`: 标签样式
- `.tenant-value`: 值样式，使用等宽字体和蓝色高亮

## 租户ID优先级

系统按以下优先级确定使用的租户ID：

1. **环境特定租户ID** (`environments[env].tenantId`)
2. **项目默认租户ID** (`defaultTenantId`)
3. **系统默认租户ID** (`194338`)

## 验证工具

创建了测试脚本 `ci/scripts/test-tenant-config.js` 用于验证配置：

```bash
# 运行租户ID配置测试
node ci/scripts/test-tenant-config.js
```

## 使用说明

### 编辑项目配置：
1. 在Web界面中点击项目的"编辑"按钮
2. 在"默认租户ID"字段中设置项目的默认租户ID
3. 在每个环境配置中，可选择性设置环境专用的租户ID
4. 如果环境不设置专用租户ID，将使用项目默认租户ID

### 查看租户ID信息：
1. 项目卡片会显示默认租户ID
2. 环境选择器会显示每个环境实际使用的租户ID
3. 可以清楚看到租户ID的来源（环境专用/项目默认/系统默认）

## 配置示例

```json
{
  "projects": {
    "main": {
      "name": "艾尔母婴商家管理平台",
      "appid": "wx1df89e340decbc7a",
      "defaultTenantId": "194338",
      "environments": {
        "develop": {
          "name": "开发版",
          "tenantId": "194338"
        },
        "trial": {
          "name": "体验版"
          // 未设置tenantId，将使用defaultTenantId
        }
      }
    }
  }
}
```

## 兼容性

- 现有配置文件完全兼容
- 如果项目没有设置 `defaultTenantId`，系统会使用默认值 `194338`
- 如果环境没有设置 `tenantId`，会继承项目的 `defaultTenantId`

## 测试结果

运行测试脚本显示所有现有项目的租户ID配置都是正确的：
- main项目：默认租户ID 194338，所有环境都有专用设置
- xinxiyue项目：默认租户ID 123456，所有环境都有专用设置

修复完成后，项目编辑功能现在可以正确设置和显示租户ID信息。
