// 分页配置
export const PAGINATION_CONFIG = {
  DEFAULT_PAGE_SIZE: 10,
  INITIAL_PAGE: 1
}

// 响应状态码
export const RESPONSE_CODES = {
  SUCCESS: 200
}

// 加载状态
export const LOAD_STATUS = {
  HAS_MORE: 1,
  NO_MORE: 2
}

// 页面路由
export const PAGE_ROUTES = {
  POSTER: '/pageA/poster/poster',
  PUSH_MESSAGE: '/pageA/pageB/community/pushMessage/pushMessage',
  DYNAMICS_DETAIL: '/pageA/pageB/dynamics/dynamicsdetail'
}

// 用户角色
export const USER_ROLES = {
  SALES: 'SALES',
  ADMIN: 'ADMIN',
  CUSTOMER: 'CUSTOMER'
}

// 内容显示配置
export const CONTENT_CONFIG = {
  MAX_CONTENT_LENGTH: 100,
  MAX_LIKE_DISPLAY: 14
}

// 订阅消息模板ID
export const SUBSCRIBE_TEMPLATE_IDS = [
  '4ozrWQOn4Z0lxEfPNXyVzNaKCWaanMNYypzOu8PH9I0'
]
