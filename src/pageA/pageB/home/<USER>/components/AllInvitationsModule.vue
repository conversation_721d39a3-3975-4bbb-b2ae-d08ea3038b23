<template>
  <view class="all-invitations-module">
    <!-- 分类标签 -->
    <view class="category-tabs">
      <view
        v-for="(item, index) in categoryList"
        :key="index"
        :class="['category-item', categoryType === item.type ? 'active' : '']"
        @click="categoryClick(item.type)"
      >
        {{ item.name }}
      </view>
    </view>

    <!-- 列表内容 -->
    <scroll-view
      class="scroll-container"
      scroll-y
      :show-scrollbar="false"
      :lower-threshold="100"
      @scrolltolower="handleScrollToLower"
    >
      <view class="list-content">
        <InvitationGridCard
          v-for="(item, index) in list"
          :key="index"
          :item="item"
        />
      </view>
      <view v-if="loading" class="loading-view">
        <u-loading mode="circle" color="#FF4F61" size="40"></u-loading>
        数据加载中...
      </view>
    </scroll-view>
  </view>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted } from 'vue';
import InvitationGridCard from './InvitationGridCard.vue';

// 获取当前实例
const instance = getCurrentInstance();

// 响应式数据
const list = ref([]);
const categoryList = ref([
  {
    name: '全部',
    type: -1,
  },
  {
    name: '翻页',
    type: 0,
  },
  {
    name: '长页',
    type: 1,
  },
]);
const categoryType = ref(-1); // 默认显示全部
const pageNum = ref(1);
const pageSize = ref(10);
const total = ref(0);
const loading = ref(false);

// 生命周期
onMounted(() => {
  getList();
});

// 获取列表数据
const getList = () => {
  if (loading.value) return; // 防止重复请求

  loading.value = true;
  const params = {
    pageNum: pageNum.value,
    pageSize: pageSize.value,
  };

  // 如果选择了特定分类，添加type参数
  if (categoryType.value !== -1) {
    params.type = categoryType.value;
  }

  instance.proxy.$axios
    .get(instance.proxy.$api.getRehabilitationList, params)
    .then((res) => {
      const result = [];
      const { rows, total: totalCount } = res.data;

      // 设置总数据量
      total.value = totalCount || 0;

      rows.forEach((item) => {
        result.push({
          ...item,
          id: item?.id,
          title: item?.title || '请帖',
          total: item?.pageNum || 0,
          backGroundImage: item?.coverImage,
        });
      });
      list.value = list.value.concat(result);
    })
    .catch((error) => {
      console.error('获取全部请帖数据失败:', error);
    })
    .finally(() => {
      loading.value = false;
    });
};

// 分类点击处理
const categoryClick = (type) => {
  if (categoryType.value !== type) {
    categoryType.value = type;
    pageNum.value = 1;
    list.value = [];
    getList();
  }
};

// 触底加载
const handleScrollToLower = () => {
  // 判断是否还有更多数据：已加载数据数量 < 总数据量
  if (!loading.value && list.value.length < total.value) {
    pageNum.value = pageNum.value + 1;
    getList();
  }
};
</script>

<style scoped lang="less">
.all-invitations-module {
  height: 100%;
  display: flex;
  flex-direction: column;

  .category-tabs {
    display: flex;
    background-color: white;
    padding: 20rpx 0;

    .category-item {
      flex: 1;
      text-align: center;
      font-size: 28rpx;
      color: #333333;
      position: relative;
    }

    .active {
      color: #ff4f61;
      font-weight: 500;

      &::after {
        content: '';
        position: absolute;
        bottom: -10rpx;
        left: 50%;
        transform: translateX(-50%);
        width: 44rpx;
        height: 4rpx;
        background-color: #ff4f61;
        border-radius: 2rpx;
      }
    }
  }

  .scroll-container {
    flex: 1;
    height: 0;
    padding: 20rpx 0 120rpx 0;

    .list-content {
      display: grid;
      gap: 20rpx;
      grid-template-columns: 1fr 1fr;
      width: 100%;
      padding: 0 24rpx;
    }
    .loading-view {
      display: flex;
      justify-content: center;
      align-items: flex-end;
      padding: 20rpx 0;
      gap: 10rpx;
      color: #666;
    }
  }
}
</style>
