{"uni-app": {"scripts": {"test": {"title": "微信小程序——测试版", "env": {"ENV_TYPE": "test", "UNI_PLATFORM": "mp-weixin", "VITE_BASE_API": "https://test-api.xiaodingdang1.com/"}}, "pro": {"title": "微信小程序——正式版", "env": {"ENV_TYPE": "pro", "UNI_PLATFORM": "mp-weixin", "VITE_BASE_API": "https://admin-api.xiaodingdang1.com/"}}}}, "name": "uni-mother-club", "version": "0.0.0", "scripts": {"dev:custom": "uni -p", "dev:h5": "uni", "dev:h5:ssr": "uni --ssr", "dev:mp-alipay": "uni -p mp-alipay", "dev:mp-baidu": "uni -p mp-baidu", "dev:mp-jd": "uni -p mp-jd", "dev:mp-kuaishou": "uni -p mp-kua<PERSON>ou", "dev:mp-lark": "uni -p mp-lark", "dev:mp-qq": "uni -p mp-qq", "dev:mp-toutiao": "uni -p mp-to<PERSON><PERSON>", "dev:mp-weixin": "uni -p mp-weixin --mode test", "dev:mp-xhs": "uni -p mp-xhs", "dev:quickapp-webview": "uni -p quickapp-webview", "dev:quickapp-webview-huawei": "uni -p quickapp-webview-huawei", "dev:quickapp-webview-union": "uni -p quickapp-webview-union", "build:custom": "uni build -p", "build:h5": "uni build", "build:h5:ssr": "uni build --ssr", "build:mp-alipay": "uni build -p mp-alipay", "build:mp-baidu": "uni build -p mp-baidu", "build:mp-jd": "uni build -p mp-jd", "build:mp-kuaishou": "uni build -p mp-kuaishou", "build:mp-lark": "uni build -p mp-lark", "build:mp-qq": "uni build -p mp-qq", "build:mp-toutiao": "uni build -p mp-to<PERSON>ao", "build:mp-weixin": "uni build -p mp-weixin -- mode production", "build:mp-xhs": "uni build -p mp-xhs", "build:quickapp-webview": "uni build -p quickapp-webview", "build:quickapp-webview-huawei": "uni build -p quickapp-webview-huawei", "build:quickapp-webview-union": "uni build -p quickapp-webview-union", "upload:main:dev": "node ci/scripts/upload.js main develop", "upload:main:trial": "node ci/scripts/upload.js main trial", "upload:main:release": "node ci/scripts/upload.js main release", "upload:test:dev": "node ci/scripts/upload.js test develop", "upload:test:trial": "node ci/scripts/upload.js test trial", "upload:demo:dev": "node ci/scripts/upload.js demo develop", "upload:demo:trial": "node ci/scripts/upload.js demo trial", "preview:main:dev": "node ci/scripts/preview.js main develop", "preview:main:trial": "node ci/scripts/preview.js main trial", "preview:test:dev": "node ci/scripts/preview.js test develop", "preview:demo:dev": "node ci/scripts/preview.js demo develop", "ci:server": "node ci/server/index.js", "ci:gui": "node ci/scripts/start-gui.js", "ci:manage": "node ci/scripts/manage.js", "ci:test": "node ci/scripts/test-config-injection.js", "ci:test-tenant": "node ci/scripts/test-tenant-injection.js", "ci:test-full": "node ci/scripts/test-full-workflow.js", "ci:regenerate": "node ci/scripts/regenerate-config.js", "ci:demo": "node ci/scripts/demo-tenant-build.js --demo"}, "dependencies": {"@dcloudio/uni-app": "3.0.0-4030620241128001", "@dcloudio/uni-app-harmony": "3.0.0-4030620241128001", "@dcloudio/uni-app-plus": "3.0.0-4030620241128001", "@dcloudio/uni-components": "3.0.0-4030620241128001", "@dcloudio/uni-h5": "3.0.0-4030620241128001", "@dcloudio/uni-mp-alipay": "3.0.0-4030620241128001", "@dcloudio/uni-mp-baidu": "3.0.0-4030620241128001", "@dcloudio/uni-mp-jd": "3.0.0-4030620241128001", "@dcloudio/uni-mp-kuaishou": "3.0.0-4030620241128001", "@dcloudio/uni-mp-lark": "3.0.0-4030620241128001", "@dcloudio/uni-mp-qq": "3.0.0-4030620241128001", "@dcloudio/uni-mp-toutiao": "3.0.0-4030620241128001", "@dcloudio/uni-mp-weixin": "3.0.0-4030620241128001", "@dcloudio/uni-mp-xhs": "3.0.0-4030620241128001", "@dcloudio/uni-quickapp-webview": "3.0.0-4030620241128001", "@qiun/ucharts": "^2.5.0-20230101", "dayjs": "^1.11.13", "echarts": "^5.6.0", "file-loader": "^6.2.0", "html2canvas": "^1.4.1", "less": "^4.2.1", "less-loader": "^12.2.0", "marked": "^15.0.12", "mp-html": "^2.5.1", "text-encoding-shim": "^1.0.5", "vue": "^3.4.21", "vue-i18n": "^9.1.9", "vue-virtual-scroll-list": "^2.3.5", "vuex": "^4.1.0"}, "devDependencies": {"@dcloudio/types": "^3.4.8", "@dcloudio/uni-automator": "3.0.0-4030620241128001", "@dcloudio/uni-cli-shared": "3.0.0-4030620241128001", "@dcloudio/uni-stacktracey": "3.0.0-4030620241128001", "@dcloudio/vite-plugin-uni": "3.0.0-4030620241128001", "@vue/runtime-core": "^3.4.21", "chokidar": "^4.0.3", "cors": "^2.8.5", "cross-env": "^7.0.3", "express": "^5.1.0", "miniprogram-ci": "^2.1.14", "multer": "^2.0.1", "open": "^10.1.2", "sass": "^1.82.0", "sass-loader": "^16.0.4", "vite": "5.2.8", "ws": "^8.18.2"}}