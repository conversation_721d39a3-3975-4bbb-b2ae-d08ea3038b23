<template>
	<view class="main">
		<view class="content">
			<view class="head">
				<view class="cancel"></view>
				<view class="publish" @tap="publish">发表</view>
			</view>
			<textarea :value="content" maxlength="1000" placeholder-class="input-placeholder" placeholder="这一刻你想说些什么？"
				class="textarea" @input="searchInput" />
			<upload-vue class="publish-upload" @clickFile="clickAlbumImg"></upload-vue>
			<!-- 假设itemList是一个列表 -->
			<view class="item-list">
				<view v-for="(item, index) in itemList" :key="index" class="item">
					<image :src="item.img" class="item-img" lazy-load />
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import uploadVue from "@/components/upload.vue";
	export default {
		components: {
			uploadVue,
		},
		data() {
			return {
				videosList: [],
				videosShow: true,
				photosShow: true,
				videos: [],
				content: "",
				//动态内容
				images: [],
				//动态图片
				imgUrl: [],
			};
		},
		/**
		 * 生命周期函数--监听页面加载
		 */
		onLoad(options) {},
		/**
		 * 生命周期函数--监听页面初次渲染完成
		 */
		onReady() {},
		/**
		 * 生命周期函数--监听页面显示
		 */
		onShow() {},
		/**
		 * 生命周期函数--监听页面隐藏
		 */
		onHide() {},
		/**
		 * 生命周期函数--监听页面卸载
		 */
		onUnload() {},
		/**
		 * 页面相关事件处理函数--监听用户下拉动作
		 */
		onPullDownRefresh() {},
		/**
		 * 页面上拉触底事件的处理函数
		 */
		onReachBottom() {},
		/**
		 * 用户点击右上角分享
		 */
		onShareAppMessage() {},

		onShareTimeline() {},
		methods: {
			clickAlbumImg(value) {
				let imgUrl = [];
				let videos = [];
				if (value && value.length > 0) {
					value.forEach((item) => {
						item.type == "video" ? videos.push(item.url) : imgUrl.push(item.url);
					});
					this.setData({
						imgUrl,
						videos,
					});
				} else {
					this.setData({
						imgUrl: [],
						videos: [],
					});
				}
			},
			searchInput(e) {
				//输入框获取值
				this.setData({
					content: e.detail.value,
				});
			},
			async publish() {
				//发布
				if (this.content == "") {
					uni.showToast({
						title: "请填写发布内容",
						icon: "none",
						duration: 3000, //持续的时间
					});
					return;
				}
				console.log(this.imgUrl);
				let data = {
					content: this.content,
				};
				if (this.imgUrl.length > 0) {
					let img=[]
					this.imgUrl.forEach(item=>{
						img.push(item.url)
					})
					data.images = img
				}
				if (this.videos.length > 0) {
					data.videos = this.videos;
				}
				this.changeLoading(true);
				let res = null;
				res = await this.$axios.post(this.$api.getDeptSave, data);
				if (res?.data?.code == 200) {
					setTimeout(() => {
						uni.navigateBack({
							delta: 1,
						});
					}, 200);
				}
			},
		},
	};
</script>
<style scoped>
	@import "./branchdynamic.less";
</style>
<style>
	.input-placeholder {
		color: #bcbcbc;
	}
</style>