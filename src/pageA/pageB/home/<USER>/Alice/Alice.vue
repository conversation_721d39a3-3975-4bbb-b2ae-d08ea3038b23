<template>
    <view>
        <web-view :src="src" @messag="getMessage"></web-view>
    </view>
</template>

<script>
    export default {
        data() {
            return {
                src: ''
            };
        }
        /**
         * 生命周期函数--监听页面加载
         */
        ,
        onLoad(options) {
            const t = new Date().getTime();
            let src = decodeURIComponent(options.src);
            this.setData({
                src
            });

            // console.log(src,this.src);
        },
        /**
         * 生命周期函数--监听页面初次渲染完成
         */
        onReady() {},
        /**
         * 生命周期函数--监听页面显示
         */
        onShow() {},
        /**
         * 生命周期函数--监听页面隐藏
         */
        onHide() {},
        /**
         * 生命周期函数--监听页面卸载
         */
        onUnload() {},
        /**
         * 页面相关事件处理函数--监听用户下拉动作
         */
        onPullDownRefresh() {},
        /**
         * 页面上拉触底事件的处理函数
         */
        onReachBottom() {},
        /**
         * 用户点击右上角分享
         */
        onShareAppMessage: function(res) {
            // if (res.from === 'button') {
            //   console.log("来自页面内转发按钮");
            //   console.log(res.target);
            // } else {
            //   console.log("来自右上角转发菜单");
            // }
            return {
                title: '宝宝请帖',
                path: 'pageA/pageB/home/<USER>/Alice/Alice'
                // 可以添加其他自定义内容，‌如imageUrl等
            };
        },
        methods: {
            closeWebview() {
                console.log(111);
            },

            getMessage: function(res) {
                console.log('H5传递过来的参数', res);
                // this.setData({
                //   Title: res.detail.data[res.detail.data.length - 1].foo
                // })
            }
        }
    };
</script>
<style>
    /* @import './Alice.css'; */
</style>