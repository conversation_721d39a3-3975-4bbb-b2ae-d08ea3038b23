<template>
	<view class="main">
	<view class="head">
		<view class="head-tab">
	       <view v-for="(item,index) in listTab" :key="index" :class="index==tabIndex?'activeTab':'head-tab1'" @click="listTab(index)" >{{item}}</view>
		</view>
		<view class="head-time">
	     <view class="head-time1" bindtap="prevMonth">
			 <image src="http://cdn.xiaodingdang1.com/2025/06/25/75e64c7243c247c9b1edcb23a495f381.png" mode="" style="width: 20rpx;height: 28rpx;"/>
		 </view>
		 <view class="head-time2">
			<text>{{currentYear}}年{{currentMonth}}月</text>
		 </view>
		 <view class="head-time1" bindtap="nextMonth">
			<image src="http://cdn.xiaodingdang1.com/2025/06/25/7acef938a5c74e81b7cc4e4c5efa8cfd.png" mode="" style="width: 30rpx;height: 30rpx;"/>
		</view>
		</view>
	</view>
	
	<view class="calendar-container">
	    <!-- 月份切换区 -->
	    <view class="header">
	      <text @click="prevMonth">◀</text>
	      <text>{{currentYear}}年{{currentMonth}}月</text>
	      <text @click="nextMonth">▶</text>
	    </view>
	
	    <!-- 星期标题 -->
	    <view class="week-header">
	      <text v-for="day in weekDays" :key="day">{{day}}</text>
	    </view>
	
	    <!-- 日期网格 -->
	    <view class="days-grid">
	      <view 
	        v-for="(day,index) in daysArray" 
	        :key="index"
	        :class="[
	          'day-cell',
	          day.isCurrentMonth?'':'other-month',
	          day.isToday?'today':'',
	          day.isSelected?'selected':''
	        ]"
	        @click="selectDay(day)">
	        {{day.date}}
	        <view v-if="day.hasEvent" class="event-mark"></view>
	      </view>
	    </view>
	  </view>
	  
		<view class="customer-list">
		         <view class="customer-list1">
					 <view class="hue2">
						 <view class="hue"></view>
						 <view class="huetitle">定制餐单</view>
					 </view>
					 <view class="hue2">
						<view class="hue1"></view>
						<view class="huetitle">会所餐单</view>
					</view>
				 </view>
				 <view class="customer-list3" >
					<view class="customer-list2" v-for="item,index in [1,2,3,4,5,6,7,8,9]" :key="index">
						<view class="num">1001</view>
						<view class="customer-list4">
						   <view class="customer-list4-1">周婷婷</view>
						   <view class="customer-list4-2">2025-06-25</view>
						</view>
					 </view>
	
				 </view>
			
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				listTab:['全部','客户餐单'],
					tabIndex:0,
					 currentYear: new Date().getFullYear(),
					      currentMonth: new Date().getMonth() + 1,
					      weekDays: ['日','一','二','三','四','五','六'],
					      daysArray: []
			}
		},
		  mounted() {
		    this.generateCalendar()
		  },
		onLoad(options) {
			this.generateCalendarData();
		},
		methods: {
			listTab(index){
				  this.setData({
					tabIndex:index
				  })
			  },
			     generateCalendar() {
			        const firstDay = new Date(this.currentYear, this.currentMonth - 1, 1)
			        const lastDay = new Date(this.currentYear, this.currentMonth, 0)
			        const startWeekDay = firstDay.getDay()
			        const totalDays = lastDay.getDate()
			        const today = new Date()
			  
			        let days = []
			        // 添加上月残余
			        const prevMonthDays = new Date(this.currentYear, this.currentMonth - 1, 0).getDate()
			        for (let i = 0; i < startWeekDay; i++) {
			          days.push(this.createDayData(
			            prevMonthDays - startWeekDay + i + 1,
			            false
			          ))
			        }
			  
			        // 添加本月日期
			        for (let i = 1; i <= totalDays; i++) {
			          days.push(this.createDayData(
			            i,
			            true,
			            this.currentYear === today.getFullYear() && 
			            this.currentMonth === today.getMonth() + 1 && 
			            i === today.getDate()
			          ))
			        }
			  
			        // 添加下月开头
			        const remaining = 42 - days.length
			        for (let i = 1; i <= remaining; i++) {
			          days.push(this.createDayData(i, false))
			        }
			  
			        this.daysArray = days
			      },
			      createDayData(date, isCurrentMonth, isToday = false) {
			        return {
			          date,
			          isCurrentMonth,
			          isToday,
			          fullDate: `${this.currentYear}-${this.currentMonth}-${date}`,
			          hasEvent: Math.random() > 0.7,
			          isSelected: false
			        }
			      },
			      prevMonth() {
			        if (this.currentMonth === 1) {
			          this.currentYear--
			          this.currentMonth = 12
			        } else {
			          this.currentMonth--
			        }
			        this.generateCalendar()
			      },
			      nextMonth() {
			        if (this.currentMonth === 12) {
			          this.currentYear++
			          this.currentMonth = 1
			        } else {
			          this.currentMonth++
			        }
			        this.generateCalendar()
			      },
			      selectDay(day) {
			        this.daysArray.forEach(d => d.isSelected = false)
			        day.isSelected = true
			        uni.showToast({
			          title: `选中:${day.fullDate}`,
			          icon: 'none'
			        })
			      }
			    }
		}
</script>

<style lang="less" scoped>
	@import './list.less';
</style>