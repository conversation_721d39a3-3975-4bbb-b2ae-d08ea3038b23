#!/usr/bin/env node

/**
 * 小程序多项目管理工具
 * 提供项目配置管理、私钥检查等功能
 */

const fs = require('fs');
const path = require('path');
const { projects, getProjectNames, validateProject } = require('../config/config-loader.js');
const {
  validatePrivateKeyPath,
  generatePrivateKeyPath,
  extractAppIdFromPrivateKeyPath,
  generateProjectTemplate
} = require('../utils/path-helper.js');

// 命令行参数解析
const args = process.argv.slice(2);
const command = args[0];

// 颜色输出
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

// 显示帮助信息
function showHelp() {
  log('小程序多项目管理工具', 'cyan');
  log('');
  log('使用方法:', 'yellow');
  log('  node scripts/manage.js <命令>', 'white');
  log('');
  log('可用命令:', 'yellow');
  log('  list      - 列出所有项目配置', 'white');
  log('  check     - 检查项目配置和私钥文件', 'white');
  log('  info      - 显示项目详细信息', 'white');
  log('  template  - 生成项目配置模板', 'white');
  log('  validate  - 验证私钥文件路径格式', 'white');
  log('  help      - 显示帮助信息', 'white');
  log('');
  log('示例:', 'yellow');
  log('  node scripts/manage.js list', 'white');
  log('  node scripts/manage.js check', 'white');
  log('  node scripts/manage.js info main', 'white');
}

// 列出所有项目
function listProjects() {
  log('📋 项目列表', 'cyan');
  log('='.repeat(50));
  
  const projectNames = getProjectNames();
  
  projectNames.forEach((name, index) => {
    const project = projects[name];
    log(`${index + 1}. ${name}`, 'yellow');
    log(`   名称: ${project.name}`, 'white');
    log(`   AppID: ${project.appid}`, 'white');
    log(`   默认租户ID: ${project.defaultTenantId || '未设置'}`, 'white');
    log(`   环境: ${Object.keys(project.environments).join(', ')}`, 'white');
    log('');
  });
  
  log(`总计: ${projectNames.length} 个项目`, 'green');
}

// 检查项目配置
function checkProjects() {
  log('🔍 检查项目配置', 'cyan');
  log('='.repeat(50));
  
  const projectNames = getProjectNames();
  let allValid = true;
  
  projectNames.forEach((name) => {
    log(`检查项目: ${name}`, 'yellow');
    
    // 验证项目配置
    const validation = validateProject(name);
    if (!validation.valid) {
      logError(`配置错误: ${validation.error}`);
      allValid = false;
      return;
    }
    
    const project = projects[name];

    // 验证私钥文件路径格式
    const pathValidation = validatePrivateKeyPath(project.privateKeyPath);
    if (!pathValidation.valid) {
      logWarning(`私钥路径格式不正确: ${pathValidation.error}`);
      const expectedPath = generatePrivateKeyPath(project.appid);
      logInfo(`建议路径: ${expectedPath}`);
      allValid = false;
    } else {
      logSuccess(`私钥路径格式正确: ${project.privateKeyPath}`);
    }

    // 检查私钥文件是否存在
    if (fs.existsSync(project.privateKeyPath)) {
      logSuccess(`私钥文件存在: ${project.privateKeyPath}`);
    } else {
      logWarning(`私钥文件不存在: ${project.privateKeyPath}`);
      allValid = false;
    }
    
    // 检查构建路径
    if (fs.existsSync(project.projectPath)) {
      logInfo(`构建路径存在: ${project.projectPath}`);
    } else {
      logWarning(`构建路径不存在: ${project.projectPath} (需要先构建)`);
    }
    
    log('');
  });
  
  if (allValid) {
    logSuccess('🎉 所有项目配置检查通过！');
  } else {
    logWarning('⚠️ 部分项目配置存在问题，请检查上述提示');
  }
}

// 显示项目详细信息
function showProjectInfo(projectName) {
  if (!projectName) {
    logError('请指定项目名称');
    logInfo('可用项目: ' + getProjectNames().join(', '));
    return;
  }
  
  const validation = validateProject(projectName);
  if (!validation.valid) {
    logError(validation.error);
    return;
  }
  
  const project = projects[projectName];
  
  log(`📊 项目信息: ${projectName}`, 'cyan');
  log('='.repeat(50));
  
  log('基本信息:', 'yellow');
  log(`  项目名称: ${project.name}`, 'white');
  log(`  AppID: ${project.appid}`, 'white');
  log(`  默认租户ID: ${project.defaultTenantId || '未设置'}`, 'white');
  log(`  构建路径: ${project.projectPath}`, 'white');
  log(`  私钥路径: ${project.privateKeyPath}`, 'white');
  log('');
  
  log('环境配置:', 'yellow');
  Object.entries(project.environments).forEach(([env, config]) => {
    log(`  ${env}:`, 'white');
    log(`    名称: ${config.name}`, 'white');
    log(`    描述: ${config.desc}`, 'white');
    log(`    租户ID: ${config.tenantId || project.defaultTenantId || '未设置'}`, 'white');
    log(`    构建命令: ${config.buildCommand}`, 'white');
    log('');
  });
  
  log('文件状态:', 'yellow');
  log(`  私钥文件: ${fs.existsSync(project.privateKeyPath) ? '✅ 存在' : '❌ 不存在'}`, 'white');
  log(`  构建路径: ${fs.existsSync(project.projectPath) ? '✅ 存在' : '⚠️ 不存在'}`, 'white');
  
  if (project.ignores && project.ignores.length > 0) {
    log('');
    log('忽略文件:', 'yellow');
    project.ignores.forEach(ignore => {
      log(`  - ${ignore}`, 'white');
    });
  }
}

// 生成项目配置模板
function generateTemplate() {
  const template = {
    your_project_id: {
      name: '项目名称',
      appid: 'wx1234567890abcdef',
      projectPath: '../../dist/build/mp-weixin',
      privateKeyPath: '../private/private.wx1234567890abcdef.key',
      defaultTenantId: '194338',
      ignores: ['node_modules/**/*'],
      environments: {
        develop: {
          name: '开发版',
          desc: '开发环境版本',
          buildCommand: 'pnpm run build:mp-weixin --mode test',
          tenantId: '194338'
        },
        trial: {
          name: '体验版',
          desc: '体验环境版本',
          buildCommand: 'pnpm run build:mp-weixin --mode trial',
          tenantId: '194338'
        },
        release: {
          name: '正式版',
          desc: '生产环境版本',
          buildCommand: 'pnpm run build:mp-weixin --mode production',
          tenantId: '194338'
        }
      }
    }
  };

  log('📝 项目配置模板', 'cyan');
  log('='.repeat(50));
  console.log(JSON.stringify(template, null, 2));
  log('');
  log('使用说明:', 'yellow');
  console.log('1. 将 your_project_id 替换为实际的项目ID');
  console.log('2. 将 wx1234567890abcdef 替换为实际的AppID');
  console.log('3. 私钥文件路径会自动根据AppID生成为: private.{appid}.key');
  console.log('4. 根据需要调整租户ID和构建命令');
  console.log('5. 将此配置添加到 ci/config/projects.json 的 projects 对象中');
}

// 验证所有项目的私钥路径格式
function validatePrivateKeyPaths() {
  log('🔍 验证私钥路径格式', 'cyan');
  log('='.repeat(50));

  const projectNames = getProjectNames();
  let allValid = true;

  projectNames.forEach((name) => {
    const project = projects[name];
    log(`检查项目: ${name}`, 'yellow');

    const pathValidation = validatePrivateKeyPath(project.privateKeyPath);
    if (!pathValidation.valid) {
      logError(`路径格式错误: ${pathValidation.error}`);
      logInfo(`当前路径: ${project.privateKeyPath}`);

      const expectedPath = generatePrivateKeyPath(project.appid);
      logInfo(`建议路径: ${expectedPath}`);

      const extractedAppId = extractAppIdFromPrivateKeyPath(project.privateKeyPath);
      if (extractedAppId && extractedAppId !== project.appid) {
        logWarning(`路径中的AppID (${extractedAppId}) 与配置中的AppID (${project.appid}) 不匹配`);
      }

      allValid = false;
    } else {
      logSuccess(`路径格式正确: ${project.privateKeyPath}`);
    }

    log('');
  });

  if (allValid) {
    logSuccess('🎉 所有项目的私钥路径格式都正确！');
  } else {
    logWarning('⚠️ 部分项目的私钥路径格式需要修正');
    log('');
    log('私钥文件命名规范:', 'yellow');
    log('  格式: private.{appid}.key', 'white');
    log('  示例: private.wx1df89e340decbc7a.key', 'white');
  }
}

// 主函数
function main() {
  switch (command) {
    case 'list':
      listProjects();
      break;
    case 'check':
      checkProjects();
      break;
    case 'info':
      showProjectInfo(args[1]);
      break;
    case 'template':
      generateTemplate();
      break;
    case 'validate':
      validatePrivateKeyPaths();
      break;
    case 'help':
    case '--help':
    case '-h':
      showHelp();
      break;
    default:
      if (!command) {
        showHelp();
      } else {
        logError(`未知命令: ${command}`);
        logInfo('使用 "node scripts/manage.js help" 查看帮助信息');
      }
  }
}

// 运行主函数
main();
