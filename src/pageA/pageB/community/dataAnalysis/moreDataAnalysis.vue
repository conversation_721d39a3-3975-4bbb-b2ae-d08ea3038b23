<template>
	<navigation title="最近访问" :background="bgColor"></navigation>
	<view class="main">
		<view class="content">
			<view class="recently">
				<view class="recently1">
					最近访问
				</view>
			</view>
			<view class="recentlys">
				<view class="analysisList" v-for="item,index in lastList" :key="index">
					<view class="analysisList3">
						<image
							:src="item.avatar?item.avatar:'http://cdn.xiaodingdang1.com/2024/11/08/6d8894bb65e1418880530c88baf5b001.png'"
							mode="" style="width: 110rpx;height: 110rpx;border-radius: 100rpx;"></image>
						<view class="analysisList1">
							<view class="analysisList1_1">
								{{item.nickname==null||item.nickname==''?'微信用户':item.nickname}}
							</view>
							<view class="analysisList1_2">
								<view class="analysisList1_3">
									{{item.ipLocation==null?'---':item.ipLocation}}
								</view>
								<view class="analysisList1_4">
									{{item.tel==null?'---':item.tel}}
								</view>
							</view>
							<view class="analysisList1_5" v-if="type==1">
								<view class="analysisList1_6">
									最近访问：
								</view>
								<view class="analysisList1_7">
									{{item.lastLoginTime==null?'---':item.lastLoginTime}}
								</view>
							</view>
							<view class="analysisList1_5" v-if="type==2">
								<view class="analysisList1_6">
									访问时长：
								</view>
								<view class="analysisList1_7">
									{{item.viewTimeStr==null?'---':item.viewTimeStr}}
								</view>
							</view>
							<view class="analysisList1_5" v-if="type==3">
								<view class="analysisList1_6">
									访问次数：
								</view>
								<view class="analysisList1_7">
									{{item.viewNum==null?'---':item.viewNum}}
								</view>
							</view>
						</view>
					</view>
					<view class="analysisList2" @click="ringup(item.tel)">
						联系客户
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				bgColor: '',
				longestList: [],
				maxList: [],
				lastList: [],
				type: '',
				pageNum: 1,
				types: '',
				module:''
			}
		},
		onLoad(options) {
			this.type = options.type
			this.module=options.module
			this.lastList=[]
			this.pageNum=1
			if (options.type == 1) {
				this.getlastList()
			} else if (options.type == 2) {
				this.getlongestList(options.module)
			} else {
				this.getmaxList(options.module)
			}
			// this.getlongestList(options.module) //访问时间最长
			// this.getmaxList(options.module) //浏览次数最多
		},
		onShow() {

		},
		methods: {
			ringup(tel){
				// 拨打电话
				const phoneNumber =tel; // 客户的电话号码
				uni.makePhoneCall({
				    phoneNumber: phoneNumber,
				    success: () => {
				        console.log('拨打电话成功！');
				    },
				    fail: () => {
				        console.log('拨打电话失败！');
				    }
				});
			},
			getlongestList(module) {
				//访问时间最长
				let data = {
					pageSize: 10,
					pageNum: this.pageNum,
					orderBy: 'viewTime',
					module: module
				};
				this.$axios.get(this.$api.mList, data).then((res) => {
					if (res.data.code == 200) {
						let data = res.data.rows
						this.types = data.length == 10 ? 1 : 2;
						let lastList = this.lastList.concat(data);
						this.setData({
							lastList: lastList
						});
					}
				});
			},
			getmaxList(module) {
				//浏览次数最多
				let data = {
					pageSize: 10,
					pageNum: this.pageNum,
					orderBy: 'viewNum',
					module: module
				};
				this.$axios.get(this.$api.mList, data).then((res) => {
					if (res.data.code == 200) {
						let data = res.data.rows
						this.types = data.length == 10 ? 1 : 2;
						let lastList = this.lastList.concat(data);
						this.setData({
							lastList: lastList
						});
					}
				});
			},
			getlastList() {
				//获取所有节
				let data = {
					pageSize: 10,
					pageNum: this.pageNum
				};
				this.$axios.get(this.$api.lastList, data).then((res) => {
					if (res.data.code == 200) {
						let data = res.data.rows
						this.types = data.length == 10 ? 1 : 2;
						let lastList = this.lastList.concat(data);
						this.setData({
							lastList: lastList
						});
					}
				});
			},
		},
		onPageScroll: function(e) {
			if (e.scrollTop > 0) {
				this.setData({
					bgColor: '#ffffff'
				});
			} else if (e.scrollTop < 2) {
				this.setData({
					bgColor: ''
				});
			}
		},
		onReachBottom() {
			if (this.type == 1) {
				if (this.types == 1) {
					this.pageNum = this.pageNum + 1
					this.getlastList();
				}
			} else if (this.type == 2) {
				if (this.types == 1) {
					this.pageNum = this.pageNum + 1
					this.getlongestList(this.module)
				}
			} else {
				if (this.types == 1) {
					this.pageNum = this.pageNum + 1
					this.getmaxList(this.module)
				}
			}

		}
	}
</script>

<style lang="less" scoped>
	@import './moreDataAnalysis.less';
</style>