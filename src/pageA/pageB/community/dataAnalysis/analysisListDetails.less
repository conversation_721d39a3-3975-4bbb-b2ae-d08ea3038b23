.main{
	height: 300vh;
     background: linear-gradient(to bottom, #FFE9E7, #F3F3F4);
	 .content{
		 padding: 184rpx 24rpx 28rpx 24rpx;
		 .head{
			 display: flex;
			 align-items: center;
			 position: relative;
			 .head1{
				 display: flex;
				  align-items: center;
				 .head1_1{
					 margin-left: 46rpx;
					 .head1_2{
						 color:#333333;
						 font-size: 36rpx;
						 font-weight: bold;
					 }
					 .head1_3{
						 padding: 8rpx 12rpx;
						 border-radius: 8rpx;
						  background: linear-gradient(to top, #F8D8A8, #FAE6C6);
						  color: #964016;
						  font-size: 28rpx;
						  margin-top: 8rpx;
					 }
				 }
			 }
			 .head2{
				 position: fixed;
				 right: 0;
			 }
		 }
		 .visit{
			 margin-top: 40rpx;
			 color: #B46C00;
			 font-size: 24rpx;
			 .visitHead{
				 border-radius: 20rpx;
				 background: linear-gradient(to top, #FFF7EC, #FFF6E9);
				 height: 200rpx;
				 .visitHead1{
					 padding: 20rpx 24rpx;
					 display: flex;
					 justify-content: space-between;
					 .visitHead1_1{
						 display: flex;
						 align-items: center;
					 }
				 }
				 .visitHeads2{
					 background: #ffffff;
					 border-radius: 20rpx;
					 padding: 20rpx 24rpx;
				 .visitHead2{
					 display: flex;
					 align-items: center;
					 justify-content: space-between;
					 .visitHead2_1{
						 display: flex;
						 align-items: center;
						 .fgx{
							width: 8rpx;
							 height: 28rpx;
							 background: #3F6FFF;
							 border-radius: 8rpx;
						 }
						 .visitHead2_2{
							 color: #333333;
							 font-size: 28rpx;
							 font-weight: bold;
							 margin-left: 10rpx;			
																	   }
					 }
					 .visitHead2_3{
						 display: flex;
						 align-items: center;
						 .visitHead2_4{
							 color:#AAAAAA ;
							 font-size: 22rpx;
						 }
					 }
				 }
				 .visitHead3{
					 margin-top: 20rpx;
					 .visitHead4{
						 display: flex;
						 justify-content: space-between;
						 padding: 26rpx 0;
						 color: #333333;
						 font-size: 24rpx;
						 border-top: 1rpx solid #F2F7FF;
					 }
				 }
				 }
			 }
			 
		 }
		 }
		 }
		 .analysis{
			 background: #ffffff;
			 border-radius: 20rpx;
			 padding: 20rpx 24rpx;
			 margin-top: 20rpx;
			 .analysis1{
				 display: flex;
				 align-items: center;
				 .fgx{
					 width: 8rpx;
					  height: 28rpx;
					  background: #3F6FFF;
					  border-radius: 8rpx;
				 }
				 .analysis1_1{
					 color: #333333;
					 font-size: 28rpx;
					 font-weight: bold;
					 margin-left: 10rpx;
				 }
			 }
			 .rule{
				 width: 100%;
				 height: 1rpx;
				 background: #F2F7FF;
				 margin: 20rpx 0;
			 }
			 .analysisTitle{
				 color:#3F6FFF;
				 font-size: 28rpx;
				 font-weight: bold;
			 }
			 .analysis2{
				 .analysis3{
					 display: flex;
					 align-items: center;
					 color: #777777;
					 font-size: 24rpx;
					 padding:10rpx 20rpx ;
					 .analysis3_1{
						 width: 30%;
					 }
					 #analysis3_1{
						 color: #333333;
						 font-size: 28rpx;
						 font-weight: bold;
					 }
				 }
				 .analyse1{
					 	width: 30%;
					 .analyse1_3{
						 padding: 20rpx 0;
							width: 100%;
							color: #333333;
							font-size: 28rpx;
							font-weight: bold; 
					 }
				 }
			 }
			 .step{
			 	display: flex;
			 	justify-content: space-between;
			 	margin-top: 30rpx;
				.step1{
					color: #777777;
					font-size: 24rpx;
					width: 25%;
				}
				.step2{
					width: 75%;
					.step2_3{
						padding: 16rpx 16rpx;
						border-radius: 16rpx;
						background: #F7F8F9;
						width: 96%;
						margin-bottom: 10rpx;
						.step2_1{
							color: #777777;
							font-size: 24rpx;
						}
						.step2_2{
							color: #333333;
							font-size: 24rpx;
						}
					}
				}
			 }
			 .comments{
			 	margin-top: 30rpx;
				.comment{
					display: flex;
					.commentImg{
						width: 16%;
						image{
							width: 84rpx;
							height: 84rpx;
							border-radius: 10rpx;
						}
					}
					.comment1{
						.comment1_1{
							color: #FF6E00;
							font-size: 30rpx;
							font-weight: bold;
						}
						.comment1_2{
							color: #aaaaaa;
							font-size: 24rpx;
							margin-top: 10rpx;
						}
					}
				}
				.commentTitle{
					font-size: 28rpx;
					font-weight: bold;
					color: #333333;
					padding: 16rpx 0;
				}
				.comment3{
					height: 640rpx;
					overflow-y: auto;
					.comment2{
						background: #F2F7FF;
						display: flex;
						justify-content: space-between;
						align-items: center;
						border-top-right-radius: 20rpx;
						border-bottom-right-radius: 20rpx;
						margin-bottom: 20rpx;
						.comment2_1{
							width: 22%;
							height: 140rpx;
							border-radius: 6rpx;
							image{
								width: 100%;
								height: 140rpx;
								border-radius: 6rpx;
							}
						}
						.comment2_2{
							font-size: 28rpx;
							width: 70%;
							display: flex;
							.comment2_5{
								overflow: hidden;
								display: -webkit-box;
								-webkit-box-orient: vertical;
								-webkit-line-clamp: 2;
								.comment2_3{
									color: #5B6799;
									font-weight: bold;
								}
								.comment2_4{
									color: #777777;
								}
							}
						}
					}
				}
			 }
		 }
		.charts{
		  width: 550rpx;
		  height: 500rpx;
		}
