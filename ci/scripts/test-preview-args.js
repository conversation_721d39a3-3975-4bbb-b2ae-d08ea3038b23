#!/usr/bin/env node

/**
 * 测试预览脚本的参数解析
 */

// 复制preview.js中的参数解析逻辑
function parseArgs(args) {
  const result = {
    projectName: null,
    environment: null,
    pagePath: null,
    searchQuery: null
  };

  let positionalIndex = 0;
  let i = 0;
  
  while (i < args.length) {
    const arg = args[i];
    
    if (arg === '--page' && i + 1 < args.length) {
      result.pagePath = args[i + 1];
      i += 2;
    } else if (arg === '--query' && i + 1 < args.length) {
      result.searchQuery = args[i + 1];
      i += 2;
    } else if (arg === '--help' || arg === '-h') {
      result.showHelp = true;
      i++;
    } else if (!arg.startsWith('--')) {
      // 位置参数
      if (positionalIndex === 0) {
        result.projectName = arg;
      } else if (positionalIndex === 1) {
        result.environment = arg;
      }
      positionalIndex++;
      i++;
    } else {
      i++;
    }
  }

  // 设置默认值
  result.projectName = result.projectName || 'main';
  result.environment = result.environment || 'develop';

  return result;
}

// 测试不同的参数组合
const testCases = [
  ['main', 'trial', '--page', 'pageA/home'],
  ['main', 'develop', '--page', 'pageA/aiService'],
  ['--page', 'pageA/home', 'main', 'trial'],
  ['main', '--page', 'pageA/home', 'trial'],
  ['--page', 'pageA/home'],
  []
];

console.log('🧪 测试预览脚本参数解析');
console.log('='.repeat(50));

testCases.forEach((testCase, index) => {
  console.log(`\n测试 ${index + 1}: ${testCase.join(' ')}`);
  const result = parseArgs(testCase);
  console.log('解析结果:', JSON.stringify(result, null, 2));
});

// 测试实际的命令行参数
if (process.argv.length > 2) {
  console.log('\n🎯 实际命令行参数测试');
  console.log('='.repeat(50));
  const actualArgs = process.argv.slice(2);
  console.log('输入参数:', actualArgs);
  const actualResult = parseArgs(actualArgs);
  console.log('解析结果:', JSON.stringify(actualResult, null, 2));
}
