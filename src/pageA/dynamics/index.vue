<template>
  <view>
    <!-- 会所头部 -->
    <ClubHeader :clubInfo="clubInfo" />

    <!-- 动态列表 -->
    <DynamicsList
      ref="dynamicsListRef"
      :dynamicsList="postPageList"
      :likeList="likeList"
      :showPoster="showPoster"
      :showPushMessage="showPushMessage"
      @update-dynamic="updateDynamic"
      @update-like-list="updateLikeList"
      @hide-shadow="hideShadow"
    />

    <!-- 浮动按钮 -->
    <FloatingButtons :scrollTop="scrollTop" />

    <!-- 遮罩层 -->
    <view class="shadow" v-show="shadowVisible" @tap="hideShadow"></view>

    <!-- 发布按钮 -->
    <publishbutton-vue
      ref="publishRef"
      permissions="feed:post:create"
      roleIf="3"
    />

    <!-- 自定义 tabbar -->
    <CustomTabBar activeKey="dynamics" />
  </view>
</template>

<script setup>
import { ref, getCurrentInstance, nextTick } from 'vue';
import {
  onLoad,
  onReady,
  onShow,
  onHide,
  onUnload,
  onReachBottom,
  onPageScroll,
  onShareAppMessage,
  onShareTimeline,
} from '@dcloudio/uni-app';
import publishbuttonVue from '@/components/publishbutton.vue';
import CustomTabBar from '@/components/CustomTabBar.vue';
import ClubHeader from './components/ClubHeader.vue';
import DynamicsList from './components/DynamicsList.vue';
import FloatingButtons from './components/FloatingButtons.vue';
import {
  PAGINATION_CONFIG,
  RESPONSE_CODES,
  LOAD_STATUS,
  USER_ROLES,
} from './constants.js';
import {
  checkHasMoreData,
  processContentDisplay,
  processLikeUsers,
  formatCreateTime,
} from './utils.js';

const { proxy } = getCurrentInstance();

// 响应式数据
const clubInfo = ref({
  clubName: '',
  clubTagNames: [],
  clubDescription: '',
  clubBackgroundPhotos: [],
});

const shadowVisible = ref(false);
const hasMoreData = ref('');
const pageSize = ref(PAGINATION_CONFIG.DEFAULT_PAGE_SIZE);
const currentPage = ref(PAGINATION_CONFIG.INITIAL_PAGE);
const postPageList = ref([]);
const likeList = ref([]);
const intersectionObserver = ref(null);
const scrollTop = ref(0);
const showPushMessage = ref(false);
const showPoster = ref(false);

// refs
const publishRef = ref(null);
const dynamicsListRef = ref(null);

// 生命周期函数
onLoad(() => {
  loadDynamicsData();
  loadClubInfo();
});

onReady(() => {
  intersectionObserver.value = uni.createIntersectionObserver(proxy, {
    thresholds: [0],
  });
});

onShow(() => {
  uni.$on('publishsuccesss', () => {
    reloadDynamicsData();
  });

  nextTick(() => {
    publishRef.value?.publishInit();
  });

  updateUserPermissions();
});

onHide(() => {
  intersectionObserver.value?.disconnect();
});

onUnload(() => {
  uni.$off('publishsuccesss');
  intersectionObserver.value?.disconnect();
});

onReachBottom(() => {
  if (hasMoreData.value === LOAD_STATUS.HAS_MORE) {
    currentPage.value = currentPage.value + 1;
    loadDynamicsData();
  }
});

onPageScroll((e) => {
  scrollTop.value = e.scrollTop;
  // 滚动时关闭所有打开的 ActionMenu
  dynamicsListRef.value?.closeAllActionMenus();
});

onShareAppMessage(() => {});
onShareTimeline(() => {});

// 方法定义
const loadDynamicsData = async () => {
  proxy.changeLoading(true);

  try {
    const data = {
      pageSize: pageSize.value,
      pageNum: currentPage.value,
    };

    const res = await proxy.$axios.get(proxy.$api.postPage, data);

    if (res.data.code === RESPONSE_CODES.SUCCESS) {
      const responseData = res.data.rows;
      const hasMore = checkHasMoreData(responseData.length, pageSize.value);

      let currentData;
      let currentLikeList;

      if (currentPage.value === PAGINATION_CONFIG.INITIAL_PAGE) {
        currentData = [];
        currentLikeList = [];
      } else {
        currentData = postPageList.value;
        currentLikeList = likeList.value;
      }

      responseData.forEach((item) => {
        item.show = false;
        processContentDisplay(item);

        const likeUsers = processLikeUsers(item.likeUsers);
        currentLikeList.push(likeUsers);

        item.createTime = formatCreateTime(item.createTime);
      });

      postPageList.value = currentData.concat(responseData);
      hasMoreData.value = hasMore;
      likeList.value = currentLikeList;
    }
  } catch (error) {
    console.error('加载动态数据失败:', error);
  } finally {
    proxy.changeLoading(false);
  }
};

const reloadDynamicsData = () => {
  currentPage.value = PAGINATION_CONFIG.INITIAL_PAGE;
  pageSize.value = PAGINATION_CONFIG.DEFAULT_PAGE_SIZE;
  postPageList.value = [];
  likeList.value = [];
  loadDynamicsData();
};

const loadClubInfo = async () => {
  try {
    const res = await proxy.$axios.get(proxy.$api.clubInfo);
    if (res.data.code === RESPONSE_CODES.SUCCESS) {
      clubInfo.value = res.data.data;
    }
  } catch (error) {
    console.error('加载会所信息失败:', error);
  }
};

const updateUserPermissions = () => {
  const roles = uni.getStorageSync('roles');
  const token = uni.getStorageSync('token');

  showPushMessage.value =
    roles[0] === USER_ROLES.SALES || roles[0] === USER_ROLES.ADMIN;
  showPoster.value = !(!token || roles[0] === USER_ROLES.CUSTOMER);
};

// 事件处理函数
const updateDynamic = (index, field, value) => {
  postPageList.value[index][field] = value;
};

const updateLikeList = (index, newLikeList) => {
  likeList.value[index] = newLikeList;
};

const hideShadow = () => {
  shadowVisible.value = false;
  // 关闭所有打开的操作菜单
  postPageList.value.forEach((item) => {
    item.show = false;
  });
};
</script>

<style scoped lang="less">
.shadow {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 99;
}
</style>
