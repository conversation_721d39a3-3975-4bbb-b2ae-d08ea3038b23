## 2.1.0（2021-11-19）
- 优化 组件UI，并提供设计资源，详见:[https://uniapp.dcloud.io/component/uniui/resource](https://uniapp.dcloud.io/component/uniui/resource)
- 文档迁移，详见:[https://uniapp.dcloud.io/component/uniui/uni-tag](https://uniapp.dcloud.io/component/uniui/uni-tag)
## 2.0.0（2021-11-09）
- 新增 提供组件设计资源，组件样式调整
- 移除 插槽
- 移除 type 属性的 royal 选项
## 1.1.1（2021-08-11）
- type 不是 default 时，size 为 small 字体大小显示不正确
## 1.1.0（2021-07-30）
- 组件兼容 vue3，如何创建vue3项目，详见 [uni-app 项目支持 vue3 介绍](https://ask.dcloud.net.cn/article/37834)
## 1.0.7（2021-06-18）
- 修复 uni-tag 在字节跳动小程序上 css 类名编译错误的 bug
## 1.0.6（2021-06-04）
- 修复 未定义 sass 变量 "$uni-color-royal" 的bug
## 1.0.5（2021-05-10）
- 修复 royal 类型无效的bug
- 修复 uni-tag 宽度不自适应的bug
- 新增 uni-tag 支持属性 custom-style 自定义样式
## 1.0.4（2021-02-05）
- 调整为uni_modules目录规范
