<template>
    <view>
        <navigation :background="'#fff'" title="发送请帖" :isSowArrow="true"></navigation>
        <view class="main" :style="'margin-top: ' + navbarHeight + 'px;'">
            <view class="content">
                <view class="header">
                    <p class="title_top">请帖编辑（可直接编辑封面和文字）</p>
                    <view class="input_content">
                        <input maxlength="24" @blur="onInput" placeholder="请输入" :value="share.title" />
                        <span class="input_icon">{{ share.title.length }}/24</span>
                    </view>
                </view>
                <view class="center">
                    <view class="image_content">
                        <image
                            :src="share.thumbUrl || 'http://cdn.xiaodingdang1.com/2024/09/15/09ffea5af9bd417ba0f81771feed14d0.png'">
                        </image>
                        <span class="img_btn" @tap.native="chooseImg">更换封面</span>
                    </view>
                    <textarea maxlength="36" @blur="bindText" placeholder="请输入" :value="share.description" />
                    <span class="text_icon">{{ share.description.length }}/36</span>
                </view>
                <view class="footer">
                    <view class="footer_top">
                        <span></span>
                        <p>分享至</p>
                        <span></span>
                    </view>
                    <view class="footer_list">
                        <view class="footer_btn" :data-index="index" v-for="(item, index) in list" :key="index">
                            <button @blur="onBlur" open-type="contact" :show-message-card="true"
                                @getuserinfo="getuserinfo" @contact="handleContact" session-from="请柬"
                                :send-message-title="data.title"
                                style="margin: 0; padding: 0; background-color: transparent">
                                <image :src="item.src"></image>
                                <p>{{ item.title }}</p>
                            </button>
                        </view>
                    </view>
                </view>

                <!-- <button show-message-card='true' open-type="contact" send-message-title='{{data.title}}' open-type="contact" session-from='请柬'   bindcontact="handleContact" style="margin-top: 50rpx;">4545454</button> -->
            </view>
        </view>
    </view>
</template>

<script>
    const app = getApp();
    export default {
        data() {
            return {
                share: {
                    title: '',
                    description: '',
                    thumbUrl: 'http://cdn.xiaodingdang1.com/2024/09/15/09ffea5af9bd417ba0f81771feed14d0.png',
                    value: ''
                },
                capsuleTop: '',
                capsuleHeight: '',
                navbarHeight: '',
                list: [{
                        title: '微信好友',
                        src: 'http://cdn.xiaodingdang1.com/2024/09/15/259257bc9c3c49f1a8c568f85c83c782.png'
                    },
                    {
                        title: '朋友圈',
                        src: 'http://cdn.xiaodingdang1.com/2024/09/15/68651e8904f54175af89126cbf71610e.png'
                    }
                ],
                data: {
                    path: 'https://qingtie.xiaodingdang1.com/%E7%88%B1%E4%B8%BD%E4%B8%9D/alice.html',
                    ThumbUrl: 'http://cdn.xiaodingdang1.com/2024/09/15/e3c16d15fee446c589773ffd14644377.png',
                    title: '1837070025413480449'
                },
                id: ''
                // from: "",
            };
        },
        /**
         * 生命周期函数--监听页面加载
         */
        onLoad(options) {
            this.getDetail(options.id);
            this.setData({
                id: options.id
            });
        },
        /**
         * 生命周期函数--监听页面初次渲染完成
         */
        onReady() {},
        /**
         * 生命周期函数--监听页面显示
         */
        onShow() {
            this.setData({
                capsuleTop: app.globalData.capsule.top,
                capsuleHeight: app.globalData.capsule.height,
                navbarHeight: (app.globalData.capsule.top - app.globalData.system.statusBarHeight) * 2 + app
                    .globalData.capsule.height + app.globalData.system.statusBarHeight
            });

            //  data = {
            //   // MsgType: "miniprogrampage",
            //   ThumbUrl:
            //     "http://cdn.xiaodingdang1.com/2024/09/15/e3c16d15fee446c589773ffd14644377.png",
            //   Title: "1837070025413480449",
            // };
            // this.$point.basePoint(data);
        },
        /**
         * 生命周期函数--监听页面隐藏
         */
        onHide() {},
        /**
         * 生命周期函数--监听页面卸载
         */
        onUnload() {},
        /**
         * 页面相关事件处理函数--监听用户下拉动作
         */
        onPullDownRefresh() {},
        /**
         * 页面上拉触底事件的处理函数
         */
        onReachBottom() {},
        /**
         * 用户点击右上角分享
         */
        onShareAppMessage() {
            console.log(33);
        },
        methods: {
            handleContact() {
                // this.$axios.post(this.$api.editInviation,param).then((res))
                console.log(1111);
                this.save();
            },

            getuserinfo() {
                console.log(333);
            },

            onInput(e) {
                this.setData({
                    'share.title': e.detail.value
                });
                this.save();
            },

            bindText(e) {
                this.setData({
                    'share.value': e.detail.value
                });
                this.save();
            },

            onClick(e) {
                let index = e.currentTarget.dataset.index;
                console.log(index);
                uni.showShareMenu({
                    withShareTicket: true,
                    menus: ['shareAppMessage', 'shareTimeline']
                });
            },

            onBlur() {
                console.log(333);
            },

            save() {
                const param = {
                    ...this.share,
                    id: this.id
                };
                this.$axios.post(this.$api.editInviation, param).then((res) => {
                    if (res.data.code == 200) {}
                });
            },

            getDetail(id) {
                this.$axios
                    .get(this.$api.getInviation, {
                        id
                    })
                    .then((res) => {
                        if (res.data.code == 200) {
                            this.setData({
                                share: res.data.data,
                                'data.ThumbUrl': res.data.thumbUrl,
                                'data.path': res.data.templeUrl,
                                'data.title': res.data.id
                            });
                        }
                    });
            },

            // 上传图片
            chooseImg: function(e) {
                var that = this;
                uni.chooseImage({
                    // count: 1, // 默认9
                    sizeType: ['compressed'],
                    // 可以指定是原图还是压缩图，默认二者都有
                    sourceType: ['album', 'camera'],
                    // 可以指定来源是相册还是相机，默认二者都有
                    success: function(res) {
                        var tempFilePaths = res.tempFilePaths;
                        let picNum = tempFilePaths.length;
                        uni.showLoading({
                            title: '图片上传中'
                        });
                        uni.uploadFile({
                            filePath: tempFilePaths[0],
                            name: 'file',
                            url: this.$api.uploadOSS,
                            formData: {},
                            header: {
                                'Content-Type': 'multipart/form-data',
                                Authorization: 'Bearer ' + uni.getStorageSync('token'),
                                'Accept-Encoding': 'gzip',
                                Clientid: '428a8310cd442757ae699df5d894f051'
                            },
                            success: function(rests) {
                                let data = JSON.parse(rests.data);
                                if (data.code == 200) {
                                    uni.hideLoading();
                                    that.setData({
                                        'share.thumbUrl': data.data.url
                                    });
                                    this.save();
                                }
                            }
                        });
                    }
                });
            }
        }
    };
</script>
<style>
    @import './index.css';
</style>