<template>
  <view>
    <navigation
      :background="bgColor"
      title="详情"
      :isSowArrow="true"
    ></navigation>
    <view class="main">
      <view class="top">
        <view class="top-img">
          <swiper-banner :imgList="imgList" :list="banner_list"></swiper-banner>
        </view>
        <view class="top-price">
          <view class="top-price-icon">￥</view>
          <view class="top-price-text">{{
            inforList.price ? inforList.price : '----'
          }}</view>
          <view class="top-price-qes"
            >已有人{{ inforList.consultNumber }}咨询</view
          >
        </view>
        <view class="top-des">{{ inforList.description }}</view>
        <view class="top-list">
          <view
            class="top-list-enum"
            v-for="(item, i) in inforList.tag"
            :key="i"
            >{{ item }}</view
          >
        </view>
        <view class="top-button">
          <button
            @click="handleContact"
            style="
              width: 200rpx;
              padding: 0 28rpx;
              border-radius: 100rpx;
              background: #ff4f61;
              color: #ffffff;
              font-size: 28rpx;
              font-weight: bold;
            "
          >
            咨询报价
          </button>
        </view>
      </view>
      <view class="tabbar">
        <scroll-view
          id="tab-bar"
          class="scroll-h"
          :scroll-x="true"
          :show-scrollbar="false"
          :scroll-into-view="scrollInto"
        >
          <view
            v-for="(tab, index) in comList"
            :key="tab.name"
            class="uni-tab-item"
            :id="tab.id"
            @click="ontabtap(tab)"
          >
            <text
              class="uni-tab-item-title"
              :class="tabIndex == tab.id ? 'uni-tab-item-title-active' : ''"
              >{{ tab.name }}</text
            >
          </view>
        </scroll-view>
      </view>
      <view class="content">
        <specifics
          :inforList="inforList"
          v-show="comId === 'specifics'"
        ></specifics>
        <care :contentHeight="pageTop" v-show="comId === 'care'"></care>
        <instrument
          :contentHeight="pageTop"
          v-show="comId === 'instrument'"
        ></instrument>
        <mother :contentHeight="pageTop" v-show="comId === 'mother'"></mother>
        <science
          :contentHeight="pageTop"
          v-show="comId === 'science'"
        ></science>
      </view>
    </view>
    <new-bottom
      :from="SessionFrom"
      :type="3"
      :id="inforList.projectId"
    ></new-bottom>
    <rightMenuBarVue :scrollTop="scrollTop"></rightMenuBarVue>
  </view>
</template>
<script setup>
import rightMenuBarVue from '@/components/rightMenuBar.vue';
import swiperBanner from '@/components/swiperBanner.vue';
import specifics from '../component/specifics.vue';
import care from '../component/care.vue';
import instrument from '../component/instrument.vue';
import mother from '../component/mother.vue';
import science from '../component/science.vue';
import {
  onShow,
  onHide,
  onLoad,
  onPageScroll,
  onShareAppMessage,
  onShareTimeline,
} from '@dcloudio/uni-app';
import { ref, reactive, onMounted, computed, getCurrentInstance } from 'vue';
const instance = getCurrentInstance();
const App = instance.appContext.config.globalProperties;

onShareAppMessage(() => {});
onShareTimeline(() => {});
onShow(async () => {
  let newDateTime = Date.parse(new Date());
  let pointStart = {
    eventType: 'PAGE_VIEW',
    pageUrl: '/pageA/pageB/home/<USER>/detail',
    module: 'recovery',
    eventTime: newDateTime,
    pageTitle: '产后康复',
  };
  let iftoken = uni.getStorageSync('token');
  if (iftoken) {
    App.$point.basePoint(pointStart);
  }
  await suiteInfo();
});
onHide(() => {
  let newDateTime = Date.parse(new Date());
  let pointEnd = {
    eventId: uni.getStorageSync('eventId'),
    leaveTime: newDateTime,
  };
  App.$point.reportEnd(pointEnd);
});
const bgColor = ref('');
const pageScrollTop = ref('');
const pageTop = ref('');
const scrollTop = ref('');
onPageScroll((e) => {
  if (e.scrollTop > 0) {
    bgColor.value = '#ffffff';
  } else if (e.scrollTop < 2) {
    bgColor.value = '';
  }
  pageScrollTop.value = Number(e.scrollTop) + 318;
  scrollTop.value = e.scrollTop;
});
const projectId = ref('');
const SessionFrom =
  uni.getStorageSync('tenantId') +
  ',' +
  uni.getStorageSync('userInfo').nickname +
  ',' +
  '产后康复';
onLoad((options) => {
  console.log('onLoad', options);
  projectId.value = options.projectId;
  pageInit(options);
});
const pageInit = async (options) => {
  try {
    // await suiteInfo();
    await reviewListFun();
    await recoveryRecommendation();
    // await signing()
  } catch {}
};
const inforList = reactive({});
const banner_list = reactive([]);
const imgList = reactive([]);
const reviewList = reactive([]);
const signingShow = ref(false);
const suiteInfo = async () => {
  //查询产后康复信息
  const result = await App.$axios.get(App.$api.postpartumRecoveryInfo, {
    projectId: projectId.value,
  });
  if (result.data.code == 200) {
    let data = result.data.data;
    let slideshow = [];
    if (data.videos) {
      if (data.videos.length > 0) {
        data.videos.forEach((item) => {
          let list = {
            type: 1,
            video: item,
          };
          slideshow.push(list);
        });
      }
    }
    if (data.backgroundPhotos && data.backgroundPhotos.length > 0) {
      data.backgroundPhotos.forEach((item) => {
        let list = {
          type: 0,
          url: item,
        };
        slideshow.push(list);
      });
    } else {
      data.displayPhotos.forEach((item) => {
        let list = {
          type: 0,
          url: item,
        };
        slideshow.push(list);
      });
    }
    Object.assign(inforList, data);
    Object.assign(banner_list, slideshow);
    Object.assign(imgList, data.displayPhotos);
  }
};
const reviewListFun = async () => {
  //查询商家总体评价列表
  let data = {
    pageSize: 3,
    pageNum: 1,
    reviewType: 'recovery',
  };
  const res = await App.$axios.get(App.$api.reviewPage, data);
  if (res.data.code == 200) {
    Object.assign(reviewList, res.data.rows);
  }
};

const handleContact = () => {
  uni.navigateTo({
    url: '/subPackages/customerService/pages/userChat',
  });
};

// const signing = async (contractGiftId) => {
//     const res = await App.$axios.get(App.$api.signing, {
//         contractGiftId: contractGiftId
//     })
//     if (res.data.code == 200) {
//         this.setData({
//             signingShow: true
//         });
//         signingShow.value = true
//         suiteInfo();
//     }
// }
// const claim = async (e) => {
//     let contractGiftId = e.currentTarget.dataset.contractgiftid;
//     let isExistContractGift = e.currentTarget.dataset.isexist;
//     uni.setStorageSync('isExistContractGift', isExistContractGift);
//     if (!isExistContractGift) {
//         await signing(contractGiftId);
//     } else {
//         signingShow.value = true
//     }
// }
const scrollInto = ref('');
let tabIndex = ref(1);
let comId = ref('specifics');
const comList = reactive([
  {
    id: 1,
    name: '详情',
    com: 'specifics',
  },
  // {
  //     id: 2,
  //     name: '仪器',
  //     com: 'instrument'
  // },
  {
    id: 3,
    name: '科普',
    com: 'science',
  },
  {
    id: 4,
    name: '护理人员',
    com: 'care',
  },
  // {
  //     id: 5,
  //     name: '宝妈评价',
  //     com: 'mother'
  // }
]);
const ontabtap = (item) => {
  switchTab(item);
};
const ontabchange = (item) => {
  switchTab(item);
};
const switchTab = (item) => {
  if (tabIndex.value == item.id) {
    return;
  }
  tabIndex.value = item.id;
  comId.value = item.com;
  pageTop.value = pageScrollTop.value;
};
</script>
<style lang="less" scoped>
@import './detail.less';
</style>
