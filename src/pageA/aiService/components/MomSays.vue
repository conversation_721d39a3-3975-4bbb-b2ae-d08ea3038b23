<template>
  <view class="mom-says-container" v-if="shouldShow">
    <view class="mom-says-card" @click="handleViewMore">
      <!-- 标题 -->
      <view class="mom-says-title">宝妈说</view>

      <!-- 副标题 -->
      <view class="mom-says-subtitle" v-if="currentRecommendation.title">
        {{ currentRecommendation.title }}
      </view>

      <!-- 内容文本 -->
      <view class="mom-says-text u-line-4" v-if="currentRecommendation.content">
        {{ currentRecommendation.content }}
      </view>

      <!-- 图片展示区域 -->
      <view
        class="content-images"
        v-if="
          currentRecommendation.contentPhotos &&
          currentRecommendation.contentPhotos.length > 0
        "
      >
        <view
          class="image-container"
          v-for="(img, index) in currentRecommendation.contentPhotos.slice(0, 2)"
          :key="index"
        >
          <image
            :src="img"
            class="content-image"
            mode="aspectFill"
            @error="handleImageError"
          />
        </view>
      </view>

      <!-- 查看更多按钮 -->
      <view class="more-button">
        <view class="more-button-content">
          <text class="more-text">点击查看更多</text>
          <view class="more-arrow">
            <image src="/static/images/chat/arrow-right.svg" mode="aspectFit" />
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue';
import { navigateToPage } from '@/utils/urlUtils.js';

// Props
const props = defineProps({
  recommendations: {
    type: Array,
    default: () => [],
  },
  conversationId: {
    type: String,
    default: '',
  },
  userMessage: {
    type: String,
    default: '',
  },
});

// Events
const emit = defineEmits(['view-more']);

// 数据
const currentIndex = ref(0);

// 计算属性
const currentRecommendation = computed(() => {
  if (props.recommendations.length > 0) {
    return props.recommendations[currentIndex.value];
  }
  return {};
});

const shouldShow = computed(() => {
  return props.recommendations.length > 0;
});

// 处理查看更多
const handleViewMore = () => {
  console.log('点击查看更多宝妈说');

  // 将宝妈说数据存储到全局状态中，供列表页面使用
  const momSaysData = {
    list: props.recommendations,
    conversationId: props.conversationId,
    userMessage: props.userMessage,
    timestamp: Date.now(), // 添加时间戳，确保数据新鲜度
  };

  // 存储到全局状态
  uni.setStorageSync('momSaysListData', momSaysData);

  // 构建跳转参数
  const params = {};
  if (props.conversationId) {
    params.conversationId = props.conversationId;
  }
  if (props.userMessage) {
    params.userMessage = props.userMessage;
  }

  // 跳转到宝妈说列表页面
  navigateToPage('/subPackages/aiService/momSaysList', params);

  emit('view-more', {
    recommendations: props.recommendations,
    current: currentRecommendation.value,
    conversationId: props.conversationId,
    userMessage: props.userMessage,
  });
};

// 处理图片加载失败
const handleImageError = (e) => {
  console.log('图片加载失败:', e);
  // 可以在这里设置一个默认的图片或者隐藏图片
};

// 暴露方法给父组件
defineExpose({
  currentRecommendation,
});
</script>

<style lang="scss" scoped>
.mom-says-container {
  margin-top: 24rpx;
}

.mom-says-card {
  background-color: #ffffff;
  border-radius: 28rpx;
  width: 690rpx;
  height: fit-content;
  display: flex;
  flex-direction: column;
  padding: 28rpx;
  box-sizing: border-box;
}

.mom-says-title {
  font-family: PingFang SC;
  font-size: 32rpx;
  font-weight: 500;
  color: #000000;
  line-height: 46rpx;
  width: 96rpx;
  height: 46rpx;
  margin-bottom: 32rpx;
}

.mom-says-subtitle {
  font-family: PingFang SC;
  font-size: 26rpx;
  font-weight: 500;
  color: #333333;
  line-height: 44rpx;
  width: 585rpx;
  height: 44rpx;
  margin-bottom: 8rpx;
}

.mom-says-text {
  font-family: PingFang SC;
  font-size: 24rpx;
  font-weight: 400;
  color: #333333;
  line-height: 40rpx;
  text-align: justify;
  width: 634rpx;
  margin-bottom: 28rpx;
}

.content-images {
  display: flex;
  gap: 24rpx;
  margin-bottom: 32rpx;
}

.image-container {
  width: 305rpx;
  height: 305rpx;
  border-radius: 20rpx;
  overflow: hidden;
  background-color: #d9d9d9;
}

.content-image {
  width: 100%;
  height: 100%;
}

.more-button {
  background-color: #eff3ff;
  border-radius: 60rpx;
  width: 634rpx;
  height: 66rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.more-button-content {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.more-text {
  font-family: PingFang SC;
  font-size: 24rpx;
  font-weight: 400;
  color: #406cff;
  line-height: 40rpx;
}

.more-arrow {
  width: 28rpx;
  height: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;

  image {
    width: 28rpx;
    height: 28rpx;
    filter: brightness(0) saturate(100%) invert(39%) sepia(100%) saturate(1000%)
      hue-rotate(220deg) brightness(95%) contrast(105%);
  }
}
</style>
