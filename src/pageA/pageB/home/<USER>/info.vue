<template>
  <view class="page-container">
    <view class="form-container">
      <view class="form-item">
        <view class="label">宝宝姓名</view>
        <input
          class="input"
          type="text"
          placeholder="请输入真实姓名"
          v-model="formData.name"
        />
      </view>

      <view class="form-item">
        <view class="label">入宴时间</view>
        <view class="time-picker" @click="openTimePicker">
          <text class="picker-text">{{
            formData.eventTime || '选择时间'
          }}</text>
          <view class="picker-icon">
            <uni-icons type="calendar" size="20" color="#ccc"></uni-icons>
          </view>
        </view>
      </view>

      <view class="form-item address-item">
        <view class="label">入宴地址</view>
        <view class="address-info">
          地址将在请帖内显示，方便宾客准确找到位置
        </view>
        <textarea
          class="address-input"
          placeholder="请填写详细地址，例:澜悦湾大酒店4F"
          v-model="formData.eventAddress"
        ></textarea>
      </view>

      <view class="form-item map-item">
        <view class="label">地图定位</view>
        <view class="map-button" @click="startLocation">
          <uni-icons type="location" size="18" color="#f56c6c"></uni-icons>
          <text class="map-button-text">{{
            formData.latitude && formData.longitude ? '修改定位' : '选取定位'
          }}</text>
        </view>
      </view>

      <!-- 使用封装的地图组件 -->
      <location-map
        :latitude="formData.latitude"
        :longitude="formData.longitude"
        :address="formData.location"
      />
    </view>

    <view class="action-button">
      <button class="save-btn" @click="saveInfo">保存</button>
    </view>

    <u-picker
      mode="time"
      :default-time="formData.eventTime || dayjs().format('YYYY-MM-DD HH:mm')"
      v-model="isShowPicker"
      :params="params"
      @confirm="handleConfirm"
    >
    </u-picker>
  </view>
</template>

<script setup>
import { ref, reactive, computed } from 'vue';
import utils from '@/utils/util';
import { onLoad } from '@dcloudio/uni-app';
import { getInvitationApi, saveInvitationApi } from './api/rehabilitation';
import LocationMap from './components/LocationMap.vue';
import dayjs from 'dayjs';

const pageType = ref('share'); // edit: 编辑, share: 分享

const formData = reactive({
  name: '', // 宝宝姓名
  eventTime: '', // 入宴时间
  eventAddress: '', // 入宴地址
  location: '', // 位置
  latitude: '', // 纬度
  longitude: '', // 经度
});

const cardId = ref('');
const getInvitation = async () => {
  const res = await getInvitationApi(cardId.value);
  Object.assign(formData, res.data);
};

onLoad((options) => {
  if (options?.cardId) {
    cardId.value = options.cardId;
    getInvitation();

    pageType.value = options.pageType || 'share';
  }
});

const isShowPicker = ref(false);
const params = ref({
  year: true,
  month: true,
  day: true,
  hour: true,
  minute: true,
});

const handleConfirm = (value) => {
  formData.eventTime = `${value.year}-${value.month}-${value.day} ${value.hour}:${value.minute}`;
};

const openTimePicker = () => {
  isShowPicker.value = true;
};

const startLocation = () => {
  let target = {};
  if (formData.latitude && formData.longitude) {
    target = {
      latitude: formData.latitude,
      longitude: formData.longitude,
    };
  }
  uni.chooseLocation({
    ...target,
    success: (res) => {
      console.log('位置选择结果:', res);
      formData.latitude = res.latitude;
      formData.longitude = res.longitude;
      formData.location = res.address;

      uni.showToast({
        title: '位置已选择',
        icon: 'success',
      });
    },
    fail: (err) => {
      console.error('选择位置失败:', err);
      uni.showToast({
        title: '获取位置失败',
        icon: 'none',
      });
    },
  });
};

const validateForm = (formData) => {
  const errors = [];
  const requiredFields = [
    { key: 'name', label: '姓名' },
    { key: 'eventTime', label: '入宴时间' },
    { key: 'eventAddress', label: '入宴详细地址' },
    { key: 'location', label: '地图位置' },
  ];

  for (const field of requiredFields) {
    if (!formData[field.key] || formData[field.key].trim() === '') {
      errors.push({ field: field.key, message: `请设置${field.label}` });
    }
  }

  return {
    valid: errors.length === 0,
    errors,
    message: errors.length > 0 ? `${errors[0].message}，请检查必填项` : '',
  };
};

const saveInfo = async () => {
  const validationResult = validateForm(formData);
  if (!validationResult.valid) {
    uni.showToast({
      title: validationResult.message,
      icon: 'none',
    });
    return;
  }
  console.log('保存表单数据:', formData);

  try {
    const res = await saveInvitationApi({
      ...formData,
      cardId: cardId.value,
    });
    if (res.data) {
      uni.showToast({
        title: '保存成功',
        icon: 'success',
      });
      if (pageType.value === 'share') {
        const { tenantId, userId } = uni.getStorageSync('userInfo');
        uni.navigateTo({
          url: `/pageA/pageB/home/<USER>/share?${utils.queryParams({
            userId,
            tenantId,
            cardId: cardId.value,
          })}`,
        });
      } else {
        uni.navigateBack();
      }
    }
  } catch (err) {
    console.log('保存失败:', err);
  }
};
</script>

<style scoped lang="less">
.page-container {
  width: 100%;
  height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
  overflow: auto;
}

.form-container {
  flex: 1;
  padding: 24rpx;
}

.form-item {
  background-color: #fff;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 12px;

  .label {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 10px;
  }

  .input {
    height: 40px;
    font-size: 14px;
    color: #333;
    width: 100%;
  }

  .time-picker {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 40px;

    .picker-text {
      font-size: 15px;
      color: #999;
    }
  }
}

.address-item {
  .address-info {
    font-size: 24rpx;
    color: #999;
    margin-bottom: 12rpx;
    margin-left: 12rpx;
  }

  .address-input {
    width: 100%;
    height: 56px;
    font-size: 14px;
  }
}

.map-item {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .label {
    margin-bottom: 0;
  }

  .map-button {
    display: flex;
    align-items: center;
    background-color: #fff;
    border: 1px solid #f56c6c;
    border-radius: 20px;
    padding: 5px 15px;

    .map-button-text {
      color: #f56c6c;
      font-size: 14px;
      margin-left: 5px;
    }
  }
}

.action-button {
  padding: 20px;

  .save-btn {
    width: 100%;
    height: 45px;
    line-height: 45px;
    background-color: #f56c6c;
    color: #fff;
    font-size: 16px;
    border-radius: 22.5px;
    text-align: center;
  }
}
</style>
