# Community 页面组件

本目录包含了会所服务页面拆分出来的各个组件，经过优化后具有更好的命名规范和代码结构。

## 文件结构

```
src/pageA/community/
├── components/           # 组件目录
│   ├── ClubTabs.vue     # 服务标签组件
│   ├── ServiceTabs.vue  # 横向标签组件
│   ├── MomRecommendation.vue # 宝妈推荐组件
│   ├── MomSays.vue      # 宝妈说组件
│   ├── ContentList.vue  # 内容列表组件
│   └── README.md        # 组件说明文档
├── constants.js         # 常量定义
├── utils.js            # 工具函数
└── index.vue           # 主页面
```

## 组件列表

### 1. ClubTabs.vue
**功能**: 顶部四个服务标签组件
- 产后康复
- 护理服务
- 月子膳食
- 其他服务

**Props**:
- `activeTab`: 当前激活的标签

**Events**:
- `tab-change`: 标签切换事件

**样式类名**:
- `.service-tabs`: 容器
- `.service-tab`: 标签项
- `.service-tab--active`: 激活状态
- `.service-tab__icon`: 图标
- `.service-tab__text`: 文本

### 2. ServiceTabs.vue
**功能**: 横向滚动的二级标签组件

**Props**:
- `tagList`: 标签列表
- `selectedTaskNodeIds`: 选中的任务节点ID列表
- `isAllSelected`: 全选状态
- `scrollPosition`: 滚动位置

**Events**:
- `tab-click`: 标签点击事件
- `select-all`: 全选事件

**样式类名**:
- `.horizontal-tabs`: 容器
- `.horizontal-tab`: 标签项
- `.horizontal-tab--active`: 激活状态

### 3. MomRecommendation.vue
**功能**: 优质动态宝妈推荐区域

**Props**:
- `recommendedMomList`: 推荐宝妈列表
- `scrollPosition`: 滚动位置

**内部功能**:
- 自动处理宝妈主页跳转逻辑
- 包含订阅消息处理

**样式类名**:
- `.mom-card-wrapper`: 卡片包装器
- `.mom-card`: 宝妈卡片
- `.mom-card__name`: 宝妈姓名
- `.mom-card__description`: 描述
- `.mom-card__button`: 按钮

### 4. MomSays.vue
**功能**: 宝妈说模块（必看动态）

**Props**:
- `momSaysData`: 宝妈说内容列表

**内部功能**:
- 自动处理查看详情跳转逻辑
- 使用工具函数格式化URL参数

**样式类名**:
- `.mom-says`: 容器
- `.mom-says__header`: 头部
- `.mom-says__title`: 标题
- `.mom-says__item`: 项目
- `.mom-says__content`: 内容
- `.mom-says__media`: 媒体

### 5. ContentList.vue
**功能**: 瀑布流内容列表组件

**Props**:
- `contentList`: 内容列表数据

**内部功能**:
- 自动处理内容详情跳转逻辑
- 根据用户类型选择不同的跳转路由

**Slots**:
- `header`: 头部插槽，用于插入宝妈说组件

**样式类名**:
- `.content-waterfall`: 瀑布流容器

## 优化内容

### 命名优化
- **变量命名**: 使用更语义化的命名，如 `activeServiceTab`、`selectedTaskNodeIds`、`recommendedMomList` 等
- **函数命名**: 使用动词开头的命名，如 `handleServiceTabChange`、`loadContentData`、`handleMomHomepage` 等
- **CSS类名**: 采用 BEM 命名规范，如 `.service-tab--active`、`.mom-card__name` 等

### 代码结构优化
- **常量提取**: 将魔法数字和字符串提取到 `constants.js` 文件
- **工具函数**: 将通用逻辑提取到 `utils.js` 文件
- **组件拆分**: 将大型页面拆分为多个小组件，提高可维护性

### 逻辑优化
- **数据处理**: 使用工具函数统一处理数据逻辑
- **状态管理**: 优化状态变量的命名和使用
- **事件处理**: 将事件处理逻辑内置到组件中，减少父子组件耦合
- **职责分离**: 每个组件负责自己的完整业务逻辑

## 使用方式

```vue
<template>
  <view>
    <ClubTabs
      :activeTab="activeServiceTab"
      @tab-change="handleServiceTabChange"
    />

    <ServiceTabs
      :tagList="serviceTagList"
      :selectedTaskNodeIds="selectedTaskNodeIds"
      :isAllSelected="isAllSelected"
      :scrollPosition="horizontalScrollPosition"
      @tab-click="onSecondClick"
      @select-all="selectAll"
    />

    <!-- 组件内部处理跳转逻辑，无需绑定事件 -->
    <MomRecommendation
      :recommendedMomList="recommendedMomList"
      :scrollPosition="horizontalScrollPosition"
    />

    <!-- 组件内部处理跳转逻辑，无需绑定事件 -->
    <ContentList
      ref="contentListRef"
      :contentList="contentList"
    >
      <template #header>
        <!-- 组件内部处理跳转逻辑，无需绑定事件 -->
        <MomSays :momSaysData="momSaysData" />
      </template>
    </ContentList>
  </view>
</template>
```

## 设计优势

### 1. 降低耦合度
- 父组件不需要关心子组件的具体跳转逻辑
- 子组件可以独立处理自己的业务逻辑
- 减少了父子组件之间的事件传递

### 2. 提高复用性
- 组件可以在其他地方直接使用，无需重复实现跳转逻辑
- 组件更加自包含和独立

### 3. 简化维护
- 相关逻辑集中在组件内部，便于维护
- 减少了主页面的复杂度
- 每个组件负责自己的完整功能

## 常量和工具函数

### constants.js
- `SERVICE_TYPES`: 服务类型枚举
- `PAGINATION_CONFIG`: 分页配置
- `USER_TYPES`: 用户类型
- `PAGE_ROUTES`: 页面路由
- `RESPONSE_CODES`: 响应状态码
- `LOAD_STATUS`: 加载状态

### utils.js
- `getServiceListByType()`: 根据服务类型获取列表
- `mergeAllServiceLists()`: 合并所有服务列表
- `processStaffEmploymentInfo()`: 处理员工信息
- `formatUrlWithParams()`: 格式化URL参数
- `checkHasMoreData()`: 检查是否有更多数据
