import App from './App'
import store from './store/index'
import {
    jumpPage
} from './utils/jump.js'
// 全局mixins，用于实现setData等功能，请勿删除！';
import zpMixins from '@/uni_modules/zp-mixins/index.js';
import api from './utils/api.js'
import auth from './utils/auth.js'
import axios from './utils/request.js'
import point from './utils/basePoint.js'
import navigation from '@/components/navigation';
import newBottom from './components/newBottom.vue';
import newRequestLoading from './components/requestloading.vue';
import maskDialog from './components/maskDialog.vue';
import storeMixin from './store/storeMixin.js';
import uView from './uni_modules/vk-uview-ui';
// import utils from './utils/util.js'
// 默认头像地址
// const defaultAvatar = 'http://cdn.xiaodingdang1.com/2024/11/08/6d8894bb65e1418880530c88baf5b001.png'
const defaultAvatar = 'http://cdn.xiaodingdang1.com/2025/01/23/2e26cf8ae247467889fcc8b4faea6768.png'
const defaultName = "微信用户"
// #ifndef VUE3
import Vue from 'vue'
Vue.config.productionTip = false
Vue.prototype.$store = store
Vue.use(zpMixins);
Vue.use(uView)
Vue.mixin(storeMixin);
Vue.prototype.$jumpPage = jumpPage
Vue.prototype.$api = api
Vue.prototype.$auth = auth
Vue.prototype.$axios = axios
Vue.prototype.$point = point
Vue.prototype.$defaultAvatar = defaultAvatar
Vue.prototype.$defaultName = defaultName
// Vue.prototype.$noMultipleClicks = utils.noMultipleClicks
Vue.component('navigation', navigation)
Vue.component('new-bottom', newBottom)
Vue.component('mask-dialog', maskDialog);
Vue.component('new-request-loading', newRequestLoading);

App.mpType = 'app'
const app = new Vue({
    store,
    ...App
})
app.$mount()
// #endif

// #ifdef VUE3
import {
    createSSRApp
} from 'vue'
export function createApp() {
    const app = createSSRApp(App)
    app.use(uView)
    app.use(store)
    app.mixin(zpMixins)
    app.mixin(storeMixin)
    app.component('navigation', navigation)
    app.component('new-bottom', newBottom)
    app.component('mask-dialog', maskDialog);
    app.component('new-request-loading', newRequestLoading)
    app.config.globalProperties.$jumpPage = jumpPage
    app.config.globalProperties.$api = api
    app.config.globalProperties.$auth = auth
    app.config.globalProperties.$axios = axios
    app.config.globalProperties.$point = point
    app.config.globalProperties.$defaultAvatar = defaultAvatar
    app.config.globalProperties.$defaultName = defaultName
    // app.config.globalProperties.$noMultipleClicks = utils.noMultipleClicks
    return {
        app
    }
}
// #endif