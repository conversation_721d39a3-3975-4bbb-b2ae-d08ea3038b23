// 服务类型枚举
export const SERVICE_TYPES = {
  POSTPARTUM_RECOVERY: 'CHKF', // 产后康复
  CONFINEMENT_MEAL: 'YZSS',    // 月子膳食
  NURSING_SERVICE: 'HLFW',     // 护理服务
  OTHER_SERVICE: 'QTFW'        // 其他服务
}

// 服务类型映射
export const SERVICE_TYPE_MAP = {
  [SERVICE_TYPES.POSTPARTUM_RECOVERY]: '产后康复',
  [SERVICE_TYPES.CONFINEMENT_MEAL]: '月子膳食',
  [SERVICE_TYPES.NURSING_SERVICE]: '护理服务',
  [SERVICE_TYPES.OTHER_SERVICE]: '其他服务'
}

// 分页配置
export const PAGINATION_CONFIG = {
  DEFAULT_PAGE_SIZE: 10,
  INITIAL_PAGE: 1,
  MOM_SAYS_PAGE_SIZE: 3,
  RECOMMENDED_MOM_PAGE_SIZE: 10
}

// 用户类型
export const USER_TYPES = {
  USER: 'USER',
  STAFF: 'STAFF'
}

// 页面路由
export const PAGE_ROUTES = {
  USER_DETAIL: '/pageA/pageB/community/sending',
  STAFF_DETAIL: '/pageA/pageB/community/staffdetail',
  MOM_HOMEPAGE: '/pageA/pageB/community/note/notedetail',
  MOM_INFLUENCE: '/pageA/pageB/community/motherinfluencing',
  SENDING_LIST: '/pageA/pageB/community/sending/list'
}

// 响应状态码
export const RESPONSE_CODES = {
  SUCCESS: 200
}

// 加载状态
export const LOAD_STATUS = {
  HAS_MORE: 1,
  NO_MORE: 2
}
