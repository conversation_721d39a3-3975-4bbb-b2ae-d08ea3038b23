<template>
  <view class="community-module" v-if="hasCustomerPage">
    <!-- 标题区域 -->
    <view class="section-header">
      <view class="section-header__title">宝妈社区</view>
      <view class="section-header__more" @tap="handleMoreClick">
        <view>更多</view>
        <u-icon name="arrow-right" color="#aaaaaa" size="26"></u-icon>
      </view>
    </view>

    <!-- 内容区域 -->
    <view class="scroll-list">
      <swiper
        class="community-swiper"
        :current="currentTab"
        :indicator-dots="indicatorDots"
        indicator-color="#cccccc"
        indicator-active-color="#FE5B86"
      >
        <!-- 宝妈说 -->
        <swiper-item>
          <view class="head-content" @tap="handleMomSpeakClick">
            <view class="head-content__title">
              <view>宝妈说</view>
              <u-icon name="arrow-right" color="#fff" size="24"></u-icon>
            </view>
            <image
              src="@/static/images/home/<USER>"
              style="width: 84rpx; height: 84rpx"
            />
          </view>
          <view class="swiper-item">
            <view
              class="hot-topic"
              @tap="handleMomInfluencingClick"
              v-for="(dayItem, index) in customerPage"
              :key="index"
            >
              <view class="topic-image">
                <image
                  v-if="dayItem?.contentPhotos?.length"
                  :src="dayItem.contentPhotos[0]"
                  class="topic-image__content"
                  mode="aspectFill"
                >
                </image>
              </view>
              <view class="topic-content">
                <view class="topic-header">
                  <!-- <image
                    src="@/static/images/home/<USER>"
                    mode="aspectFill"
                    style="width: 28rpx; height: 24rpx"
                  /> -->
                  <view class="topic-header__text">
                    {{ dayItem.content }}
                  </view>
                </view>
                <view class="topic-description">
                  <text class="topic-description__text">{{
                    dayItem.content
                  }}</text>
                </view>
                <view class="topic-footer">
                  <view v-if="dayItem">
                    <image
                      lazy-load
                      :src="dayItem.avatar || $defaultAvatar"
                      class="topic-footer__avatar"
                    />
                  </view>
                  <view class="topic-footer__name">
                    {{ dayItem.nickname || $defaultName }}
                  </view>
                </view>
              </view>
            </view>
          </view>
        </swiper-item>

        <!-- 热门话题 -->
        <swiper-item>
          <view class="head-content" @tap="handleTopicDetailClick">
            <view class="head-content__title">
              <view>热门话题</view>
              <u-icon name="arrow-right" color="#fff" size="24"></u-icon>
            </view>
            <image
              src="@/static/images/home/<USER>"
              style="width: 84rpx; height: 84rpx"
            />
          </view>
          <view class="swiper-item">
            <view
              class="hot-topic"
              @tap="handleHotTopicClick"
              :data-topicId="dayItem.topicId"
              v-for="(dayItem, index) in hotList"
              :key="index"
            >
              <view class="topic-image">
                <image
                  v-if="dayItem?.topicPhotos?.length"
                  :src="dayItem.topicPhotos[0]"
                  class="topic-image__content"
                >
                </image>
              </view>
              <view class="topic-content">
                <view class="topic-header">
                  <image
                    src="@/static/images/home/<USER>"
                    mode="aspectFill"
                    style="width: 28rpx; height: 24rpx"
                  />
                  <view class="topic-header__text">
                    {{ dayItem.topicName }}
                  </view>
                </view>
                <view class="topic-description">
                  <text class="topic-description__label">简介：</text>
                  <text class="topic-description__text">{{
                    dayItem.topicDescription
                  }}</text>
                </view>
                <view class="topic-footer">
                  <view>
                    <template v-if="index <= 4 && dayItem.userList">
                      <image
                        :src="item.avatar || $defaultAvatar"
                        class="topic-footer__avatar topic-footer__avatar--overlap"
                        v-for="(item, index1) in dayItem.userList"
                        :key="index1"
                      />
                    </template>
                  </view>
                  <view class="topic-footer__participants">
                    {{ dayItem.postNum < 99 ? dayItem.postNum : '99' }}
                    <text v-if="dayItem.postNum > 100">+</text>
                    人参与
                  </view>
                </view>
              </view>
            </view>
          </view>
        </swiper-item>
      </swiper>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted, getCurrentInstance } from 'vue';

// Props定义（保留默认头像和名称的可配置性）
const props = defineProps({
  defaultAvatar: {
    type: String,
    default: '',
  },
  defaultName: {
    type: String,
    default: '匿名用户',
  },
});

// 响应式数据
const customerPage = ref([]);
const hotList = ref([]);
const currentTab = ref(0);
const indicatorDots = ref(true);

// 获取当前实例
const instance = getCurrentInstance();

// 计算属性
const hasCustomerPage = computed(() => {
  return customerPage.value && customerPage.value.length > 0;
});

// 全局变量（兼容原有代码）
const $defaultAvatar = computed(() => props.defaultAvatar);
const $defaultName = computed(() => props.defaultName);

// 生命周期
onMounted(() => {
  initCommunityData();
});

// 初始化社区数据
const initCommunityData = () => {
  customerPageFun();
  hotListFun();
};

// 获取宝妈说数据
const customerPageFun = () => {
  const data = {
    pageSize: 3,
    pageNum: 1,
    type: 'USER',
  };
  instance.proxy.$axios
    .get(instance.proxy.$api.customerPage, data)
    .then((res) => {
      if (res.data.code == 200) {
        customerPage.value = res.data.rows;
      }
    })
    .catch((error) => {
      console.error('获取宝妈说数据失败:', error);
    });
};

// 获取热门话题数据
const hotListFun = () => {
  const data = {
    limit: 3,
  };
  instance.proxy.$axios
    .get(instance.proxy.$api.hotList, data)
    .then((res) => {
      if (res.data.code == 200) {
        hotList.value = res.data.data;
      }
    })
    .catch((error) => {
      console.error('获取热门话题数据失败:', error);
    });
};

// 事件处理 - 内部实现页面跳转
const handleMoreClick = () => {
  instance.proxy.$jumpPage('/pageA/pageB/community/motherspeak');
};

const handleMomSpeakClick = () => {
  instance.proxy.$jumpPage('/pageA/pageB/community/motherinfluencing');
};

const handleMomInfluencingClick = () => {
  instance.proxy.$jumpPage('/pageA/pageB/community/motherinfluencing');
};

const handleTopicDetailClick = () => {
  instance.proxy.$jumpPage('/pageA/pageB/community/topicdetail/topic');
};

const handleHotTopicClick = (e) => {
  const topicId = e.currentTarget.dataset.topicid;
  instance.proxy.$jumpPage(
    '/pageA/pageB/community/topicdetail?topicId=' + topicId,
  );
};
</script>

<style scoped>
/* ===== 宝妈社区模块样式 ===== */
.community-module {
  width: 100%;
  margin: 40rpx 0;
}

/* 标题区域 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0 0 20rpx 0;
}

.section-header__title {
  font-weight: bold;
  font-size: 34rpx;
  color: #333333;
}

.section-header__more {
  display: flex;
  color: #aaaaaa;
  font-size: 28rpx;
  align-items: center;
}

/* 轮播区域 */
.scroll-list {
  width: 100%;
  margin-top: 20rpx;
  background: #ffffff;
  border-top-left-radius: 20rpx;
  border-top-right-radius: 20rpx;
}

.community-swiper {
  height: 594rpx;
}

/* 头部内容 - 与原有headContent样式一致 */
.head-content {
  height: 80rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);

  padding: 0rpx 20rpx;
  justify-content: space-between;
  align-items: center;
  display: flex;
}

.head-content__title {
  display: flex;
  align-items: baseline;
  /* color: #ffffff; */
  font-size: 28rpx;
  font-weight: bold;
}

/* 轮播项 - 与原有swiperItem样式一致 */
.swiper-item {
  height: 514rpx;
  padding: 0rpx 20rpx;
  border-bottom-right-radius: 20rpx;
  border-bottom-left-radius: 20rpx;
}

/* 话题卡片 - 与原有hotTopic样式一致 */
.hot-topic {
  display: flex;
  margin-top: 20rpx;
  height: 140rpx;
}

/* 话题图片 - 与原有fit样式一致 */
.topic-image {
  width: 27%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.topic-image__content {
  width: 100%;
  height: 100%;
  border-radius: 10rpx;
}

/* 话题内容 - 与原有hotTopic1样式一致 */
.topic-content {
  margin-left: 20rpx;
  width: 100%;
}

.topic-header {
  display: flex;
  align-items: center;
}

.topic-header__text {
  color: #333333;
  font-size: 28rpx;
  font-weight: bold;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  flex: 1;
  width: 0;
}

.topic-description {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  margin-top: 6rpx;
}

.topic-description__label {
  color: #333333;
  font-size: 24rpx;
}

.topic-description__text {
  color: #777777;
  font-size: 24rpx;
}

/* 话题底部 - 与原有hotTopic4样式一致 */
.topic-footer {
  margin-top: 15rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.topic-footer__avatar {
  width: 40rpx;
  height: 40rpx;
  border-radius: 100rpx;
}

.topic-footer__avatar--overlap {
  margin-right: -0.8em !important;
}

.topic-footer__name {
  color: hsla(0, 0%, 67%, 1);
  font-size: 24rpx;
  margin-left: 50rpx;
}

.topic-footer__participants {
  color: hsla(0, 0%, 67%, 1);
  font-size: 24rpx;
}
</style>
