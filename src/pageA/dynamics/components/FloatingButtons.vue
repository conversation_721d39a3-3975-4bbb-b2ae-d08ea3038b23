<template>
  <view class="floating-buttons">
    <!-- 分享按钮 -->
    <button open-type="share" class="floating-buttons__share">
      <image
        :src="ICONS.SHARE"
        class="floating-buttons__share-icon"
      />
    </button>
    
    <!-- 回到顶部按钮 -->
    <u-back-top
      class="floating-buttons__back-top"
      :scrollTop="scrollTop"
      :mode="backToTopMode"
      :icon-style="backToTopIconStyle"
      :custom-style="backToTopCustomStyle"
    />
  </view>
</template>

<script setup>
import { ICONS } from '../assets.js'

const props = defineProps({
  scrollTop: {
    type: Number,
    default: 0
  }
})

const backToTopMode = 'square'
const backToTopIconStyle = {
  fontSize: '35rpx',
  color: '#4A4A4A',
}
const backToTopCustomStyle = {
  backgroundColor: '#ffffff',
  borderRadius: '100rpx',
  width: '70rpx',
  height: '70rpx',
  bottom: '180rpx',
  right: '24rpx',
  boxShadow: '2px 2px 5px rgba(0,0,0,0.1)',
}
</script>

<style scoped lang="less">
.floating-buttons {
  position: fixed;
  bottom: 260rpx;
  right: 0;
  z-index: 999;

  &__share {
    padding: 0;
    border: none;
    background: none;
    width: 120rpx;
    height: 120rpx;
    overflow: hidden;
    cursor: pointer;
    margin-bottom: 20rpx;
  }

  &__share-icon {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
    transition: transform 0.3s ease;
  }

  &__back-top {
    border-radius: 100rpx;
  }
}
</style>
