.main {
	background: #f6f7fb;
}

.content {
	background: #fff;
	padding: 20rpx 24rpx;
}

.head {
	display: flex;
	justify-content: space-between;
}

.cancel {
	color: #333333;
	font-size: 28rpx;
}

.publish-upload {
	display: block;
}

.publish {
	width: 96rpx;
	text-align: center;
	height: 56rpx;
	line-height: 56rpx;
	background: #7e6dfc;
	color: #ffffff;
	border-radius: 10rpx;
	font-size: 28rpx;
}
.textarea {
	margin-top: 20rpx;
	margin-bottom: 20rpx;
	width: 690rpx;
	font-size: 26rpx;
}
.uploading {
	display: flex;
	flex-wrap: wrap;
	margin-bottom: 20rpx;
}

.uploadingImg {
	margin-right: 20rpx;
	position: relative;
}





