#!/usr/bin/env node

/**
 * 小程序多项目上传脚本
 * 支持上传到不同的 appid 项目
 */

const ci = require('miniprogram-ci');
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const { projects, defaultConfig, getProject, validateProject } = require('../config/config-loader.js');
const BuildConfigInjector = require('./build-config-injector.js');

// 命令行参数解析
const args = process.argv.slice(2);
const projectName = args[0] || 'main';
const environment = args[1] || 'develop';
const version = args[2];
const desc = args[3];

// 颜色输出
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

// 显示帮助信息
function showHelp() {
  log('小程序多项目上传工具', 'cyan');
  log('');
  log('使用方法:', 'yellow');
  log('  node scripts/upload.js [项目名] [环境] [版本号] [描述]', 'white');
  log('');
  log('参数说明:', 'yellow');
  log('  项目名   - 项目标识，可选值：' + Object.keys(projects).join(', '), 'white');
  log('  环境     - 部署环境，可选值：develop, trial, release', 'white');
  log('  版本号   - 版本号，不传则自动生成', 'white');
  log('  描述     - 版本描述，不传则使用默认描述', 'white');
  log('');
  log('示例:', 'yellow');
  log('  node scripts/upload.js main develop', 'white');
  log('  node scripts/upload.js main trial 1.0.0 "新功能上线"', 'white');
  log('  node scripts/upload.js test develop', 'white');
}

// 检查私钥文件
function checkPrivateKey(privateKeyPath) {
  if (!fs.existsSync(privateKeyPath)) {
    logError(`私钥文件不存在: ${privateKeyPath}`);
    logInfo('请确保私钥文件存在，或者从微信公众平台下载私钥文件');
    logInfo('私钥文件下载地址：https://mp.weixin.qq.com -> 开发 -> 开发管理 -> 开发设置 -> 小程序代码上传');
    return false;
  }
  return true;
}

// 构建项目
async function buildProject(project, environment, projectName) {
  const envConfig = project.environments[environment];
  if (!envConfig) {
    throw new Error(`项目 "${projectName}" 不支持环境 "${environment}"`);
  }

  logInfo(`开始构建项目: ${project.name} (${envConfig.name})`);

  // 注入构建配置
  const injector = new BuildConfigInjector();
  const tenantId = envConfig.tenantId || project.defaultTenantId || '194338';

  try {
    // 注入配置
    injector.inject({
      tenantId,
      appid: project.appid,
      projectName,
      environment
    });

    // 执行构建
    execSync(envConfig.buildCommand, {
      stdio: 'inherit',
      cwd: path.resolve(__dirname, '../..')
    });

    logSuccess('项目构建完成');

    // 清理备份文件
    injector.cleanupBackups();

  } catch (error) {
    // 构建失败时恢复备份
    injector.restoreBackups();
    throw new Error(`项目构建失败: ${error.message}`);
  }
}

// 上传到微信小程序
async function uploadToWeChat(project, environment, version, desc) {
  logInfo(`开始上传到微信小程序: ${project.name}`);
  
  try {
    // 创建项目实例
    const miniProgram = new ci.Project({
      appid: project.appid,
      type: 'miniProgram',
      projectPath: project.projectPath,
      privateKeyPath: project.privateKeyPath,
      ignores: project.ignores || defaultConfig.upload.ignores
    });

    // 上传
    const uploadResult = await ci.upload({
      project: miniProgram,
      version: version,
      desc: desc,
      setting: {
         es6: true,
        es7: true,
        minify: true,
        codeProtect: false,
        minifyJS: true,
        minifyWXML: true,
        minifyWXSS: true,
        autoPrefixWXSS: true
      },
      robot: defaultConfig.upload.robot,
      onProgressUpdate: (progress) => {
        logInfo(`上传进度: ${progress}%`);
      }
    });

    logSuccess(`上传成功！`);
    logInfo(`版本号: ${version}`);
    logInfo(`描述: ${desc}`);
    
    if (uploadResult.subPackageInfo) {
      logInfo('分包信息:');
      uploadResult.subPackageInfo.forEach((pkg, index) => {
        logInfo(`  分包${index + 1}: ${pkg.name} (${pkg.size}KB)`);
      });
    }

    return uploadResult;
  } catch (error) {
    throw new Error(`上传失败: ${error.message}`);
  }
}

// 主函数
async function main() {
  try {
    // 显示帮助信息
    if (args.includes('--help') || args.includes('-h')) {
      showHelp();
      return;
    }

    logInfo('='.repeat(50));
    logInfo('小程序多项目上传工具');
    logInfo('='.repeat(50));

    // 验证项目配置
    const validation = validateProject(projectName);
    if (!validation.valid) {
      logError(validation.error);
      logInfo('可用项目: ' + Object.keys(projects).join(', '));
      return;
    }

    // 获取项目配置
    const project = getProject(projectName);
    
    // 检查环境配置
    if (!project.environments[environment]) {
      logError(`项目 "${projectName}" 不支持环境 "${environment}"`);
      logInfo('可用环境: ' + Object.keys(project.environments).join(', '));
      return;
    }

    // 检查私钥文件
    if (!checkPrivateKey(project.privateKeyPath)) {
      return;
    }

    // 生成版本号
    const finalVersion = version || defaultConfig.version.auto();
    
    // 生成描述
    const finalDesc = desc || `${project.environments[environment].desc} - ${finalVersion}`;

    logInfo(`项目名称: ${project.name}`);
    logInfo(`项目 AppID: ${project.appid}`);
    logInfo(`部署环境: ${project.environments[environment].name}`);
    logInfo(`版本号: ${finalVersion}`);
    logInfo(`版本描述: ${finalDesc}`);
    logInfo('');

    // 构建项目
    await buildProject(project, environment, projectName);
    
    // 检查构建产物
    if (!fs.existsSync(project.projectPath)) {
      logError(`构建产物不存在: ${project.projectPath}`);
      logInfo('请检查构建命令是否正确执行');
      return;
    }

    // 上传到微信小程序
    await uploadToWeChat(project, environment, finalVersion, finalDesc);

    logSuccess('🎉 上传完成！');
    logInfo('请到微信公众平台查看上传结果');
    
  } catch (error) {
    logError(error.message);
    process.exit(1);
  }
}

// 运行主函数
main();
