<template>
  <view class="main">
    <view class="content">
      <view class="head">
        <view class="cancel"></view>
        <view class="publish" @tap="publish">发表</view>
      </view>
      <!-- <input class="uni-input" placeholder-class="input-placeholder" placeholder="填写标题会有更多赞哦(选填)"
                v-model="form.title" /> -->
      <textarea
        :maxlength="800"
        placeholder-class="input-placeholder"
        :value="form.content"
        placeholder="这一刻你想说些什么？"
        class="textarea"
        @input="searchInput"
      />
      <upload-vue
        class="publish-upload"
        @clickFile="clickAlbumImg"
      ></upload-vue>
      <u-form :model="form" ref="uform" class="publish-form">
        <view class="basics">
          <u-form-item
            label="角色"
            label-width="120"
            prop="roleCode"
            style="width: 45%"
          >
            <u-input
              placeholder="请选择角色"
              v-model="label.roleCode"
              type="select"
              @click="openSelect('roleCode')"
            />
          </u-form-item>
          <u-form-item
            label="姓名"
            label-width="120"
            prop="userId"
            style="width: 45%"
          >
            <u-input
              placeholder="请选择姓名"
              v-model="label.userId"
              type="select"
              @click="openSelect('userId')"
            />
          </u-form-item>
        </view>
        <!--       <u-form-item label="标签" label-width="120" prop="taskNodeId">
                    <u-input placeholder="请选择标签" v-model="label.taskNodeId" type="select"
                        @click="openSelect('taskNodeId')" />
                </u-form-item> -->
        <u-form-item label="服务天数" label-width="160" prop="timeNumber">
          <u-input
            placeholder="请选择服务天数"
            v-model="label.timeNumber"
            type="select"
            @click="openSelect('timeNumber')"
          />
        </u-form-item>
      </u-form>
      <view class="formBoard">
        <view class="switchName"> 内容模版 </view>
        <view class="switchName1">
          <u-switch v-model="checked" size="50rpx"></u-switch>
          <view style="font-size: 22rpx; color: #c4c4c4; margin-left: 20rpx">
            点击按钮展示模版可供使用
          </view>
        </view>
      </view>
      <u-select
        v-model="selectShow"
        :list="selectList"
        @confirm="onSelected"
      ></u-select>
      <view style="margin-top: 20rpx">
        <text style="color: #ff0000">*</text>
        <text class="roomNUm">标签</text>
      </view>
      <view class="grid-container1" v-if="unfoldShow">
        <view
          :class="tabIndexs == index ? 'gridActive' : 'grid-item1'"
          @tap="tab"
          :data-id="item.value"
          :data-index="index"
          :data-name="item.label"
          v-for="(item, index) in tagList"
          :key="index"
        >
          {{ item.label }}
        </view>
      </view>
      <view class="grid-container1" v-if="!unfoldShow">
        <view
          @tap="tab"
          :data-id="item.value"
          :data-index="index"
          :data-name="item.label"
          v-for="(item, index) in tagList"
          :key="index"
        >
          <view
            v-if="index <= 8"
            :class="tabIndexs == index ? 'gridActive' : 'grid-item1'"
          >
            {{ item.label }}
          </view>
        </view>
      </view>
      <view class="unfold" v-if="tagList.length > 9" @click="unfold">
        <view style="color: #3f6fff">
          {{ unfoldShow ? "收起" : "展开" }}
        </view>
        <image
          :src="
            unfoldShow
              ? 'http://cdn.xiaodingdang1.com/2025/04/08/de07df2be20242fd852ae84d3b0c30b5.png'
              : 'http://cdn.xiaodingdang1.com/2025/04/08/aca3e630588846e381feea6bac9dc10f.png'
          "
          mode=""
          style="width: 30rpx; height: 20rpx; margin-left: 10rpx"
        ></image>
      </view>
      <u-mask :show="show" @click="show = false">
        <view class="maskContent">
          <view class="maskTitle"> 模板详情 </view>
          <scroll-view scroll-y="true" class="maskContent1">
            {{ tabcontent }}
          </scroll-view>
          <view class="maskBtn">
            <u-button size="medium" @click="show = false">取消</u-button>
            <u-button size="medium" type="primary" @click="employ"
              >使用</u-button
            >
          </view>
        </view>
      </u-mask>
      <view class="tabs1" v-if="checked">
        <view style="height: 20rpx"></view>
        <view class="tabs" v-if="tabsList.length > 0">
          <view class="tabsHead">
            <!-- <u-tabs :list="tabsList" :is-scroll="true" :current="current" @change="changes"></u-tabs> -->
            <view class="container">
              <scroll-view scroll-x="true" class="tab-scroll-view">
                <view class="tab-wrapper">
                  <view
                    v-for="(tab, index) in tabsList"
                    :key="index"
                    class="tab-item"
                    :class="{ active: current === index }"
                    @click="changes(index)"
                  >
                    {{ tab.name }}
                  </view>
                </view>
              </scroll-view>
            </view>
            <view class="cutOff"></view>
          </view>
          <view
            class="tabsContent"
            v-for="(item, index) in templateList"
            :key="index"
            @click="tabsContent(item)"
          >
            <view class="tabsContent1">
              {{ item.content }}
            </view>
            <view class="cutOff"></view>
          </view>
        </view>
      </view>
    </view>
    <new-request-loading></new-request-loading>
  </view>
</template>

<script>
import uploadVue from "@/components/upload.vue";
import utils from "@/utils/util.js";
const dayList = utils.getCheckInDayList();
export default {
  components: {
    uploadVue,
  },
  data() {
    return {
      unfoldShow: false,
      current: 0,
      contentStyle: "",
      checked: false,
      tabIndexs: -1,
      form: {
        // title: '',
        roleCode: "",
        userId: "",
        taskNodeId: "",
        timeNumber: "",
        content: "",
        imgs: [],
        videos: [],
      },
      label: {
        roleCode: "",
        userId: "",
        taskNodeId: "",
        timeNumber: "",
      },
      selectShow: false,
      selectList: [],
      selectType: "",
      rules: {
        roleCode: [
          {
            required: true,
            message: "请选择角色",
            trigger: ["change", "blur"],
          },
        ],
        userId: [
          {
            required: true,
            message: "请选择姓名",
            trigger: ["change", "blur"],
          },
        ],
        taskNodeId: [
          {
            required: true,
            message: "请选择标签",
            trigger: ["change", "blur"],
          },
        ],
        timeNumber: [
          {
            type: "number",
            required: true,
            message: "请选择服务天数",
            trigger: ["change", "blur"],
          },
        ],
      },
      roleList: [
        {
          label: "护理",
          value: "NURSE",
        },
        {
          label: "产康",
          value: "POSTPARTUM",
        },
        {
          label: "厨师",
          value: "CHEF",
        },
      ],
      tagList: [],
      momList: [],
      labelName: "",
      templateList: [],
      tabsList: [],
      show: false,
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onPullDownRefresh() {},
  onShareTimeline() {},
  onLoad(options) {
    this.form.momId = options.momId;
    // this.form.userId = options.userId
    this.getMom();
    this.getTag();
    // 厨师和产康不能全选
    let roles = uni.getStorageSync("roles");
    if (roles == "POSTPARTUM") {
      this.roleList = this.roleList.filter(
        (item) => item.value == "POSTPARTUM"
      );
    } else if (roles == "CHEF") {
      this.roleList = this.roleList.filter((item) => item.value == "CHEF");
    }
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    this.$refs.uform.setRules(this.rules);
  },
  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {},
  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},
  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},
  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},
  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {},
  methods: {
    unfold() {
      this.unfoldShow = !this.unfoldShow;
    },
    change(status) {},
    employ() {
      //选择模板
      this.setData({
        "form.content": this.tabcontent,
        show: false,
      });
    },
    tabsContent(item) {
      this.show = true;
      this.tabcontent = item.content;
    },
    changes(index) {
      this.current = index;
      this.contentStyle = this.tabsList[index].label;
      this.getlistByLabel();
    },
    getlistByLabel() {
      //通过标签名称查询模板
      let data = {
        labelName: this.labelName,
        type: "服务笔记",
      };
      if (this.contentStyle) {
        data.contentStyle = this.contentStyle;
      }
      this.$axios.get(this.$api.listByLabel, data).then((res) => {
        if (res.data.code == 200) {
          this.setData({
            templateList: res.data.data,
          });
        }
      });
    },
    getTemplateStyle() {
      //获取所有模板风格
      this.$axios.get(this.$api.templateStyle).then((res) => {
        if (res.data.code == 200) {
          this.tabsList = [];
          const data = res.data.data;
          if (data) {
            data.forEach((item) => {
              const list = {
                id: item.id,
                name: item.name,
                label: item.name,
              };
              this.tabsList.push(list);
            });
          }
          this.tabsList.unshift({
            id: "",
            name: "全部",
            label: "",
          });
        }
      });
    },
    tab(e) {
      console.log(e.currentTarget.dataset.id);
      let name = e.currentTarget.dataset.name;
      let index = e.currentTarget.dataset.index;
      let taskNodeId = e.currentTarget.dataset.id;
      this.labelName = name;
      this.tabIndexs = index;
      this.form.taskNodeId = taskNodeId;
      this.current = 0;
      this.contentStyle = "";
      this.getlistByLabel();
      this.getTemplateStyle();
    },
    // 根据角色获取姓名
    async getMom() {
      const res = await this.$axios.get(this.$api.getMomTeam, {
        roleCode: this.form.roleCode,
      });
      const data = res?.data?.data;
      if (data) {
        this.momList = [];
        data.forEach((item) => {
          this.momList.push({
            label: item.name,
            value: item.userId,
          });
        });
      }
    },
    // 根据角色获取标签
    async getTag() {
      const res = await this.$axios.get(this.$api.getAllTag, {
        nurseRole: this.form.roleCode,
      });
      const data = res?.data?.data;
      if (data) {
        this.tabIndexs = -1;
        this.form.taskNodeId = "";
        this.tagList = [];
        data.forEach((item) => {
          this.tagList.push({
            label: item.nodeName,
            value: item.taskNodeId,
          });
        });
      }
    },
    openSelect(type) {
      this.selectList = [];
      this.selectType = type;
      switch (this.selectType) {
        case "roleCode":
          this.selectList = this.roleList;
          break;
        case "userId":
          this.selectList = this.momList;
          break;
        case "taskNodeId":
          this.selectList = this.tagList;
          break;
        case "timeNumber":
          this.selectList = dayList;
          break;
        default:
          break;
      }
      this.selectShow = true;
    },
    onSelected(options) {
      this.form[this.selectType] = options[0].value;
      this.label[this.selectType] = options[0].label;
      switch (this.selectType) {
        case "roleCode":
          // 切换角色时候重置标签和姓名
          this.form.userId = "";
          this.form.taskNodeId = "";
          this.label.userId = "";
          this.label.taskNodeId = "";
          this.momList = [];
          this.tagList = [];
          this.getMom();
          this.getTag();
          break;
        case "userId":
          break;
        case "taskNodeId":
          break;
        case "timeNumber":
          break;
        default:
          break;
      }
    },
    clickAlbumImg(value) {
      console.log("clickAlbumImg", value);
      // value: [{type: '',url: ''}]
      let imgUrl = [];
      let videos = [];
      if (value && value.length > 0) {
        value.forEach((item) => {
          item.type == "video" ? videos.push(item.url) : imgUrl.push(item.url);
        });
        this.form.imgs = imgUrl;
        this.form.videos = videos;
      } else {
        this.form.imgs = [];
        this.form.videos = [];
      }
    },
    //发布
    async publish() {
      if (this.form.roleCode == "") {
        uni.showToast({
          title: "请选择角色",
          icon: "none",
          duration: 3000, //持续的时间
        });
        return;
      }
      if (this.form.userId == "") {
        uni.showToast({
          title: "请选择姓名",
          icon: "none",
          duration: 3000, //持续的时间
        });
        return;
      }
      if (this.form.taskNodeId == "") {
        uni.showToast({
          title: "请选择标签",
          icon: "none",
          duration: 3000, //持续的时间
        });
        return;
      }
      if (this.form.timeNumber == "") {
        uni.showToast({
          title: "请选择服务天数",
          icon: "none",
          duration: 3000, //持续的时间
        });
        return;
      }
      if (this.form.content == "") {
        uni.showToast({
          title: "请填写发布内容",
          icon: "none",
          duration: 3000, //持续的时间
        });
        return;
      }
	  let imgsList=[]
	  this.form.imgs.forEach(item=>{
		  imgsList.push(item.url)
	  })
	  this.form.imgs=imgsList
      this.changeLoading(true);
      const res = await this.$axios.post(this.$api.pushDiaryList, this.form);
      if (res?.data?.code == 200) {
        uni.$emit("publishon");
        this.cancel();
      }
    },
    searchInput(e) {
      //输入框获取值
      this.form.content = e.detail.value;
    },
    cancel() {
      //取消
      uni.navigateBack({
        delta: 1,
      });
    },
  },
};
</script>
<style scoped lang="less">
@import "./publishnote.less";
</style>
<style>
.input-placeholder {
  color: #bcbcbc;
}
</style>
