<template>
  <view
    class="custom-tabbar"
    :class="{ 'tabbar-fixed': fixed, 'tabbar-relative': !fixed }"
  >
    <!-- tabbar 内容 -->
    <view class="tabbar-content" id="tabbar-content">
      <!-- 前两个 tabbar 项目 -->
      <view
        class="tabbar-item"
        v-for="(item, index) in tabList.slice(0, 2)"
        :key="index"
        @click="switchTab(item, index)"
      >
        <view class="tabbar-icon">
          <image
            :src="
              isActiveTab(item, index) ? item.selectedIconPath : item.iconPath
            "
            class="icon-image"
            mode="aspectFit"
          />
        </view>
        <text class="tabbar-text" :class="{ active: isActiveTab(item, index) }">
          {{ item.text }}
        </text>
      </view>

      <!-- AI 助理特殊处理 - 中间突出的圆球 -->
      <view
        class="tabbar-item ai-service-item"
        @click="switchTab(tabList[2], 2)"
      >
        <view class="ai-tabbar-container">
          <view class="ai-circle">
            <image
              :src="tabList[2].iconPath"
              class="ai-icon-image"
              mode="aspectFit"
            />
          </view>
          <!-- AI 助理标题使用图片 -->
          <view class="ai-title">
            <image
              src="/static/images/tabbar/ai_title.png"
              :class="[
                'ai-title-image',
                { 'ai-title-active': isActiveTab(tabList[2], 2) },
              ]"
              mode="aspectFit"
            />
          </view>
        </view>
      </view>

      <!-- 后两个 tabbar 项目 -->
      <view
        class="tabbar-item"
        v-for="(item, index) in tabList.slice(3)"
        :key="item.key || item.type || index"
        @click="switchTab(item, index + 3)"
      >
        <view class="tabbar-icon">
          <image
            :src="
              isActiveTab(item, index + 3)
                ? item.selectedIconPath
                : item.iconPath
            "
            class="icon-image"
            mode="aspectFit"
          />
          <!-- 未读数量徽章 - 仅在"我的"tab显示 -->
          <view
            v-if="item.key === 'mine' && getTotalUnreadCount > 0"
            class="unread-badge"
          >
            {{ getTotalUnreadCount > 99 ? '99+' : getTotalUnreadCount }}
          </view>
        </view>
        <text
          class="tabbar-text"
          :class="{ active: isActiveTab(item, index + 3) }"
        >
          {{ item.text }}
        </text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted, watch, getCurrentInstance, computed } from 'vue';
import { useStore } from 'vuex';

// 获取 Vuex store
const store = useStore();

// 获取未读消息数量
const getTotalUnreadCount = computed(() => {
  const count = store.getters['m_user/getTotalUnreadCount'];
  console.log('CustomTabBar - 未读消息数量:', count);
  console.log('CustomTabBar - store state:', store.state.m_user.unreadCounts);
  return count;
});

// 定义 props
const props = defineProps({
  activeKey: {
    type: String,
    default: '', // 使用 key 标识当前选中项
  },
  fixed: {
    type: Boolean,
    default: true, // 默认为固定模式
  },
});

// tabbar 配置
const tabList = [
  {
    key: 'home',
    type: 'home',
    pagePath: '/pageA/home',
    iconPath: '/static/images/tabbar/home.png',
    selectedIconPath: '/static/images/tabbar/home_active.png',
    text: '首页',
  },
  {
    key: 'community',
    type: 'community',
    pagePath: '/pageA/community/index',
    iconPath: '/static/images/tabbar/service.png',
    selectedIconPath: '/static/images/tabbar/service_active.png',
    text: '会所服务',
  },
  {
    key: 'ai-service',
    type: 'ai-service',
    pagePath: '/pageA/aiService/aiService',
    iconPath: '/static/images/tabbar/ai.png',
    selectedIconPath: '/static/images/tabbar/ai_active.png',
    text: 'AI助理',
  },
  {
    key: 'dynamics',
    type: 'dynamics',
    pagePath: '/pageA/dynamics/index',
    iconPath: '/static/images/tabbar/dynamic.png',
    selectedIconPath: '/static/images/tabbar/dynamic_active.png',
    text: '会所动态',
  },
  {
    key: 'mine',
    type: 'mine',
    pagePath: '/pageA/mine/index',
    iconPath: '/static/images/tabbar/mine.png',
    selectedIconPath: '/static/images/tabbar/mine_active.png',
    text: '我的',
  },
];

const currentKey = ref('');

// 获取当前实例
const instance = getCurrentInstance();

// 获取当前页面路径对应的 key
const getCurrentPageKey = () => {
  const pages = getCurrentPages();
  if (pages.length > 0) {
    const currentPage = pages[pages.length - 1];
    const currentRoute = '/' + currentPage.route;

    const item = tabList.find((item) => item.pagePath === currentRoute);
    return item ? item.key : '';
  }
  return '';
};

// 判断是否为当前选中的 tab
const isActiveTab = (item, index) => {
  // 优先使用传入的 activeKey
  if (props.activeKey && item.key) {
    return props.activeKey === item.key;
  }
  // 否则使用当前页面的 key
  if (currentKey.value && item.key) {
    return currentKey.value === item.key;
  }
  return false;
};

// 切换 tab
const switchTab = (item, index) => {
  // 如果已经是当前选中项，则不执行切换
  if (isActiveTab(item, index)) return;

  uni.switchTab({
    url: item.pagePath,
    success: () => {
      currentKey.value = item.key || '';
    },
    fail: (err) => {
      console.error('switchTab failed:', err);
    },
  });
};

// 初始化当前状态
const initCurrentState = () => {
  // 优先使用传入的 activeKey
  if (props.activeKey) {
    currentKey.value = props.activeKey;
  } else {
    // 否则自动检测当前页面对应的 key
    currentKey.value = getCurrentPageKey();
  }
};

// 监听 props 变化
watch(
  () => props.activeKey,
  (newKey) => {
    if (newKey) {
      currentKey.value = newKey;
    }
  },
  { immediate: true },
);

// 获取tabbar高度并注册到全局
const getTabbarHeightAndRegister = () => {
  return new Promise((resolve) => {
    // 添加延迟确保DOM已渲染
    setTimeout(() => {
      const query = uni.createSelectorQuery().in(instance.proxy);
      query
        .select('#tabbar-content')
        .boundingClientRect((data) => {
          console.log('CustomTabBar获取到:', data);
          if (data && data.height) {
            const tabbarHeight = data.height;
            console.log('CustomTabBar获取到高度:', tabbarHeight);

            // 注册到全局变量
            getApp().globalData.customTabbarHeight = tabbarHeight;

            // 触发全局事件，通知其他组件tabbar高度已更新
            uni.$emit('customTabbarHeightUpdated', tabbarHeight);

            resolve(tabbarHeight);
          } else {
            // 降级方案：计算默认高度
            const systemInfo = uni.getSystemInfoSync();
            const safeAreaBottom = systemInfo.safeAreaInsets?.bottom || 0;
            const defaultHeight =
              (150 * systemInfo.windowWidth) / 750 + safeAreaBottom;

            console.log('CustomTabBar使用默认高度:', defaultHeight);
            getApp().globalData.customTabbarHeight = defaultHeight;
            uni.$emit('customTabbarHeightUpdated', defaultHeight);

            resolve(defaultHeight);
          }
        })
        .exec();
    }, 100);
  });
};

onMounted(async () => {
  initCurrentState();
  getTabbarHeightAndRegister();
});
</script>

<style scoped lang="scss">
.custom-tabbar {
  z-index: 1000;
  height: fit-content;
  box-sizing: border-box;

  // 固定模式 - 固定在底部
  &.tabbar-fixed {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
  }

  // 相对模式 - 相对定位
  &.tabbar-relative {
    position: relative;
  }
}

.tabbar-content {
  position: relative;
  width: 100%;
  min-height: 120rpx;
  background-color: #ffffff;
  display: flex;
  padding: 20rpx 0;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

.tabbar-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  position: relative;
}

.tabbar-icon {
  width: 46rpx;
  height: 46rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 4rpx;
  position: relative;
}

.icon-image {
  width: 46rpx;
  height: 46rpx;
}

.tabbar-text {
  font-size: 18rpx;
  font-weight: 500;
  color: #9e9e9e;
  text-align: center;
  font-family: PingFang SC, sans-serif;
}

.tabbar-text.active {
  color: #ff4f61;
}

.unread-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  min-width: 32rpx;
  height: 32rpx;
  border-radius: 16rpx;
  background-color: #ff4d4f;
  color: #ffffff;
  font-size: 20rpx;
  font-weight: 500;
  text-align: center;
  line-height: 32rpx;
  padding: 0 6rpx;
  box-shadow: 0 2rpx 4rpx rgba(255, 77, 79, 0.3);
  z-index: 10;
}

// AI 助理特殊样式
.ai-service-item {
  position: relative;
}

.ai-tabbar-container {
  position: absolute;
  top: -44rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.ai-circle {
  width: 86rpx;
  height: 86rpx;
  border-radius: 50%;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ai-icon-image {
  position: relative;
  width: 60rpx;
  height: 60rpx;
  z-index: 2;
  animation: breathe 3s ease-in-out infinite;
}

/* AI 图标呼吸浮动动画 */
@keyframes breathe {
  0%, 100% {
    transform: scale(1) translateY(0);
    opacity: 1;
  }
  50% {
    transform: scale(1.1) translateY(-4rpx);
    opacity: 0.8;
  }
}

// AI 助理标题样式
.ai-title {
  margin-top: 4rpx;
  line-height: 1;
}

.ai-title-image {
  width: 67rpx;
  height: 18rpx;
}

/* AI 助理标题激活状态 - 红色滤镜 */
.ai-title-active {
  filter: invert(36%) sepia(74%) saturate(7482%) hue-rotate(346deg)
    brightness(101%) contrast(101%);
}
</style>
