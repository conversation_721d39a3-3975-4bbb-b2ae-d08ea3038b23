<template>
	<navigation :title="title" :background="bgColor"></navigation>
	<view class="main">
		<view class="content">
			<view class="recentlys">
				<view class="recently">
					<view class="recently1">
						访问时间最长
					</view>
					<view class="recently2" @click="more(2)">
						<view class="recently2_1">
							更多
						</view>
						<image src="http://cdn.xiaodingdang1.com/2025/05/21/938b2a470a9b47c0b4a8580e27dc37fc.png"
							mode="" style="width: 30rpx;height: 30rpx;"></image>
					</view>
				</view>
				<view class="analysisList" v-for="item,index in longestList" :key="index">
					<view class="analysisList3">
						<image
							:src="item.avatar?item.avatar:'http://cdn.xiaodingdang1.com/2024/11/08/6d8894bb65e1418880530c88baf5b001.png'"
							mode="" style="width: 110rpx;height: 110rpx;border-radius: 100rpx;"></image>
						<view class="analysisList1">
							<view class="analysisList1_1">
								{{item.nickname==null||item.nickname==''?'微信用户':item.nickname}}</view>
							<view class="analysisList1_2">
								<view class="analysisList1_3">
									{{item.ipLocation==null?'---':item.ipLocation}}
								</view>
								<view class="analysisList1_4">
									{{item.tel==null?'---':item.tel}}
								</view>
							</view>
							<view class="analysisList1_5">
								<view class="analysisList1_6">
									最近访问时长：
								</view>
								<view class="analysisList1_7">
									{{item.viewTimeStr==null?'---':item.viewTimeStr}}
								</view>
							</view>
						</view>
					</view>
					<view class="analysisList2" @click="ringup(item.tel)">
						联系客户
					</view>
				</view>
			</view>
			<view class="recentlys">
				<view class="recently">
					<view class="recently1">
						浏览次数最多
					</view>
					<view class="recently2" @click="more(3)">
						<view class="recently2_1">
							更多
						</view>
						<image src="http://cdn.xiaodingdang1.com/2025/05/21/938b2a470a9b47c0b4a8580e27dc37fc.png"
							mode="" style="width: 30rpx;height: 30rpx;"></image>
					</view>
				</view>
				<view class="analysisList" v-for="item,index in maxList" :key="index">
					<view class="analysisList3">
						<image
							:src="item.avatar?item.avatar:'http://cdn.xiaodingdang1.com/2024/11/08/6d8894bb65e1418880530c88baf5b001.png'"
							mode="" style="width: 110rpx;height: 110rpx;border-radius: 100rpx;"></image>
						<view class="analysisList1">
							<view class="analysisList1_1">
								{{item.nickname==null||item.nickname==''?'微信用户':item.nickname}}</view>
							<view class="analysisList1_2">
								<view class="analysisList1_3">
									{{item.ipLocation==null?'---':item.ipLocation}}
								</view>
								<view class="analysisList1_4">
									{{item.tel==null?'---':item.tel}}
								</view>
							</view>
							<view class="analysisList1_5">
								<view class="analysisList1_6">
									最近访问次数：
								</view>
								<view class="analysisList1_7">
									{{item.viewNum==null?'---':item.viewNum}}
								</view>
							</view>
						</view>
					</view>
					<view class="analysisList2" @click="ringup(item.tel)">
						联系客户
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				bgColor: '',
				longestList: [],
				maxList: [],
				title: '数据分析',
				module:''

			}
		},
		onLoad(options) {
			this.title = options.module == 'room' ? '月子套房' : options.module == 'community' ? '宝妈社区' : options.module ==
				'recovery' ? '产后康复' : options.module == 'meal' ? '月子膳食' : options.module == 'staff' ? '护理团队' : ''
			this.getlongestList(options.module) //访问时间最长
			this.getmaxList(options.module) //浏览次数最多
			this.module=options.module
		},
		onShow() {

		},
		methods: {
			ringup(tel){
				// 拨打电话
				const phoneNumber =tel; // 客户的电话号码
				uni.makePhoneCall({
				    phoneNumber: phoneNumber,
				    success: () => {
				        console.log('拨打电话成功！');
				    },
				    fail: () => {
				        console.log('拨打电话失败！');
				    }
				});
			},
			more(type){//更多
				uni.navigateTo({
					url:'/pageA/pageB/community/dataAnalysis/moreDataAnalysis?type='+type+'&module='+this.module
				})
			},
			getlongestList(module) {
				//访问时间最长
				let data = {
					pageSize: 5,
					pageNum: 1,
					orderBy: 'viewTime',
					module: module
				};
				this.$axios.get(this.$api.mList, data).then((res) => {
					if (res.data.code == 200) {
						this.setData({
							longestList: res.data.rows
						});
					}
				});
			},
			getmaxList(module) {
				//浏览次数最多
				let data = {
					pageSize: 5,
					pageNum: 1,
					orderBy: 'viewNum',
					module: module
				};
				this.$axios.get(this.$api.mList, data).then((res) => {
					if (res.data.code == 200) {
						this.setData({
							maxList: res.data.rows
						});
					}
				});
			},
		},
		onPageScroll: function(e) {
			if (e.scrollTop > 0) {
				this.setData({
					bgColor: '#ffffff'
				});
			} else if (e.scrollTop < 2) {
				this.setData({
					bgColor: ''
				});
			}
		},
	}
</script>

<style lang="less" scoped>
	@import './dataAnalysisDetails.less';
</style>