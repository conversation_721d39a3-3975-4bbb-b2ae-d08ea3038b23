<template>
  <view class="comment-list">
    <view
      class="comment-list__item"
      v-for="(comment, index) in comments"
      :key="index"
    >
      <view class="comment-list__username">{{ comment.nickname || $defaultName }}:</view>
      <view class="comment-list__content">{{ comment.comment }}</view>
    </view>
  </view>
</template>

<script setup>
const props = defineProps({
  comments: {
    type: Array,
    required: true
  }
})
</script>

<style scoped lang="less">
.comment-list {
  background: #ebebeb;
  border-radius: 4rpx;
  font-size: 24rpx;
  padding: 4rpx 8rpx;
  margin: 15rpx 0 24rpx 0;

  &__item {
    display: flex;
    margin-bottom: 4rpx;

    &:last-child {
      margin-bottom: 0;
    }
  }

  &__username {
    color: #5b6799;
    margin-right: 8rpx;
    flex-shrink: 0;
  }

  &__content {
    color: #777777;
    flex: 1;
    word-wrap: break-word;
    word-break: break-all;
  }
}
</style>
