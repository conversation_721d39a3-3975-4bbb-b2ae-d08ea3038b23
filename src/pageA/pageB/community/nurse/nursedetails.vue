<template>
	<view class="main">
		<view class="coment">
			<view class="titles">
				基础信息
			</view>
			<view class="fgx"></view>
			<view class="list1">
				<view class="title">
					宝妈姓名
				</view>
				<view class="name">
						{{detailsList.momName?detailsList.momName:'--'}}
				</view>
			</view>
			<view class="fgx"></view>
			<view class="list1">
				<view class="title">
					宝妈年龄
				</view>
				<view class="name">
					{{detailsList.age?detailsList.age:'--'}}
				</view>
			</view>
			<view class="fgx"></view>
			<view class="list1">
				<view class="title">
					记录时间
				</view>
				<view class="name">
					{{detailsList.recordTime?detailsList.recordTime:'--'}}
				</view>
			</view>
			<view class="fgx"></view>
			<view class="list1">
				<view class="title">
					入住房号
				</view>
				<view class="name">
					{{detailsList.roomNumber?detailsList.roomNumber:"--"}}
				</view>
			</view>
			<view class="fgx"></view>
			<view class="list1">
				<view class="title">
					入住日期
				</view>
				<view class="name">
					{{detailsList.deliveryDays?detailsList.deliveryDays:'--'}}
				</view>
			</view>
		</view>
		<view class="coment">
			<view class="titles">
				评估表
			</view>
			<view class="fgx"></view>
			<view v-if="fieldList[2].isEnabled">
				<view class="list1">
					<view class="title">
						入住体温
					</view>
					<view class="name">
						{{detailsList.checkinTemperature?detailsList.checkinTemperature:'--'}}
					</view>
				</view>
				<view class="fgx"></view>
			</view>
			<view v-if="fieldList[3].isEnabled">
				<view class="list1">
					<view class="title">
						入住体重
					</view>
					<view class="name">
						{{detailsList.checkinWeight?detailsList.checkinWeight:'--'}}
					</view>
				</view>
				<view class="fgx"></view>
			</view>
			<view v-if="fieldList[4].isEnabled">
				<view class="list1">
					<view class="title">
						收缩压
					</view>
					<view class="name">
						{{detailsList.systolicPressure?detailsList.systolicPressure:'--'}}
					</view>
				</view>
				<view class="fgx"></view>
			</view>
			<view v-if="fieldList[5].isEnabled">
				<view class="list1">
					<view class="title">
						舒张压
					</view>
					<view class="name">
						{{detailsList.diastolicPressure?detailsList.diastolicPressure:'--'}}
					</view>
				</view>
				<view class="fgx"></view>
			</view>
			<view v-if="fieldList[6].isEnabled">
				<view class="list1">
					<view class="title">
						血糖
					</view>
					<view class="name">
						{{detailsList.bloodSugar?detailsList.bloodSugar:'--'}}
					</view>
				</view>
				<view class="fgx"></view>
			</view>
			<view v-if="fieldList[7].isEnabled">
				<view class="list1">
					<view class="title">
						血压
					</view>
					<view class="name">
						{{detailsList.bloodPressure?detailsList.bloodPressure:'--'}}
					</view>
				</view>
				<view class="fgx"></view>
			</view>
			<view v-if="fieldList[8].isEnabled">
				<view class="list1">
					<view class="title">
						睡眠情况
					</view>
					<view class="name">
						{{detailsList.sleepCondition?detailsList.sleepCondition:'--'}}
					</view>
				</view>
				<view class="fgx"></view>
			</view>
			<view v-if="fieldList[9].isEnabled">
				<view class="list1">
					<view class="title">
						饮食情况
					</view>
					<view class="name">
						{{detailsList.dietCondition?detailsList.dietCondition:'--'}}
					</view>
				</view>
				<view class="fgx"></view>
			</view>
			<view v-if="fieldList[10].isEnabled">
				<view class="list1">
					<view class="title">
						食欲
					</view>
					<view class="name">
						{{detailsList.appetite?detailsList.appetite:'--'}}
					</view>
				</view>
				<view class="fgx"></view>
			</view>
			<view v-if="fieldList[11].isEnabled">
				<view class="list1">
					<view class="title">
						乳房
					</view>
					<view class="name">
						{{detailsList.breastCondition?detailsList.breastCondition:'--'}}
					</view>
				</view>
				<view class="fgx"></view>
			</view>
			<view v-if="fieldList[12].isEnabled">
				<view class="list1">
					<view class="title">
						乳汁
					</view>
					<view class="name">
						{{detailsList.milkCondition?detailsList.milkCondition:'--'}}
					</view>
				</view>
				<view class="fgx"></view>
			</view>
			<view v-if="fieldList[13].isEnabled">
				<view class="list1">
					<view class="title">
						乳头
					</view>
					<view class="name">
						{{detailsList.nippleCondition?detailsList.nippleCondition:'--'}}
					</view>
				</view>
				<view class="fgx"></view>
			</view>
			<view v-if="fieldList[14].isEnabled">
				<view class="list1">
					<view class="title">
						情绪情况
					</view>
					<view class="name">
						{{detailsList.moodCondition?detailsList.moodCondition:'--'}}
					</view>
				</view>
				<view class="fgx"></view>
			</view>
			<view v-if="fieldList[15].isEnabled">
				<view class="list1">
					<view class="title">
						宫体
					</view>
					<view class="name">
						{{detailsList.uterineCondition?detailsList.uterineCondition:'--'}}
					</view>
				</view>
				<view class="fgx"></view>
			</view>
			<view v-if="fieldList[16].isEnabled">
				<view class="list1">
					<view class="title">
						恶露
					</view>
					<view class="name">
						{{detailsList.lochiaCondition?detailsList.lochiaCondition:'--'}}
					</view>
				</view>
				<view class="fgx"></view>
			</view>
			<view v-if="fieldList[17].isEnabled">
				<view class="list1">
					<view class="title">
						颜色
					</view>
					<view class="name">
						{{detailsList.lochiaColor?detailsList.lochiaColor:'--'}}
					</view>
				</view>
				<view class="fgx"></view>
			</view>
			<view v-if="fieldList[18].isEnabled">
				<view class="list1">
					<view class="title">
						腹直肌分离
					</view>
					<view class="name">
						{{detailsList.diastasisRecti?detailsList.diastasisRecti:'--'}}
					</view>
				</view>
				<view class="fgx"></view>
			</view>
			<view v-if="fieldList[19].isEnabled">
				<view class="list1">
					<view class="title">
						伤口(顺/剖)
					</view>
					<view class="name">
						{{detailsList.woundCondition?detailsList.woundCondition:'--'}}
					</view>
				</view>
				<view class="fgx"></view>
			</view>
			<view v-if="fieldList[20].isEnabled">
				<view class="list1">
					<view class="title">
						痔疮
					</view>
					<view class="name">
						{{detailsList.hemorrhoids?detailsList.hemorrhoids:'--'}}
					</view>
				</view>
				<view class="fgx"></view>
			</view>
			<view v-if="fieldList[21].isEnabled">
				<view class="list1">
					<view class="title">
						下肢水肿
					</view>
					<view class="name">
						{{detailsList.lowerLimbEdema?detailsList.lowerLimbEdema:'--'}}
					</view>
				</view>
				<view class="fgx"></view>
			</view>
			<view v-if="fieldList[22].isEnabled">
				<view class="list1">
					<view class="title">
						用药
					</view>
					<view class="name">
						{{detailsList.medication?detailsList.medication:'--'}}
					</view>
				</view>
				<view class="fgx"></view>
			</view>
			<view v-if="fieldList[23].isEnabled">
				<view class="list2">
					<view class="title2">
						备注信息
					</view>
					<view class="name2">
						{{detailsList.notes?detailsList.notes:''}}
					</view>
				</view>
				<view class="fgx"></view>
			</view>
			<view v-if="fieldList[24].isEnabled">
				<view class="list2">
					<view class="title2">
						照片
					</view>
					<view class="chooseImg2">
						<view class="chooseImg" v-for="item,index in detailsList.photoList" :key="index">
							<view class="chooseImg1">
								<image :src="item" mode="" style="width: 100%;height: 100%;border-radius: 8rpx;"></image>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				bgColor: '#ffffff',
				title: "",
				listImg: [1, 2, 3, 4, 5, 6],
				detailsList: '',
				type: 2,
				fieldList: []
			};
		},
		methods: {
			configGetField(id) {
				this.changeLoading(true)
				let data = {
					tableName: 'biz_mother_care'
				}
				this.$axios.get(this.$api.configGetField, data).then((res) => {
					if (res.data.code == 200) {
						this.setData({
							fieldList: res.data.data
						});
					}
				}).finally(() => {
					this.changeLoading(false)
				})
			},
			getGetDetail(id) {
				this.changeLoading(true)
				let data = {
					id: id
				}
				this.$axios.get(this.$api.motherCareGetDetail, data).then((res) => {
					if (res.data.code == 200) {
						this.setData({
							detailsList: res.data.data
						});
					}
				}).finally(() => {
					this.changeLoading(false)
				})
			}
		},
		/**
		 * 生命周期函数--监听页面加载
		 */
		onLoad: function(options) {
			this.title = options.title
			this.getGetDetail(options.id)
			this.configGetField()
		},
		/**
		 * 生命周期函数--监听页面初次渲染完成
		 */
		onReady: function() {},
		/**
		 * 生命周期函数--监听页面显示
		 */
		onShow: function() {},
		/**
		 * 生命周期函数--监听页面隐藏
		 */
		onHide: function() {},
		/**
		 * 生命周期函数--监听页面卸载
		 */
		onUnload: function() {},
		/**
		 * 页面相关事件处理函数--监听用户下拉动作
		 */
		onPullDownRefresh: function() {},
		/**
		 * 页面上拉触底事件的处理函数
		 */
		onReachBottom: function() {},
		/**
		 * 用户点击右上角分享
		 */
		onShareAppMessage() {},

		onShareTimeline() {},
	}
</script>

<style lang="less" scoped>
	@import './nursedetails.less';
</style>