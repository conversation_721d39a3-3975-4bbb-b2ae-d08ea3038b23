<template>
	<navigation :background="bgColor" title="服务人员" :isSowArrow="true"></navigation>
	<view class="main">
		<view class="blacks">
		</view>
		<view class="content">
			<view class="headimg">
				<view class="contentImg1" @click="uploadImage" v-if="!from.photos">
					<image src="http://cdn.xiaodingdang1.com/2025/03/18/42edb68fed65491aa987c04884573b06.png" mode=""
						class="contentImg1_1" />
					<image src="http://cdn.xiaodingdang1.com/2025/03/18/ec8f435dabba4ec59a30783958ac57a6.png" mode=""
						class="contentImg1_2" />
				</view>
				<view class="contentImg1" @click="uploadImage" v-if="from.photos">
					<image :src="from.photos" mode="" class="contentImg1_1" />
				</view>
				<view>
					<image src="http://cdn.xiaodingdang1.com/2025/03/18/5de159a654ee428e95ceee35e2f47d26.png" mode=""
						class="contentImg1_3" />
				</view>
			</view>
			<view class="basics">
				<view class="basics1">
					<view class="basics1_1">
						姓名
					</view>
					<view class="basics1_2">
						<input type="text" placeholder="请填写姓名" :value="from.name" @input='bindInput2' />
					</view>
				</view>
				<view class="fgx"></view>
				<view class="basics1">
					<view class="basics1_1">
						手机
					</view>
					<view class="basics1_2">
						<input type="text" placeholder="请输入手机号码" :value="from.phone" @input='bindInput3' />
					</view>
				</view>
				<view class="fgx"></view>
				<u-form :model="form" ref="uform" class="publish-form">
					<u-form-item label="角色" label-width="190" prop="roleCode">
						<u-input placeholder="请选择角色" v-model="roleName" type="select" @click="openSelect(1)" />
					</u-form-item>
					<u-form-item label="职业" label-width="190" prop="userId">
						<u-input placeholder="请选择职业" v-model="from.staffPost" type="select" @click="openSelect(2)" />
					</u-form-item>
				</u-form>
				<u-select v-model="selectShow" :list="selectList" @confirm="onSelected"></u-select>
				<view class="basics1">
					<view class="basics1_1">
						从业时间
					</view>
					<view class="basics1_2">
						<!-- <input type="text" placeholder="请填写从业年数" :value="from.yearsEmployment" @input='bindInput4' /> -->
						<picker mode="date" @change="onDateChange" :value="time" fields="year" style="width: 100%;">
							<view class="date-picker">{{from.practiceTime?from.practiceTime:"请选择"}}</view>
						</picker>
						<u-icon name="arrow-down-fill" color="#c0c4cc"></u-icon>
					</view>
				</view>
				<view class="fgx"></view>
				<view class="basics1">
					<view class="basics1_1">
						服务人员
					</view>
					<view class="basics1_2">
						<input type="number" placeholder="请填写服务人员" :value="from.serviceNum" @input='bindInput5' />
					</view>
				</view>
			</view>
			<view class="basics">
				<view class="title">
					<view class="title1">
						标签
					</view>
					<view class="title2">
						宝妈小叮当客户端服务人员标签
					</view>
					<view class="title2" style="color: red;">
						例如:十年经验、科学护理
					</view>

				</view>
				<view class="tagAbbs">
					<view v-for="(item, index) in tagList" :key="index" class="tag">
						<view class="tags">
							<view>
								{{item}}
							</view>
							<view @tap="del(index)">
								<image
									src="http://cdn.xiaodingdang1.com/2025/03/18/7494940a2de04eaf879ff03ca723bef8.png"
									mode="" class="delimg" />
							</view>
						</view>
					</view>
					<view class="tagAbbInp" v-if="tagAbbShow">
						<input type="text" placeholder="请输入" :value="tagTitle" @input='bindInput1' />
					</view>
					<view class="tagAbb" @tap="tagAbb" v-if="!tagAbbShow">
						+ 添加标签
					</view>
					<view class="tagAbb" @tap="confirm" v-if="tagAbbShow">
						确定添加
					</view>
				</view>
				<view class="title1">
					个人简介
				</view>
				<view class="textarea">
					<textarea placeholder="请输入个人简介信息" :value="from.description" @input="searchInput" />
				</view>
			</view>
		</view>
		<view class="black">
		</view>
	</view>

	<view class="bootBtn">
		<view class="bootBtn1" @tap="add">
			保存
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				bgColor: '',
				from: {
					name: '',
					phone: '',
					roleCode: '',
					staffPost: '',
					description: '',
					practiceTime: '',
					serviceNum: '',
					tag: [],
					photos: '',
					momId: ''
				},
				roleList:[
					  {
					        label:'护理',
					        value:'NURSE'
					      },
					      {
					        label:'产康',
					        value:'POSTPARTUM'
					      },
					      {
					        label:'厨师',
					        value:'CHEF'
					      }
				],
				staffPostList: [{
						label: '孕产厨师',
						value: '孕产厨师'
					},
					{
						label: '孕产医师',
						value: '孕产医师'
					},
					{
						label: '孕产护士',
						value: '孕产护士'
					},
					{
						label: '孕产月嫂',
						value: '孕产月嫂'
					},
					{
						label: '孕产育婴员',
						value: '孕产育婴员'
					},
					{
						label: '孕产心理咨询师',
						value: '孕产心理咨询师'
					},
					{
						label: '孕产产后康复师',
						value: '孕产产后康复师'
					},
					{
						label: '孕产健康管理师',
						value: '孕产健康管理师'
					},
					{
						label: '孕产母婴护理师',
						value: '孕产母婴护理师'
					},
					{
						label: '母婴顾问',
						value: '母婴顾问'
					},
				],
				selectShow: false,
				label: {
					roleCode: '',
					userId: '',
				},
				rules: {
					roleCode: [{
						required: true,
						message: '请选择角色',
						trigger: ['change', 'blur']
					}],
					userId: [{
						required: true,
						message: '请选择职业',
						trigger: ['change', 'blur']
					}],
				},
				selectList: [],
				selectType: '',
				tagTitle: '',
				tagList: [],
				tagAbbShow: false,
				roleCode: 'NURSE',
				roleName: '',
			}
		},
		onLoad(options) {
			this.from.momId = options.momId
			// this.getMom()
		},
		onPageScroll(e) {
			if (e.scrollTop > 0) {
				this.bgColor = '#ffffff'
			} else if (e.scrollTop < 2) {
				this.bgColor = ''
			}
		},
		methods: {
			onDateChange(e){
				this.from.practiceTime=e.detail.value
			},
			async uploadImage() {
				var that = this;
				var photos = that.from.photos;
				uni.chooseImage({
					// count: 1, // 默认9
					sizeType: ['compressed'],
					// 可以指定是原图还是压缩图，默认二者都有
					sourceType: ['album', 'camera'],
					// 可以指定来源是相册还是相机，默认二者都有
					success: function(res) {
						var tempFilePaths = res.tempFilePaths;
						var photos = that.from.photos;
						let picNum = tempFilePaths.length;
						uni.showLoading({
							title: '图片上传中'
						});

						uni.uploadFile({
							filePath: tempFilePaths[0],
							name: 'file',
							url: that.$api.uploadOSS,
							formData: {},
							header: {
								'Content-Type': 'multipart/form-data',
								Authorization: 'Bearer ' + uni.getStorageSync('token'),
								'Accept-Encoding': 'gzip',
								Clientid: '428a8310cd442757ae699df5d894f051'
							},
							success: function(rests) {
								let data = JSON.parse(rests.data);
								if (data.code == 200) {
									console.log(data.data.url);
									that.from.photos = data.data.url

								}
								uni.hideLoading();
							}
						});
					}

				});
			},
			clickAlbumImg(value) {
				console.log('clickAlbumImg', value)
				// value: [{type: '',url: ''}]
				let imgUrl = []
				let videos = []
				if (value && value.length > 0) {
					value.forEach((item) => {
						item.type == 'video' ? videos.push(item.url) : imgUrl.push(item.url)
					})
					this.form.imgs = imgUrl
					this.form.videos = videos
				} else {
					this.form.imgs = []
					this.form.videos = []
				}

			},
			//提交
			add() {
				this.publish()
			},
			async publish() {
				if (this.from.name == '') {
					uni.showToast({
						title: '请填写姓名',
						icon: 'none',
						duration: 3000 //持续的时间
					});
					return;
				}
				if (this.from.phone == '') {
					uni.showToast({
						title: '请填写手机号',
						icon: 'none',
						duration: 3000 //持续的时间
					});
					return;
				}
				if (this.from.roleCode == '') {
					uni.showToast({
						title: '请选择角色',
						icon: 'none',
						duration: 3000 //持续的时间
					});
					return;
				}
				if (this.from.staffPost == '') {
					uni.showToast({
						title: '请选择职业',
						icon: 'none',
						duration: 3000 //持续的时间
					});
					return;
				}
				if (this.from.practiceTime == '') {
					uni.showToast({
						title: '请填写从业年限',
						icon: 'none',
						duration: 3000 //持续的时间
					});
					return;
				}
				if (this.from.serviceNum == '') {
					uni.showToast({
						title: '请填写服务人员',
						icon: 'none',
						duration: 3000 //持续的时间
					});
					return;
				}
				this.from.tag = this.tagList
				this.from.practiceTime=this.from.practiceTime+'-01-01'
				this.changeLoading(true)
				const res = await this.$axios.post(this.$api.employeeAdd, this.from)
				if (res?.data?.code == 200) {
					uni.$emit('publishon')
					uni.navigateBack({
						delta: 1
					});
				}else{
					this.from.practiceTime=this.from.practiceTime.substring(0, 3);
					console.log(this.from.practiceTime.substring(0, 3));
					uni.showToast({
						title: res.data.msg, 
						icon: 'none',
						duration: 3000 //持续的时间
					});
				}
			},
			onSelected(options) {
				switch (this.selectType) {
					case 1:
						this.from.roleCode = options[0].value;
						this.roleName = options[0].label;
						break;
					case 2:
						this.from.staffPost = options[0].label;
						break;
					default:
						break;
				}
			},
			bindInput2(e) {
				this.from.name = e.detail.value
			},
			bindInput3(e) {
				this.from.phone = e.detail.value
			},
			bindInput4(e) {
				this.from.yearsEmployment = e.detail.value
			},
			bindInput5(e) {
				this.from.serviceNum = e.detail.value
			},
			searchInput(e) {
				this.from.description = e.detail.value
			},
			// 根据角色获取姓名
			// async getMom() {
			// 	const res = await this.$axios.get(this.$api.getRoleList)
			// 	const data = res?.data?.data
			// 	if (data) {
			// 		this.roleList = []
			// 		data.forEach((item) => {
			// 			this.roleList.push({
			// 				label: item.name,
			// 				value: item.code
			// 			})
			// 		})
			// 	}
			// },
			tagAbb() {
				this.tagAbbShow = !this.tagAbbShow
			},
			confirm() {
				if (this.tagTitle == '') {
					uni.showToast({
						title: '请填写标签',
						icon: 'none',
						duration: 2000 //持续的时间
					});
					return
				}
				let tagList = this.tagList
				tagList.push(this.tagTitle)
				this.tagAbbShow = !this.tagAbbShow,
					this.tagList = tagList
				this.tagTitle = ''
			},
			bindInput1(e) {
				console.log(e);
				let value = e.detail.value
				this.tagTitle = value
			},
			del(index) {
				let tagList = this.tagList
				tagList.splice(index, 1)
				this.tagList = tagList
			},
			openSelect(type) {
				this.selectList = []
				this.selectType = type
				switch (this.selectType) {
					case 1:
						this.selectList = this.roleList
						break;
					case 2:
						this.selectList = this.staffPostList
						break;
					default:
						break;
				}
				this.selectShow = true
			},
		}


	}
</script>

<style lang="less" scoped>
	@import './servicestafflist.less';
</style>