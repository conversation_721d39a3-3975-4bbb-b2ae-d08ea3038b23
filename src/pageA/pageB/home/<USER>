<template>
  <navigation title="产后康复" :background="bgColor"></navigation>
  <view class="main">
    <view class="bg">
      <view class="content">
        <scroll-view class="top-container" scroll-x="true">
          <view
            :class="['top', topStyle]"
            v-for="(item, index) in pageDataList"
            :key="index"
            @tap.self="postpartumNext(item.projectId)"
          >
		  	<card :list="item" ></card>
          </view>
        </scroll-view>
      </view>
      <view class="invite">
        <view class="invite-title">
          <view class="invite-title-dot"></view>
          产后康复
        </view>
        <scroll-view class="invite-card" scroll-y="true">
          <view
            class="invite-card-list"
            v-for="(item, index) in pageList"
            :key="index"
            @tap="postpartumNext(item.projectId)"
          >
            <card styleType="list" :list="item" :showPrice="false"></card>
          </view>
        </scroll-view>
      </view>
    </view>
  </view>
  <mask-dialog></mask-dialog>
  <rightMenuBarVue :scrollTop="scrollTop"></rightMenuBarVue>
</template>
<script setup>
	import rightMenuBarVue from "@/components/rightMenuBar.vue";
import card from "./component/card.vue";
import {
  onShow,
  onHide,
  onLoad,
  onPageScroll,
  onShareAppMessage,
  onShareTimeline,
} from "@dcloudio/uni-app";
import { ref, reactive, getCurrentInstance } from "vue";
const instance = getCurrentInstance();
const App = instance.appContext.config.globalProperties;
onShareAppMessage(() => {});
onShareTimeline(() => {});
onShow(() => {
  let newDateTime = Date.parse(new Date());
  let pointStart = {
    eventType: "PAGE_VIEW",
    pageUrl: "pageA/pageB/home/<USER>",
    module: "recovery",
    eventTime: newDateTime,
    pageTitle: "产后康复",
  };
  let iftoken = uni.getStorageSync("token");
  if (iftoken) {
    App.$point.basePoint(pointStart);
  }
});
onHide(() => {
  let newDateTime = Date.parse(new Date());
  let pointEnd = {
    eventId: uni.getStorageSync("eventId"),
    leaveTime: newDateTime,
  };
  App.$point.reportEnd(pointEnd);
});
const bgColor = ref("");
const scrollTop = ref("");
onPageScroll((e) => {
  if (e.scrollTop > 0) {
    bgColor.value = "#ffffff";
  } else if (e.scrollTop < 2) {
    bgColor.value = "";
  }
   scrollTop.value=e.scrollTop
});
console.log("instance", instance, App);
const suiteList = reactive([]);
const pageList = reactive([]);
const pageDataList=reactive([]);
const topStyle = ref("top2");
onLoad((options) => {
  pageInit();
});
const pageInit = async () => {
  try {
    const result = await App.$axios.get(App.$api.recoveryRecommendation);
    if (result?.data?.code == 200) {
      let res = result?.data?.data || [];
      res.forEach((item) => {
        if (item.price) {
          let price1 = item.price.slice(0, 1);
          let price2 = item.price.slice(2, 10);
          item.prices = price1 + "*" + price2;
        }
      });
      topStyle.value = res.length > 1 ? "top2" : "top1";
      Object.assign(suiteList, res);
    }
    const data = {
      pageSize: 1000,
      pageNum: 1,
    };
    const pageListData = await App.$axios.get(
      App.$api.postpartumRecoveryPage,
      data
    );
    if (pageListData.data.code == 200) {
		let dataList=[]
		pageListData.data.rows.forEach(item=>{
			if(item.onHomepage){
				console.log('测试1111');
				dataList.push(item)
			}
		})
      Object.assign(pageList, pageListData.data.rows);
	  Object.assign(pageDataList, dataList);
    }
  } catch {}
};
const postpartumNext = (projectId) => {
  App.$jumpPage("invite/newdetail", {
    projectId,
  });
};
</script>
<style lang="less" scoped>
@import "./invite.less";
</style>
