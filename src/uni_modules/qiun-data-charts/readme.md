![logo](https://img-blog.csdnimg.cn/4a276226973841468c1be356f8d9438b.png)


[![star](https://gitee.com/uCharts/uCharts/badge/star.svg?theme=gvp)](https://gitee.com/uCharts/uCharts/stargazers)
[![fork](https://gitee.com/uCharts/uCharts/badge/fork.svg?theme=gvp)](https://gitee.com/uCharts/uCharts/members)
[![License](https://img.shields.io/badge/license-Apache%202-4EB1BA.svg)](https://www.apache.org/licenses/LICENSE-2.0.html)
[![npm package](https://img.shields.io/npm/v/@qiun/ucharts.svg?style=flat-square)](https://www.npmjs.com/~qiun)


## uCharts简介

`uCharts`是一款基于`canvas API`开发的适用于所有前端应用的图表库，开发者编写一套代码，可运行到 Web、iOS、Android（基于 uni-app / taro ）、以及各种小程序（微信/支付宝/百度/头条/飞书/QQ/快手/钉钉/淘宝）、快应用等更多支持 canvas API 的平台。

## 官方网站

## [https://www.ucharts.cn](https://www.ucharts.cn)

## 快速体验

一套代码编到多个平台，依次扫描二维码，亲自体验uCharts图表跨平台效果！其他平台请自行编译。

![](https://www.ucharts.cn/images/web/guide/qrcode20220224.png)

![](https://img-blog.csdnimg.cn/7d0115593ff24ac39a224fb7c6ed72a4.png)

## 致开发者

感谢各位开发者`五年`来对秋云及uCharts的支持，uCharts的进步离不开各位开发者的鼓励与贡献。为更好的帮助各位开发者使用图表工具，我们推出了新版官网，增加了在线定制、问答社区、在线配置等一些增值服务，为确保您能更好的应用图表组件，建议您先`仔细阅读官网指南`以及`常见问题`，而不是下载下来`直接使用`。如仍然不能解决，请到`官网社区`或开通会员后加入`专属VIP会员群`提问将会很快得到回答。

## 视频教程

## [uCharts新手入门教程](https://www.bilibili.com/video/BV1qA411Q7se/?share_source=copy_web&vd_source=42a1242f9aaade6427736af69eb2e1d9)


## 社群支持

uCharts官方拥有5个2000人的QQ群及专属VIP会员群支持，庞大的用户量证明我们一直在努力，请各位放心使用！uCharts的开源图表组件的开发，团队付出了大量的时间与精力，经过四来的考验，不会有比较明显的bug，请各位放心使用。如果您有更好的想法，可以在`码云提交Pull Requests`以帮助更多开发者完成需求，再次感谢各位对uCharts的鼓励与支持！

#### 官方交流群
- 交流群1：371774600（已满）
- 交流群2：619841586（已满）
- 交流群3：955340127（已满）
- 交流群4：641669795（已满）
- 交流群5：236294809（只能扫码加入）

![](https://www.ucharts.cn/images/web/qq5.jpg)

- 口令`uniapp`

#### 专属VIP会员群
- 开通会员后详见【账号详情】页面中顶部的滚动通知
- 口令`您的用户ID`

## 版权信息

uCharts始终坚持开源，遵循 [Apache Licence 2.0](https://www.apache.org/licenses/LICENSE-2.0.html) 开源协议，意味着您无需支付任何费用，即可将uCharts应用到您的产品中。

注意：这并不意味着您可以将uCharts应用到非法的领域，比如涉及赌博，暴力等方面。如因此产生纠纷或法律问题，uCharts相关方及秋云科技不承担任何责任。

## 合作伙伴

[![DIY官网](https://www.ucharts.cn/images/web/guide/links/diy-gw.png)](https://www.diygw.com/)
[![HasChat](https://www.ucharts.cn/images/web/guide/links/haschat.png)](https://gitee.com/howcode/has-chat)
[![uViewUI](https://www.ucharts.cn/images/web/guide/links/uView.png)](https://www.uviewui.com/)
[![图鸟UI](https://www.ucharts.cn/images/web/guide/links/tuniao.png)](https://ext.dcloud.net.cn/plugin?id=7088)
[![thorui](https://www.ucharts.cn/images/web/guide/links/thorui.png)](https://ext.dcloud.net.cn/publisher?id=202)
[![FirstUI](https://www.ucharts.cn/images/web/guide/links/first.png)](https://www.firstui.cn/)
[![nProUI](https://www.ucharts.cn/images/web/guide/links/nPro.png)](https://ext.dcloud.net.cn/plugin?id=5169)
[![GraceUI](https://www.ucharts.cn/images/web/guide/links/grace.png)](https://www.graceui.com/)


## 更新记录

详见官网指南中说明，[点击此处查看](https://www.ucharts.cn/v2/#/guide/index?id=100)


## 相关链接
- [uCharts官网](https://www.ucharts.cn)
- [DCloud插件市场地址](https://ext.dcloud.net.cn/plugin?id=271)
- [uCharts码云开源托管地址](https://gitee.com/uCharts/uCharts) [![star](https://gitee.com/uCharts/uCharts/badge/star.svg?theme=gvp)](https://gitee.com/uCharts/uCharts/stargazers)
- [uCharts npm开源地址](https://www.ucharts.cn)
- [ECharts官网](https://echarts.apache.org/zh/index.html)
- [ECharts配置手册](https://echarts.apache.org/zh/option.html)
- [图表组件在项目中的应用 ReportPlus数据报表](https://www.ucharts.cn/v2/#/layout/info?id=1) 