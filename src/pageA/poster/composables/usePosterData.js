import { ref, getCurrentInstance } from 'vue';

/**
 * 海报数据管理 Composable
 * 处理不同类型海报的数据获取和处理
 */
export function usePosterData() {
  const instance = getCurrentInstance();
  const isLoading = ref(false);
  const dataError = ref(null);

  // 基础数据
  const clubName = ref(uni.getStorageSync('clubName'));
  const clubLogo = ref(uni.getStorageSync('clubInfo')?.logo);
  const defaultLogan = ref('专业呵护新起点');
  const str1 = ref('不用来回体验');
  const str2 = ref('在线上就能查看合适再选择');

  // 海报数据
  const posterData = ref({});
  const qrCode = ref('');

  /**
   * 获取视频首帧
   * @param {string} videoUrl - 视频URL
   * @returns {Promise<string>} 返回首帧图片URL
   */
  const getVideoFirstFrame = async (videoUrl) => {
    if (!videoUrl) {
      throw new Error('视频URL不能为空');
    }

    try {
      const data = { videoUrl };
      const res = await instance.proxy.$axios.get(
        instance.proxy.$api.videoFirstFrame,
        data,
      );

      if (res.data.code === 200) {
        const frameUrl = res.data.data.frameUrl;
        console.log('获取视频首帧成功:', frameUrl);
        return frameUrl;
      } else {
        throw new Error(res.data.msg || '获取视频首帧失败');
      }
    } catch (error) {
      console.error('获取视频首帧失败:', error);
      uni.showToast({
        title: error.message || '获取视频首帧失败',
        icon: 'none',
        duration: 3000,
      });
      throw error;
    }
  };

  /**
   * 获取动态信息（用于动态海报）
   * @param {string} postId - 动态ID
   * @returns {Promise<Object>} 返回动态数据
   */
  const getFeedPostInfo = async (postId) => {
    if (!postId) {
      throw new Error('动态ID不能为空');
    }

    isLoading.value = true;
    dataError.value = null;

    try {
      const data = { postId };
      const res = await instance.proxy.$axios.get(
        instance.proxy.$api.getClubInfo,
        data,
      );

      if (res.data.code === 200) {
        const feedData = res.data.data;

        // 处理视频首帧
        if (feedData?.videos?.length) {
          const videoUrl = feedData.videos[0];
          const frameUrl = await getVideoFirstFrame(videoUrl);
          feedData.contentPhotos = [frameUrl];
        }

        posterData.value = feedData;
        console.log('获取动态信息成功:', feedData);
        return feedData;
      } else {
        throw new Error(res.data.msg || '获取动态信息失败');
      }
    } catch (error) {
      console.error('获取动态信息失败:', error);
      dataError.value = error.message;
      throw error;
    } finally {
      isLoading.value = false;
    }
  };

  /**
   * 获取发送信息（用于发送海报）
   * @param {string} postId - 动态ID
   * @returns {Promise<Object>} 返回发送数据
   */
  const getSendingPostInfo = async (postId) => {
    if (!postId) {
      throw new Error('动态ID不能为空');
    }

    isLoading.value = true;
    dataError.value = null;

    try {
      const data = { postId };
      const res = await instance.proxy.$axios.get(
        instance.proxy.$api.feedPostInfo,
        data,
      );

      if (res.data.code === 200) {
        const sendingData = res.data.data;

        // 处理视频首帧
        if (sendingData?.videos?.length) {
          const videoUrl = sendingData.videos[0];
          const frameUrl = await getVideoFirstFrame(videoUrl);
          sendingData.contentPhotos = [frameUrl];
        }

        posterData.value = sendingData;
        console.log('获取发送信息成功:', sendingData);
        return sendingData;
      } else {
        throw new Error(res.data.msg || '获取发送信息失败');
      }
    } catch (error) {
      console.error('获取发送信息失败:', error);
      dataError.value = error.message;
      throw error;
    } finally {
      isLoading.value = false;
    }
  };

  /**
   * 获取二维码
   * @param {Object} params - 二维码参数
   * @param {string} params.pathUrl - 页面路径
   * @param {string} params.uuid - 唯一标识
   * @param {string} params.diaryId - 日记ID（可选）
   * @returns {Promise<string>} 返回二维码URL
   */
  const getQrCode = async (params) => {
    const { pathUrl, uuid, diaryId = '' } = params;

    if (!pathUrl || !uuid) {
      throw new Error('二维码参数不完整');
    }

    try {
      const res = await instance.proxy.$axios.get(
        instance.proxy.$api.getQrCode,
        {
          pathUrl,
          uuid,
          diaryId,
        },
      );

      console.log('获取二维码响应:', res.data);

      if (res.data.code === 200) {
        const qrCodeUrl = res.data.msg;
        qrCode.value = qrCodeUrl;
        console.log('获取二维码成功:', qrCodeUrl);
        return qrCodeUrl;
      } else {
        throw new Error(res.data.msg || '获取二维码失败');
      }
    } catch (error) {
      console.error('获取二维码失败:', error);
      throw error;
    }
  };

  /**
   * 获取员工详情信息
   * @param {string} postId - 动态ID
   * @returns {Promise<Object>} 返回员工数据
   */
  const getStaffInfo = async (postId) => {
    if (!postId) {
      throw new Error('动态ID不能为空');
    }

    isLoading.value = true;
    dataError.value = null;

    try {
      const data = { postId };
      const res = await instance.proxy.$axios.get(
        instance.proxy.$api.feedPostInfo,
        data,
      );

      if (res.data.code === 200) {
        const staffData = res.data.data;

        // 处理视频首帧
        if (staffData?.videos?.length) {
          const videoUrl = staffData.videos[0];
          const frameUrl = await getVideoFirstFrame(videoUrl);
          staffData.contentPhotos = [frameUrl];
        }

        posterData.value = staffData;
        console.log('获取员工信息成功:', staffData);
        return staffData;
      } else {
        throw new Error(res.data.msg || '获取员工信息失败');
      }
    } catch (error) {
      console.error('获取员工信息失败:', error);
      dataError.value = error.message;
      throw error;
    } finally {
      isLoading.value = false;
    }
  };

  /**
   * 获取创建信息
   * @param {string} diaryId - 日记ID
   * @returns {Promise<Object>} 返回创建数据
   */
  const getCreateInfo = async (diaryId) => {
    if (!diaryId) {
      throw new Error('日记ID不能为空');
    }

    isLoading.value = true;
    dataError.value = null;

    try {
      const res = await instance.proxy.$axios.get(
        instance.proxy.$api.getDiaryDetail,
        { diaryId },
      );

      if (res.data.code === 200) {
        const createData = res.data.data;

        // 处理服务开始日期
        if (createData?.serviceStartDate) {
          createData.serviceStartDate = createData.serviceStartDate.slice(
            0,
            10,
          );
        }

        // 处理评论数量
        createData.count = createData?.userCount
          ? `该用户还有${createData.userCount}条精彩评论`
          : '该用户还有1条精彩评论';

        // 处理海报标语
        if (createData.chefPosterSlogan) {
          const arr = createData.chefPosterSlogan.split('，');
          str1.value = arr[0] || '不用来回体验';
          str2.value = arr[1] || '在线上就能查看合适再选择';
        }

        posterData.value = createData;
        console.log('获取创建信息成功:', createData);
        return createData;
      } else {
        throw new Error(res.data.msg || '获取创建信息失败');
      }
    } catch (error) {
      console.error('获取创建信息失败:', error);
      dataError.value = error.message;
      throw error;
    } finally {
      isLoading.value = false;
    }
  };

  /**
   * 处理文本分行
   * @param {string} str - 原始文本
   * @param {number} length - 每行长度
   * @returns {Array<string>} 分行后的文本数组
   */
  const handlerText = (str, length) => {
    const result = [];
    for (let i = 0; i < str.length; i += length) {
      result.push(str.slice(i, i + length));
    }
    return result;
  };

  /**
   * 初始化动态海报数据
   * @param {Object} options - 初始化选项
   * @returns {Promise<void>}
   */
  const initDynamicsPosterData = async (options) => {
    const { postId } = options;

    try {
      // 并行获取数据
      const promises = [
        getFeedPostInfo(postId),
        getQrCode({
          pathUrl: 'pageA/pageB/dynamics/dynamicsdetail',
          uuid: postId,
          diaryId: postId,
        }),
      ];

      await Promise.all(promises);
      console.log('动态海报数据初始化完成');
    } catch (error) {
      console.error('动态海报数据初始化失败:', error);
      throw error;
    }
  };

  /**
   * 初始化员工海报数据
   * @param {Object} options - 初始化选项
   * @returns {Promise<void>}
   */
  const initStaffPosterData = async (options) => {
    const { postId } = options;

    try {
      // 并行获取数据
      const promises = [
        getStaffInfo(postId),
        getQrCode({
          pathUrl: 'pageA/pageB/community/staffdetail',
          uuid: postId,
          diaryId: postId,
        }),
      ];

      await Promise.all(promises);
      console.log('员工海报数据初始化完成');
    } catch (error) {
      console.error('员工海报数据初始化失败:', error);
      throw error;
    }
  };

  /**
   * 初始化创建海报数据
   * @param {Object} options - 初始化选项
   * @returns {Promise<void>}
   */
  const initCreatePosterData = async (options) => {
    const { postId, uuid } = options;

    try {
      // 并行获取数据
      const promises = [
        getCreateInfo(postId),
        getQrCode({
          pathUrl: 'pageA/pageB/community/note/notedetail',
          uuid,
          diaryId: '',
        }),
      ];

      await Promise.all(promises);
      console.log('创建海报数据初始化完成');
    } catch (error) {
      console.error('创建海报数据初始化失败:', error);
      throw error;
    }
  };

  /**
   * 初始化发送海报数据
   * @param {Object} options - 初始化选项
   * @returns {Promise<void>}
   */
  const initSendingPosterData = async (options) => {
    const { postId } = options;

    try {
      // 并行获取数据
      const promises = [
        getSendingPostInfo(postId),
        getQrCode({
          pathUrl: 'pageA/pageB/community/sending',
          uuid: postId,
          diaryId: postId,
        }),
      ];

      await Promise.all(promises);
      console.log('发送海报数据初始化完成');
    } catch (error) {
      console.error('发送海报数据初始化失败:', error);
      throw error;
    }
  };

  /**
   * 通用数据初始化方法
   * @param {string} type - 海报类型
   * @param {Object} options - 初始化选项
   * @returns {Promise<void>}
   */
  const initPosterData = async (type, options) => {
    console.log('options', options);
    try {
      switch (type) {
        case 'dynamics':
          return await initDynamicsPosterData(options);
        case 'staff':
          return await initStaffPosterData(options);
        case 'create':
          return await initCreatePosterData(options);
        case 'sending':
          return await initSendingPosterData(options);
        default:
          throw new Error(`不支持的海报类型: ${type}`);
      }
    } catch (error) {
      console.error('海报数据初始化失败:', error);
      throw error;
    }
  };

  return {
    // 状态
    isLoading,
    dataError,

    // 数据
    clubName,
    clubLogo,
    defaultLogan,
    str1,
    str2,
    posterData,
    qrCode,

    // 方法
    initPosterData,
    getFeedPostInfo,
    getSendingPostInfo,
    getStaffInfo,
    getCreateInfo,
    getQrCode,
    getVideoFirstFrame,
    handlerText,
  };
}
