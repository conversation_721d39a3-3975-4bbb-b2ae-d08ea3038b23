<template>
	<view class="main">
		<view class="coment">
			<view class="titles">
				基础信息
			</view>
			<view class="fgx"></view>
			<view class="list1">
				<view class="title">
					宝妈姓名
				</view>
				<view class="name">
					{{detailsList.momName?detailsList.momName:'--'}}
				</view>
			</view>
			<view class="fgx"></view>
			<view class="list1" @click="selectShow = true">
				<view class="title">
					宝宝姓名
				</view>
				<view class="name">
					<view class="name_1">
						<view>
							{{detailsList.babyName?detailsList.babyName:'--'}}
						</view>
						<u-icon name="arrow-down-fill" style="margin-left: 20rpx;"></u-icon>
					</view>
				</view>
			</view>
			<view class="fgx"></view>
			<view class="list1">
				<view class="title">
					宝宝性别
				</view>
				<view class="name">
					{{detailsList.gender?detailsList.gender:'--'}}
				</view>
			</view>
			<view class="fgx"></view>
			<view class="list1">
				<view class="title">
					出生身高
				</view>
				<view class="name">
					{{detailsList.birthHeight?detailsList.birthHeight:"--"}}
				</view>
			</view>
			<view class="fgx"></view>
			<view class="list1">
				<view class="title">
					出生体重
				</view>
				<view class="name">
					{{detailsList.birthWeight?detailsList.birthWeight:'--'}}
				</view>
			</view>
			<view class="fgx"></view>
			<view class="list1">
				<view class="title">
					出生日期
				</view>
				<view class="name">
					{{detailsList.birthDate?detailsList.birthDate:'--'}}
				</view>
			</view>
			<view class="fgx"></view>
			<view class="list1">
				<view class="title">
					分娩医院
				</view>
				<view class="name">
					{{detailsList.deliveryHospital?detailsList.deliveryHospital:'--'}}
				</view>
			</view>
		</view>
		<view class="coment">
			<view class="titles">
				选择日期
			</view>
			<view class="timeinput">
				<picker mode="date" @change="onDateChange" :value="time" fields="month" style="width: 100%;">
					<view class="date-picker">{{time?time:"请选择日期"}}</view>
				</picker>
				<u-icon name="arrow-down-fill"></u-icon>
			</view>
		</view>
		<view class="coment">
			<view class="charts-box">
				<view class="titles">
					宝宝体重(kg)
				</view>
				<qiun-data-charts type="line" :opts="opts" :chartData="chartData" />
			</view>
			<view class="charts-box">
				<view class="titles">
					宝宝身高(cm)
				</view>
				<qiun-data-charts type="line" :opts="opts" :chartData="chartData1" />
			</view>
			<view class="charts-box">
				<view class="titles">
					宝宝体温(°C)
				</view>
				<qiun-data-charts type="line" :opts="opts" :chartData="chartData2" />
			</view>
			<view class="charts-box">
				<view class="titles">
					宝宝黄疸(md/dl)
				</view>
				<qiun-data-charts type="line" :opts="opts" :chartData="chartData3" />
			</view>
		</view>
		<u-select v-model="selectShow" :list="selectList" @confirm="onSelected"></u-select>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				babyId:'',
				timeList: [],
				amountLists: [],
				amountLists1: [],
				amountLists2: [],
				amountLists3: [],
				selectShow: false,
				time: '',
				title: "",
				listImg: [1, 2, 3, 4, 5, 6],
				detailsList: '',
				type: 2,
				selectList: [],
				chartData: {},
				chartData1: {},
				chartData2: {},
				chartData3: {},
				opts: {
					color: ["#1890FF", "#91CB74", "#FAC858", "#EE6666", "#73C0DE", "#3CA272", "#FC8452", "#9A60B4",
						"#ea7ccc"
					],
					padding: [15, 10, 0, 15],
					enableScroll: false,
					legend: {},
					xAxis: {
						disableGrid: true,
					},
					yAxis: {
						gridType: "dash",
						dashLength: 2
					},
					extra: {
						line: {
							type: "curve",
							width: 2,
							activeType: "hollow"
						}
					},
				}
			};
		},
		methods: {
			getServerData() {
				//模拟从服务器获取数据时的延时
				setTimeout(() => {
					let res = {
						categories: this.timeList,
						series: [{
							name: "宝宝体重",
							data: this.amountLists
						}, ]
					};
					this.chartData = JSON.parse(JSON.stringify(res));
				}, 500);
			},
			getServerData1() {
				//模拟从服务器获取数据时的延时
				setTimeout(() => {
					let res = {
						categories: this.timeList,
						series: [{
							name: "宝宝身高",
							data: this.amountLists1
						}, ]
					};
					this.chartData1 = JSON.parse(JSON.stringify(res));
				}, 500);
			},
			getServerData2() {
				//模拟从服务器获取数据时的延时
				setTimeout(() => {
					let res = {
						categories: this.timeList,
						series: [{
							name: "宝宝体温",
							data: this.amountLists2
						}, ]
					};
					this.chartData2 = JSON.parse(JSON.stringify(res));
				}, 500);
			},
			getServerData3() {
				//模拟从服务器获取数据时的延时
				setTimeout(() => {
					let res = {
						categories: this.timeList,
						series: [{
							name: "宝宝黄疸",
							data: this.amountLists3
						}, ]
					};
					this.chartData3 = JSON.parse(JSON.stringify(res));
				}, 500);
			},
			onSelected(options) {
				let value = options[0].value;
				let label = options[0].label;
				this.getServerData();
				this.getServerData1();
				this.getServerData2();
				this.getServerData3();
				this.babyId=value
				this.getGetDetail()
			},
			onDateChange(e) {
				this.time = e.detail.value
				this.getBabyAnalysis();
				this.getServerData();
				this.getServerData1();
				this.getServerData2();
				this.getServerData3();
			},
			getMyDetailList() {
				this.changeLoading(true)
				this.$axios.get(this.$api.getMyDetailList).then((res) => {
					if (res.data.code == 200) {
						let data = res.data.data
						this.selectList = []
						data.forEach((item) => {
							this.selectList.push({
								label: item.babyName,
								value: item.babyId
							})
						})
					}
				}).finally(() => {
					this.changeLoading(false)
				})
			},
			getGetDetail() {
				this.changeLoading(true)
				this.$axios.get(this.$api.getMyDetail).then((res) => {
					if (res.data.code == 200) {
						this.setData({
							detailsList: res.data.data,
							babyId:res.data.data.babyId
						});
						this.getBabyAnalysis()
					}
				}).finally(() => {
					this.changeLoading(false)
				})
			},
			getBabyAnalysis() {
				this.changeLoading(true)
				let data = {
					babyId:this.babyId,
					time: this.time
				}
				this.$axios.get(this.$api.getBabyAnalysis, data).then((res) => {
					if (res.data.code == 200) {
						let timeList = []
						let amountLists = []
						let amountLists1 = []
						let amountLists2 = []
						let amountLists3 = []
						if(res.data.data.length>0){
							res.data.data.forEach(item => {
								let createTime = item.createTime.substring(0, 10)
								timeList.push(createTime)
								amountLists.push(item.weight)
								amountLists1.push(item.height)
								amountLists2.push(item.temperature)
								amountLists3.push(item.jaundiceValue)
							})
						}
						this.setData({
							timeList: timeList,
							amountLists: amountLists,
							amountLists1: amountLists1,
							amountLists2: amountLists2,
							amountLists3: amountLists3

						});
					}
				}).finally(() => {
					this.changeLoading(false)
				})
			}
		},
		mounted() {

		},
		/**
		 * 生命周期函数--监听页面加载
		 */
		onLoad: function(options) {
			let now = new Date();
			let year = now.getFullYear();
			let month = now.getMonth() + 1; //月份从0开始，需要加1
			let time=year+'-'+month
			this.time=time
			// this.echartsComponnets7 = this.selectComponent('#mychart-dom-bars7');
			this.title = options.title
			this.getGetDetail()
			this.getMyDetailList()
		},
		/**
		 * 生命周期函数--监听页面初次渲染完成
		 */
		onReady: function() {
			this.getServerData();
			this.getServerData1();
			this.getServerData2();
			this.getServerData3();
		},
		/**
		 * 生命周期函数--监听页面显示
		 */
		onShow: function() {},
		/**
		 * 生命周期函数--监听页面隐藏
		 */
		onHide: function() {},
		/**
		 * 生命周期函数--监听页面卸载
		 */
		onUnload: function() {},
		/**
		 * 页面相关事件处理函数--监听用户下拉动作
		 */
		onPullDownRefresh: function() {},
		/**
		 * 页面上拉触底事件的处理函数
		 */
		onReachBottom: function() {},
		/**
		 * 用户点击右上角分享
		 */
		onShareAppMessage() {},

		onShareTimeline() {},
	}
</script>

<style lang="less" scoped>
	@import './nursedetails.less';
</style>