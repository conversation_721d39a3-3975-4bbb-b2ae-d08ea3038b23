<template>
  <view>
    <!-- 转接面板 -->
    <view
      v-if="visible"
      class="transfer-panel"
      :style="{ paddingBottom: safeAreaBottom + 'px' }"
    >
      <view class="panel-header">
        <text class="panel-title">转接客服</text>
        <view class="close-btn" @click="handleClose">
          <u-icon name="close" size="32" color="#999999"></u-icon>
        </view>
      </view>
      <view class="service-list">
        <!-- 加载状态 -->
        <view v-if="isLoadingServices" class="loading-state">
          <text class="loading-text">加载中...</text>
        </view>

        <!-- 客服列表 -->
        <view
          v-for="service in availableServices"
          :key="service.id"
          class="service-item"
        >
          <view class="service-avatar-container">
            <image :src="service.avatar" class="service-avatar" />
          </view>
          <view class="service-info">
            <view class="service-name">{{ service.name }}</view>
            <text class="service-workload">
              接待中: {{ service.currentChats }}
            </text>
          </view>
          <view class="service-actions">
            <button
              class="transfer-btn"
              @click="handleTransfer(service)"
              :disabled="isTransferring"
            >
              {{ isTransferring ? '转接中...' : '转接' }}
            </button>
          </view>
        </view>



        <!-- 无数据状态 -->
        <view
          v-if="!isLoadingServices && availableServices.length === 0"
          class="empty-state"
        >
          <text class="empty-text">暂无可用客服</text>
        </view>
      </view>
    </view>

    <!-- 遮罩层 -->
    <view v-if="visible" class="overlay" @click="handleClose"></view>
  </view>
</template>

<script setup>
import { ref, watch } from 'vue';
import { transferConversation, getOnlineAgentList } from '../api/customerService.js';

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  session: {
    type: Object,
    default: null,
  },
  safeAreaBottom: {
    type: Number,
    default: 0,
  },
});

// Events
const emit = defineEmits(['close', 'transfer-success', 'transfer-error']);

// 响应式数据
const availableServices = ref([]);
const isLoadingServices = ref(false);
const isTransferring = ref(false);

// 监听面板显示状态，自动加载客服列表
watch(
  () => props.visible,
  (newVisible) => {
    if (newVisible && props.session) {
      loadAvailableServices();
    }
  },
  { immediate: true }
);

// 加载可用客服列表
const loadAvailableServices = async () => {
  try {
    isLoadingServices.value = true;

    const res = await getOnlineAgentList();

    console.log('在线客服列表响应:', res);

    if (res.code === 200 && res.data) {
      // 过滤掉当前客服自己，避免转接给自己
      const currentUserId = uni.getStorageSync('userInfo')?.id;

      availableServices.value = res.data
        .filter((agent) => agent.userId !== currentUserId)
        .map((agent) => ({
          id: agent.id,
          name: agent.nickname || '客服',
          avatar: agent.avatarUrl || '/static/images/default-avatar.png',
          currentChats: agent.currentChats || 0,
        }));

      console.log('处理后的客服列表:', availableServices.value);
    } else {
      console.error('获取在线客服列表失败:', res);
      availableServices.value = [];
    }
  } catch (error) {
    console.error('加载可用客服列表失败:', error);
    availableServices.value = [];
    uni.showToast({
      title: '加载客服列表失败',
      icon: 'none',
    });
  } finally {
    isLoadingServices.value = false;
  }
};

// 确认转接
const handleTransfer = async (service) => {
  if (!props.session) {
    emit('transfer-error', '会话信息不存在');
    return;
  }

  if (isTransferring.value) {
    return; // 防止重复点击
  }

  try {
    console.log('开始转接会话:', {
      conversationId: props.session.conversationId,
      targetAgentId: service.id,
      serviceName: service.name,
    });

    // 设置转接状态
    isTransferring.value = true;

    // 调用转接API
    const res = await transferConversation(
      props.session.conversationId,
      service.id
    );

    console.log('转接响应:', res);

    if (res.code === 200) {
      uni.showToast({
        title: `已转接给${service.name}`,
        icon: 'success',
      });

      emit('transfer-success', {
        session: props.session,
        targetService: service,
        result: res,
      });
    } else {
      const errorMsg = res.msg || '转接失败';
      uni.showToast({
        title: errorMsg,
        icon: 'none',
      });
      emit('transfer-error', errorMsg);
    }
  } catch (error) {
    console.error('转接失败:', error);
    const errorMsg = '转接失败，请重试';
    uni.showToast({
      title: errorMsg,
      icon: 'none',
    });
    emit('transfer-error', errorMsg);
  } finally {
    // 重置转接状态
    isTransferring.value = false;
  }
};

// 关闭面板
const handleClose = () => {
  emit('close');
};
</script>

<style lang="scss" scoped>
.transfer-panel {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  border-radius: 32rpx 32rpx 0 0;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
  z-index: 1000;
  max-height: 80vh;
  display: flex;
  flex-direction: column;

  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 32rpx 32rpx 24rpx;
    border-bottom: 1rpx solid #f0f0f0;

    .panel-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333333;
    }

    .close-btn {
      padding: 8rpx;
      border-radius: 50%;
      background-color: #f5f5f5;
    }
  }

  .service-list {
    flex: 1;
    overflow-y: auto;
    padding: 0 32rpx 32rpx;

    .loading-state {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 80rpx 0;

      .loading-text {
        font-size: 28rpx;
        color: #999999;
      }
    }

    .service-item {
      display: flex;
      align-items: center;
      padding: 24rpx 0;
      border-bottom: 1rpx solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .service-avatar-container {
        position: relative;
        margin-right: 24rpx;

        .service-avatar {
          width: 80rpx;
          height: 80rpx;
          border-radius: 50%;
          background-color: #f0f0f0;
        }


      }

      .service-info {
        flex: 1;

        .service-name {
          font-size: 30rpx;
          font-weight: 500;
          color: #333333;
          margin-bottom: 12rpx;
        }

        .service-workload {
          font-size: 24rpx;
          color: #666666;
          display: block;
        }
      }

      .service-actions {
        margin-left: 24rpx;

        .transfer-btn {
          padding: 12rpx 24rpx;
          background-color: #007aff;
          color: white;
          border: none;
          border-radius: 8rpx;
          font-size: 24rpx;
          min-width: 120rpx;
          transition: all 0.2s ease;

          &:active {
            background-color: #0056cc;
            transform: scale(0.95);
          }

          &:disabled {
            background-color: #cccccc;
            color: #999999;
            transform: none;
          }
        }
      }
    }

    .empty-state {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 80rpx 0;

      .empty-text {
        font-size: 28rpx;
        color: #999999;
      }
    }
  }
}

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
}
</style>
