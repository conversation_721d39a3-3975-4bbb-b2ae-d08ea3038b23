import request from './request';
import { sendSSEChatMessage } from '@/utils/sse.js';

/**
 * 创建新会话
 * @param {Object} data - 请求参数
 * @param {string} data.title - 会话标题
 * @returns {Promise<Object>} - 请帖详情
 */
export const createConversationApi = (data) => {
  return request({
    url: 'mp/ai/conversations',
    method: 'POST',
    data,
  });
};

/**
 * 更新会话标题
 * @param {string} conversationId - 会话ID
 * @param {string} title - 新标题
 * @returns {Promise<Object>} - 请帖详情
 */
export const updateConversationTitleApi = (conversationId, title) => {
  return request({
    url: `mp/ai/conversations/${conversationId}/title?title=${title}`,
    method: 'PUT',
  });
};

/**
 * 获取会话详情
 * @param {string} conversationId - 会话ID
 * @returns {Promise<Object>} - 请帖详情
 */
export const getConversationApi = (conversationId) => {
  return request({
    url: `mp/ai/conversations/${conversationId}`,
    method: 'GET',
  });
};

/**
 * 获取用户会话列表
 * @param {Object} data - 请求参数
 * @returns {Promise<Object>} - 请帖详情
 */
export const getConversationListApi = (data) => {
  return request({
    url: 'mp/ai/conversations/my',
    method: 'GET',
    data,
  });
};

/**
 * 删除会话
 * @param {string} conversationId - 会话ID
 * @returns {Promise<Object>} - 请帖详情
 */
export const deleteConversationApi = (conversationId) => {
  return request({
    url: `mp/ai/conversations/${conversationId}`,
    method: 'DELETE',
  });
};

/**
 * 获取用户会话分页列表
 * @param {Object} data - 请求参数
 * @param {number} data.pageNum - 页码
 * @param {number} data.pageSize - 每页大小
 * @returns {Promise<Object>} - 请帖详情
 */
export const getConversationListPageApi = (data) => {
  return request({
    url: 'mp/ai/conversations/my/page',
    method: 'GET',
    data,
  });
};

/**
 * 发送消息 (普通请求)
 * @param {Object} data - 请求参数
 * @param {number} data.userId - 用户ID
 * @param {string} data.userMessage - 用户消息
 * @param {string} data.conversationId - 会话ID
 * @returns {Promise<Object>} - 请帖详情
 */
export const chatApi = (data) => {
  return request({
    url: 'mp/ai/chat',
    method: 'POST',
    data,
  });
};

/**
 * 发送消息 (SSE流式请求)
 * @param {Object} data - 请求参数
 * @param {number} data.userId - 用户ID
 * @param {string} data.userMessage - 用户消息
 * @param {string} data.conversationId - 会话ID
 * @param {Object} callbacks - 回调函数
 * @param {Function} callbacks.onMessage - 接收到消息片段时的回调
 * @param {Function} callbacks.onError - 错误回调
 * @param {Function} callbacks.onComplete - 完成回调
 * @returns {Object} - SSE客户端实例
 */
export const chatSSEApi = (data, callbacks) => {
  // 动态导入SSE工具，避免循环依赖
  return sendSSEChatMessage(data, callbacks);
};

/**
 * 获取会话消息列表
 * @param {string} conversationId - 会话ID
 * @param {Object} data - 请求参数
 * @param {number} data.pageNum - 页码
 * @param {number} data.pageSize - 每页大小
 * @returns {Promise<Object>} - 响应数据
 */
export const getConversationMessagesApi = (conversationId, data) => {
  return request({
    url: `mp/ai/chat-messages/conversation/${conversationId}`,
    method: 'GET',
    data,
  });
};

/**
 * 获取起始问题推荐
 * @param {Object} data - 请求参数
 * @param {number} data.questionCount - 问题数量
 * @param {string} data.businessCategory - 业务分类
 * @param {string} data.knowledgeId - 知识库ID
 * @param {boolean} data.includeAiGenerated - 是否包含AI生成的问题
 * @returns {Promise<Object>} - 请帖详情
 */

export const getStartingQuestionsApi = (data) => {
  return request({
    url: 'mp/ai/chat/starting-questions',
    method: 'POST',
    data,
  });
};

/**
 * 获取相关问题推荐
 * @param {Object} data - 请求参数
 * @param {string} data.userQuestion - 用户问题
 * @param {number} data.recommendationCount - 推荐数量
 * @param {string} data.knowledgeId - 知识库ID
 * @returns {Promise<Object>} - 请帖详情
 */
export const getQuestionRecommendationsApi = (data) => {
  return request({
    url: 'mp/ai/chat/question-recommendations',
    method: 'POST',
    data,
  });
};

/**
 * 获取聊天推荐
 * @param {Object} data - 请求参数
 * @param {string} conversationId - 会话ID
 * @param {string} userMessage - 用户消息
 * @param {number} recommendationLimit - 推荐数量
 * @returns {Promise<Object>} - 请帖详情
 */
export const getChatRecommendationsApi = (data) => {
  return request({
    url: `mp/ai/recommendation`,
    method: 'GET',
    data,
  });
};
