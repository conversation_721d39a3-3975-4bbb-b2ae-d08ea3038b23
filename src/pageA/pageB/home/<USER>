<template>
	<view class="main">
		<view class="title">
			我要反馈
		</view>
		<view class="content">
			<textarea v-model="content" @input="countChars" :maxlength="maxLength" placeholder="请输入"></textarea>
			<view class="counter">{{ charCount }}/{{ maxLength }}</view>
			<upload-vue class="publish-upload" @clickFile="clickAlbumImg"></upload-vue>
		</view>
	</view>
	<view class="addbutton">
		<view class="add" @click="add">
			提交
		</view>
	</view>
</template>

<script>
	import uploadVue from '@/components/upload.vue';
	export default {
		components: {
			uploadVue,
		},
		data() {
			return {
				content: '',
				charCount: 0,
				maxLength: 500,
				imgUrl: [],
				videos:[],
			}
		},
		methods: {
			add() {
				//查询护理团队列表
				if (this.content == '') {
					uni.showToast({
						title: '请填写反馈内容',
						icon: 'none',
						duration: 3000, //持续的时间
					});
					return
				}
				let data = {
					content: this.content,
				};
				if (this.imgUrl.length > 0) {
					let imageUrl=[]
					this.imgUrl.forEach(item=>{
						imageUrl.push(item.url)
					})
					data.imageUrl = imageUrl
				}
				this.$axios.post(this.$api.cardFeedback, data).then((res) => {
					if (res.data.code == 200) {
						uni.showToast({
							title: '反馈成功',
							icon: 'none',
							duration: 3000, //持续的时间
						});
						setTimeout(() => {
							uni.navigateBack({
								delta: 1, // 返回的页面数，如果 delta 大于现有页面数，则返回到首页。
							});
						}, 2000)
					}
				});
			},
			countChars() {
				this.charCount = this.content.length;
			},
			clickAlbumImg(value) {
				let imgUrl = []
				let videos = []
				if (value && value.length > 0) {
					value.forEach((item) => {
						item.type == 'video' ? videos.push(item.url) : imgUrl.push(item.url)
					})
					this.setData({
						imgUrl,
						videos
					});
				} else {
					this.setData({
						imgUrl: [],
						videos: []
					});
				}

			},
		}
	}
</script>
<style scoped>
	@import './suggest.less';
</style>