#!/usr/bin/env node

/**
 * 完整CI工作流程测试脚本
 * 测试所有CI功能是否正常工作
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// 颜色输出
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

/**
 * 执行命令并返回结果
 */
function runCommand(command, description) {
  try {
    logInfo(`执行: ${description}`);
    const result = execSync(command, { 
      cwd: path.resolve(__dirname, '../..'),
      encoding: 'utf8',
      stdio: 'pipe'
    });
    logSuccess(`${description} - 成功`);
    return { success: true, output: result };
  } catch (error) {
    logError(`${description} - 失败: ${error.message}`);
    return { success: false, error: error.message, output: error.stdout };
  }
}

/**
 * 测试配置管理功能
 */
async function testConfigManagement() {
  log('\n🔧 测试配置管理功能', 'cyan');
  log('='.repeat(50));

  const tests = [
    {
      command: 'node ci/scripts/manage.js list',
      description: '列出所有项目'
    },
    {
      command: 'node ci/scripts/manage.js check',
      description: '检查项目配置'
    },
    {
      command: 'node ci/scripts/manage.js validate',
      description: '验证私钥路径格式'
    },
    {
      command: 'node ci/scripts/manage.js info main',
      description: '查看main项目详情'
    },
    {
      command: 'node ci/scripts/manage.js template',
      description: '生成项目配置模板'
    }
  ];

  let passed = 0;
  for (const test of tests) {
    const result = runCommand(test.command, test.description);
    if (result.success) {
      passed++;
    }
  }

  logInfo(`配置管理测试: ${passed}/${tests.length} 通过`);
  return passed === tests.length;
}

/**
 * 测试租户ID注入功能
 */
async function testTenantIdInjection() {
  log('\n🏢 测试租户ID注入功能', 'cyan');
  log('='.repeat(50));

  const tests = [
    {
      command: 'node ci/scripts/test-config-injection.js',
      description: '基础配置注入测试'
    },
    {
      command: 'node ci/scripts/test-tenant-injection.js main develop',
      description: '测试main项目develop环境'
    },
    {
      command: 'node ci/scripts/test-tenant-injection.js xinxiyue develop',
      description: '测试xinxiyue项目develop环境'
    }
  ];

  let passed = 0;
  for (const test of tests) {
    const result = runCommand(test.command, test.description);
    if (result.success) {
      passed++;
    }
  }

  logInfo(`租户ID注入测试: ${passed}/${tests.length} 通过`);
  return passed === tests.length;
}

/**
 * 测试构建功能（不实际构建）
 */
async function testBuildConfiguration() {
  log('\n📦 测试构建配置', 'cyan');
  log('='.repeat(50));

  // 检查构建配置注入器
  try {
    const BuildConfigInjector = require('../scripts/build-config-injector.js');
    const injector = new BuildConfigInjector();
    
    logInfo('测试配置注入器实例化');
    logSuccess('配置注入器实例化成功');
    
    // 测试配置注入（不实际执行）
    logInfo('验证配置注入方法');
    if (typeof injector.inject === 'function') {
      logSuccess('inject方法存在');
    } else {
      logError('inject方法不存在');
      return false;
    }
    
    if (typeof injector.createBackups === 'function') {
      logSuccess('createBackups方法存在');
    } else {
      logError('createBackups方法不存在');
      return false;
    }
    
    if (typeof injector.restoreBackups === 'function') {
      logSuccess('restoreBackups方法存在');
    } else {
      logError('restoreBackups方法不存在');
      return false;
    }
    
    return true;
  } catch (error) {
    logError(`构建配置测试失败: ${error.message}`);
    return false;
  }
}

/**
 * 测试配置文件完整性
 */
async function testConfigFileIntegrity() {
  log('\n📄 测试配置文件完整性', 'cyan');
  log('='.repeat(50));

  const configFiles = [
    'ci/config/projects.json',
    'ci/config/ci.config.js',
    'ci/config/config-loader.js',
    'ci/config/manager.js',
    'ci/config/json-manager.js'
  ];

  let passed = 0;
  for (const file of configFiles) {
    const filePath = path.resolve(__dirname, '../..', file);
    if (fs.existsSync(filePath)) {
      logSuccess(`${file} 存在`);
      
      // 尝试加载配置文件
      try {
        if (file.endsWith('.js')) {
          delete require.cache[require.resolve(filePath)];
          require(filePath);
          logSuccess(`${file} 语法正确`);
        } else if (file.endsWith('.json')) {
          JSON.parse(fs.readFileSync(filePath, 'utf8'));
          logSuccess(`${file} JSON格式正确`);
        }
        passed++;
      } catch (error) {
        logError(`${file} 格式错误: ${error.message}`);
      }
    } else {
      logError(`${file} 不存在`);
    }
  }

  logInfo(`配置文件完整性测试: ${passed}/${configFiles.length} 通过`);
  return passed === configFiles.length;
}

/**
 * 运行完整测试套件
 */
async function runFullTest() {
  log('🚀 开始完整CI工作流程测试', 'magenta');
  log('='.repeat(60));

  const testResults = [];

  // 运行各项测试
  testResults.push({
    name: '配置管理功能',
    result: await testConfigManagement()
  });

  testResults.push({
    name: '租户ID注入功能',
    result: await testTenantIdInjection()
  });

  testResults.push({
    name: '构建配置',
    result: await testBuildConfiguration()
  });

  testResults.push({
    name: '配置文件完整性',
    result: await testConfigFileIntegrity()
  });

  // 汇总结果
  log('\n📊 测试结果汇总', 'magenta');
  log('='.repeat(60));

  const passed = testResults.filter(test => test.result).length;
  const total = testResults.length;

  testResults.forEach(test => {
    const status = test.result ? '✅ 通过' : '❌ 失败';
    console.log(`  ${test.name}: ${status}`);
  });

  log('');
  if (passed === total) {
    logSuccess(`🎉 所有测试通过！(${passed}/${total})`);
    logInfo('CI工作流程完全正常，可以安全使用');
  } else {
    logWarning(`⚠️ 部分测试失败 (${passed}/${total})`);
    logInfo('请检查失败的测试项目');
  }

  return passed === total;
}

/**
 * 显示帮助信息
 */
function showHelp() {
  log('完整CI工作流程测试脚本', 'cyan');
  log('');
  log('使用方法:', 'yellow');
  log('  node ci/scripts/test-full-workflow.js', 'white');
  log('');
  log('测试内容:', 'yellow');
  log('  - 配置管理功能', 'white');
  log('  - 租户ID注入功能', 'white');
  log('  - 构建配置', 'white');
  log('  - 配置文件完整性', 'white');
  log('');
}

// 主函数
async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--help') || args.includes('-h')) {
    showHelp();
    return;
  }

  const success = await runFullTest();
  process.exit(success ? 0 : 1);
}

// 运行主函数
if (require.main === module) {
  main().catch(error => {
    logError(`测试执行失败: ${error.message}`);
    process.exit(1);
  });
}

module.exports = {
  runFullTest
};
