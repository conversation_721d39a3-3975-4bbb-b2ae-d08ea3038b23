<template>
	<view class="main">
		<img :src="item" alt="" mode="widthFix"
			style="width: 100%;" v-for="item,index in mealTags" :key="index"/>
		<view class="blank"></view>
		<new-bottom :from="SessionFrom"></new-bottom>
		<mask-dialog></mask-dialog>
	</view>

</template>

<script>
	export default {
			data() {
				return {
	mealTags:[]
				};
			},
			/**
			 * 生命周期函数--监听页面加载
			 */
			onLoad(options) {
	              this.mealTags=uni.getStorageSync('mealTags')
			},
			/**
			 * 生命周期函数--监听页面初次渲染完成
			 */
			onReady() {},
			/**
			 * 生命周期函数--监听页面显示
			 */
			onShow() {},
			/**
			 * 生命周期函数--监听页面隐藏
			 */
			onHide() {},
			/**
			 * 生命周期函数--监听页面卸载
			 */
			onUnload() {},
			/**
			 * 页面相关事件处理函数--监听用户下拉动作
			 */
			onPullDownRefresh() {},
			/**
			 * 页面上拉触底事件的处理函数
			 */
			onReachBottom() {},
			/**
			 * 用户点击右上角分享
			 */
			onShareAppMessage() {},
	
			onShareTimeline() {},
			methods: {
				suiteInfo(deliveryId) {
					//查询优惠套餐列表
					let data = {
						deliveryId: deliveryId
					};
					this.$axios.get(this.$api.productDelivery, data).then((res) => {
						if (res.data.code == 200) {
							let data = res.data.data;
							let list = [];
							data.videos.forEach((item) => {
								let data = {
									type: 'video',
									url: item
								};
								list.push(data);
							});
							data.productPhotoUrl.forEach((item) => {
								let data = {
									type: 'image',
									url: item
								};
								list.push(data);
							});
							console.log(data);
							this.setData({
								infoList: data,
								banner_list: list,
								imgList: data.productPhotoUrl
							});
						}
					});
				},
			}
		};
</script>
<style lang="less" scoped>
	@import './foodDetails.less';
</style>