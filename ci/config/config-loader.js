/**
 * 配置加载器
 * 从 JSON 文件加载配置，提供向后兼容的接口
 */

const path = require('path');
const JsonConfigManager = require('./json-manager');

// 创建 JSON 配置管理器实例
const jsonManager = new JsonConfigManager();

// 从 JSON 文件加载配置
function loadConfig() {
  const result = jsonManager.readConfig();
  if (!result.success) {
    console.error('加载配置失败:', result.error);
    return { projects: {}, defaultConfig: {} };
  }
  return result.data;
}

// 加载配置
const config = loadConfig();
const projects = config.projects || {};

// 默认配置，包含函数方法
const defaultConfig = {
  // 版本号生成规则
  version: {
    // 自动生成版本号：年.月.日.时分
    auto: () => {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      const hour = String(now.getHours()).padStart(2, '0');
      const minute = String(now.getMinutes()).padStart(2, '0');
      return `${year}.${month}.${day}.${hour}${minute}`;
    },
    // 手动指定版本号
    manual: (version) => version
  },

  // 上传设置
  upload: {
    // 默认描述
    desc: '通过 miniprogram-ci 自动上传',
    // 是否启用机器人通知
    robot: 1,
    // 上传超时时间（毫秒）
    timeout: 300000
  },

  // 预览设置
  preview: {
    // 预览页面路径
    pagePath: 'pages/index/index',
    // 预览页面参数
    searchQuery: '',
    // 场景值
    scene: 1001,
    // 二维码格式
    qrcodeFormat: 'image',
    // 二维码输出目录
    qrcodeOutputDir: path.resolve(__dirname, '../preview'),
    // 二维码输出路径（向后兼容）
    qrcodeOutputDest: path.resolve(__dirname, '../preview/preview-default.jpg')
  },

  // 合并 JSON 配置中的默认配置
  ...(config.defaultConfig || {})
};

module.exports = {
  projects,
  defaultConfig,
  jsonManager, // 导出管理器实例供其他模块使用
  
  // 获取项目配置
  getProject: (projectName) => {
    const project = projects[projectName];
    if (!project) {
      throw new Error(`项目 "${projectName}" 不存在，可用项目：${Object.keys(projects).join(', ')}`);
    }
    return project;
  },

  // 获取所有项目名称
  getProjectNames: () => Object.keys(projects),

  // 验证项目配置
  validateProject: (projectName) => {
    const project = projects[projectName];
    if (!project) {
      return { valid: false, error: `项目 "${projectName}" 不存在` };
    }

    // 检查必要的配置
    if (!project.appid) {
      return { valid: false, error: `项目 "${projectName}" 缺少 appid 配置` };
    }

    if (!project.privateKeyPath) {
      return { valid: false, error: `项目 "${projectName}" 缺少私钥路径配置` };
    }

    return { valid: true };
  },

  // 重新加载配置
  reloadConfig: () => {
    const newConfig = loadConfig();
    Object.assign(projects, newConfig.projects || {});
    Object.assign(defaultConfig, newConfig.defaultConfig || {});
    return { projects, defaultConfig };
  }
};
