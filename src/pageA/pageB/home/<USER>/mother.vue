<template>
    <view class="submain" :style="{'minHeight': contentHeight + 'px'}">
        宝妈说
        <view class="main">

        </view>
    </view>
</template>
<script setup>
    // 待开发： 产康页面 宝妈说动态
    import {
        onShow,
        onHide,
        onLoad,
        onPageScroll
    } from "@dcloudio/uni-app";
    import {
        ref,
        reactive,
        onMounted,
        computed,
        getCurrentInstance
    } from "vue";
    const instance = getCurrentInstance();
    const App = instance.appContext.config.globalProperties
    const props = defineProps({
        contentHeight: {
            type: Number,
            default: 0
        }
    })
    onLoad((options) => {
        console.log('optionsssss', options)
        pageInit()
    })
    const pageInit = async () => {
        try {

        } catch {

        }
    }
</script>
<style lang="less" scoped>
    .submain {}
</style>