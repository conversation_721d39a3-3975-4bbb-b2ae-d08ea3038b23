<template>
  <view>
    <navigation
      background="#fff"
      title="个人中心"
      :isSowArrow="false"
    ></navigation>
    
    <view class="mains">
      <view class="prosonal">
        <!-- 用户信息头部组件 -->
        <UserProfile :userInfo="userInfo" />
        
        <!-- 我的内容网格组件 -->
        <MyContentGrid
          :messageUnreadCount="messageUnreadCount"
          @message-read="handleMessageRead"
        />

        <!-- 我的服务网格组件 -->
        <MyServiceGrid
          :mealShow="mealShow"
          :nurseShow="nurseShow"
          :complaintShow="complaintShow"
          :isCustomerService="isCustomerService"
          :unreadMessageCount="unreadMessageCount"
        />
      </view>
    </view>
    
    <mask-dialog></mask-dialog>

    <!-- 自定义 tabbar -->
    <CustomTabBar activeKey="mine" />
  </view>
</template>

<script setup>
import { onLoad, onShow } from '@dcloudio/uni-app'
import CustomTabBar from '@/components/CustomTabBar.vue'
import UserProfile from './components/UserProfile.vue'
import MyContentGrid from './components/MyContentGrid.vue'
import MyServiceGrid from './components/MyServiceGrid.vue'
import { useUserInfo } from './composables/useUserInfo.js'
import { usePermissions } from './composables/usePermissions.js'
import { useMessageCount } from './composables/useMessageCount.js'
import { useSubscription } from './composables/useSubscription.js'

// 使用 composables
const { userInfo, checkInfo } = useUserInfo()
const {
  mealShow,
  complaintShow,
  nurseShow,
  isCustomerService,
  checkPermissions
} = usePermissions()
const {
  unreadMessageCount,
  messageUnreadCount,
  getUnreadMessageCount,
  getMessageUnreadCount,
  resetMessageUnreadCount
} = useMessageCount()
const { preloadSubscriptionTemplates } = useSubscription()

// 生命周期函数
onLoad(() => {
  // 预加载常用订阅模板
  preloadSubscriptionTemplates()
})

onShow(() => {
  checkPermissions()
  checkInfo()
  getUnreadMessageCount() // 获取未读客服消息数量
  getMessageUnreadCount() // 获取未读普通消息数量
})

// 事件处理函数
const handleMessageRead = () => {
  resetMessageUnreadCount()
}
</script>

<style scoped>
.mains {
  background: #f5f6fa;
  height: 100vh;
}

.prosonal {
  background: url('http://cdn.xiaodingdang1.com/2025/02/06/275a9ad2ba1c404693b6cc7de3e4e5a3.png')
    no-repeat;
  background-size: 100% 1000rpx;
  padding: 188rpx 30rpx calc(30rpx + 150rpx + env(safe-area-inset-bottom));
}
</style>
