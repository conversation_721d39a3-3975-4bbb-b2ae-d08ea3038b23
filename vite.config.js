import {
    defineConfig
} from "vite";
import uni from "@dcloudio/vite-plugin-uni";
import {
    resolve
} from "path";
export default defineConfig(() => {
    return {
        server: {
            port: "3002",
        },
        resolve: {
            alias: {
                "@": resolve(__dirname, "/src"),
            },
        },
        plugins: [uni()],
        define: {
            'process.env': process.env, //配置二
        },
    };
});