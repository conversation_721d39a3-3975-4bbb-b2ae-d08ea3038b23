<template>
	<view class="mom-detail">
		<navigation :background="bgColor" title="会所服务" :isSowArrow="true"></navigation>
		<view class="main">
			<view class="mainss">
				<view class="mainss-top">
					<userdesc-vue :item="feedPostInfo" pageType="secondDetail"></userdesc-vue>
				</view>
				<swiper :indicator-dots="indicatorDots" :interval="interval" :duration="duration" :autoplay="autoplay"
					indicator-color="#D9D9D9" indicator-active-color="#333333" :current="current" class="swipper1">
					<block v-for="(item, index) in backgroundArr" :key="index">
						<swiper-item>
							<!-- <video id="myVideo" @touchstart="onTouchStart($event, index)"
								@touchend="onTouchEnd($event, index)" v-if="item.type == 'video'" class="u-wrp-bnr"
								:src="item.url" @tap="previews" :data-index="index" @fullscreenchange="fullscreenchange"></video> -->
								<view class="swiper-item-box" >
							<video id="myVideo" v-if="item.type == 'video'" class="u-wrp-bnr" :src="item.url"
								@tap="previews" :data-index="index" @fullscreenchange="fullscreenchange"></video>
							<image lazy-load class="u-wrp-bnr" :src="item.url" @tap="preview" data-type="18"
								:data-index="index" :data-clubactivities="clubHomeList.clubActivities[index]"
								mode="widthFix" v-if="item.type == 'image'"></image>
								</view>
						</swiper-item>
					</block>
				</swiper>
				<view class="content">
					<view class="title" v-if="originContent">
						<!-- <view class="title_1">#</view> -->
						<view class="title_2">
							{{ originContent }}
						</view>
						<view class="title-devide"></view>
					</view>
					<view class="title_3" v-html="feedPostInfo.content"></view>
					<view class="issue">
						<view class="issue_1">发布于{{ feedPostInfo.createTimes }}</view>
						<view class="issue_3">
							<view class="issue_2" @tap="pushMessage(feedPostInfo.postId)" v-if="pushMessageIf">
								<image
									src="http://cdn.xiaodingdang1.com/2025/04/24/5260b6e5a4fe4c0b80d7439f341d9009.png"
									mode="" style="width: 32rpx; height: 32rpx"></image>
								<view class="issue_2_1"> 消息推送 </view>
							</view>
							<view class="issue_2" @tap="poster()" v-if="posterIf">
								<image
									src="http://cdn.xiaodingdang1.com/2025/05/19/e9ca9aba2b584e2784a717430cf51c8d.png"
									mode="" style="width: 32rpx; height: 32rpx"></image>
								<view class="issue_2_1"> 海报 </view>
							</view>
							<view class="issue_2" @tap="delStaff(feedPostInfo.postId)" v-if="deleteIf">
								<image
									src="http://cdn.xiaodingdang1.com/2025/04/24/44f91868a30d429d9372dc2788d27c42.png"
									mode="" style="width: 32rpx; height: 32rpx"></image>
								<view class="issue_2_1"> 删除 </view>
							</view>
						</view>
					</view>
				</view>
				<!-- 服务介绍 -->
				<!-- <view class="introduce">
                    <introduce-vue></introduce-vue>
                </view> -->
				<view class="comment-list">
					<comment-list-vue pageType="user" ref="commentListRef" :item="feedPostInfo" @repay="repay"
						:inputBottomHeight="inputBottomHeight"></comment-list-vue>
				</view>
				<view class="note" v-if="noteList.length > 0">
					<view class="note-image">
						<image src="http://cdn.xiaodingdang1.com/2025/01/02/f1fec23f0c11418783ac55320d31e276.png">
						</image>
					</view>
					<view class="note-content" v-if="noteList.length > 0">
						<!--           <swiper class="top-container card-reset" @change="changeNote">
                            <block v-for="(item, index) in noteList" :key='index'>
                                <swiper-item>
                                    <view v-if="index > 0" :class="['top', 'top3']">
                                        <service-card-vue :item="noteList[index]"></service-card-vue>
                                        <img-content-area-vue :showContent="true" pageType="note"
                                            :listData="noteList[index]" :index="index"
                                            @onDetail="details"></img-content-area-vue>
                                        <dynamic-button-vue @shareNote="shareNote" :dyData="item"
                                            @refresh="refresh"></dynamic-button-vue>
                                    </view>
                                    <view :class="['top',noteList.length == 1? 'top1':index == 0? 'top2':'top4']">
                                        <service-card-vue :item="item"></service-card-vue>
                                        <img-content-area-vue :showContent="true" pageType="note" :listData="item"
                                            :index="index" @onDetail="details"></img-content-area-vue>
                                        <dynamic-button-vue @shareNote="shareNote" :dyData="item"
                                            @refresh="refresh"></dynamic-button-vue>
                                        <view class="right" v-if="noteList.length - 1 == index && noteList.length > 1">
                                            <view class="text" @tap="toDetail">查看更多</view>
                                        </view>
                                    </view>
                                    <view :class="['top', 'top2' ]" v-if="index < noteList.length - 1">
                                        <service-card-vue :item="noteList[index + 1]"></service-card-vue>
                                        <img-content-area-vue :showContent="true" pageType="note"
                                            :listData="noteList[index + 1]" :index="index"
                                            @onDetail="details"></img-content-area-vue>
                                        <dynamic-button-vue @shareNote="shareNote" :dyData="item"
                                            @refresh="refresh"></dynamic-button-vue>
                                    </view>
                                </swiper-item>
                            </block>
                        </swiper> -->
						<scroll-view class="top-container card-reset" scroll-x="true" v-if="noteList.length > 0">
							<view :class="['top', topStyle]" v-for="(item, index) in noteList" :key="index">
								<view class="note-title">
									<view class="note-title-tag"></view>
									<view class="note-title-content">服务{{ item.noteTitle }}</view>
								</view>
								<service-card-vue :item="item"></service-card-vue>
								<img-content-area-vue :showContent="true" pageType="note" :listData="item"
									:index="index" @onDetail="details"></img-content-area-vue>
								<dynamic-button-vue @shareNote="shareNote" :dyData="item"
									@refresh="refresh"></dynamic-button-vue>
								<view class="right" v-if="noteList.length - 1 == index && noteList.length > 1">
									<view class="text" @tap="toDetail">查看更多</view>
								</view>
							</view>
						</scroll-view>
					</view>
				</view>
			</view>
		</view>
		<bottom-button-vue :item="feedPostInfo" :inputBottomHeight="inputBottomHeight"
			@input="inputComment"></bottom-button-vue>
		<new-request-loading></new-request-loading>
		<mask-dialog></mask-dialog>
		<rightMenuBarVue :scrollTop="scrollTop"></rightMenuBarVue>
	</view>
</template>

<script>
	import rightMenuBarVue from '@/components/rightMenuBar.vue';
	import imgContentAreaVue from '@/components/imgContentArea.vue';
	import serviceCardVue from '@/pageA/pageB/community/component/serviceCard.vue';
	import userdescVue from '@/components/userdesc.vue';
	import utils from '@/utils/util.js';
	import commentListVue from '@/components/commentList.vue';
	import waterfallCardVue from '@/components/waterfallCard.vue';
	import introduceVue from '@/components/introduce.vue';
	import bottomButtonVue from '@/components/bottomButton.vue';
	import dynamicButtonVue from '@/components/dynamicButton.vue';
	const dayList = utils.getCheckInDayList();
	export default {
		components: {
			waterfallCardVue,
			commentListVue,
			userdescVue,
			introduceVue,
			bottomButtonVue,
			serviceCardVue,
			imgContentAreaVue,
			dynamicButtonVue,
			rightMenuBarVue,
		},
		data() {
			return {
				scrollTop: 0,
				posterIf: false,
				imageSrc: '',
				pushMessageIf: false,
				deleteIf: false,
				backgroundArr: [],
				feedPostInfo: {
					avatar: '',
					nickname: '',
					createTimes: '',
					liveDays: '',
					content: '',
					shareCount: false,
					postId: '',
					isFavorite: '',
					favoriteCount: false,
					isLike: '',
					likesCount: false,
				},
				myPageList: [],
				pageSize: 4,
				pageNum: 1,
				listData: [1, 2, 3, 4, 5],
				indicatorDots: true,
				autoplay: true,
				interval: 4000,
				duration: 1000,
				userId: '',
				postId: '',
				bgColor: '#ffffff',
				titleName: '',
				clubHomeList: {
					clubActivities: '',
				},
				current: 0,
				// 记录是否正在滑动
				isSwiping: false,
				isZoomed: false, // 是否缩放状态
				videoContext: null,
				originContent: '',
				inputBottomHeight: 0,
				// noteTitle: '',
				noteList: [],
				topStyle: 'top2',
			};
		},

		/**
		 * 生命周期函数--监听页面加载
		 */
		onLoad(options) {
			this.setData({
				userId: options.userId,
				postId: options.postId,
			});
			uni.onKeyboardHeightChange((res) => {
				if (res.height) {
					this.inputBottomHeight = res.height + 'px';
				} else {
					this.inputBottomHeight = 0;
				}
			});
			if (options.scene) {
				let params = {};
				let pairs = options.scene.split('%26');
				for (let i = 0; i < pairs.length; i++) {
					let pair = pairs[i].split('-');
					params[pair[0]] = pair[1];
				}
				this.postId = params.uuid;
			}
			this.getfeedPostInfo();
		},

		onShareAppMessage() {},

		onShareTimeline() {},
		/**
		 * 生命周期函数--监听页面初次渲染完成
		 */
		onReady() {},
		/**
		 * 生命周期函数--监听页面显示
		 */
		onShow() {
			let roles = uni.getStorageSync('roles');
			let token = uni.getStorageSync('token');
			this.pushMessageIf =
				roles[0] == 'SALES' || roles[0] == 'ADMIN' ? true : false;
			this.posterIf = !token || roles[0] == 'CUSTOMER' ? false : true;
		},
		/**
		 * 生命周期函数--监听页面隐藏
		 */
		onHide() {},
		/**
		 * 生命周期函数--监听页面卸载
		 */
		onUnload() {
			uni.offKeyboardHeightChange();
			var newDateTime = Date.parse(new Date());
			let data = {
				eventId: uni.getStorageSync('eventId'),
				leaveTime: newDateTime,
			};
			let iftoken = uni.getStorageSync('token');
			if (iftoken) {
				this.$point.reportComEnd(data);
			}
		},
		/**
		 * 页面相关事件处理函数--监听用户下拉动作
		 */
		onPullDownRefresh() {},
		/**
		 * 页面上拉触底事件的处理函数
		 */
		onReachBottom() {},
		onPageScroll: function(e) {
			if (e.scrollTop > 0) {
				this.setData({
					bgColor: '#ffffff',
				});
			} else if (e.scrollTop < 2) {
				this.setData({
					bgColor: '#ffffff',
				});
			}
			this.scrollTop = e.scrollTop;
		},
		methods: {
			getreportComStart(nodeName, title) {
				var newDateTime = Date.parse(new Date());
				let data = {
					eventType: 'PAGE_VIEW',
					pageUrl: '/pageA/pageB/community/staffdetail',
					module: nodeName,
					eventTime: newDateTime,
					pageTitle: title,
					additionalData:this.postId
				};
				let iftoken = uni.getStorageSync('token');
				if (iftoken) {
					this.$point.reportComStart(data);
				}
			},
			poster() {
				uni.navigateTo({
					url: '/pageA/poster/poster?type=staff&postId=' +
						this.postId +
						'&taskNodeId=' +
						this.feedPostInfo.taskNodeId,
				});
			},
			pushMessage(postId) {
				uni.requestSubscribeMessage({
					tmplIds: ['4ozrWQOn4Z0lxEfPNXyVzNaKCWaanMNYypzOu8PH9I0'],
					success(res) {
						console.log('订阅消息成功', res);
						// 订阅成功后跳转
						uni.navigateTo({
							url: '/pageA/pageB/community/pushMessage/pushMessage?receivingType=1' +
								'&postId=' +
								postId,
						});
					},
					fail(res) {
						console.log('订阅消息失败', res);
						// 订阅失败也允许跳转
						uni.navigateTo({
							url: '/pageA/pageB/community/pushMessage/pushMessage?receivingType=1' +
								'&postId=' +
								postId,
						});
					},
				});
			},
			delStaff(postId) {
				uni.showModal({
					title: '提示',
					content: '是否确定删除该会所动态?',
					success: (res) => {
						uni.hideLoading();
						if (res.cancel) {
							console.log('用户点击取消');
						} else if (res.confirm) {
							console.log('用户点击确定');
							this.getdelStaff(postId);
						}
					},
				});
			},
			async getdelStaff(postId) {
				let data = {
					postId: postId,
				};
				const res = await this.$axios.get(this.$api.feedPostRemove, data);
				if (res.data.code == 200) {
					//返回
					uni.$emit('publishsuccesss');
					uni.navigateBack({
						delta: 1, // 返回的页面数，如果 delta 大于现有页面数，则返回到首页。
					});
				} else {
					uni.showToast({
						title: res.data.msg,
						icon: 'none',
						duration: 3000, //持续的时间
					});
				}
			},
			// 生成海报
			shareNote() {},
			refresh() {
				uni.offKeyboardHeightChange();
				this.getfeedPostInfo();
			},
			// 评论里面回复
			repay(value) {
				if (value) {
					this.feedPostInfo.commentsCount = this.feedPostInfo.commentsCount + 1;
				}
			},
			// 外面回复
			inputComment(value) {
				if (value) {
					this.feedPostInfo.commentsCount = this.feedPostInfo.commentsCount + 1;
					this.$axios
						.post(this.$api.feedPostComment, {
							comment: value,
							postId: this.feedPostInfo.postId,
						})
						.then((res) => {
							const userInfo = uni.getStorageSync('userInfo');
							const currentDate = utils.formatTime(new Date());
							const commentData = {
								comment: value,
								avatar: userInfo.avatar,
								createTime: currentDate,
								nickname: userInfo.nickname,
							};
							if (
								this.feedPostInfo.comments &&
								this.feedPostInfo.comments.length > 0
							) {
								this.feedPostInfo.comments.push(commentData);
							} else {
								this.feedPostInfo.comments = [commentData];
							}
							console.log('this.feedPostInfo', this.feedPostInfo);
						});
				}
			},
			// onTouchStart(event, index) {
			// 	this.isZoomed = true; // 触摸开始时设置为缩放状态
			// },
			// onTouchEnd(event, index) {
			// 	this.isZoomed = false; // 触摸结束时设置为非缩放状态
			// 	if (!this.isZoomed) {
			// 		this.pauseVideo(index); // 如果不是缩放状态，暂停视频播放
			// 	}
			// },
			// pauseVideo(index) {
			// 	this.videoContext = uni.createVideoContext(`sendingVideo${index}`, this);
			// 	if (this.videoContext) {
			// 		this.videoContext.pause()
			// 	}
			// },
			onShareClick() {
				// console.log('onShareClickonShareClick')
				this.setData({
					'feedPostInfo.shareCount': ++this.feedPostInfo.shareCount,
				});
			},
			async getNoteList(taskNodeId) {
				let data = {
					pageSize: 10,
					pageNum: 1,
					taskNodeId,
				};
				const res = await this.$axios.get(this.$api.getDiaryList, data);
				if (res.data.code == 200) {
					const data = res.data.rows;
					data.forEach((item) => {
						if (item.imgs && item.imgs.length > 0) {
							item.contentPhotos = item.imgs;
						} else {
							item.contentPhotos = [
								'http://cdn.xiaodingdang1.com/2024/12/06/2638525a978d4d93905491269f790474.png',
							];
						}
						// if (item.content.length > 115) {
						//   item.packUpShow = false;
						//   item.moreBtns = true;
						//   item.moreBtn = true;
						// } else {
						//   item.packUpShow = false;
						//   item.moreBtns = false;
						//   item.moreBtn = false;
						// }
						if (item?.createTime) {
							item.endTime = item.createTime.slice(11, item.createTime.length);
							item.createTime = item.createTime.slice(0, 10);
						}
						let serviceNumName = dayList.find(
							(list) => list.value == item.timeNumber,
						).label;
						item.noteTitle =
							(item.momName || this.$defaultName) +
							serviceNumName.slice(2, serviceNumName.length);
					});
					if (data && data.length > 0) {
						this.noteList = data;
						this.topStyle = data.length == 1 ? 'top1' : 'top2';

						// this.changeNote({
						//     detail: {
						//         current: 0
						//     }
						// })
					}
				}
			},

			getfeedPostInfo() {
				let data = {
					postId: this.postId,
				};
				this.$axios.get(this.$api.feedPostInfo, data).then((res) => {
					if (res.data.code == 200) {
						let data = res.data.data;
						this.getreportComStart(data.nodeName, data.title)
						let userId = uni.getStorageSync('uid');
						if (userId == data.userId) {
							this.deleteIf = true;
						}
						let list = [];
						if (data.videos && data.videos.length > 0) {
							data.videos.forEach((item) => {
								let data = {
									type: 'video',
									url: item,
								};
								list.push(data);
							});
						}
						if (data && data.contentPhotos) {
							data.contentPhotos.forEach((item) => {
								let data = {
									type: 'image',
									url: item,
								};
								list.push(data);
							});
						}
						if (data.createTime && data.createTime != null) {
							var timearr = data.createTime
								.replace(' ', ':')
								.replace(/\:/g, '-')
								.split('-');
							data.createTimes =
								timearr[1] +
								'/' +
								timearr[2] +
								' ' +
								timearr[3] +
								':' +
								timearr[4];
						}
						if (data.title) {
							this.originContent = data.title;
						}
						this.getNoteList(data.taskNodeId);
						this.taskNodeId = data.taskNodeId;
						this.setData({
							backgroundArr: list,
							feedPostInfo: data,
							titleName: data.nickname + '动态',
						});
						// this.getvideoFirstFrame();
					}
				});
			},

			preview(event) {
				let maparr = this.backgroundArr;
				let index = event.currentTarget.dataset.index;
				// 既有视频又有图片用这个
				uni.previewMedia({
					sources: maparr,
					// 官方文档 : 素材展示列表 是个数组对象 一个url链接 一个type 判断这是视频还是图片
					// 接收image 和 video 所以处理数据时 最好给个type 指定 image 或者 video
					current: index,
					// 当前显示的资源序号
					autoplay: true,
				});
			},
			previews(e) {
				this.videoContext = uni.createVideoContext(id, this);
				this.videoContext.requestFullScreen({
					direction: 0,
				});
			},
			fullscreenchange(e) {
				if (!e.detail.fullScreen) {
					this.videoContext.pause();
				}
			},

			toDetail() {
				let params = {
					taskNodeId: this.feedPostInfo.taskNodeId,
					nodeName: this.feedPostInfo.nodeName,
				};
				uni.navigateTo({
					url: '/pageA/pageB/community/note/notelist?taskNodeId=' +
						this.feedPostInfo.taskNodeId +
						'&nodeName=' +
						this.feedPostInfo.nodeName,
				});
			},
			details(item) {
				const params = {
					diaryId: item.diaryId,
				};
				this.$jumpPage('/pageA/pageB/community/note/onedetail', params);
				console.log('detail', item);
			},
			// changeNote(e) {
			//     const index = e.detail.current
			//     let number = this.noteList[index].timeNumber
			//     let serviceNumName = dayList.find((item) => item.value == number).label
			//     this.noteTitle = (this.noteList[index].momName || this.$defaultName) + serviceNumName.slice(2,
			//         serviceNumName.length)
			// }
		},
	};
</script>
<style scoped lang="less">
	@import './staffdetail.less';
</style>