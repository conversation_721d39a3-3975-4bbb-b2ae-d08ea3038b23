<template>
  <view class="my-works-module">
    <!-- 标签切换 -->
    <view class="header">
      <u-tabs
        bar-width="44"
        :list="activeList"
        active-color="#FF4F61"
        inactive-color="#333333"
        font-size="28"
        gutter="148"
        v-model="current"
        @change="change"
      ></u-tabs>
    </view>

    <!-- 列表内容 -->
    <scroll-view
      class="scroll-container"
      scroll-y
      :show-scrollbar="false"
      lower-threshold="100"
      @scrolltolower="handleScrollToLower"
    >
      <view class="list-content" :class="{ 'favorite-list': current === 1 }">
        <!-- 收藏列表 -->
        <template v-if="current === 1">
          <InvitationGridCard
            v-for="(item, index) in favoriteList"
            :key="index"
            :item="item"
          />
        </template>

        <!-- 我的请帖列表 -->
        <template v-else>
          <InvitationListCard
            v-for="(mine, index) in mineList"
            :key="index"
            :item="mine"
            @refresh="handleRefresh"
          />
        </template>

        <!-- 加载状态 -->
        <view v-if="loading" class="loading-view">
          <u-loading mode="circle" color="#FF4F61" size="40"></u-loading>
          数据加载中...
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted } from 'vue';
import InvitationGridCard from './InvitationGridCard.vue';
import InvitationListCard from './InvitationListCard.vue';

// 获取当前实例
const instance = getCurrentInstance();

// 定义事件
const emit = defineEmits([]);

// 响应式数据
const mineList = ref([]);
const favoriteList = ref([]);
const activeList = ref([
  {
    name: '我的请帖',
    current: 0,
  },
  {
    name: '收藏',
    current: 1,
  },
]);
const current = ref(0);
const pageNum = ref(1);
const pageSize = ref(10);
const total = ref(0);
const favoriteTotal = ref(0);
const loading = ref(false);

// 生命周期
onMounted(() => {
  getPageList();
});

// 获取我的请帖列表
const getPageList = () => {
  if (loading.value) {
    console.log('正在加载中，跳过重复请求');
    return; // 防止重复请求
  }

  console.log('开始获取我的请帖列表，页码:', pageNum.value);
  loading.value = true;

  instance.proxy.$axios
    .get(instance.proxy.$api.getRehabiliteUserList, {
      pageNum: pageNum.value,
      pageSize: pageSize.value,
    })
    .then((res) => {
      console.log('我的请帖列表请求成功:', res);
      const result = [];
      const { rows, total: totalCount } = res.data;

      // 设置总数据量
      total.value = totalCount || 0;

      rows.forEach((item) => {
        result.push({
          ...item,
          id: item?.id,
          title: item?.title || '请帖',
          total: item?.pageNum || 0,
          backGroundImage: item?.coverImage,
        });
      });
      mineList.value = mineList.value.concat(result);
      console.log('我的请帖列表更新完成，当前数量:', mineList.value.length);
    })
    .catch((error) => {
      console.error('获取我的请帖数据失败:', error);
    })
    .finally(() => {
      console.log('我的请帖列表请求完成，设置loading为false');
      loading.value = false;
    });
};

// 获取收藏列表
const getFavoriteList = () => {
  if (loading.value) {
    console.log('正在加载中，跳过重复请求');
    return; // 防止重复请求
  }

  console.log('开始获取收藏列表，页码:', pageNum.value);
  loading.value = true;

  instance.proxy.$axios
    .get(instance.proxy.$api.getMyRehabilite, {
      pageNum: pageNum.value,
      pageSize: pageSize.value,
    })
    .then((res) => {
      console.log('收藏列表请求成功:', res);
      const result = [];
      const { data, total: totalCount } = res.data;

      // 设置收藏总数据量
      favoriteTotal.value = totalCount || 0;

      data.forEach((item) => {
        result.push({
          ...item,
          id: item?.id,
          title: item?.title || '请帖',
          total: item?.pageNum || 0,
          backGroundImage: item?.coverImage,
        });
      });
      favoriteList.value = favoriteList.value.concat(result);
      console.log('收藏列表更新完成，当前数量:', favoriteList.value.length);
    })
    .catch((error) => {
      console.error('获取收藏数据失败:', error);
    })
    .finally(() => {
      console.log('收藏列表请求完成，设置loading为false');
      loading.value = false;
    });
};

// 标签切换
const change = (index) => {
  if (index != current.value) {
    console.log('标签切换，从', current.value, '切换到', activeList.value[index].current);

    // 重置loading状态，防止切换时loading状态不一致
    loading.value = false;

    current.value = activeList.value[index].current;
    pageNum.value = 1;
    mineList.value = [];
    favoriteList.value = [];
    total.value = 0;
    favoriteTotal.value = 0;

    if (current.value == 0) {
      getPageList();
    } else {
      getFavoriteList();
    }
  }
};

// 触底加载
const handleScrollToLower = () => {
  if (loading.value) {
    console.log('正在加载中，跳过触底加载');
    return; // 防止重复请求
  }

  if (current.value == 0) {
    // 我的请帖：判断已加载数据数量 < 总数据量
    if (mineList.value.length < total.value) {
      console.log('触底加载我的请帖，当前页码:', pageNum.value);
      pageNum.value = pageNum.value + 1;
      getPageList();
    } else {
      console.log('我的请帖已全部加载完成');
    }
  } else if (current.value == 1) {
    // 收藏列表：判断已加载数据数量 < 收藏总数据量
    if (favoriteList.value.length < favoriteTotal.value) {
      console.log('触底加载收藏列表，当前页码:', pageNum.value);
      pageNum.value = pageNum.value + 1;
      getFavoriteList();
    } else {
      console.log('收藏列表已全部加载完成');
    }
  }
};

// 处理刷新
const handleRefresh = () => {
  refresh();
};

// 刷新数据
const refresh = () => {
  console.log('刷新数据，当前标签:', current.value);

  // 重置loading状态
  loading.value = false;

  pageNum.value = 1;
  mineList.value = [];
  favoriteList.value = [];
  total.value = 0;
  favoriteTotal.value = 0;

  if (current.value == 0) {
    getPageList();
  } else {
    getFavoriteList();
  }
};

// 暴露方法给父组件
defineExpose({
  refresh,
});
</script>

<style scoped lang="less">
.my-works-module {
  height: 100%;
  display: flex;
  flex-direction: column;

  .header {
    padding-bottom: 10rpx;
    background-color: white;
    /deep/.u-tab-item {
      font-weight: normal !important;
      height: 70rpx !important;
      line-height: 70rpx !important;
    }
  }

  .scroll-container {
    flex: 1;
    height: 0;
    padding: 10rpx 0 120rpx 0;

    .list-content {
      width: 100%;
      display: grid;
      grid-template-columns: 1fr;
      gap: 20rpx;
      padding: 0 24rpx;
    }

    .favorite-list {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
    }

    .loading-view {
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: flex-end;
      padding: 20rpx 0;
      gap: 10rpx;
      color: #666;
    }
  }

}
</style>