<template>
  <view class="service-tabs">
    <view
      :class="activeTab === item.title ? 'service-tab--active' : 'service-tab'"
      v-for="(item, index) in serviceTabList"
      :key="index"
      @tap="onTabClick(item)"
    >
      <image
        :src="activeTab === item.title ? item.activeIcon : item.icon"
        mode=""
        class="service-tab__icon"
      />
      <view
        :class="activeTab === item.title ? 'service-tab__text--active' : 'service-tab__text'"
      >
        {{ item.title }}
      </view>
    </view>
  </view>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue'

const props = defineProps({
  activeTab: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['tab-change'])

const serviceTabList = [
  {
    icon: 'http://cdn.xiaodingdang1.com/2025/05/22/963651c3d7514d40b1c7d3471fd08360.png',
    activeIcon: 'http://cdn.xiaodingdang1.com/2025/06/23/36547b5e704349199bcc6b86ec8266b4.png',
    title: '产后康复',
  },
  {
    icon: 'http://cdn.xiaodingdang1.com/2025/05/22/667ba5b08dc642c1866ec5ca00ed9a6a.png',
    activeIcon: 'http://cdn.xiaodingdang1.com/2025/06/23/351b1ab8709b41808bdfd6d4a550e5f2.png',
    title: '护理服务',
  },
  {
    icon: 'http://cdn.xiaodingdang1.com/2025/05/22/8e15ab7a93774a08a985e1b1e36af780.png',
    activeIcon: 'http://cdn.xiaodingdang1.com/2025/06/23/c95b8683394e496e86291af332876ea9.png',
    title: '月子膳食',
  },
  {
    icon: 'http://cdn.xiaodingdang1.com/2025/05/22/5db94bbae1724b62bf4458fd753e3f7e.png',
    activeIcon: 'http://cdn.xiaodingdang1.com/2025/06/23/1c2c67e8dce24fa99114999dc8c8d30d.png',
    title: '其他服务',
  },
]

const onTabClick = (item) => {
  emit('tab-change', item)
}
</script>

<style scoped lang="less">
.service-tabs {
  display: flex;
  justify-content: space-between;
  width: 100%;
  padding: 0 20rpx 0 12rpx;
}

.service-tab {
  text-align: center;
  width: 23%;
  background: #f6f6f6;
  border-radius: 32rpx;
  padding: 22rpx 0;

  &--active {
    text-align: center;
    width: 23%;
    background: #ff4f61;
    border-radius: 32rpx;
    padding: 22rpx 0;
  }

  &__icon {
    width: 54rpx;
    height: 54rpx;
  }

  &__text {
    color: #333333;
    font-size: 22rpx;
    margin-top: 18rpx;

    &--active {
      color: #fff;
      font-size: 22rpx;
      margin-top: 18rpx;
    }
  }
}
</style>
