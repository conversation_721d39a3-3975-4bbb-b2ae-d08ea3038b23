import { ref } from 'vue';

/**
 * 海报截图功能 Composable
 * 支持 snapshot 和 canvas 两种截图方式
 */
export function usePosterCapture() {
  const isCapturing = ref(false);
  const captureError = ref(null);

  /**
   * 使用 snapshot 方式截图
   * @param {string} snapshotId - snapshot 元素的 ID
   * @param {Object} options - 截图选项
   * @returns {Promise<string>} 返回生成的文件路径
   */
  const captureBySnapshot = async (snapshotId = 'poster-snapshot', options = {}) => {
    const defaultOptions = {
      type: 'arraybuffer',
      format: 'png',
      delay: 500,
      ...options
    };

    isCapturing.value = true;
    captureError.value = null;

    try {
      uni.showLoading({
        title: '生成中...',
        mask: true,
      });

      // 等待页面渲染完成
      await new Promise(resolve => setTimeout(resolve, defaultOptions.delay));

      return new Promise((resolve, reject) => {
        // 获取 snapshot 节点
        uni
          .createSelectorQuery()
          .select(`#${snapshotId}`)
          .node()
          .exec((res) => {
            if (res[0] && res[0].node) {
              const node = res[0].node;
              console.log('获取到 snapshot 节点:', node);

              // 调用 takeSnapshot 生成图片
              node.takeSnapshot({
                type: defaultOptions.type,
                format: defaultOptions.format,
                success: (result) => {
                  console.log('snapshot 生成成功:', result);

                  try {
                    // 生成文件路径
                    const filePath = `${wx.env.USER_DATA_PATH}/poster_${new Date().getTime()}.${defaultOptions.format}`;
                    const fs = wx.getFileSystemManager();

                    // 将图片数据写入本地文件
                    fs.writeFileSync(filePath, result.data, 'binary');
                    console.log('文件写入成功:', filePath);

                    resolve(filePath);
                  } catch (error) {
                    console.error('文件写入失败:', error);
                    reject(new Error('文件写入失败'));
                  }
                },
                fail: (error) => {
                  console.error('snapshot 生成失败:', error);
                  reject(new Error('截图生成失败'));
                },
              });
            } else {
              console.error('未找到 snapshot 节点');
              reject(new Error('未找到截图节点'));
            }
          });
      });
    } catch (error) {
      console.error('截图过程出错:', error);
      captureError.value = error.message;
      throw error;
    } finally {
      isCapturing.value = false;
      uni.hideLoading();
    }
  };

  /**
   * 使用 canvas 方式截图（兼容旧版本）
   * @param {string} canvasId - canvas 元素的 ID
   * @param {Object} options - 截图选项
   * @returns {Promise<string>} 返回生成的文件路径
   */
  const captureByCanvas = async (canvasId = 'posterCanvas', options = {}) => {
    const defaultOptions = {
      delay: 500,
      ...options
    };

    isCapturing.value = true;
    captureError.value = null;

    try {
      uni.showLoading({
        title: '生成中...',
        mask: true,
      });

      // 等待页面渲染完成
      await new Promise(resolve => setTimeout(resolve, defaultOptions.delay));

      return new Promise((resolve, reject) => {
        uni.canvasToTempFilePath({
          canvasId: canvasId,
          success: (res) => {
            console.log('canvas 截图成功:', res.tempFilePath);
            resolve(res.tempFilePath);
          },
          fail: (err) => {
            console.error('canvas 截图失败:', err);
            reject(new Error('Canvas 截图失败'));
          },
        });
      });
    } catch (error) {
      console.error('Canvas 截图过程出错:', error);
      captureError.value = error.message;
      throw error;
    } finally {
      isCapturing.value = false;
      uni.hideLoading();
    }
  };

  /**
   * 通用截图方法，自动选择最佳截图方式
   * @param {Object} options - 截图选项
   * @returns {Promise<string>} 返回生成的文件路径
   */
  const capture = async (options = {}) => {
    const { method = 'snapshot', ...otherOptions } = options;

    try {
      if (method === 'snapshot') {
        return await captureBySnapshot(otherOptions.snapshotId, otherOptions);
      } else if (method === 'canvas') {
        return await captureByCanvas(otherOptions.canvasId, otherOptions);
      } else {
        throw new Error(`不支持的截图方式: ${method}`);
      }
    } catch (error) {
      // 显示错误提示
      uni.showToast({
        title: error.message || '生成失败',
        icon: 'none',
      });
      throw error;
    }
  };

  return {
    // 状态
    isCapturing,
    captureError,
    
    // 方法
    capture,
    captureBySnapshot,
    captureByCanvas,
  };
}
