<template>
  <view
    class="club-header"
    :style="{
      backgroundImage: `url(${clubInfo.clubBackgroundPhotos[0]})`,
    }"
  >
    <view class="club-header__overlay">
      <view class="club-header__content">
        <view class="club-header__info">
          <view class="club-header__title">
            {{ clubInfo.clubName }}
          </view>
          <view class="club-header__tags">
            <view
              class="club-header__tag"
              v-for="(item, index) in clubInfo.clubTagNames"
              :key="index"
            >
              {{ item }}
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
const props = defineProps({
  clubInfo: {
    type: Object,
    default: () => ({
      clubName: '',
      clubTagNames: [],
      clubDescription: '',
      clubBackgroundPhotos: [],
    })
  }
})
</script>

<style scoped lang="less">
.club-header {
  width: 100%;
  height: 402rpx;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;

  &__overlay {
    background: rgba(0, 0, 0, 0.3);
    height: 402rpx;
  }

  &__content {
    padding: 180rpx 30rpx 78rpx 30rpx;
  }

  &__info {
    display: flex;
    flex-direction: column;
  }

  &__title {
    color: #fff;
    font-size: 56rpx;
    font-weight: bold;
  }

  &__tags {
    display: flex;
    margin-top: 20rpx;
  }

  &__tag {
    padding: 8rpx 12rpx;
    border-radius: 10rpx;
    background: #fff;
    color: #ff4f61;
    font-size: 24rpx;
    font-weight: bold;
    margin-right: 20rpx;
  }
}
</style>
