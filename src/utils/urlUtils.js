/**
 * URL工具函数
 */

/**
 * 构建URL参数字符串
 * @param {Object} params - 参数对象
 * @returns {string} - 参数字符串，如 "?key1=value1&key2=value2"
 */
export const buildUrlParams = (params) => {
  if (!params || typeof params !== 'object') {
    return '';
  }
  
  const paramArray = [];
  for (const key in params) {
    if (params.hasOwnProperty(key) && params[key] !== undefined && params[key] !== null && params[key] !== '') {
      paramArray.push(`${key}=${encodeURIComponent(params[key])}`);
    }
  }
  
  return paramArray.length > 0 ? '?' + paramArray.join('&') : '';
};

/**
 * 解析URL参数字符串
 * @param {string} search - 参数字符串，如 "?key1=value1&key2=value2"
 * @returns {Object} - 参数对象
 */
export const parseUrlParams = (search) => {
  if (!search || typeof search !== 'string') {
    return {};
  }
  
  // 移除开头的 ?
  const queryString = search.startsWith('?') ? search.slice(1) : search;
  
  if (!queryString) {
    return {};
  }
  
  const params = {};
  const pairs = queryString.split('&');
  
  for (const pair of pairs) {
    const [key, value] = pair.split('=');
    if (key) {
      params[key] = value ? decodeURIComponent(value) : '';
    }
  }
  
  return params;
};

/**
 * 构建导航URL
 * @param {string} path - 页面路径
 * @param {Object} params - 参数对象
 * @returns {string} - 完整的URL
 */
export const buildNavigateUrl = (path, params = {}) => {
  if (!path) {
    return '';
  }
  
  return path + buildUrlParams(params);
};

/**
 * 安全的页面跳转
 * @param {string} path - 页面路径
 * @param {Object} params - 参数对象
 * @param {Object} options - 跳转选项
 */
export const navigateToPage = (path, params = {}, options = {}) => {
  const url = buildNavigateUrl(path, params);
  
  console.log('页面跳转:', { path, params, url });
  
  const navigateOptions = {
    url,
    ...options
  };
  
  // 根据跳转类型选择不同的方法
  if (options.type === 'redirect') {
    uni.redirectTo(navigateOptions);
  } else if (options.type === 'reLaunch') {
    uni.reLaunch(navigateOptions);
  } else if (options.type === 'switchTab') {
    uni.switchTab(navigateOptions);
  } else {
    uni.navigateTo(navigateOptions);
  }
};

/**
 * 编码页面标题用于URL传递
 * @param {string} title - 标题
 * @returns {string} - 编码后的标题
 */
export const encodePageTitle = (title) => {
  if (!title || typeof title !== 'string') {
    return '';
  }
  
  return encodeURIComponent(title);
};

/**
 * 解码页面标题
 * @param {string} encodedTitle - 编码的标题
 * @returns {string} - 解码后的标题
 */
export const decodePageTitle = (encodedTitle) => {
  if (!encodedTitle || typeof encodedTitle !== 'string') {
    return '';
  }
  
  try {
    return decodeURIComponent(encodedTitle);
  } catch (error) {
    console.warn('解码标题失败:', error);
    return encodedTitle;
  }
};
