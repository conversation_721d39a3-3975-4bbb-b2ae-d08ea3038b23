<template>
  <view class="team-module" v-if="hasStaffPageList">
    <!-- 标题区域 -->
    <view class="section-header" @tap="handleMoreClick">
      <view class="section-header__title">护理团队</view>
      <view class="section-header__more">
        <view>更多</view>
        <u-icon name="arrow-right" color="#aaaaaa" size="26"></u-icon>
      </view>
    </view>

    <!-- 护理团队列表 -->
    <scroll-view :show-scrollbar="false" :scroll-x="true" class="nurse-tab">
      <view
        class="nurse-tab1"
        @tap="handleStaffClick"
        :data-staffid="item.staffId"
        v-for="(item, index) in staffPageList"
        :key="index"
      >
        <view class="nurse-img">
          <image  mode="aspectFill" :src="item.staffPhotos[0]" />
        </view>
        <view class="nurse-text">{{ item.staffName }}</view>
        <view class="nurse-text1">{{ item.staffPost }}</view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted, getCurrentInstance } from 'vue';

// 响应式数据
const staffPageList = ref([]);

// 获取当前实例
const instance = getCurrentInstance();

// 计算属性
const hasStaffPageList = computed(() => {
  return staffPageList.value && staffPageList.value.length > 0;
});

// 生命周期
onMounted(() => {
  initTeamData();
});

// 初始化护理团队数据
const initTeamData = () => {
  staffPage();
};

// 获取护理团队数据
const staffPage = () => {
  const data = {
    pageSize: 100,
    pageNum: 1,
    auditStatus: 1,
    isShow: true,
  };
  instance.proxy.$axios
    .get(instance.proxy.$api.staffPage, data)
    .then((res) => {
      if (res.data.code == 200) {
        staffPageList.value = res.data.rows;
      }
    })
    .catch((error) => {
      console.error('获取护理团队数据失败:', error);
    });
};

// 事件处理 - 内部实现页面跳转
const handleMoreClick = () => {
  uni.navigateTo({
    url: '/pageA/pageB/home/<USER>',
  });
};

const handleStaffClick = (e) => {
  const staffId = e.currentTarget.dataset.staffid;
  uni.navigateTo({
    url: '/pageA/pageB/home/<USER>/detail?staffId=' + staffId,
  });
};
</script>

<style scoped>
/* ===== 护理团队模块样式 ===== */
.team-module {
  width: 100%;
  margin: 40rpx 0;
}

/* 标题区域 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 40rpx 0 20rpx 0;
}

.section-header__title {
  font-weight: bold;
  font-size: 34rpx;
  color: #333333;
}

.section-header__more {
  display: flex;
  color: #aaaaaa;
  font-size: 28rpx;
  align-items: center;
}

.section-header__arrow {
  padding-left: 8rpx;
}

.section-header__arrow image {
  width: 8rpx;
  height: 18rpx;
}

/* 护理团队列表 - 与原有nurseTab样式一致 */
.nurse-tab {
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
  white-space: nowrap;
  margin: 20rpx 0;
}

.nurse-tab1 {
  display: inline-block;
  text-align: center;
  margin-right: 20rpx;
}

.nurse-img image {
  border: 1rpx solid #ff4f61;
  width: 150rpx;
  height: 150rpx;
  border-radius: 100rpx;
}

.nurse-text {
  color: #333333;
  font-size: 28rpx;
}

.nurse-text1 {
  color: #777777;
  font-size: 24rpx;
}
</style>
