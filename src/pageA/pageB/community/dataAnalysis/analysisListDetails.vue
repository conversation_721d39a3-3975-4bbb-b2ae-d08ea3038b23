<template>
	<navigation title="最近访问" :background="bgColor"></navigation>
	<view class="main">
		<view class="content">
			<view class="head">
				<view class="head1">
					<image
						:src="item.avatar?item.avatar:'http://cdn.xiaodingdang1.com/2024/11/08/6d8894bb65e1418880530c88baf5b001.png'"
						mode="" style="width: 120rpx;height: 120rpx;border-radius: 100rpx;"></image>
					<view class="head1_1">
						<view class="head1_2">
							{{item.nickname?item.nickname:'微信用户'}}
						</view>
						<view class="head1_3">
							{{item.tel}}
						</view>
					</view>
				</view>
				<view class="head2">
					<image src="http://cdn.xiaodingdang1.com/2024/11/14/42cfd22b83634547ac4ac7981359c24a.png" mode=""
						style="width: 116rpx;height: 80rpx;" @click="ringup(item.tel)"/>
				</view>
			</view>
			<view class="visit">
				<view class="visitHead">
					<view class="visitHead1">
						<view class="visitHead1_1">
							<image src="http://cdn.xiaodingdang1.com/2024/11/14/d07835d7d00547bb8fa933e9bdc08b13.png"
								mode="" style="width: 24rpx;height: 24rpx;margin-right: 5rpx;" />
							<view class="visitHead1_2">
								访问累计
							</view>
						</view>
						<view>
							<text>
								共{{trajectoryTop?.LJFW[0].number}}次
							</text><text>总用时{{trajectoryTop?.LJFW[0].durationStr}}</text>
						</view>
					</view>
					<view class="visitHeads2">
						<view class="visitHead2">
							<view class="visitHead2_1">
								<view class="fgx">
								</view>
								<view class="visitHead2_2">
									访问详情
								</view>
							</view>
							<view class="visitHead2_3" @click="visitDetails()">
								<view class="visitHead2_4">
									查看详情
								</view>
								<image
									src="http://cdn.xiaodingdang1.com/2025/01/06/6e3deb0f6bbb4f2aa485ec20ec381ac9.png"
									mode="" style="width: 30rpx;height: 30rpx;" />
							</view>
						</view>
						<view class="visitHead3">
							<view v-for="item,index in FWXQ" :key="index">
								<view class="visitHead4" v-if="index<=4">
									<view class="visitHead4_1">
										{{item.lastTime}}
									</view>
									<view class="visitHead4_2">
										访问了{{item.title}}
									</view>
									<view class="visitHead4_3">
										浏览{{item.durationStrMinute}}
									</view>
								</view>
							</view>
						</view>
					</view>
					<view class="analysis">
						<view class="analysis1">
							<view class="fgx">
							</view>
							<view class="analysis1_1">
								门店分析
							</view>
						</view>
						<view class="rule"></view>
						<view class="analysisTitle">访问最多</view>
						<view class="analysis2">
							<view class="analysis3" v-for="item,index in trajectoryTop.FWZD" :key="index">
								<view class="analysis3_1" id="analysis3_1">
									{{item.title}}
								</view>
								<view class="analysis3_2">
									{{item.number}}次
								</view>
							</view>
						</view>
						<view class="analysisTitle">访问时间</view>
						<view class="analysis2">
							<view class="analysis3" v-for="item,index in trajectoryTop.FWSJ" :key="index">
								<view class="analysis3_1" id="analysis3_1">
									{{item.title}}
								</view>
								<view class="analysis3_2">
									{{item.durationStrMinute}}
								</view>
							</view>
						</view>
						<view class="analysisTitle">最后访问</view>
						<view class="analysis2">
							<view class="analysis3" v-for="item,index in trajectoryTop.ZHFW" :key="index">
								<view class="analysis3_1" id="analysis3_1">
									{{item.title}}
								</view>
								<view class="analysis3_2">
									{{item.lastTime}}
								</view>
							</view>
						</view>
					</view>
					<view class="analysis">
						<view class="analysis1">
							<view class="fgx">
							</view>
							<view class="analysis1_1">
								社区分析
							</view>
						</view>
						<view class="rule"></view>
						<view class="analysisTitle">访问次数</view>
						<view class="analysis2">
							<view class="analysis3" v-for="item,index in trajectoryTop.FWCSDynamics" :key="index"
								v-if="trajectoryTop?.FWCSDynamics?.length">
								<view class="analysis3_1" id="analysis3_1">
									{{item.title}}
								</view>
								<view class="analysis3_2">
									{{item.number}}次
								</view>
							</view>
							<view v-if="trajectoryTop?.FWCSDynamics?.length==0" class="analyse1">
								<view class="analyse1_3">
									暂无访问记录
								</view>
							</view>
						</view>
						<view class="analysisTitle">访问时间</view>
						<view class="analysis2">
							<view class="analysis3" v-for="item,index in trajectoryTop.FWSJDynamics" :key="index"
								v-if="trajectoryTop?.FWSJDynamics?.length>0">
								<view class="analysis3_1" id="analysis3_1">
									{{item.title}}
								</view>
								<view class="analysis3_2">
									38次
								</view>
							</view>
							<view v-if="trajectoryTop?.FWSJDynamics.length==0" class="analyse1">
								<view class="analyse1_3">
									暂无访问记录
								</view>
							</view>
						</view>
						<view class="analysisTitle">最后访问</view>
						<view class="analysis2">
							<view class="analysis3" v-for="item,index in trajectoryTop.ZHFWDynamics" :key="index"
								v-if="trajectoryTop?.ZHFWDynamics?.length>0">
								<view class="analysis3_1" id="analysis3_1">
									{{item.title}}
								</view>
								<view class="analysis3_2">
									{{item.lastTime}}
								</view>
							</view>
							<view v-if="trajectoryTop?.ZHFWDynamics.length==0" class="analyse1">
								<view class="analyse1_3">
									暂无访问记录
								</view>
							</view>
						</view>
						<view class="step" v-for="item,index in ZHFWDTDynamics" :key="index">
							<view class="step1">
								{{item.lastTime}}
							</view>
							<view class="step2">
								<view class="step2_3">
									<view class="step2_1">
										浏览了《{{item.description}}》的动态
									</view>
									<view class="step2_2">
										浏览{{item.durationStrMinute}}
									</view>
								</view>
							</view>
						</view>
					</view>
					<view class="analysis">
						<view class="analysis1">
							<view class="fgx">
							</view>
							<view class="analysis1_1">
								访问频率
							</view>
						</view>
						<view class="rule"></view>
						<canvas canvas-id="tJSkCibHugJJCyVPUIvGxTnvDkoMJSsy" id="tJSkCibHugJJCyVPUIvGxTnvDkoMJSsy"
							type="2d" style="height:300px;padding: 0;margin: 0;" @touchend="tap" />
						<canvas canvas-id="tJSkCibHugJJCyVPUIvGxTnvDkoMJSsys" id="tJSkCibHugJJCyVPUIvGxTnvDkoMJSsys"
							type="2d" style="height:300px;padding: 0;margin: 0;" @touchend="taps" />
					</view>
					<view class="analysis">
						<u-tabs name="cate_name" count="cate_count" :list="list" :is-scroll="false" :current="current"
							@change="change"></u-tabs>
						<view class="comments">
							<view class="comment">
								<view class="commentImg">
									<image
										:src="item.avatar?item.avatar:'http://cdn.xiaodingdang1.com/2024/11/08/6d8894bb65e1418880530c88baf5b001.png'"
										mode="" />
								</view>
								<view class="comment1">
									<view class="comment1_1">
										{{item.nickname}}
									</view>
									<view class="comment1_2">
										{{item.lastLoginTime}}
									</view>
								</view>
							</view>
							<view class="commentTitle">
								{{nav_type==0?'收藏了':nav_type==1?'点赞了':nav_type==2?'评论了':''}}
							</view>
							<view class="comment3">
								<view class="comment2" v-for="item,index in dataList" :key="index">
									<view class="comment2_1">
										<image
											:src="item.imgUrl?item.imgUrl:'http://cdn.xiaodingdang1.com/2024/11/08/6d8894bb65e1418880530c88baf5b001.png'"
											mode="" />
									</view>
									<view class="comment2_2">
										<view class="comment2_5">
											<text class="comment2_3">{{item.senderName}}：</text>
											<text class="comment2_4">{{item.senderContent}}</text>
										</view>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import pieChartVue from '@/components/pieChart'
	import uCharts from '@qiun/ucharts';
	var uChartsInstance = {};
	export default {
		components: {
			pieChartVue
		},
		data() {
			return {
				cWidth: 750,
				cHeight: 500,
				pixelRatio: 2,
				FWXQ: [],
				trajectoryTop: {
					FWSJDynamics: [],
					ZHFWDynamics: []
				},
				ZHFWDTDynamics: [],
				fwplsj: [],
				fwplcs: [],
				item: '',
				nav_type: 0,
				dataList: [],
				lists: [1, 2, 3, 4, 5],
				bgColor: '',
				list: [{
					cate_name: '收藏'
				}, {
					cate_name: '点赞'
				}, {
					cate_name: '评论'
				}],
				current: 0,
				userId: '1855142617252163586'
			}
		},
		onLoad(options) {
			let item = JSON.parse(options.item)
			this.setData({
				item: item
			})
			this.trajectoryTops()
			this.collect()
		},
		onShow() {

		},
		onReady() {
			//这里的 750 对应 css .charts 的 width
			this.cWidth = uni.upx2px(650);
			//这里的 500 对应 css .charts 的 height
			this.cHeight = uni.upx2px(500);
			this.pixelRatio = uni.getSystemInfoSync().pixelRatio;
		},
		mounted() {

		},
		methods: {
			visitDetails(){
				  let FWXQ=JSON.stringify(this.FWXQ)
				uni.navigateTo({
					url:'/pageA/pageB/community/dataAnalysis/visitDetails?item='+FWXQ
				})
			},
			ringup(tel){
				// 拨打电话
				const phoneNumber =tel; // 客户的电话号码
				uni.makePhoneCall({
				    phoneNumber: phoneNumber,
				    success: () => {
				        console.log('拨打电话成功！');
				    },
				    fail: () => {
				        console.log('拨打电话失败！');
				    }
				});
			},
			getServerDatas() {
				//模拟从服务器获取数据时的延时
				setTimeout(() => {
					//模拟服务器返回数据，如果数据格式和标准格式不同，需自行按下面的格式拼接
					let res = {
						series: [{
							data: this.fwplsj
						}]
					};
					this.drawCharts('tJSkCibHugJJCyVPUIvGxTnvDkoMJSsys', res);
				}, 500);
			},
			getServerData() {
				//模拟从服务器获取数据时的延时
				setTimeout(() => {
					//模拟服务器返回数据，如果数据格式和标准格式不同，需自行按下面的格式拼接
					let res = {
						series: [{
							data: this.fwplcs,
						}]
					};
					this.drawCharts('tJSkCibHugJJCyVPUIvGxTnvDkoMJSsy', res);
				}, 500);
			},
			drawCharts(id, data) {
				const query = uni.createSelectorQuery().in(this);
				query.select('#' + id).fields({
					node: true,
					size: true
				}).exec(res => {
					if (res[0]) {
						const canvas = res[0].node;
						const ctx = canvas.getContext('2d');
						canvas.width = res[0].width * this.pixelRatio;
						canvas.height = res[0].height * this.pixelRatio;
						uChartsInstance[id] = new uCharts({
							type: "pie",
							context: ctx,
							width: this.cWidth * this.pixelRatio,
							height: this.cHeight * this.pixelRatio,
							series: data.series,
							pixelRatio: this.pixelRatio,
							animation: true,
							background: "#FFFFFF",
							color: ["#953FFF", "#FA8710", "#1DBE86", "#3FC5FF", "#F56A75", "#D93FFF"],
							padding: [5, 5, 5, 5],
							enableScroll: false,
							extra: {
								pie: {
									activeOpacity: 0.5,
									activeRadius: 10,
									offsetAngle: 0,
									labelWidth: 15,
									border: true,
									borderWidth: 3,
									borderColor: "#FFFFFF",
								}
							}
						});
					} else {
						console.error("[uCharts]: 未获取到 context");
					}
				});
			},

			tap(e) {
				uChartsInstance[e.target.id].touchLegend(e);
				uChartsInstance[e.target.id].showToolTip(e);
			},
			taps(e) {
				uChartsInstance[e.target.id].touchLegend(e);
				uChartsInstance[e.target.id].showToolTip(e);
			},
			collect() { //收藏
				let left = this
				let data = {
					userId: left.userId
				}
				this.$axios.get(this.$api.collect, data).then((res) => {
					if (res.data.code == 200) {
						left.setData({
							dataList: res.data.data
						})
					} else {
						wx.showToast({
							title: res.data.msg,
							icon: 'none',
							duration: 2000 //持续的时间
						})
					}
				})
			},
			like() { //点赞
				let left = this
				let data = {
					userId: left.userId
				}
				this.$axios.get(this.$api.like, data).then((res) => {
					if (res.data.code == 200) {
						left.setData({
							dataList: res.data.data
						})
					} else {
						wx.showToast({
							title: res.data.msg,
							icon: 'none',
							duration: 2000 //持续的时间
						})
					}
				})
			},
			comment() { //评论
				let left = this
				let data = {
					userId: left.userId
				}
				this.$axios.get(this.$api.comment, data).then((res) => {
					if (res.data.code == 200) {
						left.setData({
							dataList: res.data.data
						})
					} else {
						wx.showToast({
							title: res.data.msg,
							icon: 'none',
							duration: 2000 //持续的时间
						})
					}
				})
			},
			trajectoryTops() { //客户轨迹分析-头部信息
				let left = this
				let data = {
					userId: this.userId
				}
				this.$axios.get(this.$api.trajectoryTop, data).then((res) => {
					if (res.data.code == 200) {
						let data = [{
								value: res.data.data.FWPLCS[0].number,
								name: res.data.data.FWPLCS[0].title + res.data.data.FWPLCS[0].number + '次',
								itemStyle: {
									color: '#3FC5FF'
								}
							},
							{
								value: res.data.data.FWPLCS[1].number,
								name: res.data.data.FWPLCS[1].title + res.data.data.FWPLCS[1].number + '次',
								itemStyle: {
									color: '#953FFF'
								}
							},
							{
								value: res.data.data.FWPLCS[2].number,
								name: res.data.data.FWPLCS[2].title + res.data.data.FWPLCS[2].number + '次',
								itemStyle: {
									color: '#FA8710'
								}
							},
							{
								value: res.data.data.FWPLCS[3].number,
								name: res.data.data.FWPLCS[3].title + res.data.data.FWPLCS[3].number + '次',
								itemStyle: {
									color: '#1DBE86'
								}
							},
							{
								value: res.data.data.FWPLCS[4].number,
								name: res.data.data.FWPLCS[4].title + res.data.data.FWPLCS[4].number + '次',
								itemStyle: {
									color: '#F56A75'
								}
							},
						]

						let data1 = [{
								value: res.data.data.FWPLSJ[0].duration,
								name: res.data.data.FWPLSJ[0].title + res.data.data.FWPLSJ[0].duration + '分钟',
								itemStyle: {
									color: '#3FC5FF'
								}
							},
							{
								value: res.data.data.FWPLSJ[1].duration,
								name: res.data.data.FWPLSJ[1].title + res.data.data.FWPLSJ[1].duration + '分钟',
								itemStyle: {
									color: '#953FFF'
								}
							},
							{
								value: res.data.data.FWPLSJ[2].duration,
								name: res.data.data.FWPLSJ[2].title + res.data.data.FWPLSJ[2].duration + '分钟',
								itemStyle: {
									color: '#FA8710'
								}
							},
							{
								value: res.data.data.FWPLSJ[3].duration,
								name: res.data.data.FWPLSJ[3].title + res.data.data.FWPLSJ[3].duration + '分钟',
								itemStyle: {
									color: '#1DBE86'
								}
							},
							{
								value: res.data.data.FWPLSJ[4].duration,
								name: res.data.data.FWPLSJ[4].title + res.data.data.FWPLSJ[3].duration + '分钟',
								itemStyle: {
									color: '#F56A75'
								}
							},
						]
						let ZHFWDTDynamics = res.data.data.ZHFWDTDynamics
						if (res.data.data.ZHFWDTDynamics.length > 0) {
							ZHFWDTDynamics.forEach(item => {
								let lastTime = item.lastTime.slice(0, 10);
								item.lastTime = lastTime
							})
						}
						this.setData({
							FWXQ: res.data.data.FWXQ,
							trajectoryTop: res.data.data,
							fwplcs: data,
							fwplsj: data1,
							ZHFWDTDynamics: ZHFWDTDynamics
						})
						this.getServerData()
						this.getServerDatas()
					} else {
						wx.showToast({
							title: res.data.msg,
							icon: 'none',
							duration: 2000 //持续的时间
						})
					}
				})
			},
			change(index) {
				this.current = index;
				if (this.nav_type === index || index === undefined) {
					return false;
				} else {
					this.setData({
						nav_type: index
					})
					if (index == 0) {
						this.collect() //收藏
					}
					if (index == 1) {
						this.like() //点赞
					}
					if (index == 2) {
						this.comment() //评论
					}
				}
			},
			ringup(tel) {
				// 拨打电话
				const phoneNumber = tel; // 客户的电话号码
				uni.makePhoneCall({
					phoneNumber: phoneNumber,
					success: () => {
						console.log('拨打电话成功！');
					},
					fail: () => {
						console.log('拨打电话失败！');
					}
				});
			},
			getlongestList(module) {
				//访问时间最长
				let data = {
					pageSize: 10,
					pageNum: this.pageNum,
					orderBy: 'viewTime',
					module: module
				};
				this.$axios.get(this.$api.mList, data).then((res) => {
					if (res.data.code == 200) {
						let data = res.data.rows
						this.types = data.length == 10 ? 1 : 2;
						let lastList = this.lastList.concat(data);
						this.setData({
							lastList: lastList
						});
					}
				});
			},
			getmaxList(module) {
				//浏览次数最多
				let data = {
					pageSize: 10,
					pageNum: this.pageNum,
					orderBy: 'viewNum',
					module: module
				};
				this.$axios.get(this.$api.mList, data).then((res) => {
					if (res.data.code == 200) {
						let data = res.data.rows
						this.types = data.length == 10 ? 1 : 2;
						let lastList = this.lastList.concat(data);
						this.setData({
							lastList: lastList
						});
					}
				});
			},

		},
		onPageScroll: function(e) {
			if (e.scrollTop > 0) {
				this.setData({
					bgColor: '#ffffff'
				});
			} else if (e.scrollTop < 2) {
				this.setData({
					bgColor: ''
				});
			}
		},
		onReachBottom() {

		}
	}
</script>

<style lang="less" scoped>
	@import './analysisListDetails.less';
</style>