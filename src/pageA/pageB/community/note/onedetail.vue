<template>
    <view class="note-list">
        <view class="card">
            <service-card-vue :item="pageList"></service-card-vue>
            <img-content-area-vue :showContent="true" :listData="pageList" :index="index"></img-content-area-vue>
        </view>
        <!-- <u-back-top :scroll-top="scrollTop" top="1000"></u-back-top> -->
    </view>
</template>
<script setup>
    import imgContentAreaVue from "@/components/imgContentArea.vue";
    import serviceCardVue from "@/pageA/pageB/community/component/serviceCard.vue";
    import {
        onShow,
        onHide,
        onLoad,
        onPageScroll,
        onReachBottom,
        onShareAppMessage,
        onShareTimeline
    } from "@dcloudio/uni-app";
    import {
        ref,
        nextTick,
        reactive,
        getCurrentInstance,
        onMounted
    } from "vue";
    import {
        useStore
    } from "vuex";
    const store = useStore();
    const instance = getCurrentInstance();
    const App = instance.appContext.config.globalProperties
    onShareAppMessage(() => {

    })
    onShareTimeline(() => {

    })
    // const bgTitle = ref('胡哥')
    // const scrollTop = ref(0)
    const pageList = ref([])
    const types = ref(1)
    const pageNum = ref(1)
    const pageSize = ref(4)
    const diaryId = ref('')
    onLoad((options) => {
        console.log('optionssssss', options)
        diaryId.value = options.diaryId
        // bgTitle.value = options.nodeName
    })
    onMounted(async () => {
        await getList()
    })
    onReachBottom(() => {
        if (types.value == 1) {
            pageNum.value = pageNum.value + 1
            getList();
        }
    })
    // onPageScroll((e) => {
    //     scrollTop.value = e.scrollTop
    // })
    const details = () => {

    }
    const getList = async () => {
        let data = {
            diaryId: diaryId.value
        };
        store.commit("m_user/changeLoading", true);
        const res = await App.$axios.get(App.$api.getDiaryDetail, data)
        try {
            if (res.data.code == 200) {
                let data = res.data.data;
                if (data.imgs && data.imgs.length > 0) {
                    data.contentPhotos = data.imgs
                }
                if (data.content.length > 115) {
                    data.packUpShow = false;
                    data.moreBtns = true;
                    data.moreBtn = true;
                } else {
                    data.packUpShow = false;
                    data.moreBtns = false;
                    data.moreBtn = false;
                }
                pageList.value = data;
            }
        } catch (e) {

        }
        store.commit("m_user/changeLoading", false);
    }
</script>
<style lang="less" scoped>
    @import "./onedetail.less";
</style>