import subscriptionManager, { TEMPLATE_TYPES } from '@/utils/subscriptionManager.js'

export function useSubscription() {
  
  // 预加载订阅模板
  const preloadSubscriptionTemplates = async () => {
    try {
      await subscriptionManager.preloadCommonTemplates()
      console.log('订阅模板预加载完成')
    } catch (error) {
      console.error('订阅模板预加载失败:', error)
    }
  }

  // 客服订阅处理
  const handleCustomerServiceSubscription = async () => {
    try {
      await subscriptionManager.requestSubscription(
        [
          TEMPLATE_TYPES.CUSTOMER_SERVICE_MESSAGE_UNREAD,
          TEMPLATE_TYPES.CLIENT_CONSULTATION_REMINDER,
        ],
        {
          onSuccess: (res) => {
            console.log('客服订阅成功', res)
            // 订阅成功后跳转到客服页面
            uni.navigateTo({
              url: '/subPackages/customerService/pages/serviceReception',
            })
          },
          onFail: (err) => {
            console.error('客服订阅失败', err)
            // 即使订阅失败也允许进入客服页面
            uni.navigateTo({
              url: '/subPackages/customerService/pages/serviceReception',
            })
          },
          onError: (error) => {
            console.error('客服订阅出错', error)
            uni.showToast({
              title: '订阅消息失败',
              icon: 'none',
            })
            // 出错时也允许进入客服页面
            uni.navigateTo({
              url: '/subPackages/customerService/pages/serviceReception',
            })
          },
        },
      )
    } catch (error) {
      console.error('客服订阅异常', error)
      // 发生异常时直接跳转到客服页面
      uni.navigateTo({
        url: '/subPackages/customerService/pages/serviceReception',
      })
    }
  }

  // 消息订阅处理
  const handleMessageSubscription = async (jumpCallback, resetCallback) => {
    try {
      await subscriptionManager.requestSubscription(
        [TEMPLATE_TYPES.CLIENT_CONSULTATION_REMINDER],
        {
          onSuccess: (res) => {
            console.log('消息订阅成功', res)
            jumpCallback()
            resetCallback()
          },
          onFail: (err) => {
            console.error('消息订阅失败', err)
            jumpCallback()
            resetCallback()
          },
          onError: (error) => {
            console.error('消息订阅出错', error)
            uni.showToast({
              title: '订阅消息失败',
              icon: 'none',
            })
            jumpCallback()
            resetCallback()
          },
        },
      )
    } catch (error) {
      console.error('消息订阅异常', error)
      jumpCallback()
      resetCallback()
    }
  }

  return {
    preloadSubscriptionTemplates,
    handleCustomerServiceSubscription,
    handleMessageSubscription
  }
}
