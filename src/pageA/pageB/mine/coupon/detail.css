.main {
    background: #f8f9f9;
    padding: 20rpx 24rpx;
}

.head {
    display: flex;
    align-items: center;
}

.headTitle {
    color: #333333;
    font-size: 36rpx;
    margin-left: 4rpx;
}

.content {
    background: #ffffff;
    padding: 24rpx 24rpx;
    border-radius: 20rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.content1 {
    display: flex;
}

.content1_1 {
    display: flex;
    margin: 16rpx 0 26rpx 0;
}

.content1_2 {
    color: #aaaaaa;
    font-size: 24rpx;
}

.fgx {
    width: 2rpx;
    height: 20rpx;
    background: #ebebeb;
    margin: 0 8rpx;
}

.content1_3 {
    color: #333333;
    font-size: 30rpx;
}

.content2 {
    margin-left: 16rpx;
}

.content1_4 {
    color: #333333;
    font-size: 20rpx;
}

.cancel {
    background: #ffffff;
    padding: 24rpx 24rpx;
    border-radius: 20rpx;
    margin-top: 20rpx;
}

.cancelTitle {
    font-size: 28rpx;
    color: #333333;
}

.cancel1 {
    font-size: 28rpx;
    color: #333333;
    padding: 48rpx 0;
    text-align: center;
    font-weight: bold;
}

.cancel2 {
    font-size: 28rpx;
    color: #aaaaaa;
    padding: 48rpx 0;
    text-align: center;
    font-weight: bold;
    text-decoration: line-through;
}

.status {
    display: flex;
}

.status1 {
    color: #777777;
    font-size: 28rpx;
}

.sum {
    color: #333333;
    font-size: 40rpx;
    font-weight: bold;
}