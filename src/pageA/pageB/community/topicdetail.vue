<template>
    <view>
        <view class="main">
            <navigation :background="bgColor" :color="color" title="话题详情" :isSowArrow="true"></navigation>
            <view class="mainss">
                <view class="headTitle1">
                    <image :src="topicInfo.topicPhotos[0]" mode=""
                        style="width: 130rpx; height: 130rpx; border-radius: 10rpx" />
                    <view class="headTitle1_1">#{{ topicInfo.topicName }}</view>
                </view>
                <view class="topic1Text3">
                    <view class="topicImg">
                        <!-- <image wx:for="{{topicInfo.userList}}" wx:key="index" src="{{item.avatar}}" class="topic1Text3topic1Text3" style='transform:translateX({{-index*15}}rpx)'></image> -->
                        <view class="avatar-list-stacked">
                            <text class="avatar"
                                :style="'background: url(' + item.avatar || $defaultAvatar + ') center center;background-repeat:no-repeat;background-size: cover; background-position:0px 0px;  '"
                                v-for="(item, index) in topicInfo.userList" :key="index"></text>
                        </view>
                        <view class="topic1Text3_1">
                            {{ topicInfo.postNum > 99 ? '99' : topicInfo.postNum }}
                            <text v-if="topicInfo.postNum > 99">+</text>
                            人参与
                        </view>
                    </view>
                    <view class="subscription" @tap="topicBookmark" :data-topicid="topicInfo.topicId"
                        v-if="!topicInfo.isSubscribe">订阅</view>
                    <view class="subscribed" v-if="topicInfo.isSubscribe">
                        <image src="http://cdn.xiaodingdang1.com/2024/05/11/08027776147f43bc8d77df8d1221288dpng" mode=""
                            style="width: 48rpx; height: 48rpx" />
                        <view>已订阅</view>
                    </view>
                </view>
                <view class="contentTab">
                    <view :class="index + 1 == currentIndex ? 'title-sel-selected' : 'title-sel'"
                        :data-index="index + 1" @tap="titleClick" v-for="(item, index) in tabList" :key="index">
                        {{ item.name }}

                        <hr class="line-style" />
                    </view>
                </view>
                <view class="discuss" v-for="(item, index) in communityRecord" :key="index">
                    <view class="discus">
                        <view class="discuss1">
                            <view class="discuss1_1">
                                <image :src="item.avatar || $defaultAvatar" mode=""
                                    style="width: 80rpx; height: 80rpx; border-radius: 100rpx" />
                                <view class="discuss1_2">
                                    <view class="discuss1_3">
                                        {{ item.nickname || $defaultName }}
                                    </view>
                                    <view class="discuss1_4">
                                        <text>{{ item.hisTimeStr }}</text>
                                        <text class="discussName">{{ item.postIp }}</text>
                                    </view>
                                </view>
                            </view>
                            <view class="discuss1_5" @tap="communityBookmark" :data-postid="item.postId"
                                :data-index="index">
                                <!-- <view v-if="!item.isBookmark">
                                    <image
                                        src="http://cdn.xiaodingdang1.com/2024/07/03/c5f552fafc8e40afa72ff08fb99dd398.png"
                                        mode="" style="width: 40rpx; height: 40rpx" />
                                </view>
                                <view v-if="item.isBookmark">
                                    <image
                                        src="http://cdn.xiaodingdang1.com/2024/07/03/008774f5f0d543fa8ac91c3271e5c96d.png"
                                        mode="" style="width: 40rpx; height: 40rpx" />
                                </view>
                                <view class="discussTitle">收藏</view> -->
                            </view>
                        </view>
                        <view>
                            <view class="discuss2" @tap="comment" :data-postid="item.postId">
                                <text class="discuss2_1">#{{ item.topicName }}</text>
                                <text class="discuss2_2">{{ item.content }}</text>
                            </view>
                            <!-- <view class="discuss3">
				<video src="{{item.videos[0]}}" class="discuss3_1" wx:if="{{item.videos[0]}}" bindtap="preview" data-src="{{item.videos[0]}}" data-index="0"/>
				<image src="{{res}}" mode="" class="discuss3_1" bindtap="previewImage" data-src="{{res}}" data-url="{{item.imageUrl}}" wx:for="{{item.imageUrl}}" wx:for-item="res" wx:for-index="i" wx:key="i"/>
			</view> -->
                            <!-- 视频 -->
                            <view class="img_box" v-if="item.videos && item.videos.length > 0">
                                <view class="videos">
                                    <video class="video" :src="item.videos[0]" @tap="preview" :data-src="item.videos[0]"
                                        data-index="0" :ontrols="false"></video>
                                </view>
                            </view>
                            <!-- 图片 -->
                            <view class="img_box" v-else>
                                <template v-if="item.imageUrl">
                                    <view id="loadImg{{index}}"
                                        :class="['loadImg', item.imageUrl.length > 1 ? 'many_img' : '', item.listShow? 'active': '']">
                                        <view :class="
                                            'img_item ' +
                                            (item.imageUrl.length == 1 || item.imageUrl.length == 2 || item.imageUrl.length == 4 ? 'many' : item.imageUrl.length >= 3 ? 'four' : '')
                                        " v-for="(res, index1) in item.imageUrl" :key="index1">
                                            <image v-if="item.listShow" class="img" :src="res" @tap="previewImage"
                                                :data-url="item.imageUrl" :data-src="res" :data-sources="item.imageUrl"
                                                :data-index="index" mode="aspectFill"></image>
                                        </view>
                                    </view>
                                </template>
                            </view>
                        </view>
                    </view>

                    <view class="discuss4"></view>
                    <view class="discusss5">
                        <functionbutton-vue pageType='topic' :item="item"></functionbutton-vue>
                    </view>
                </view>
            </view>
        </view>

        <view class="blank"></view>
        <view class="dynamic" @tap="dynamic">
            <image src="http://cdn.xiaodingdang1.com/Group%2048098013%403x.png" mode=""
                style="width: 22rpx; height: 22rpx; margin-right: 8rpx" />
            <view class="dynamic1">参与话题</view>
        </view>
        <new-request-loading></new-request-loading>
		<mask-dialog></mask-dialog>
    </view>
</template>

<script>
    import functionbuttonVue from '@/components/functionbutton.vue';
    export default {
        components: {
            functionbuttonVue
        },
        data() {
            return {
                observe: null,
                bgColor: '',
                color: '#ffffff',
                topicId: '',
                communityRecord: [],

                topicInfo: {
                    topicPhotos: '',
                    topicName: '',
                    userList: [],
                    postNum: 0,
                    topicId: '',
                    isSubscribe: ''
                },

                currentIndex: 1,

                tabList: [{
                        name: '推荐'
                    },
                    {
                        name: '最新'
                    }
                ],

                res: []
            };
        },
        /**
         * 生命周期函数--监听页面加载
         */
        onLoad(options) {
            this.topicInfoFun(options.topicId); //查询话题详情
            this.setData({
                topicId: options.topicId
            });
        },
        /**
         * 生命周期函数--监听页面初次渲染完成
         */
        onReady() {
            this.observe = uni.createIntersectionObserver(this, {
                thresholds: [0],
            })
        },
        /**
         * 生命周期函数--监听页面显示
         */
        onShow() {
            // this.topicInfoFun(this.topicId);
        },
        /**
         * 生命周期函数--监听页面隐藏
         */
        onHide() {
            this.observe.disconnect()
        },
        /**
         * 生命周期函数--监听页面卸载
         */
        onUnload() {
            this.observe.disconnect()
        },
        /**
         * 页面相关事件处理函数--监听用户下拉动作
         */
        onPullDownRefresh() {},
        /**
         * 页面上拉触底事件的处理函数
         */
        onReachBottom() {},
        /**
         * 用户点击右上角分享
         */
        onShareAppMessage() {},

        onShareTimeline() {},
        onPageScroll: function(e) {
            if (e.scrollTop > 0) {
                this.setData({
                    bgColor: 'linear-gradient(to right,#FFFFFF,#FFFFFF)',
                    color: 'black'
                });
            } else if (e.scrollTop < 2) {
                this.setData({
                    bgColor: '',
                    color: '#ffffff'
                });
            }
        },
        methods: {
            comment(e) {
                let postId = e.currentTarget.dataset.postid;
                uni.navigateTo({
                    url: '/pageA/pageB/community/topicdetail/commentdetail?postId=' + postId
                });
            },

            //用户点击tab时调用
            titleClick: function(e) {
                console.log(e.currentTarget.dataset.index);
                this.setData({
                    //拿到当前索引并动态改变
                    currentIndex: e.currentTarget.dataset.index
                });
                this.communityRecordFun(this.topicInfo.topicId);
            },

            next() {
                uni.navigateBack({
                    delta: 1
                });
            },

            topicInfoFun(topicId) {
                //查询话题详情

                let data = {
                    topicId: topicId
                };
                this.$axios.get(this.$api.topicInfo, data).then((res) => {
                    if (res.data.code == 200) {
                        this.setData({
                            topicInfo: res.data.data
                        });
                        this.communityRecordFun(res.data.data.topicId);
                    }
                });
            },
            visualizeLoadingImages() {
                let that = this
                let communityRecord = that.communityRecord
                for (let i in communityRecord) {
                    uni.createIntersectionObserver(that, {
                        thresholds: [0],
                    }).relativeToViewport().observe('#loadImg' + i, (ret) => {
                        if (ret.intersectionRatio > 0) {
                            communityRecord[i].listShow = true
                        }
                        that.setData({
                            [`communityRecord[${i}].listShow`]: communityRecord[i].listShow,
                        })
                    })
                }
                console.log('communityRecord', that.communityRecord)
            },
            communityRecordFun(topicId) {
                //查询宝妈社区动态列表
                this.changeLoading(true)
                let data = {
                    pageSize: 1000,
                    pageNum: 1,
                    topicId: topicId,
                    orderType: this.currentIndex == 1 ? 2 : 1
                };
                this.$axios.get(this.$api.communityRecord, data).then((res) => {
                    if (res.data.code == 200) {
                        this.setData({
                            communityRecord: res.data.data.rows
                        });
                        // #ifdef MP-WEIXIN
                        wx.nextTick(() => {
                            this.visualizeLoadingImages()
                        })
                        // #endif
                        // #ifdef H5
                        this.$nextTick(() => {
                            this.visualizeLoadingImages()
                        })
                        // #endif
                    }
                }).finally(() => {
                    this.changeLoading(false)
                });
            },

            dynamic() {
                uni.navigateTo({
                    url: '/pageA/pageB/community/topicdetail/participateproject?topicInfo=' +
                        JSON.stringify(
                            this.topicInfo) + '&topicId=' + this.topicId
                });
            },

            topicBookmark(e) {
                //话题收藏&订阅
                let topicId = e.currentTarget.dataset.topicid;

                let data = {
                    topicId: topicId
                };
                this.$axios.put(this.$api.topicBookmark, data).then((res) => {
                    if (res.data.code == 200) {
                        this.topicInfoFun(this.topicId);
                    }
                });
            },

            communityBookmark(e) {
                //社区动态收藏
                let postId = e.currentTarget.dataset.postid;
                let index = e.currentTarget.dataset.index;
                let data = {
                    postId: postId
                };
                this.setData({
                    [`communityRecord[${index}].isBookmark`]: !this.communityRecord[index].isBookmark,
                })
                this.$axios.put(this.$api.communityBookmark, data).then((res) => {
                    if (res.data.code == 200) {
                        // this.communityRecordFun(this.topicInfo.topicId);
                    }
                });
            },

            previewImage: function(e) {
                var current = e.currentTarget.dataset.src;
                var url = e.currentTarget.dataset.url;
                uni.previewImage({
                    current: current,
                    // 当前显示图片的http链接
                    urls: url // 需要预览的图片http链接列表
                });
            },

            preview(event) {
                let src = event.currentTarget.dataset.src;
                let maparr = [];
                maparr.push({
                    type: 'video',
                    url: src
                });
                let index = event.currentTarget.dataset.index;
                // 既有视频又有图片用这个
                console.log(maparr);
                uni.previewMedia({
                    sources: maparr,
                    // 官方文档 : 素材展示列表 是个数组对象 一个url链接 一个type 判断这是视频还是图片
                    // 接收image 和 video 所以处理数据时 最好给个type 指定 image 或者 video
                    current: index
                    // 当前显示的资源序号
                });
            },

            like(e) {
                //点赞
                let index = e.currentTarget.dataset.index;
                let data = {
                    postId: e.currentTarget.dataset.postid
                };
                this.setData({
                    [`communityRecord[${index}].isLike`]: !this.communityRecord[index].isLike,
                })
                this.$axios.put(this.$api.saveLike, data).then((res) => {
                    if (res.data.code == 200) {
                        // this.topicInfoFun(this.topicId); //查询话题详情
                    }
                });
            }
        }
    };
</script>
<style scoped>
    @import './topicdetail.css';
</style>