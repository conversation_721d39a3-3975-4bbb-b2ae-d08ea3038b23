<template>
  <view class="like-users-list">
    <view class="like-users-list__content">
      <!-- 点赞图标 -->
      <view class="like-users-list__icon">
        <image
          :src="ICONS.LIKE_LIST"
          class="like-users-list__like-icon"
        />
      </view>
      
      <!-- 用户头像列表 -->
      <view class="like-users-list__avatars">
        <image
          v-for="(user, index) in displayUsers"
          :key="user.userId || index"
          class="like-users-list__avatar"
          :class="{ 'like-users-list__avatar--stacked': index > 0 }"
          :style="getAvatarStyle(index)"
          :src="user.avatar || $defaultAvatar"
          mode="aspectFill"
        />
      </view>
      
      <!-- 点赞数量和展开按钮 -->
      <view class="like-users-list__count" v-if="likeUsers.length > 0">
        <view
          class="like-users-list__toggle"
          @tap="handleToggleDisplay"
        >
          <image
            :class="{ 'like-users-list__arrow--expanded': isExpanded }"
            :src="ICONS.ARROW"
            class="like-users-list__arrow"
          />
          <view class="like-users-list__count-text">
            {{ likeUsers.length }}人
          </view>
        </view>
      </view>
    </view>
    
    <!-- 展开的用户列表 -->
    <view v-if="isExpanded" class="like-users-list__expanded">
      <view
        v-for="user in likeUsers"
        :key="user.userId"
        class="like-users-list__user-item"
      >
        <image
          :src="user.avatar || $defaultAvatar"
          class="like-users-list__user-avatar"
          mode="aspectFill"
        />
        <view class="like-users-list__user-name">
          {{ user.nickname || $defaultName }}
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { computed, ref } from 'vue'
import { ICONS } from '../assets.js'

const props = defineProps({
  likeUsers: {
    type: Array,
    required: true
  },
  itemIndex: {
    type: Number,
    required: true
  },
  maxDisplayCount: {
    type: Number,
    default: 15
  }
})

// 移除事件定义，内部处理展开/收起

// 是否展开显示
const isExpanded = ref(false)

// 显示的用户列表（限制数量）
const displayUsers = computed(() => {
  return props.likeUsers.slice(0, props.maxDisplayCount)
})

/**
 * 获取头像样式
 * @param {number} index - 头像索引
 * @returns {Object} 样式对象
 */
const getAvatarStyle = (index) => {
  if (index === 0) return {}

  return {
    left: `${index * 36}rpx`,
    zIndex: index
  }
}

/**
 * 处理展开/收起切换 - 内部完成处理
 */
const handleToggleDisplay = () => {
  isExpanded.value = !isExpanded.value
  // 内部处理，不需要向外emit
}
</script>

<style scoped lang="less">
.like-users-list {
  margin: 16rpx 0;

  &__content {
    display: flex;
    align-items: center;
    gap: 12rpx;
  }

  &__icon {
    flex-shrink: 0;
  }

  &__like-icon {
    width: 32rpx;
    height: 32rpx;
  }

  &__avatars {
    position: relative;
    display: flex;
    align-items: center;
    height: 60rpx;
    flex: 1;
  }

  &__avatar {
    width: 60rpx;
    height: 60rpx;
    border-radius: 50%;
    border: 2rpx solid #ffffff;
    position: absolute;

    &--stacked {
      position: absolute;
    }
  }

  &__count {
    flex-shrink: 0;
  }

  &__toggle {
    display: flex;
    align-items: center;
    gap: 8rpx;
    cursor: pointer;
  }

  &__arrow {
    width: 24rpx;
    height: 24rpx;
    transition: transform 0.3s ease;

    &--expanded {
      transform: rotate(90deg);
    }
  }

  &__count-text {
    font-size: 24rpx;
    color: #666666;
  }

  &__expanded {
    margin-top: 16rpx;
    padding: 16rpx;
    background: #f8f9fa;
    border-radius: 8rpx;
  }

  &__user-item {
    display: flex;
    align-items: center;
    gap: 12rpx;
    padding: 8rpx 0;

    &:not(:last-child) {
      border-bottom: 1rpx solid #eeeeee;
    }
  }

  &__user-avatar {
    width: 48rpx;
    height: 48rpx;
    border-radius: 50%;
  }

  &__user-name {
    font-size: 26rpx;
    color: #333333;
  }
}
</style>
