export default {
    namespaced: true,
    state: () => ({
        isShare: false, // 请帖触发一次分享
        shareData: {}
    }),
    getters: {
        getShareFlag() {
            return state.isShare
        },
        getShareData() {
            return state.shareData
        }
    },
    mutations: {
        changeShareState(state, value) {
            state.isShare = value
        },
        setShareData(state, data) {
            state.shareData = data
        }
    },
};