.nexts {
    display: flex;
    position: fixed;
    top: 120rpx;
    width: 100%;
    margin-left: 24rpx;
}

.headImg {
    width: 41%;
}

.headTitle {
    font-size: 34rpx;
    color: #ffffff;
    font-weight: bold;
}

/* 轮播图 */

.detail-banner {
    width: 100%;
    height: 750rpx;
}

.detail-banner-img {
    width: 100%;
    height: 100%;
}

/* video */

.box-w {
    width: 100%;
    height: 750rpx;
}

.videocover {
    width: 100%;
    overflow: hidden;
}

.videocoverbg {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
}

.playbtn {
    position: absolute;
    top: 50%;
    z-index: 2;
    left: 50%;
    width: 200rpx;
    height: 200rpx;
    transform: translate(-50%, -50%);
}

.videocover .cover {
    width: 100%;
}

.ico-share {
    width: 18px;
    height: 18px;
    display: block;
}

.main {
    background: #f6f6f6;
}

.titleName {
    color: #333333;
    font-size: 34rpx;
    font-weight: bold;
}

.content {
    font-size: 28rpx;
    color: #777777;
    margin-top: 12rpx;
    display: flex;
}

.content1 {
    display: flex;
}

.fgxTag {
    width: 2rpx;
    background: #ebebeb;
    margin: 0 10rpx;
}

.price {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    margin-top: 10rpx;
}

.price1 {
    font-size: 34rpx;
    color: #333333;
    font-weight: bold;
    width: 80%;
}

.price2 {
    padding: 12rpx 28rpx;
    border-radius: 100rpx;
    background: #ff4f61;
    color: #ffffff;
    font-size: 24rpx;
    font-weight: bold;
}

.fgx {
    width: 100%;
    height: 2rpx;
    background: #ebebeb;
    margin-top: 24rpx;
}

.tag {
    display: flex;
    justify-content: space-between;
    margin-top: 24rpx;
}

.tagOne {
    display: flex;
    background: #ff4f61;
    border-radius: 10rpx;
    color: #ffffff;
    font-size: 24rpx;
    font-weight: bold;
    padding: 8rpx 20rpx;
    align-items: center;
}

.tagTwo {
    display: flex;
    border: 1rpx solid #ff4f61;
    border-radius: 10rpx;
    color: #ff4f61;
    font-size: 24rpx;
    font-weight: bold;
    padding: 8rpx 20rpx;
    align-items: center;
}

.tagName {
    margin-left: 10rpx;
}

.facility {
    margin-top: 24rpx;
    padding: 16rpx 20rpx;
    border-radius: 20rpx;
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #ffe8eb), color-stop(100%, #ffe8eb));
    background: -webkit-linear-gradient(top, #ffe8eb 0%, #ffe8eb 100%);
    background: -o-linear-gradient(top, #ffe8eb 0%, #ffe8eb 100%);
    background: -ms-linear-gradient(top, #ffe8eb 0%, #ffe8eb 100%);
}

.facility1 {
    display: flex;
    justify-content: space-between;
}

.facility1_1 {
    color: #ff4f61;
    font-size: 36rpx;
    font-weight: bold;
}

.facility1_2 {
    padding: 6rpx 28rpx;
    border-radius: 100rpx;
    color: #ffffff;
    background: #ff4f61;
    font-size: 24rpx;
    font-weight: bold;
}

.facility2 {
    background: #ffffff;
    border-radius: 10rpx;
    padding: 20rpx 38rpx;
    margin-top: 12rpx;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-around;
}

.facility2_1 {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;
    width: 33%;
    justify-content: center;
}

.facility2_1_1 {
    color: #333333;
    font-size: 24rpx;
    margin-left: 8rpx;
}

.main1 {
    padding: 40rpx 24rpx;
    background: #ffffff;
}

.main2 {
    margin-top: 20rpx;
    padding: 20rpx 24rpx;
    background: #ffffff;
}

.signingGift {
    position: relative;
}

.signingGift1 {
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #ffe4e8), color-stop(100%, #fff6df));
    background: -webkit-linear-gradient(top, #ffe4e8 0%, #fff6df 100%);
    background: -o-linear-gradient(top, #ffe4e8 0%, #fff6df 100%);
    background: -ms-linear-gradient(top, #ffe4e8 0%, #fff6df 100%);
    padding: 16rpx 20rpx;
    border-top-left-radius: 20rpx;
    border-top-right-radius: 20rpx;
}

.signingGift2 {
    background: #fff7f8;
    padding: 16rpx 38rpx;
    border-radius: 20rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: -10rpx;
}

.signingGift2_1 {
    color: #333333;
    font-size: 22rpx;
    display: flex;
    align-items: center;
}

.description {
    font-size: 28rpx;
}

.signingGift2_2 {
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #ffd9df), color-stop(100%, #fff5d8));
    background: -webkit-linear-gradient(top, #ffd9df 0%, #fff5d8 100%);
    background: -o-linear-gradient(top, #ffd9df 0%, #fff5d8 100%);
    background: -ms-linear-gradient(top, #ffd9df 0%, #fff5d8 100%);
    padding: 8rpx 24rpx;
    border-radius: 100rpx;
    color: #e48e39;
    font-size: 24rpx;
    font-weight: bold;
}

.head {
    display: flex;
    justify-content: space-between;
}

.head1 {
    display: flex;
    align-items: center;
}

.head2 {
    display: flex;
    align-items: center;
}

.head1_1 {
    width: 10rpx;
    height: 30rpx;
    background: #ff4f61;
    border-radius: 5rpx;
}

.head1_2 {
    font-size: 34rpx;
    font-weight: bold;
    color: #333333;
    margin-left: 20rpx;
}

.head2_1 {
    color: #aaaaaa;
    font-size: 22rpx;
    margin-right: 10rpx;
}

.roomComment {
    margin-top: 20rpx;
}

.roomComment1 {
    display: flex;
    justify-content: space-between;
}

.roomComment1_1 {
    display: flex;
}

.roomComment1_2 {
    margin-left: 18rpx;
}

.roomComment1_3 {
    color: #777777;
    font-size: 26rpx;
}

.roomComment1_4 {
    color: #aaaaaa;
    font-size: 22rpx;
}

.roomComment2 {
    margin-left: 90rpx;
}

.roomComment2_1 {
    display: flex;
    margin: 12rpx 0;
}

.roomComment2_2 {
    padding: 4rpx 12rpx;
    border: 1rpx solid #aaaaaa;
    border-radius: 50rpx;
    color: #777777;
    font-size: 20rpx;
    margin-right: 8rpx;
}

.roomComment2_3 {
    color: #777777;
    font-size: 24rpx;
}

.next1 {
    display: flex;
    align-items: center;
    width: 90%;
}

.next1_1 {
    width: 80%;
    margin-left: 12rpx;
    color: #333333;
    font-size: 28rpx;
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    text-align: left;
}

.next {
    background: #f4f6ff;
    padding: 12rpx 20rpx;
    border-radius: 10rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 10rpx;
}

.next2 {
    width: 20%;
}

.next3 {
    display: flex;
    justify-content: space-between;
}

.image-text {
    display: flex;
    align-items: center;
    justify-content: center;
}

.leftFgx {
    width: 40rpx;
    height: 2rpx;
    background: #ebebeb;
}

.rightFgx {
    width: 40rpx;
    height: 2rpx;
    background: #ebebeb;
}

.charactersTitle {
    color: #333333;
    font-size: 28rpx;
    margin: 0 20rpx;
}

.recommend {
    margin-top: 20rpx;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
}

.recommend1 {
    margin-bottom: 20rpx;
    position: relative;
    box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.1);
    border-radius: 20rpx;
}

.tag2 {
    color: #ff4f61;
    padding: 4rpx 8rpx;
    border-radius: 4rpx;
    background: #ffd9df;
    margin-top: 12rpx;
    font-size: 24rpx;
    margin-right: 10rpx;
}

.tag2_1 {
    display: flex;
    margin-bottom: 14rpx;
}

.recommend2 {
    color: #333333;
    font-size: 30rpx;
    margin-top: 16rpx;
}

.recommend1_1 {
    background: #ffffff;
    border-top: none;
    border-bottom-left-radius: 10rpx;
    border-bottom-right-radius: 10rpx;
    padding: 0 15rpx;
}

.tagOne1 {
    display: flex;
    background: #ff4f61;
    border-radius: 10rpx;
    color: #ffffff;
    font-size: 24rpx;
    font-weight: bold;
    padding: 4rpx 16rpx;
    align-items: center;
    position: absolute;
    top: 16rpx;
    left: 16rpx;
}

/* .btn{
	position: fixed;
	bottom: 0;
	display: flex;
	align-items: center;
	background: #ffffff;
	width: 100%;
	padding: 20rpx 0 68rpx 0;
	justify-content: space-around;
}
.consult{
	display: flex;
	padding: 20rpx 114rpx;
	background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #FFD9DF), color-stop(100%, #FFF5D8));
  background: -webkit-linear-gradient(top, #FFD9DF 0%, #FFF5D8 100%);
  background: -o-linear-gradient(top, #FFD9DF 0%, #FFF5D8 100%);
	background: -ms-linear-gradient(top, #FFD9DF 0%, #FFF5D8 100%);
	border-radius: 100rpx;
}
.consultTitle{
	margin-left: 10rpx;
	color: #F84343;
	font-size: 28rpx;
}
.btn1{
	text-align: center;
	color: #333333;
	font-size: 24rpx;
} */
.blank {
    height: 200rpx;
}

.head {
    margin-top: 24rpx;
}

.serve {
    margin-top: 24rpx;
    border-radius: 20rpx;
    background: #ffffff;
    padding: 32rpx 76rpx 0 76rpx;
    box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
}

.serve1 {
    text-align: center;
    margin-bottom: 32rpx;
}

.serveTitle {
    color: #ff4f61;
    font-size: 26rpx;
}

.serveNaame {
    color: #333333;
    font-size: 28rpx;
    margin-top: 10rpx;
}

.distribution {
    display: flex;
    align-items: center;
    margin-top: 20rpx;
}

.distribution1 {
    color: #ff4f61;
    font-size: 24rpx;
    margin-left: 10rpx;
}

.prices {
    display: flex;
    align-items: center;
    color: #333333;
    font-size: 22rpx;
}

.prices1 {
    color: #ff9900;
    font-size: 32rpx;
    font-weight: Medium;
}

.rule {
    width: 2rpx;
    background: #ebebeb;
    margin: 0 24rpx;
}

.signingGift2_2_1 {
    padding: 8rpx 24rpx;
    border-radius: 100rpx;
    color: #aaaaaa;
    font-size: 24rpx;
    background: #ebebeb;
}

.signingGift2_1_2 {
    color: #777777;
    font-size: 36rpx;
    font-weight: Regular;
}

.roomComments {
    color: #aaaaaa;
    font-size: 28rpx;
    font-weight: Medium;
    text-align: center;
    padding: 60rpx 0;
}

.signatory {
    background: #ffffff;
    padding: 34rpx 24rpx;
    margin: 20rpx 0;
}