.main{
	background: linear-gradient(to bottom, #E0F1FF, #FFF6EB);
	background-size: 100% auto;
padding: 32rpx 24rpx;
}
.head{
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.head-tab{
	background: #fff;
	border-radius: 60rpx;
	display: flex;
	align-items: center;
	text-align: center;
	line-height: 60rpx;
	font-weight: bold;
	font-size: 24rpx;
}
.activeTab{
	width: 140rpx;
	height: 60rpx;
	background: #3F6FFF;
	color: #FFFFFF;
	border-radius: 60rpx;
	
}
.head-tab1{
	width: 140rpx;
	height: 60rpx;
	color: #333333;
}
.head-time{
	background: #FFFFFF;
	display: flex;
	align-items: center;
	border-radius: 60rpx;
	padding: 7rpx 7rpx;
}
.head-time1{
	background: #E8EEFF;
	width: 60rpx;
	height: 60rpx;
	border-radius: 100rpx;
	text-align: center;
	line-height: 60rpx;
}
.head-time2{
	color: #333333;
	font-size: 28rpx;
	font-weight: bold;
	padding: 0rpx 31rpx;
}
.mealType{
	background: #fff;
	border-radius: 20rpx;
	padding: 24rpx 24rpx;
	margin-top: 24rpx;
}
.mealType1{
	display: flex;
	align-items: center;
	justify-content: space-between;
	width: 100%;
}
.mealType-1{
	width: 90rpx;
	height: 90rpx;
	background: #E8EEFF;
	text-align: center;
	line-height: 120rpx;
	border-radius: 100rpx;
}
.fgx{
	width: 1rpx;
	height: 64rpx;
	background: #D9D9D9;
}
.mealType-5{
	width: 75%;
}
.mealType-6{
color: #5C5C5C;
	font-size: 24rpx;
	margin-top: 8rpx;
}
.mealType-2{
	color: #333333;
	font-size: 28rpx;
	font-weight: bold;
}
.mealType-3{
	padding: 30rpx 30rpx;
	background: #F6F6F6;
	border-radius: 10rpx;
	margin-top: 18rpx;
}


.calendar-container {
	width: 100%;
	padding: 20rpx;
	box-sizing: border-box;
	background: #fff;
	border-radius: 20rpx;
	margin-top: 20rpx;
  }
  
  .header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 0;
	font-size: 32rpx;
  }
  
  .week-header {
	display: flex;
	justify-content: space-around;
	padding: 15rpx 0;
	background: #f7f7f7;
  }
  
  .days-grid {
	display: flex;
	flex-wrap: wrap;
  }
  
  .day-cell {
	width: 14.28%;
	height: 80rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	position: relative;
	border-bottom: 1rpx solid #eee;
  }
  
  .other-month {
	color: #ccc;
  }
  
  .today {
	color: #07c160;
	font-weight: bold;
  }
  
  .event-mark {
	position: absolute;
	bottom: 5rpx;
	width: 10rpx;
	height: 10rpx;
	border-radius: 50%;
	// background: #f44;
  }
  
  
  .customer-list{
  	background: #FFFFFF;
  	border-radius: 20rpx;
  	padding: 24rpx 24rpx;
  	margin-top: 24rpx;
  }
  .customer-list1{
  	display: flex;
  	justify-content: space-around;
  }
  .hue{
  	width: 25rpx;
  	height: 25rpx;
  	border-radius: 5rpx;
  	background: #3F6FFF;
  }
  
  .hue1{
  	width: 25rpx;
  	height: 25rpx;
  	border-radius: 5rpx;
  	background: #5ACD74;
  }
  .huetitle{
  	color: #000000;
  	font-size: 28rpx;
  	font-weight: bold;
  	margin-left: 9rpx;
  }
  .hue2{
  	display: flex;
  	align-items: center;
  }
  .customer-list3{
  	display: grid;
  	grid-template-columns: repeat(4, 1fr); /* 每行三个等宽列 */
  	  gap: 10px; /* 行与列之间的间隔 */
  	  margin-top: 24rpx;
  }
  .customer-list2
  {
  	border-radius: 10rpx;
  }
  .customer-list2-1
  {
  	border-radius: 10rpx;
  }
  .num{
  	padding:4rpx 0;
  	text-align: center;
  	background: #2F6CF4;
  	color: #fff;
  }
  .num-1{
	 padding:4rpx 0;
	 text-align: center;
	 background: #5ACD74;
	 color: #fff; 
  }
  .customer-list4{
  	padding: 15rpx 15rpx;
  	background: #F4F9FF;
  	text-align: center;
	border: 1rpx solid #3F6FFF;
	border-top: none;
  }
  .customer-list5{
  	padding: 15rpx 15rpx;
  	background: #F4FFF7;
  	text-align: center;
	border: 1rpx solid #5ACD74;
	border-top: none;
  }
  .customer-list4-1{
  	color: #3F6FFF;
  	font-weight: bold;
  	font-size: 22rpx;
  	padding: 10rpx 0;
  }
  .customer-list4-1-1{
  	color: #5ACD74;
  	font-weight: bold;
  	font-size: 22rpx;
  	padding: 10rpx 0;
  }
  .customer-list4-2{
  	color: #AFC3FF;
  	font-size: 20rpx;
  }
  .customer-list4-2-2{
  	color: #9EE4AE;
  	font-size: 20rpx;
  }