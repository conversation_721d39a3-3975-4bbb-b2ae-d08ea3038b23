<template>
	<view class="main">
		<view class="coment">
			 <view class="title">
			 	宝妈护理
			 </view>
			 <u-grid :col="3" :border="false">
			 		<u-grid-item @tap="nextList({name:'宝妈护理',type:8})">
			 							    <image src="http://cdn.xiaodingdang1.com/2025/03/21/762e2851184841c7b54ed775093460fb.png"
			 							        mode="" style="width: 70rpx; height: 70rpx" />
			 			<view class="grid-text">宝妈护理</view>
			 		</u-grid-item>
			 		<u-grid-item @tap="nextList({name:'宝妈记录',type:9})">
			 			<image src="http://cdn.xiaodingdang1.com/2025/03/21/b1a72822bcb646d79ba67508a0d2b659.png"
			 			    mode="" style="width: 70rpx; height: 70rpx" />
			 			<view class="grid-text">宝妈记录</view>
			 		</u-grid-item>
			 	</u-grid>
		</view>
		<view class="coment">
			 <view class="title">
			 	宝妈护理
			 </view>
			 <u-grid :col="3" :border="false">
			 		<u-grid-item @tap="nextList({name:'宝宝信息',type:1})">
			 							    <image src="http://cdn.xiaodingdang1.com/2025/03/21/17723ec227c44935b08c72c25b03dd9a.png"
			 							        mode="" style="width: 70rpx; height: 70rpx" />
			 			<view class="grid-text">宝宝信息</view>
			 		</u-grid-item>
			 		<u-grid-item @tap="nextList({name:'宝宝护理',type:2})">
			 			<image src="http://cdn.xiaodingdang1.com/2025/03/21/3f505d1230c54316b987005a311dbb99.png"
			 			    mode="" style="width: 70rpx; height: 70rpx" />
			 			<view class="grid-text">宝宝护理</view>
			 		</u-grid-item>
					<u-grid-item @tap="nextList({name:'宝宝记录',type:3})">
						<image src="http://cdn.xiaodingdang1.com/2025/03/21/7cdc8829b5b244acade22042d4ac66df.png"
						    mode="" style="width: 70rpx; height: 70rpx" />
						<view class="grid-text">宝宝记录</view>
					</u-grid-item>
					<u-grid-item @tap="nextList({name:'喂奶记录',type:4})">
						<image src="http://cdn.xiaodingdang1.com/2025/03/21/d31bf9c23729462aae8a684cb86f7411.png"
						    mode="" style="width: 70rpx; height: 70rpx" />
						<view class="grid-text">喂奶记录</view>
					</u-grid-item>
					<u-grid-item @tap="nextList({name:'更换尿布',type:5})">
						<image src="http://cdn.xiaodingdang1.com/2025/03/21/e36de7fc771944b39c021e8627b70a99.png"
						    mode="" style="width: 70rpx; height: 70rpx" />
						<view class="grid-text">更换尿布</view>
					</u-grid-item>
					<u-grid-item @tap="nextList({name:'宝宝洗澡',type:6})">
						<image src="http://cdn.xiaodingdang1.com/2025/03/21/ca0fb13a80974afb80703e684df04cca.png"
						    mode="" style="width: 70rpx; height: 70rpx" />
						<view class="grid-text">宝宝洗澡</view>
					</u-grid-item>
					<u-grid-item @tap="nextList({name:'宝宝用药',type:7})">
						<image src="http://cdn.xiaodingdang1.com/2025/03/21/ccb35513ffec4e758ee4653620e46ee1.png"
						    mode="" style="width: 70rpx; height: 70rpx" />
						<view class="grid-text">宝宝用药</view>
					</u-grid-item>
					<u-grid-item @tap="echartsdetails()">
						<image src="http://cdn.xiaodingdang1.com/2025/03/21/17bd39a4cd60464e830ba0b74f72c67b.png"
						    mode="" style="width: 70rpx; height: 70rpx" />
						<view class="grid-text">数据记录</view>
					</u-grid-item>
			 	</u-grid>
		</view>
		
	</view>
</template>

<script>
	export default {
	    data() {
	        return {
	            userInfo: {
	                nickname: '',
	                avatar: '',
	                clubName: ''
	            }
	        };
	    },
		methods:{
			nextList(page){
				if(page.type==1){
					uni.navigateTo({
					    url: '/pageA/pageB/community/nurse/babydetails'
					});
				}else{
					uni.navigateTo({
					    url: '/pageA/pageB/community/nurse/nurselist?title='+page.name+'&type='+page.type
					});
				}
				
			},
			echartsdetails(){
				uni.navigateTo({
				    url: '/pageA/pageB/community/nurse/echartsdetails'
				});
			}
		}
		}
</script>
<style lang="less" scoped>
	@import './nursemenu.less';
</style>