<template>
  <navigation title="" :background="bgColor"></navigation>
  <view class="confinement-details">
    <view class="image-gallery">
      <swiper
        :current="currentIndex"
        :autoplay="autoplayComputed"
        :interval="3000"
        :duration="300"
        :circular="true"
        indicator-dots
        indicator-color="#cccccc"
        indicator-active-color="#fff"
        @change="onSwiperChange"
        class="gallery-swiper"
      >
        <swiper-item v-for="(item, index) in list" :key="index">
          <!-- 视频项 -->
          <template v-if="getItemType(item) === 'video'">
            <video
              :id="`gallery-video-${index}`"
              :src="getSource(item)"
              :poster="item.poster || ''"
              :controls="true"
              :autoplay="false"
              object-fit="cover"
              class="gallery-video"
              @play="onVideoPlay(index)"
              @pause="onVideoPause(index)"
              @ended="onVideoEnded(index)"
            />
          </template>
          <!-- 图片项 -->
          <template v-else>
            <image
              :src="getSource(item)"
              mode="aspectFill"
              class="gallery-image"
              @tap="previewImage(index)"
            />
          </template>
        </swiper-item>
      </swiper>
    </view>
    <view class="price-section">
      <view class="price-info">
        <view class="price-amount">
          {{ packagePrice ? packagePrice : '----' }}
        </view>
        <view class="consult-count">
          已有{{ inforList.consultNumber }}人咨询
        </view>
      </view>
      <view class="package-info">
        <view class="package-header">
          <view class="package-title-row">
            <view class="package-name">
              {{ inforList.packageName }}
            </view>
            <view class="share-button-wrapper">
              <button class="share-button" open-type="share">
                <img
                  src="http://cdn.xiaodingdang1.com/2025/04/23/862e660bce0d4565bba27ff8c6f8ab17.png"
                  alt="Icon"
                  class="share-icon"
                />
                分享
              </button>
            </view>
          </view>

          <view class="service-tags">
            <view class="service-label">服务</view>
            <scroll-view :scroll-x="true" class="tags-scroll">
              <view class="tag-item" v-for="(item, index) in tag" :key="index">
                {{ item }}
              </view>
            </scroll-view>
          </view>
        </view>
      </view>
    </view>
    <view class="facility-section">
      <view class="section-title"> 房间设施 </view>
      <view class="facility-grid">
        <view class="facility-item">
          <image
            src="http://cdn.xiaodingdang1.com/2025/05/13/fb2f6f592fc94cbfbcd5266f55c0eded.png"
            class="facility-icon"
          ></image>
          <view class="facility-content">
            <view class="facility-label"> 房型 </view>
            <view class="facility-value">
              {{ suiteList.roomType }}
            </view>
          </view>
        </view>
        <view class="facility-item">
          <image
            src="http://cdn.xiaodingdang1.com/2025/05/10/b112c464f788455e9cf899a852171075.png"
            class="facility-icon"
          ></image>
          <view class="facility-content">
            <view class="facility-label"> 面积 </view>
            <view class="facility-value">
              {{ suiteList.minArea }}M²-{{ suiteList.maxArea }}M²
            </view>
          </view>
        </view>
        <view class="facility-item">
          <image
            src="http://cdn.xiaodingdang1.com/2025/05/10/3130883dc60243eabe33f8611256d114.png"
            class="facility-icon"
          ></image>
          <view class="facility-content">
            <view class="facility-label"> 朝向 </view>
            <view class="facility-value">
              {{ suiteList.orientation }}
            </view>
          </view>
        </view>
        <view class="facility-item">
          <image
            src="http://cdn.xiaodingdang1.com/2025/05/10/6bc81c34fde940d89677055912bf15d3.png"
            class="facility-icon"
          ></image>
          <view class="facility-content">
            <view class="facility-label"> 楼层 </view>
            <view class="facility-value">
              {{ suiteList.minFloor }}楼-{{ suiteList.maxFloor }}楼
            </view>
          </view>
        </view>
        <view class="facility-item">
          <image
            src="http://cdn.xiaodingdang1.com/2025/05/10/400007731ab84998bc7201e6a0b526b4.png"
            class="facility-icon"
          ></image>
          <view class="facility-content">
            <view class="facility-label"> 床型 </view>
            <view class="facility-value">
              {{ suiteList.bedType }}
            </view>
          </view>
        </view>
        <view class="facility-item">
          <image
            src="http://cdn.xiaodingdang1.com/2025/05/10/beb0c8e7905c43aa83982c4b60f65ccd.png"
            class="facility-icon"
          ></image>
          <view class="facility-content">
            <view class="facility-label"> 室外景观 </view>
            <view class="facility-value">
              <view v-for="(item, index) in outdoorFeatures" :key="index">
                {{ item }}
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="ancillary-section">
      <view class="section-title section-title--bold"> 配套设施 </view>
      <view class="ancillary-list">
        <view class="ancillary-category">
          <view class="category-header">
            <image
              src="http://cdn.xiaodingdang1.com/2025/05/13/76963a4de4bb4a449667fec65d27cfa7.png"
              class="category-icon"
            />
            <view class="category-title"> 便利设施 </view>
          </view>
          <view class="feature-grid">
            <view
              class="feature-item"
              v-for="(item, index) in inforList.facilityFeatures"
              :key="index"
            >
              √ {{ item }}
            </view>
          </view>
        </view>
        <view class="ancillary-category">
          <view class="category-header">
            <image
              src="http://cdn.xiaodingdang1.com/2025/05/13/368ea383136e4427910148cb13b2e00c.png"
              class="category-icon"
            />
            <view class="category-title"> 媒体娱乐 </view>
          </view>
          <view class="feature-grid">
            <view
              class="feature-item"
              v-for="(item, index) in inforList.mediaFeatures"
              :key="index"
            >
              √ {{ item }}
            </view>
          </view>
        </view>
        <view class="ancillary-category">
          <view class="category-header">
            <image
              src="http://cdn.xiaodingdang1.com/2025/05/13/c452d30f5f8a40468840eba0817069a7.png"
              class="category-icon"
            />
            <view class="category-title"> 卫浴配套 </view>
          </view>
          <view class="feature-grid">
            <view
              class="feature-item"
              v-for="(item, index) in inforList.bathroomFacilities"
              :key="index"
            >
              √ {{ item }}
            </view>
          </view>
        </view>
        <view class="ancillary-category">
          <view class="category-header">
            <image
              src="http://cdn.xiaodingdang1.com/2025/05/13/af31e0783fce495db8e5ada47df8e7f5.png"
              class="category-icon"
            />
            <view class="category-title"> 清洁服务 </view>
          </view>
          <view class="feature-grid">
            <view class="feature-item">
              √ {{ getCleaningText(suiteList.cleaningFrequency) }}
            </view>
            <view class="feature-item">
              √ {{ getSheetChangeText(suiteList.sheetChangeFrequency) }}
            </view>
            <view class="feature-item">
              √ {{ getDisinfectionText(suiteList.disinfectionFrequency) }}
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="serviceContent">
      <view class="roomTitle" style="font-weight: bold"> 服务内容 </view>
      <view class="serviceContent1">
        <view class="serviceContent2">
          <view class="serviceContent2_1"> 陪护服务(价格随所选服务变化) </view>
          <view class="serviceContent2_2">
            <view class="serviceContent2_2_1"> 母婴照护 </view>
            <view class="serviceContent2_2_2">
              {{
                inforList.maternityCareModel == 1
                  ? '母婴同室护理'
                  : inforList.maternityCareModel == 2
                  ? '婴儿集中托管'
                  : inforList.maternityCareModel == 3
                  ? '母婴同室（可托管）'
                  : ''
              }}
            </view>
          </view>
          <view class="serviceContent2_2">
            <view class="serviceContent2_2_1"> 陪护人员 </view>
            <view class="serviceContent2_2_2">
              {{
                inforList.attendantType == 1
                  ? '护士'
                  : inforList.attendantType == 2
                  ? '月嫂'
                  : ''
              }}
            </view>
          </view>
          <view class="serviceContent2_2">
            <view class="serviceContent2_2_1"> 护理模式 </view>
            <view class="serviceContent2_2_2">
              {{ nurseType[inforList.attendantConfiguration] || '' }}
            </view>
          </view>
        </view>
        <view class="serviceContent2">
          <view class="serviceContent2_1"> 月子餐 </view>
          <view class="serviceContent2_2">
            <view class="serviceContent2_2_1"> 餐食数量 </view>
            <view class="serviceContent2_2_2">
              {{ inforList.dailyMeals }}餐{{ inforList.dailySnacks }}点
            </view>
          </view>
        </view>
        <view class="serviceContent2">
          <view class="serviceContent2_1"> 家人陪住 </view>
          <view class="serviceContent2_2">
            <view class="serviceContent2_2_1"> 陪住人员 </view>
            <view class="serviceContent2_2_2">
              {{ inforList.isFamilyAccommodation ? '' : '不' }}
              支持家人陪住
            </view>
          </view>
        </view>
        <view class="serviceContent2">
          <view
            class="serviceContent2_1"
            style="border-bottom: 1rpx solid #f1f1f1"
          >
            护理服务
          </view>
          <view
            class="serviceContent3"
            v-for="(item, index) in inforList.mamaNursingList"
            :key="index"
          >
            <view class="serviceContent3_1">
              <text v-show="index == 0">妈妈护理</text>
            </view>
            <view class="serviceContent4">
              <view class="serviceContent4_1">
                <view class="serviceContent4_3">{{ index + 1 }}</view>
                <view>
                  {{ item.projectName }}
                </view>
              </view>
              <view class="serviceContent4_2" v-if="item.quantity == 999">
                按需
              </view>
              <view v-else> {{ item.quantity }}次 </view>
            </view>
          </view>
          <view id="serviceContent3"> </view>
          <view
            class="serviceContent3"
            v-for="(item, index) in inforList.babyNursingList"
            :key="index"
          >
            <view class="serviceContent3_1">
              <text v-show="index == 0">宝宝护理</text>
            </view>
            <view class="serviceContent4">
              <view class="serviceContent4_1">
                <view class="serviceContent4_3">{{ index + 1 }}</view>
                <view>
                  {{ item.projectName }}
                </view>
              </view>
              <view class="serviceContent4_2" v-if="item.quantity == 999">
                按需
              </view>
              <view v-else> {{ item.quantity }}次 </view>
            </view>
          </view>
        </view>
        <view class="serviceContent2">
          <view
            class="serviceContent2_1"
            style="border-bottom: 1rpx solid #f1f1f1"
          >
            提供用品
          </view>
          <view
            class="serviceContent3"
            v-for="(item, index) in inforList.mamaSuppliesList"
            :key="index"
          >
            <view class="serviceContent3_1">
              <text v-show="index == 0">妈妈用品</text>
            </view>
            <view class="serviceContent4">
              <view class="serviceContent4_1">
                <view class="serviceContent4_3">{{ index + 1 }}</view>
                <view>
                  {{ item.supplyName }}
                </view>
              </view>
              <view class="serviceContent4_2" v-if="item.quantity == 999">
                按需
              </view>
              <view v-else> {{ item.quantity }}{{ item.unit?item.unit:'个' }} </view>
            </view>
          </view>
          <view id="serviceContent3"> </view>
          <view
            class="serviceContent3"
            v-for="(item, index) in inforList.babySuppliesList"
            :key="index"
          >
            <view class="serviceContent3_1">
              <text v-show="index == 0">宝宝用品</text>
            </view>
            <view class="serviceContent4">
              <view class="serviceContent4_1">
                <view class="serviceContent4_3">{{ index + 1 }}</view>
                <view>
                  {{ item.supplyName }}
                </view>
              </view>
              <view class="serviceContent4_2" v-if="item.quantity == 999">
                按需
              </view>
              <view v-else> {{ item.quantity }}{{ item.unit?item.unit:'个' }}  </view>
            </view>
          </view>
        </view>
        <view class="serviceContent2">
          <view class="serviceContent2_1"> 产康服务 </view>
          <view
            id="serviceContent3"
            class="serviceContent5"
            v-for="(item, index) in inforList.postpartumList"
            :key="index"
          >
            <view class="serviceContent5_1">
              <view class="serviceContent5_2">{{ index + 1 }}</view>
              <view class="serviceContent5_3">
                {{ item.projectName }}
              </view>
            </view>
            <view class="serviceContent5_4" v-if="item.quantity == 999">
              按需
            </view>
            <view class="serviceContent5_4" v-else>
              {{ item.quantity }}次
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="serviceContent">
      <view class="roomTitle" style="font-weight: bold"> 图文详情 </view>
      <view class="imageText">
        <image
          :src="item"
          v-for="(item, index) in inforList.textPhotos"
          :key="index"
          mode="widthFix"
        >
        </image>
      </view>
    </view>
    <view class="menuTab">
      <view
        :class="current == index ? 'menuTab1' : 'menuTab2'"
        v-for="(item, index) in menuTab"
        :key="index"
        @click="menuTabs(index)"
      >
        <image
          src="http://cdn.xiaodingdang1.com/2025/05/21/9ba923ee11774bfc8b4c7abbf6e52360.png"
          mode=""
          style="width: 32rpx; height: 54rpx"
          v-if="current == index"
        ></image>
        <view :class="current == index ? 'menuTab1_1' : 'menuTab1_2'">
          {{ item }}
        </view>
        <image
          src="http://cdn.xiaodingdang1.com/2025/05/21/917e9c0569e44d41b50b638c2644b9f0.png"
          mode=""
          style="width: 32rpx; height: 54rpx"
          v-if="current == index"
        ></image>
      </view>
    </view>
    <view class="serviceContent">
      <view class="brandTitle">
        {{ GoodsBasicInfo.title }}
      </view>
      <image
        :src="photos"
        mode="widthFix"
        style="width: 100%; border-radius: 12rpx"
      >
      </image>
      <view class="brandComent">
        <view class="brandComent1">
          <view class="brandComent1_2">
            <rich-text :nodes="richText" type="text"></rich-text>
          </view>
        </view>
      </view>
      <view class="brandImg">
        <view class="brandImg1">
          <image :src="photos1" mode="widthFix"></image>
        </view>
        <view class="brandImg1">
          <image :src="photos2" mode="widthFix"></image>
        </view>
      </view>
      <view class="viewMore" @click="logMore">
        <view> 查看更多 </view>
        <image
          src="http://cdn.xiaodingdang1.com/2025/05/21/a096c0778b9e4f9c825e30d2681c7bb7.png"
          mode=""
          style="width: 24rpx; height: 24rpx"
        ></image>
      </view>
    </view>
    <view class="serviceContent">
      <view class="recommend">
        <view class="recommend_1"></view>
        <view class="recommend_2">为您推荐</view>
      </view>
      <view class="recommend1">
        <view
          class="recommend1_1"
          v-for="(item, index) in recoveryPageList"
          :key="index"
          @tap="nextDetails(item)"
        >
          <view style="width: 340rpx; height: 340rpx">
            <view
              class="fit-img"
              :style="{ backgroundImage: 'url(' + item.displayPhotos[0] + ')' }"
            >
            </view>
          </view>
          <view class="recommend1_3">{{ item.projectName }}</view>
          <view class="recommend1_4">价格实时波动 欢迎在线咨询</view>
          <view class="recommend1_5"> 查看详情 </view>
        </view>
      </view>
    </view>
    <view class="blank"></view>
    <new-bottom
      :from="SessionFrom"
      :type="4"
      :id="inforList.packageId"
    ></new-bottom>
    <mask-dialog></mask-dialog>
    <rightMenuBarVue :scrollTop="scrollTop"></rightMenuBarVue>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted, getCurrentInstance, computed } from 'vue';
import { onLoad, onShow, onPageScroll } from '@dcloudio/uni-app';
import rightMenuBarVue from '@/components/rightMenuBar.vue';

// 获取全局属性
const instance = getCurrentInstance();

// 响应式数据
const scrollTop = ref(0);
const tabIndex = ref(0);
const menuTab = ref(['品牌介绍', '会所设施']);
const packagePrice = ref('');
const indexxb = ref(0);
const tag = ref(['住宿服务', '餐饮服务', '妈妈专业服务', '宝宝专业护理']);
const list = ref([]);
const dataList = ref([1, 2, 3, 4, 5]);
const listTab = ref([
  { cate_name: '品牌介绍' },
  { cate_name: '会所设施' },
  { cate_name: '护理团队' },
  { cate_name: '产后康复' },
  { cate_name: '月子膳食' },
]);
const nurseType = {
  1: '1对1护理',
  2: '2对1护理',
  3: '3对1护理',
  4: '集中护理',
};
const current = ref(0);
const inforList = ref('');
const recoveryPageList = ref([]);
const packageId = ref('');
const GoodsBasicInfo = ref('');
const richText = ref('');
const suiteList = ref('');
const outdoorFeatures = ref([]);
const photos = ref('');
const photos1 = ref('');
const photos2 = ref('');

// 新增轮播相关响应式数据
const currentIndex = ref(0);
const videoContext = ref(null);
const isVideoPlaying = ref(false);

// 计算属性 - 轮播控制
const isCurrentVideo = computed(() => {
  if (!list.value || list.value.length === 0) return false;
  const currentItem = list.value[currentIndex.value];
  return getItemType(currentItem) === 'video';
});

const autoplayComputed = computed(() => {
  return !isVideoPlaying.value;
});

const imageUrls = computed(() => {
  return list.value
    .filter((item) => getItemType(item) === 'image')
    .map((item) => getSource(item));
});

// 计算属性 - 清洁服务文本转换
const getCleaningText = (frequency) => {
  const textMap = {
    1: '每天打扫',
    2: '隔天打扫',
    3: '每周打扫',
    4: '其他打扫',
  };
  return textMap[frequency] || '';
};

const getSheetChangeText = (frequency) => {
  const textMap = {
    1: '每天换床单',
    2: '隔天换床单',
    3: '每周换床单',
    4: '其他换床单',
  };
  return textMap[frequency] || '';
};

const getDisinfectionText = (frequency) => {
  const textMap = {
    1: '每天消毒',
    2: '隔天消毒',
    3: '每周消毒',
    4: '其他消毒',
  };
  return textMap[frequency] || '';
};

// 生命周期函数
onLoad(async (options) => {
  packageId.value = options.packageId;
});

onShow(() => {
  recoveryRecommendation();
  packageInfos();
  getGoodsBasicInfo();
});

onPageScroll((e) => {
  scrollTop.value = e.scrollTop;
});

// 轮播相关方法函数
const getItemType = (item) => {
  if (!item) return 'image';

  // 数据已在赋值时处理好type属性
  return item.type || 'image';
};

const getSource = (item) => {
  if (!item) return '';
  return item.url || '';
};

const onSwiperChange = (e) => {
  const newIndex = e.detail.current;

  // 暂停之前的视频播放
  if (isVideoPlaying.value && videoContext.value) {
    videoContext.value.pause();
    isVideoPlaying.value = false;
  }

  currentIndex.value = newIndex;
};

const playVideo = (index) => {
  videoContext.value = uni.createVideoContext(
    `gallery-video-${index}`,
    instance,
  );
  if (videoContext.value) {
    videoContext.value.play();
    isVideoPlaying.value = true;
  }
};

const pauseVideo = (index) => {
  const ctx = uni.createVideoContext(`gallery-video-${index}`, instance);
  if (ctx) {
    ctx.pause();
    isVideoPlaying.value = false;
  }
};

const onVideoPlay = (index) => {
  videoContext.value = uni.createVideoContext(
    `gallery-video-${index}`,
    instance,
  );
  isVideoPlaying.value = true;
};

const onVideoPause = (index) => {
  isVideoPlaying.value = false;
};

const onVideoEnded = (index) => {
  videoContext.value = null;
  isVideoPlaying.value = false;
};

const previewImage = (index) => {
  const currentItem = list.value[index];
  const currentUrl = getSource(currentItem);

  uni.previewImage({
    current: currentUrl,
    urls: imageUrls.value,
  });
};

// 方法函数
const menuTabs = (index) => {
  current.value = index;
  getGoodsBasicInfo();
};

const logMore = () => {
  // 查看更多
  const currentValue = current.value;
  const url =
    currentValue == 0
      ? '/pageA/pageB/home/<USER>'
      : currentValue == 1
      ? '/pageA/pageB/home/<USER>'
      : currentValue == 2
      ? '/pageA/pageB/home/<USER>'
      : currentValue == 3
      ? '/pageA/pageB/home/<USER>'
      : currentValue == 4
      ? '/pageA/pageB/home/<USER>'
      : '';
  uni.navigateTo({
    url: url,
  });
};

const nextDetails = (e) => {
  if (e.suiteId) {
    uni.navigateTo({
      url: '/pageA/pageB/home/<USER>/detail?suiteId=' + e.suiteId,
    });
  } else {
    uni.navigateTo({
      url: '/pageA/pageB/home/<USER>/detail?projectId=' + e.projectId,
    });
  }
};

const change = (index) => {
  current.value = index;
  getGoodsBasicInfo();
};

const clickTab = (index) => {
  current.value = index;
  getGoodsBasicInfo();
};

const getGoodsBasicInfo = () => {
  // 获取所有商品基础信息
  instance.proxy.$axios
    .get(instance.proxy.$api.getGoodsBasicInfo)
    .then((res) => {
      if (res.data.code == 200) {
        const responseData = res.data.data;
        const currentPhotos = responseData[current.value].photos[indexxb.value];
        const currentPhotos1 = responseData[current.value].photos[1];
        const currentPhotos2 = responseData[current.value].photos[2];

        GoodsBasicInfo.value = responseData[current.value];
        photos.value = currentPhotos;
        photos1.value = currentPhotos1;
        photos2.value = currentPhotos2;

        getrichText();
      }
    });
};

const getrichText = () => {
  const data = GoodsBasicInfo.value;
  richText.value = data.description
    .replace(/<img[^>]*>/gi, function (match) {
      return match.replace(/style=".*"/gi, '').replace(/style='.*'/gi, '');
    })
    .replace(/\<img/gi, '<img style="width:100%;height:auto;display:block;"');
};

const packageInfos = () => {
  // 查询优惠套餐列表
  const data = {
    packageId: packageId.value,
  };
  instance.proxy.$axios
    .get(instance.proxy.$api.packageInfo, data)
    .then((res) => {
      if (res.data.code == 200) {
        const responseData = res.data.data;
        const price1 = responseData.packagePrice.slice(0, 1);
        const price2 = responseData.packagePrice.slice(2, 10);
        // 添加card组件展示需要的字段
        const calculatedPackagePrice = price1 + '*' + price2;

        inforList.value = responseData;
        packagePrice.value = calculatedPackagePrice;
        suiteList.value = responseData.suiteList[indexxb.value];
        outdoorFeatures.value =
          responseData.suiteList[indexxb.value].outdoorFeatures;
        // 处理图片数据，添加type属性
        const photos = responseData?.packagePhotos
          ? responseData.packagePhotos
          : responseData.suiteList[indexxb.value].suitePhotos;

        const processedPhotos = photos.map((photo) => ({
          url: photo,
          type: 'image',
        }));

        // 处理视频数据，添加type属性
        const processedVideos = (responseData?.packageVideos || []).map(
          (video) => ({
            url: video,
            type: 'video',
          }),
        );
        // 合并图片和视频数据
        list.value = [...processedPhotos, ...processedVideos];
      }
    });
};

const recoveryRecommendation = () => {
  // 查询优惠套餐热门推荐列表
  const data = {
    limit: 2,
  };
  instance.proxy.$axios
    .get(instance.proxy.$api.recoveryRecommendation, data)
    .then((res) => {
      if (res.data.code == 200) {
        recoveryPageList.value = res.data.data;
      }
    });
};
</script>

<style lang="scss" scoped>
// 主容器
.confinement-details {
  background: #f6f6f6;
}

// 通用样式
.section-title {
  color: #333333;
  font-size: 32rpx;
  font-weight: bold;

  &--bold {
    font-weight: bold;
  }
}

// 图片画廊
.image-gallery {
  .gallery-swiper {
    height: 700rpx;
    width: 100%;

    .gallery-video {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .gallery-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
}

// 价格区域
.price-section {
  background: #fff;
  padding: 32rpx 24rpx;

  .price-info {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .price-amount {
      color: #fd2d39;
      font-size: 56rpx;
      font-weight: bold;
    }

    .consult-count {
      color: #aaaaaa;
      font-size: 24rpx;
    }
  }

  .package-info {
    margin-top: 32rpx;

    .package-header {
      width: 100%;

      .package-title-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;

        .package-name {
          color: #333333;
          font-size: 36rpx;
          font-weight: bold;
        }

        .share-button-wrapper {
          width: 20%;
          height: 50rpx;

          .share-button {
            display: flex;
            align-items: center;
            background: #ffffff;
            font-size: 24rpx;
            color: #777777;

            .share-icon {
              margin-right: 10rpx;
              width: 26rpx;
              height: 26rpx;
            }
          }
        }
      }

      .service-tags {
        display: flex;
        width: 100%;
        margin-top: 20rpx;
        align-items: center;

        .service-label {
          font-weight: 400;
          font-size: 28rpx;
          color: #aaaaaa;
          margin-right: 32rpx;
          padding: 4rpx 12rpx;
        }

        .tags-scroll {
          flex: 1;
          display: flex;
          justify-content: center;
          overflow-x: scroll;
          overflow-y: hidden;
          white-space: nowrap;

          .tag-item {
            background-color: #f8f8f8;
            display: inline-block;
            font-weight: 400;
            font-size: 24rpx;
            color: #333333;
            padding: 4rpx 12rpx;
            margin-right: 24rpx;
            border-radius: 6rpx;
          }
        }
      }
    }
  }
}
// 设施区域
.facility-section {
  padding: 24rpx 24rpx;
  background: #fff;
  margin-top: 20rpx;

  .facility-grid {
    width: 100%;
    display: flex;
    flex-wrap: wrap;

    .facility-item {
      display: flex;
      width: 50%;
      align-items: center;
      height: 108rpx;

      .facility-icon {
        width: 47rpx;
        height: 47rpx;
      }

      .facility-content {
        margin-left: 20rpx;

        .facility-label {
          color: #929292;
          font-size: 20rpx;
        }

        .facility-value {
          color: #333333;
          font-size: 22rpx;
          display: flex;
        }
      }
    }
  }
}
// 配套设施区域
.ancillary-section {
  background: #fff;
  padding: 24rpx 24rpx;
  margin-top: 20rpx;

  .ancillary-list {
    .ancillary-category {
      margin-top: 22rpx;

      .category-header {
        display: flex;
        align-items: center;

        .category-icon {
          width: 30rpx;
          height: 30rpx;
        }

        .category-title {
          color: #333333;
          font-size: 26rpx;
          font-weight: bold;
          margin-left: 6rpx;
        }
      }

      .feature-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        margin-top: 22rpx;
        gap: 10px;
        width: 90%;
        margin: 10px auto;

        .feature-item {
          text-align: left;
          color: #666666;
          font-size: 22rpx;
        }
      }
    }
  }
}

.serviceContent {
  background: #fff;
  padding: 24rpx 24rpx;
  .serviceContent1 {
    .serviceContent2 {
      border: 1rpx solid #f1f1f1;
      border-radius: 6rpx;
      margin-top: 20rpx;
      .serviceContent2_1 {
        color: #333333;
        font-size: 24rpx;
        font-weight: bold;
        padding: 16rpx 20rpx;
        background: #f6f6f6;
      }
      .serviceContent2_2 {
        display: flex;
        font-size: 22rpx;
        padding: 16rpx 20rpx;
        border-top: 1rpx solid #f1f1f1;
        .serviceContent2_2_1 {
          color: #666666;
          width: 20%;
        }
        .serviceContent2_2_2 {
          color: #333333;
        }
      }
      .serviceContent3 {
        display: flex;
        font-size: 22rpx;
        padding: 12rpx 20rpx;
        width: 100%;
        .serviceContent3_1 {
          width: 25%;
          color: #666666;
        }
        .serviceContent4 {
          width: 75%;
          display: flex;
          justify-content: space-between;
          color: #333333;
          .serviceContent4_1 {
            display: flex;
            align-items: center;
            .serviceContent4_3 {
              background: #fdf1ed;
              color: #eb5735;
              width: 24rpx;
              height: 24rpx;
              border-radius: 100rpx;
              font-size: 20rpx;
              text-align: center;
              line-height: 24rpx;
              margin-right: 10rpx;
            }
          }
        }
      }
    }
  }
}
#serviceContent3 {
  border-top: 1rpx solid #f1f1f1;
}
.serviceContent5 {
  display: flex;
  justify-content: space-between;
  padding: 16rpx 20rpx;
  .serviceContent5_1 {
    display: flex;
    align-items: center;
    .serviceContent5_2 {
      background: #fdf1ed;
      color: #eb5735;
      width: 24rpx;
      height: 24rpx;
      border-radius: 100rpx;
      font-size: 20rpx;
      text-align: center;
      line-height: 24rpx;
      margin-right: 10rpx;
    }
    .serviceContent5_3 {
      color: #333333;
      font-size: 22rpx;
    }
  }
  .serviceContent5_4 {
    color: #666666;
    font-size: 22rpx;
  }
}
.imageText {
  image {
    width: 100%;
    border-radius: 12rpx;
    margin-top: 20rpx;
  }
}
.brandTitle {
  text-align: center;
  color: #5c5c5c;
  font-size: 32rpx;
  font-weight: bold;
  margin: 0rpx 0 20rpx 0;
}
.brandComent {
  margin-top: 28rpx;
  .brandComent1 {
    display: flex;
    align-items: center;
    padding: 10rpx 0;
    width: 100%; /* 宽度占满容器 */
    flex-wrap: wrap;
    .brandComent1_3 {
      width: 2%;
      .brandComent1_1 {
        width: 10rpx;
        height: 10rpx;
        border-radius: 100rpx;
        background: #fd2d39;
      }
    }
    .brandComent1_2 {
      color: #333333;
      font-size: 24rpx;
      margin-left: 10rpx;
      width: 98%;
    }
  }
}
.brandImg {
  display: flex;
  margin: 28rpx 0;
  justify-content: space-between;
  .brandImg1 {
    width: 48%;
    image {
      width: 100%;
      border-radius: 8rpx;
    }
  }
}
.viewMore {
  border-radius: 100rpx;
  background: #ff4f61;
  width: 156rpx;
  height: 52rpx;
  text-align: center;
  line-height: 52rpx;
  color: #fff;
  font-size: 22rpx;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
}
.recommend {
  display: flex;
  align-items: center;
  .recommend_1 {
    background: #ff4f61;
    height: 28rpx;
    width: 8rpx;
    border-radius: 10rpx;
  }
  .recommend_2 {
    color: #333333;
    font-size: 34rpx;
    font-weight: bold;
    margin-left: 20rpx;
  }
}
.recommend1 {
  width: 100%;
  margin: 20rpx 0;
  display: flex;
  justify-content: space-between;
  .recommend1_1 {
    width: 48%;
    text-align: center;
    .recommend1_2 {
      width: 340rpx;
      height: 340rpx;
      image {
        width: 100%;
        height: 340rpx;
      }
    }
    .recommend1_3 {
      color: #333333;
      font-size: 30rpx;
      font-weight: bold;
      margin: 10rpx 0;
    }
    .recommend1_4 {
      color: #777777;
      font-size: 24rpx;
    }
    .recommend1_5 {
      width: 136rpx;
      height: 58rpx;
      border-radius: 100rpx;
      color: #fff;
      background: #ff4f61;
      font-size: 24rpx;
      line-height: 58rpx;
      margin: 20rpx auto;
    }
  }
}
.blank {
  height: 240rpx;
}
.fit-img {
  width: 100%;
  height: 100%;
  border-radius: 10rpx;
  background-size: cover;
  background-position: center;
}
.my_text {
  word-break: break-word; //可以避免文字超出限制
}
.serveTab {
  display: flex;
  width: 100%;
  margin-top: 20px;
  align-items: center;
  .serveTab1 {
    font-weight: 400;
    font-size: 28rpx;
    color: #aaaaaa;
    margin-right: 32rpx;
    padding: 4rpx 12rpx;
  }
  .serveTab2 {
    flex: 1;
    display: flex;
    justify-content: center;
    overflow-x: scroll;
    overflow-y: hidden;
    white-space: nowrap;
    .serveTab2_1 {
      background-color: #f8f8f8;
      display: inline-block;
      font-weight: 400;
      font-size: 24rpx;
      color: #333333;
      padding: 4rpx 12rpx;
      margin-right: 24rpx;
    }
  }
}
.roomMessage2_6 {
  width: 100%;
  display: flex;
  justify-content: space-between;
  .roomMessage2_4 {
    width: 20%;
    height: 50rpx;
    .image-text-button {
      display: flex; /* 使用flex布局 */
      align-items: center; /* 垂直居中 */
      background: #ffffff;
      font-size: 24rpx;
      color: #777777;
      .button-icon {
        margin-right: 10rpx; /* 图片与文字的间距 */
        width: 26rpx; /* 图片宽度 */
        height: 26rpx; /* 图片高度 */
      }
    }
  }
}

.scroll-container {
  display: flex;
  white-space: nowrap;
  height: 110rpx;
  .scroll-item {
    display: inline-block;
    background: #fdf1ed;
    border-radius: 6rpx;
    width: 172rpx;
    height: 72rpx;
    color: #5c5c5c;
    font-size: 26rpx;
    margin-right: 20rpx;
    text-align: center;
    line-height: 72rpx;
  }
}
.box-with-arrow {
  position: relative;
  display: inline-block;
  background: #ff4f61;
  border-radius: 6rpx;
  width: 172rpx;
  height: 72rpx;
  color: #5c5c5c;
  font-size: 26rpx;
  margin-right: 20rpx;
  text-align: center;
  line-height: 72rpx;
  color: #fff;
}

/* 尖角实现 */
.box-with-arrow1 {
  content: '';
  position: absolute;
  left: 50%;
  bottom: -8px; /* 调整尖角位置 */
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-top: 10px solid #ff4f61; /* 尖角颜色与边框一致 */
}
.menuTab {
  display: flex;
  width: 100%;
  margin-top: 20rpx;
  .menuTab1 {
    padding: 20rpx 0;
    display: flex;
    width: 50%;
    background: #fff;
    align-items: center;
    justify-content: space-around;
    border-top-left-radius: 50rpx;
    border-top-right-radius: 50rpx;
    .menuTab1_1 {
      color: #333333;
      font-size: 30rpx;
      font-weight: bold;
    }
  }
  .menuTab2 {
    padding: 20rpx 0;
    display: flex;
    width: 50%;
    background: #ededed;
    align-items: center;
    justify-content: space-around;
    border-top-left-radius: 50rpx;
    border-top-right-radius: 50rpx;
    .menuTab1_2 {
      color: #9c9c9c;
      font-size: 30rpx;
      font-weight: bold;
    }
  }
}
</style>
