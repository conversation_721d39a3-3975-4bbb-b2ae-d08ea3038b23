.mains {
    margin: 24rpx 24rpx;
}

.dynamicTitle {
    display: flex;
}

.dynamicTitle1 {
    width: 100%;
}

.dynamicTitle1_2 {
    margin-top: 8rpx;
    font-size: 28rpx;
    color: #333333;
}

.beyond {
    margin-top: 8rpx;
    font-size: 28rpx;
    color: #333333;
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 5;
}

.dynamicTitle1_3 {
    margin: 12px 0;
    display: flex;
    justify-content: space-between;
}

.dynamicTitle1_3_1 {
    font-size: 24rpx;
    color: #777777;
}

.fgx {
    width: 100%;
    height: 1rpx;
    background: #ebebeb;
}

.img_box {
    margin-top: 20rpx;
}

.videos {
    width: 322rpx;
    height: 430rpx;
}

.videos video {
    width: 100%;
    height: 100%;
}

.img_box .many_img {
    display: flex;
    justify-self: start;
    flex-wrap: wrap;
    width: 100%;
}

.img_item.four {
    width: 186rpx;
    height: 186rpx;
    margin-right: 10rpx;
    margin-bottom: 10rpx;
    border-radius: 16rpx;
    overflow: hidden;
}

.img_item.four image {
    width: 100%;
    height: 100%;
}

.img_item.four:nth-child(2n) {
    margin-right: 10rpx;
}

.img_item.many {
    width: 49%;
    height: 280rpx;
    margin-bottom: 10rpx;
    border-radius: 10px;
    overflow: hidden;
}

.img_item.many image {
    width: 100%;
    height: 100%;
}

.img_item.many:nth-child(2n) {
    margin-left: 1%;
}

/* .img_item.many:nth-child(3n) {
    margin-right: 0;
} */

.packUp {
    color: #4d669b;
    font-size: 30rpx;
}


.dynamicTitle1_1 {
    font-size: 30rpx;
    color: #5b6799;
    font-weight: bold;
    display: flex;
    align-items: center;
    margin-bottom: 8rpx;
}


.dynamicTitle1_1>image {
    margin-right: 10rpx;
    width: 85rpx;
    height: 85rpx;
    border-radius: 2rpx;
}

.dynamicTitle-right {
    flex: 1;
    margin-top: 8rpx;
}

.dynamicTitle-right view:first-of-type {
    font-weight: 500;
    font-size: 30rpx;
    color: #5B6799;
}

.dynamicTitle-right view:last-of-type {
    font-weight: 400;
    font-size: 24rpx;
    color: #777777;
}