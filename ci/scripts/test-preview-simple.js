#!/usr/bin/env node

/**
 * 简化的预览测试脚本
 * 用于诊断预览生成问题
 */

const ci = require('miniprogram-ci');
const path = require('path');
const fs = require('fs');

// 颜色输出
function logInfo(message) {
  console.log(`ℹ️  ${message}`);
}

function logError(message) {
  console.log(`❌ ${message}`);
}

function logSuccess(message) {
  console.log(`✅ ${message}`);
}

async function testPreview() {
  try {
    logInfo('开始预览测试...');
    
    // 检查构建产物
    const projectPath = path.resolve(__dirname, '../../dist/build/mp-weixin');
    if (!fs.existsSync(projectPath)) {
      logError('构建产物不存在');
      return;
    }
    logSuccess('构建产物存在');

    // 检查私钥文件
    const privateKeyPath = path.resolve(__dirname, '../private/private.wx1df89e340decbc7a.key');
    if (!fs.existsSync(privateKeyPath)) {
      logError('私钥文件不存在');
      return;
    }
    logSuccess('私钥文件存在');

    // 分析包大小
    const { totalSize } = getDirectorySize(projectPath);
    const sizeMB = (totalSize / (1024 * 1024)).toFixed(2);
    logInfo(`包大小: ${sizeMB}MB`);
    
    if (totalSize > 2048 * 1024) {
      logError(`包大小超出限制 (${sizeMB}MB > 2MB)`);
      logInfo('建议优化措施:');
      console.log('  1. 将部分页面移到分包');
      console.log('  2. 压缩图片资源');
      console.log('  3. 移除未使用的代码');
      return;
    }

    // 创建项目实例
    const miniProgram = new ci.Project({
      appid: 'wx1df89e340decbc7a',
      type: 'miniProgram',
      projectPath: projectPath,
      privateKeyPath: privateKeyPath,
      ignores: ['node_modules/**/*']
    });

    logInfo('创建预览...');

    // 预览选项
    const previewOptions = {
      project: miniProgram,
      version: '0.0.1',
      desc: '测试预览',
      robot: 1,
      qrcodeFormat: 'image',
      qrcodeOutputDest: path.resolve(__dirname, '../preview/test-preview.jpg'),
      pagePath: 'pageA/home',
      onProgressUpdate: (progress) => {
        console.log(`进度: ${JSON.stringify(progress)}`);
      },
      minify: true
    };

    // 确保preview目录存在
    const previewDir = path.dirname(previewOptions.qrcodeOutputDest);
    if (!fs.existsSync(previewDir)) {
      fs.mkdirSync(previewDir, { recursive: true });
    }

    const result = await ci.preview(previewOptions);
    
    logSuccess('预览生成成功！');
    logInfo(`二维码保存到: ${previewOptions.qrcodeOutputDest}`);
    
    if (result.subPackageInfo) {
      logInfo('分包信息:');
      result.subPackageInfo.forEach(pkg => {
        console.log(`  ${pkg.name}: ${pkg.size}KB`);
      });
    }

  } catch (error) {
    logError(`预览失败: ${error.message}`);
    
    if (error.message.includes('exceed max limit')) {
      const match = error.message.match(/(\d+)KB exceed max limit (\d+)KB/);
      if (match) {
        const actualSize = parseInt(match[1]);
        const maxSize = parseInt(match[2]);
        const overSize = actualSize - maxSize;
        logError(`包大小超出 ${overSize}KB`);
        logInfo('需要优化包大小才能生成预览');
      }
    }
  }
}

/**
 * 获取目录大小
 */
function getDirectorySize(dirPath) {
  let totalSize = 0;

  function scanDirectory(dir) {
    if (!fs.existsSync(dir)) return;
    
    const items = fs.readdirSync(dir);
    
    items.forEach(item => {
      const itemPath = path.join(dir, item);
      const stats = fs.statSync(itemPath);
      
      if (stats.isDirectory()) {
        scanDirectory(itemPath);
      } else {
        totalSize += stats.size;
      }
    });
  }

  scanDirectory(dirPath);
  return { totalSize };
}

// 运行测试
if (require.main === module) {
  testPreview().catch(error => {
    logError(`执行失败: ${error.message}`);
    process.exit(1);
  });
}
