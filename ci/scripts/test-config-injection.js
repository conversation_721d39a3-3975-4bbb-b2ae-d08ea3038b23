#!/usr/bin/env node

/**
 * 配置注入功能测试脚本
 * 用于测试租户ID和AppID注入功能
 */

const BuildConfigInjector = require('./build-config-injector.js');
const fs = require('fs');
const path = require('path');

// 颜色输出
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

/**
 * 测试配置注入功能
 */
async function testConfigInjection() {
  log('🧪 开始测试配置注入功能', 'blue');
  log('='.repeat(50));

  const injector = new BuildConfigInjector();
  const testConfig = {
    tenantId: '999999',
    appid: 'wx1234567890abcdef',
    projectName: 'test-project',
    environment: 'develop'
  };

  try {
    // 检查源文件是否存在
    const rootPath = path.resolve(__dirname, '../..');
    const appVuePath = path.join(rootPath, 'src/App.vue');
    const configPath = path.join(rootPath, 'src/utils/config.js');

    logInfo('检查源文件...');
    
    if (!fs.existsSync(appVuePath)) {
      logWarning(`App.vue 文件不存在: ${appVuePath}`);
    } else {
      logSuccess(`App.vue 文件存在: ${appVuePath}`);
    }

    if (!fs.existsSync(configPath)) {
      logWarning(`config.js 文件不存在: ${configPath}`);
    } else {
      logSuccess(`config.js 文件存在: ${configPath}`);
    }

    // 执行配置注入
    logInfo('执行配置注入...');
    injector.inject(testConfig);

    // 验证注入结果
    logInfo('验证注入结果...');
    
    if (fs.existsSync(configPath)) {
      const configContent = fs.readFileSync(configPath, 'utf8');
      if (configContent.includes(`TENANT_ID: '${testConfig.tenantId}'`)) {
        logSuccess(`租户ID注入成功: ${testConfig.tenantId}`);
      } else {
        logError('租户ID注入失败');
      }
    }

    // 恢复备份
    logInfo('恢复原始文件...');
    injector.restoreBackups();

    logSuccess('🎉 配置注入功能测试完成！');

  } catch (error) {
    logError(`测试失败: ${error.message}`);
    
    // 确保恢复备份
    try {
      injector.restoreBackups();
      logInfo('已恢复原始文件');
    } catch (restoreError) {
      logError(`恢复备份失败: ${restoreError.message}`);
    }
    
    process.exit(1);
  }
}

/**
 * 显示帮助信息
 */
function showHelp() {
  log('配置注入功能测试脚本', 'blue');
  log('');
  log('使用方法:', 'yellow');
  log('  node ci/scripts/test-config-injection.js', 'white');
  log('');
  log('功能说明:', 'yellow');
  log('  - 测试租户ID和AppID注入功能', 'white');
  log('  - 验证配置文件修改是否正确', 'white');
  log('  - 自动恢复原始文件', 'white');
  log('');
}

// 主函数
async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--help') || args.includes('-h')) {
    showHelp();
    return;
  }

  await testConfigInjection();
}

// 运行主函数
if (require.main === module) {
  main().catch(error => {
    logError(`脚本执行失败: ${error.message}`);
    process.exit(1);
  });
}

module.exports = {
  testConfigInjection
};
