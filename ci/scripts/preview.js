#!/usr/bin/env node

/**
 * 小程序多项目预览脚本
 * 支持预览不同的 appid 项目
 */

const ci = require('miniprogram-ci');
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const { projects, defaultConfig, getProject, validateProject } = require('../config/config-loader.js');
const BuildConfigInjector = require('./build-config-injector.js');

// 命令行参数解析
const args = process.argv.slice(2);

// 解析命令行参数
function parseArgs(args) {
  const result = {
    projectName: null,
    environment: null,
    pagePath: null,
    searchQuery: null
  };

  let positionalIndex = 0;
  let i = 0;

  while (i < args.length) {
    const arg = args[i];

    if (arg === '--page' && i + 1 < args.length) {
      result.pagePath = args[i + 1];
      i += 2;
    } else if (arg === '--query' && i + 1 < args.length) {
      result.searchQuery = args[i + 1];
      i += 2;
    } else if (arg === '--help' || arg === '-h') {
      result.showHelp = true;
      i++;
    } else if (!arg.startsWith('--')) {
      // 位置参数
      if (positionalIndex === 0) {
        result.projectName = arg;
      } else if (positionalIndex === 1) {
        result.environment = arg;
      }
      positionalIndex++;
      i++;
    } else {
      i++;
    }
  }

  // 设置默认值
  result.projectName = result.projectName || 'main';
  result.environment = result.environment || 'develop';

  return result;
}

const parsedArgs = parseArgs(args);
const projectName = parsedArgs.projectName;
const environment = parsedArgs.environment;
const pagePath = parsedArgs.pagePath;
const searchQuery = parsedArgs.searchQuery;

// 颜色输出
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

// 显示帮助信息
function showHelp() {
  log('小程序多项目预览工具', 'cyan');
  log('');
  log('使用方法:', 'yellow');
  log('  node scripts/preview.js [项目名] [环境] [页面路径] [页面参数]', 'white');
  log('');
  log('参数说明:', 'yellow');
  log('  项目名   - 项目标识，可选值：' + Object.keys(projects).join(', '), 'white');
  log('  环境     - 部署环境，可选值：develop, trial, release', 'white');
  log('  页面路径 - 预览页面路径，如：pages/index/index', 'white');
  log('  页面参数 - 页面参数，如：id=123&name=test', 'white');
  log('');
  log('示例:', 'yellow');
  log('  node scripts/preview.js main develop', 'white');
  log('  node scripts/preview.js main develop pages/detail/detail id=123', 'white');
  log('  node scripts/preview.js test develop pages/index/index', 'white');
}

// 检查私钥文件
function checkPrivateKey(privateKeyPath) {
  if (!fs.existsSync(privateKeyPath)) {
    logError(`私钥文件不存在: ${privateKeyPath}`);
    logInfo('请确保私钥文件存在，或者从微信公众平台下载私钥文件');
    logInfo('私钥文件下载地址：https://mp.weixin.qq.com -> 开发 -> 开发管理 -> 开发设置 -> 小程序代码上传');
    return false;
  }
  return true;
}

// 构建项目
async function buildProject(project, environment, projectName) {
  const envConfig = project.environments[environment];
  if (!envConfig) {
    throw new Error(`项目 "${projectName}" 不支持环境 "${environment}"`);
  }

  logInfo(`开始构建项目: ${project.name} (${envConfig.name})`);

  // 注入构建配置
  const injector = new BuildConfigInjector();
  const tenantId = envConfig.tenantId || project.defaultTenantId || '194338';

  try {
    // 注入配置
    injector.inject({
      tenantId,
      appid: project.appid,
      projectName,
      environment
    });

    // 执行构建
    execSync(envConfig.buildCommand, {
      stdio: 'inherit',
      cwd: path.resolve(__dirname, '../..')
    });

    logSuccess('项目构建完成');

    // 清理备份文件
    injector.cleanupBackups();

  } catch (error) {
    // 构建失败时恢复备份
    injector.restoreBackups();
    throw new Error(`项目构建失败: ${error.message}`);
  }
}

// 生成预览二维码
async function generatePreview(project, environment, pagePath, searchQuery) {
  logInfo(`开始生成预览二维码: ${project.name}`);

  try {
    // 确保预览目录存在
    const previewDir = defaultConfig.preview.qrcodeOutputDir;
    if (!fs.existsSync(previewDir)) {
      fs.mkdirSync(previewDir, { recursive: true });
      logInfo(`创建预览目录: ${previewDir}`);
    }

    // 生成二维码文件路径
    const qrcodeOutputPath = path.join(previewDir, `preview-${projectName}-${environment}.jpg`);

    // 创建项目实例
    const miniProgram = new ci.Project({
      appid: project.appid,
      type: 'miniProgram',
      projectPath: project.projectPath,
      privateKeyPath: project.privateKeyPath,
      ignores: project.ignores || []
    });

    // 生成预览二维码
    const previewResult = await ci.preview({
      project: miniProgram,
      desc: `${project.name} - ${project.environments[environment].name} 预览`,
      setting: {
        es6: true,
        es7: true,
        minify: true,
        codeProtect: false,
        minifyJS: true,
        minifyWXML: true,
        minifyWXSS: true,
        autoPrefixWXSS: true
      },
      qrcodeFormat: defaultConfig.preview.qrcodeFormat,
      qrcodeOutputDest: qrcodeOutputPath,
      pagePath: pagePath || defaultConfig.preview.pagePath,
      searchQuery: searchQuery || defaultConfig.preview.searchQuery,
      scene: defaultConfig.preview.scene,
      onProgressUpdate: (progress) => {
        // logInfo(`生成进度: ${JSON.stringify(progress)}%`);
      }
    });

    logSuccess(`预览二维码生成成功！`);
    logInfo(`二维码路径: ${qrcodeOutputPath}`);
    logInfo(`页面路径: ${pagePath || defaultConfig.preview.pagePath}`);
    if (searchQuery) {
      logInfo(`页面参数: ${searchQuery}`);
    }

    return previewResult;
  } catch (error) {
    throw new Error(`预览生成失败: ${error.message}`);
  }
}

// 主函数
async function main() {
  try {
    // 显示帮助信息
    if (parsedArgs.showHelp) {
      showHelp();
      return;
    }

    logInfo('='.repeat(50));
    logInfo('小程序多项目预览工具');
    logInfo('='.repeat(50));

    // 验证项目配置
    const validation = validateProject(projectName);
    if (!validation.valid) {
      logError(validation.error);
      logInfo('可用项目: ' + Object.keys(projects).join(', '));
      return;
    }

    // 获取项目配置
    const project = getProject(projectName);
    
    // 检查环境配置
    if (!project.environments[environment]) {
      logError(`项目 "${projectName}" 不支持环境 "${environment}"`);
      logInfo('可用环境: ' + Object.keys(project.environments).join(', '));
      return;
    }

    // 检查私钥文件
    if (!checkPrivateKey(project.privateKeyPath)) {
      return;
    }

    logInfo(`项目名称: ${project.name}`);
    logInfo(`项目 AppID: ${project.appid}`);
    logInfo(`部署环境: ${project.environments[environment].name}`);
    logInfo(`预览页面: ${pagePath || defaultConfig.preview.pagePath}`);
    if (searchQuery) {
      logInfo(`页面参数: ${searchQuery}`);
    }
    logInfo('');

    // 构建项目
    await buildProject(project, environment, projectName);
    
    // 检查构建产物
    if (!fs.existsSync(project.projectPath)) {
      logError(`构建产物不存在: ${project.projectPath}`);
      logInfo('请检查构建命令是否正确执行');
      return;
    }

    // 生成预览二维码
    await generatePreview(project, environment, pagePath, searchQuery);

    logSuccess('🎉 预览二维码生成完成！');
    logInfo('请使用微信扫描二维码进行预览');
    
  } catch (error) {
    logError(error.message);
    process.exit(1);
  }
}

// 运行主函数
main();
