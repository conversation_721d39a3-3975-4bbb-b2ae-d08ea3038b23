<template>
  <view class="mom-recommendation">
    <view class="discover">
      <view>发现更多宝妈笔记</view>
      <view @tap="() => $jumpPage('/pageA/pageB/community/sending/list')">
        查看更多
      </view>
    </view>
    <scroll-view
      :show-scrollbar="false"
      :scroll-x="true"
      class="tab-h1"
      :scroll-this="scrollPosition"
    >
      <view
        class="mom-card-wrapper"
        v-for="(item, index) in recommendedMomList"
        :key="index"
      >
        <view class="mom-card" @tap="handleMomHomepage(item)">
          <image :src="item.avatar || $defaultAvatar" mode="aspectFill" />
          <view class="mom-card__name">
            {{ item.name || $defaultName }}
          </view>
          <view class="mom-card__description">优质动态宝妈</view>
          <view class="mom-card__button">主页</view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup>
import { getCurrentInstance } from 'vue'
import { PAGE_ROUTES } from '../constants.js'

const { proxy } = getCurrentInstance()

const props = defineProps({
  recommendedMomList: {
    type: Array,
    default: () => []
  },
  scrollPosition: {
    type: Number,
    default: 0
  }
})

// 内部处理宝妈主页跳转逻辑
const handleMomHomepage = (item) => {
  console.log('跳转宝妈主页', item)

  const params = {
    userId: item.userId,
    momId: item.momId,
    uuid: item.uuid,
  }

  // 订阅消息
  uni.requestSubscribeMessage({
    tmplIds: ['4ozrWQOn4Z0lxEfPNXyVzNaKCWaanMNYypzOu8PH9I0'],
    success() {
      console.log('订阅消息成功')
      // 订阅成功后跳转
      proxy.$jumpPage(PAGE_ROUTES.MOM_HOMEPAGE, params)
    },
    fail(res) {
      console.log('订阅消息失败', res)
      // 订阅失败也允许跳转
      proxy.$jumpPage(PAGE_ROUTES.MOM_HOMEPAGE, params)
    },
  })
}
</script>

<style scoped lang="less">
.mom-recommendation {
  .discover {
    color: #777777;
    font-size: 24rpx;
    margin-top: 24rpx;
    margin-bottom: 12rpx;
    display: flex;
    width: 100%;
    justify-content: space-between;
    align-items: center;
    padding-right: 12rpx;
  }

  .tab-h1 {
    width: 100%;
    box-sizing: border-box;
    overflow: hidden;
    font-size: 16px;
    white-space: nowrap;
    margin-bottom: 20rpx;
  }
}

.mom-card-wrapper {
  display: inline-block;
  margin-right: 12rpx;
}

.mom-card {
  padding: 28rpx 0;
  border-radius: 20rpx;
  width: 272rpx;
  text-align: center;
  position: relative;
  background: linear-gradient(to bottom, #ffefef 0%, #ffffff 100%);
  border: 2rpx solid #ffd6d7;

  image:first-child {
    width: 110rpx;
    height: 110rpx;
    border-radius: 110rpx;
  }

  &__name {
    color: #333333;
    font-size: 30rpx;
  }

  &__description {
    color: #777777;
    font-size: 24rpx;
    margin-top: 8rpx;
    margin-bottom: 20rpx;
  }

  &__button {
    font-weight: 400;
    font-size: 28rpx;
    color: #ffffff;
    line-height: 47rpx;
    background: #ff4f61;
    text-align: center;
    margin: 0 auto;
    width: 136rpx;
    height: 47rpx;
    border-radius: 8rpx;
  }
}
</style>
