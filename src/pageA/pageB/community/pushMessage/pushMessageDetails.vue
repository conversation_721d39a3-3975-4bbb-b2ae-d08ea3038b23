<template>
  <view class="main">
    <view class="people" @tap="people">
      <view :class="peopleChecked ? 'peoples1' : 'people1'"> 所有人 </view>
      <u-icon
        name="checkbox-mark"
        :color="peopleChecked ? '#2979ff' : '#333333'"
        size="32"
      ></u-icon>
    </view>
    <view class="peopleTitle"> 可精细化推送 </view>
    <view class="collapseComent">
      <u-collapse
        class="collapse"
        active-color="#1FA2FF"
        :item-style="itemStyle"
        event-type="close"
        :arrow="arrow"
        :accordion="false"
        @change="change"
      >
        <u-collapse-item
          :value="isExpanded"
          :index="index"
          @change="itemChange"
          :title="getTitle(index)"
          v-for="(item, index) in itemList"
          :key="index"
          style="color: #333; font-size: 32rpx; font-weight: bold"
        >
          <view
            class="collapse-item"
            v-for="(res, i) in item.body"
            :key="i"
            @click.capture="checkboxChange(res, index, i)"
          >
            <view class="collapse-item-title">
              <view class="collapse-item-title1">{{ res.name }}</view>
              <view v-if="res.tel" class="collapse-item-title2">{{
                res.tel
              }}</view>
            </view>
            <view class="">
              <u-checkbox-group>
                <!-- 增加了 padding 和 margin 来扩大勾选区域 -->
                <view
                  style="padding: 10px; margin: -10px; display: inline-block"
                >
                  <u-checkbox
                    :modelValue="res.checked"
                    shape="circle"
                    :disabled="i === 0 && selectAllLoading[index]"
                  >
                    <text
                      v-if="i === 0 && selectAllLoading[index]"
                      class="loading-text"
                      >处理中...</text
                    >
                  </u-checkbox>
                </view>
              </u-checkbox-group>
            </view>
          </view>
        </u-collapse-item>
      </u-collapse>
    </view>
    <view class="black"></view>
    <view class="bottomBtn">
      <view class="confirm" @tap="confirm"> 确定 </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      isExpanded: true,
      peopleChecked: false,
      itemList: [],
      useridList: [],
      originalData: null,
      selectAllLoading: [false, false, false], // 用于防止全选操作时的重复点击
    };
  },
  onLoad() {
    this.getCustomerTracks(); // 页面加载时请求数据
  },
  methods: {
    people() {
      this.useridList = [];
      this.peopleChecked = !this.peopleChecked;
      this.getCustomerTracks(); // 页面加载时请求数据
    },
    confirm() {
      // 在当前页面
      let globalData = getApp().globalData; // 假设你在App实例中定义了globalData
      globalData.useridList = this.useridList;
      uni.navigateBack({
        delta: 1, // 返回上一页
      });
    },
    getTitle(index) {
      if (!this.itemList[index]) return "";
      const item = this.itemList[index];
      const selectedCount = item.body.filter(
        (x) => x.checked && x.userId
      ).length;
      const totalCount = item.body.length - 1; // 减去"全部"项

      switch (index) {
        case 0:
          return `高意向(${selectedCount}/${totalCount})`;
        case 1:
          return `中意向(${selectedCount}/${totalCount})`;
        case 2:
          return `低意向(${selectedCount}/${totalCount})`;
        default:
          return item.head;
      }
    },
    async checkboxChange(res, index, i) {
      // 如果是"全部"选项
      console.log(res, index, i);
      res.checked = !res.checked;
      if (i === 0) {
        this.selectAllLoading[index] = true;

        // 获取当前分类下所有用户ID（排除"全部"选项）
        const userIds = this.itemList[index].body
          .filter((item) => item.userId)
          .map((item) => item.userId);

        // 判断是全选还是取消全选
        const isSelectAll = res.checked;

        // 更新当前分类下所有用户的选中状态
        this.itemList[index].body.forEach((item, idx) => {
          if (idx !== 0) {
            // 跳过"全部"选项
            item.checked = isSelectAll;
          }
        });

        // 更新useridList
        if (isSelectAll) {
          // 全选：添加当前分类下所有用户ID（排除已存在的）
          userIds.forEach((id) => {
            if (!this.useridList.includes(id)) {
              this.useridList.push(id);
            }
          });
        } else {
          // 取消全选：移除当前分类下所有用户ID
          this.useridList = this.useridList.filter(
            (id) => !userIds.includes(id)
          );
        }

        // 强制更新视图

        this.selectAllLoading[index] = false;
      } else {
        // 普通选项的处理
        const selectId = this.useridList.indexOf(res.userId);
        if (selectId > -1) {
          this.useridList.splice(selectId, 1);
        } else {
          this.useridList.push(res.userId);
        }
        console.log(this.useridList);
      }

      // 强制更新视图
      this.itemList = [...this.itemList];
      setTimeout(() => {
        this.updateSelectAllStatus(index);
      }, 0);
      if (this.peopleChecked) {
        this.peopleChecked = !this.peopleChecked;
      }
    },
    updateSelectAllStatus(index) {
      const category = this.itemList[index];
      if (!category || category.body.length === 0) return;

      // 检查当前分类下是否所有用户都被选中（排除"全部"选项）
      const allChecked = category.body
        .slice(1) // 跳过"全部"选项
        .every((item) => item.checked);
      // 使用 $set 强制更新"全部"项的 checked 状态
      this.$set(category.body, 0, {
        ...category.body[0],
        checked: allChecked,
      });
    },
    async getCustomerTracks() {
      const res = await this.$axios.get(this.$api.getCustomerTrack);
      if (res.data.code == 200) {
        this.originalData = res.data.data;

        const processItems = (items) => {
          return items.map((item) => ({
            ...item,
            checked: false,
          }));
        };

        const seniorItems = processItems(this.originalData.seniorUserIds);
        const middleItems = processItems(this.originalData.middleUserIds);
        const lowItems = processItems(this.originalData.lowUserIds);

        // 添加"全部"项
        seniorItems.unshift({
          name: "全部",
          checked: false,
        });
        middleItems.unshift({
          name: "全部",
          checked: false,
        });
        lowItems.unshift({
          name: "全部",
          checked: false,
        });

        this.itemList = [
          {
            head: "高意向",
            body: seniorItems,
          },
          {
            head: "中意向",
            body: middleItems,
          },
          {
            head: "低意向",
            body: lowItems,
          },
        ];
      } else {
        uni.showToast({
          title: res.data.msg,
          icon: "none",
          duration: 3000,
        });
      }
    },
  },
};
</script>

<style lang="less" scoped>
@import "./pushMessageDetails.less";

.loading-text {
  font-size: 24rpx;
  color: #999;
  margin-left: 10rpx;
}
</style>
