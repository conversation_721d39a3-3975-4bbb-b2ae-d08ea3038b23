import { ref } from 'vue'
import { getCurrentInstance } from 'vue'

export function useUserInfo() {
  const { proxy } = getCurrentInstance()

  const userInfo = ref({
    nickname: '',
    avatar: '',
    clubName: ''
  })

  // 检查用户信息
  const checkInfo = () => {
    const storedUserInfo = uni.getStorageSync('userInfo')

    // 授权手机号状态下 提示用户设置头像和名称
    if (proxy.user_isLogin) {
      if (!storedUserInfo || !storedUserInfo.avatar || !storedUserInfo.nickname) {
        proxy.setAvatorName(true)
      }
    }
    userInfo.value = storedUserInfo || {
      nickname: '',
      avatar: '',
      clubName: ''
    }
  }

  // 更新用户信息
  const updateInfo = () => {
    userInfo.value = uni.getStorageSync('userInfo') || {
      nickname: '',
      avatar: '',
      clubName: ''
    }
  }

  // 跳转到个人资料页面
  const jumpToPersonal = () => {
    uni.navigateTo({
      url: '/pageA/pageB/mine/personal'
    })
  }

  return {
    userInfo,
    checkInfo,
    updateInfo,
    jumpToPersonal
  }
}
