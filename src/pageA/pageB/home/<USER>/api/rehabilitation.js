import request from './request';

/**
 * 获取请帖详情
 * @param {string} id - 请帖id
 * @returns {Promise<Object>} - 请帖详情
 */
export const getRehabilitationDetailApi = (id) => {
  return request({
    url: 'mp/card/getDetail',
    method: 'GET',
    data: {
      id,
    },
  });
};

/**
 * 查询用户是否收藏了h5模版
 * @param {string} templateId - 模版id
 * @returns {Promise<Object>} - 是否收藏
 */
export const getUserFavoritesApi = (templateId) => {
  return request({
    url: 'mp/card/getUserFavorites',
    method: 'GET',
    data: {
      templateId,
    },
  });
};

/**
 * 收藏h5模版
 * @param {Object} data - 收藏请求参数
 * @param {string} data.templateId - 模版id
 * @returns {Promise<Object>} - 收藏结果
 */
export const addUserFavoritesApi = (data) => {
  return request({
    url: 'mp/card/collect',
    method: 'POST',
    data,
  });
};

/**
 * 保存请帖邀请人和分享信息
 * @param {Object} data - 邀请请求参数
 * @param {string} data.cardId - 请帖id
 * @param {string} data.name - 邀请人姓名
 * @param {string} data.eventTime - 入宴时间
 * @param {string} data.eventAddress - 详细地址
 * @param {string} data.latitude - 纬度
 * @param {string} data.longitude - 经度
 * @param {string} data.title - 请帖标题
 * @param {string} data.coverImageUrl - 分享封面图片
 * @param {string} data.text - 请帖内容
 */
export const saveInvitationApi = (data) => {
  return request({
    url: 'mp/card/saveInvitation',
    method: 'POST',
    data,
  });
};

/**
 * 查询请帖邀请人和分享信息
 * @param {string} cardId - 请帖id
 * @returns {Promise<Object>} - 请帖邀请人和分享信息
 */
export const getInvitationApi = (cardId) => {
  return request({
    url: 'mp/card/getInvitation',
    method: 'GET',
    data: {
      cardId,
    },
  });
};

/**
 * 创建用户的h5模版
 * @param {Object} data - 创建请求参数
 * @param {string} data.id - 模版id
 * @returns {Promise<Object>} - 创建结果
 */
export const createUserH5Api = (data) => {
  return request({
    url: 'mp/card/create',
    method: 'GET',
    data,
  });
};

/**
 * 查询h5模版编辑状态
 * @param {string} templateId - 模版id
 * @returns {Promise<Object>} - 编辑状态
 */
export const getEditStatusApi = (templateId) => {
  return request({
    url: 'mp/card/getEditStatus',
    method: 'GET',
    data: {
      templateId,
    },
  });
};
