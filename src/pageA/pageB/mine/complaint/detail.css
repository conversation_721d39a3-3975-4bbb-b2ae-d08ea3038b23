.main {
    background: #f6f6f6;
    padding: 42rpx 37rpx;
}

.title {
    color: #333333;
    font-size: 32rpx;
    font-weight: bold;
}

.feedbackComent {
    padding: 28rpx 28rpx;
    border-radius: 20rpx;
    background: #ffff;
    margin: 24rpx 0;
}

.feedbackComent_1 {
    color: #333333;
    font-size: 28rpx;
}

.feedbackImg {
    display: flex;
    margin-top: 50rpx;
}

.feedbackImg1 {
    width: 32%;
    height: 180rpx;
}

.feedbackImg1 image {
    width: 100%;
    height: 100%;
    border-radius: 10rpx;
}

.feedbackImg1:nth-child(2) {
    margin: 0 2%;
}

.feedbackImg1:nth-child(5) {
    margin: 0 2%;
}

.feedbackImg1:nth-child(8) {
    margin: 0 2%;
}

.btn {
    position: fixed;
    bottom: 0;
    padding: 20rpx 32rpx 60rpx 32rpx;
    background: #ffff;
    width: 100%;
}

.btn1 {
    width: 100%;
    background: #3f6fff;
    height: 90rpx;
    border-radius: 100rpx;
    font-size: 30rpx;
    font-weight: bold;
    color: #ffff;
    text-align: center;
    line-height: 90rpx;
}