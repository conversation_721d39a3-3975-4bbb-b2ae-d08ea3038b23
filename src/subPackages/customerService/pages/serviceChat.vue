<template>
  <view class="service-chat-container" @touchmove.stop.prevent>
    <!-- 状态栏占位 -->
    <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>

    <!-- 顶部导航栏 -->
    <view class="chat-header">
      <view class="header-left" @click="goBack">
        <u-icon name="arrow-left" size="36" color="#333333"></u-icon>
      </view>
      <view class="header-center">
        <view class="user-info">
          <text class="user-name">{{ customerInfo.name || '用户' }}</text>
          <text class="user-status">{{ customerInfo.status || '在线' }}</text>
        </view>
      </view>
      <view class="header-right"></view>
    </view>

    <!-- 聊天消息列表 -->
    <scroll-view
      class="chat-messages"
      :style="{ height: chatMessagesHeight + 'px' }"
      :scroll-top="scrollTop"
      scroll-y
      :refresher-enabled="true"
      :refresher-triggered="chatState.refreshing"
      @refresherrefresh="handleRefresh"
      @scrolltoupper="handleLoadMore"
      @scroll="onScroll"
    >
      <!-- 加载更多提示（顶部） -->
      <view v-if="chatState.loadingMore" class="load-more-top">
        <text>正在加载更多历史消息...</text>
      </view>

      <!-- 没有更多消息提示 -->
      <view v-if="!chatState.hasMore && !isEmpty" class="no-more">
        <text>没有更多历史消息</text>
      </view>

      <!-- 空状态 -->
      <view v-if="isEmpty" class="empty-state">
        <text>暂无聊天记录</text>
        <text>开始你们的对话吧</text>
      </view>

      <view v-else class="message-list">
        <template
          v-for="(message, index) in chatState.messages"
          :key="message.id || index"
        >
          <!-- 时间显示 -->
          <view
            v-if="shouldShowTime(message, index, chatState.messages)"
            class="time-display-container"
          >
            <text class="time-display-text">{{
              formatTime(message.createdTime || message.createdAt)
            }}</text>
          </view>

          <view
            :id="`msg-${index}`"
            class="message-item"
            :class="
              message.type === 'service' ? 'service-message' : 'user-message'
            "
          >
            <!-- 用户消息 -->
            <view v-if="message.type === 'user'" class="user-msg">
              <view class="message-content">
                <view class="sender-name">{{
                  customerInfo.name || '用户'
                }}</view>
                <!-- 文本消息气泡 -->
                <view
                  v-if="message.messageType === 'TEXT'"
                  class="message-bubble user-bubble"
                >
                  <text class="message-text">{{ message.content }}</text>
                </view>
                <!-- 图片消息（无气泡） -->
                <view
                  v-if="message.messageType === 'IMAGE'"
                  class="image-message"
                >
                  <image
                    :src="message.content"
                    mode="widthFix"
                    class="message-image"
                    @click="previewImage(message.content)"
                    @error="onImageError"
                  />
                </view>
              </view>
            </view>

            <!-- 服务端消息 -->
            <view v-else-if="message.type === 'service'" class="service-msg">
              <view class="message-content">
                <!-- 文本消息气泡 -->
                <view
                  v-if="message.messageType === 'TEXT'"
                  class="message-bubble service-bubble"
                >
                  <text class="message-text">{{ message.content }}</text>
                </view>
                <!-- 图片消息（无气泡） -->
                <view
                  v-if="message.messageType === 'IMAGE'"
                  class="image-message"
                >
                  <image
                    :src="message.content"
                    mode="widthFix"
                    class="message-image"
                    @click="previewImage(message.content)"
                    @error="onImageError"
                  />
                </view>
                <!-- 消息状态 -->
                <view v-if="message.status" class="message-status">
                  <text
                    v-if="message.status === 'sending'"
                    class="status-text sending"
                    >发送中...</text
                  >
                  <text
                    v-else-if="message.status === 'failed'"
                    class="status-text failed"
                    >发送失败</text
                  >
                </view>
              </view>
            </view>

            <!-- 系统消息 -->
            <view v-else-if="message.type === 'system'" class="system-msg">
              <view class="system-content">
                <text class="system-text">{{ message.content }}</text>
              </view>
            </view>
          </view>
        </template>

        <!-- 用户正在输入提示 -->
        <view v-if="isUserTyping" class="typing-indicator">
          <view class="typing-content">
            <text class="typing-text">用户正在输入...</text>
            <view class="typing-dots">
              <view class="dot"></view>
              <view class="dot"></view>
              <view class="dot"></view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 输入框组件 - 只有接入中的会话才显示 -->
    <ChatInput
      v-if="canShowInput"
      v-model="inputText"
      :focus="inputFocus"
      @send="handleSendMessage"
      @image-upload="handleImageUpload"
      @focus="handleInputFocus"
      @blur="handleInputBlur"
      ref="chatInputRef"
    />

    <!-- 会话状态提示 -->
    <view v-if="!canShowInput" class="chat-status-tip">
      <text class="status-text">{{ getStatusText() }}</text>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, nextTick } from 'vue';
import { onLoad, onShow, onUnload } from '@dcloudio/uni-app';
import dayjs from 'dayjs';

// 导入组件
import ChatInput from '../components/ChatInput.vue';

// 导入 API
import { uploadFile } from '../api/request.js';
import { getConversationStatus } from '../api/customerService.js';
import chatWebSocketManager from '../utils/chatWebSocketManager.js';

// 导入 composables
import { useChatPagination } from '../composables/useChatPagination.js';

// 导入工具函数
import {
  formatTime,
  shouldShowTime,
  getMessageType,
} from '../utils/timeUtils.js';

// 使用聊天分页 composable
const {
  state: chatState,
  isEmpty,
  setConversationId,
  loadFirstPage,
  onRefresh,
  onLoadMore,
  addMessage,
  updateMessage,
} = useChatPagination(null, {
  pageSize: 20,
  initialLoad: false, // 等获取到会话ID后再加载
});

// 响应式数据
const inputText = ref('');
const inputFocus = ref(false);
const scrollTop = ref(0);

const isUserTyping = ref(false);
const isFirstLoad = ref(true); // 标记是否是首次加载

// 安全区域相关
const statusBarHeight = ref(0);
const safeAreaBottom = ref(0);

// 页面高度相关
const windowHeight = ref(0);
const chatHeaderHeight = ref(88); // 导航栏高度 88rpx
const chatInputHeight = ref(0); // 输入框区域实际高度，动态获取
const chatMessagesHeight = ref(0);

// 客户信息
const customerInfo = reactive({
  id: '',
  name: '',
  avatar: '',
  phone: '',
  type: '',
  source: '',
  status: '在线',
});

// 服务人员信息
const serviceInfo = reactive({
  id: '',
  name: '',
  avatar: '',
});

// 会话信息
const sessionInfo = reactive({
  conversationId: '',
  status: 'active',
});

// 输入框显示控制
const canShowInput = ref(false);

// 默认头像
const defaultServiceAvatar = '/static/images/default-service-avatar.png';

// 组件引用
const chatInputRef = ref(null);

// WebSocket管理器实例

// 页面加载
onLoad(async (options) => {
  console.log('服务聊天页面加载', options);

  // 获取系统信息设置安全区域
  initSafeArea();

  // 计算消息列表高度
  calculateChatMessagesHeight();

  // 处理传入的参数
  if (options.conversationId) {
    sessionInfo.conversationId = options.conversationId;
    // 设置会话ID到分页管理器
    setConversationId(options.conversationId);
    console.log('使用传入的 conversationId:', options.conversationId);
  }

  initServiceInfo();

  try {
    // 首先检查会话状态
    await checkConversationStatus();

    // 初始化WebSocket连接
    await initWebSocket();

    // 加载聊天历史和客户信息
    await loadChatHistory();
    await loadCustomerInfo();

    // 加入会话
    await joinConversation();

    // 重新计算高度并滚动到底部
    nextTick(() => {
      getChatInputHeight();
      setTimeout(() => {
        scrollToBottom();
      }, 100);
    });

    // 标记首次加载完成
    isFirstLoad.value = false;
  } catch (error) {
    console.error('页面初始化失败:', error);
    uni.showToast({
      title: '初始化失败',
      icon: 'none',
    });
    isFirstLoad.value = false;
  }
});

onShow(() => {
  // onShow 事件已移动到 onLoad 中，避免查看图片消息时触发
  console.log('serviceChat onShow - 不执行任何逻辑');
});

// 初始化安全区域
const initSafeArea = () => {
  const systemInfo = uni.getSystemInfoSync();
  statusBarHeight.value = systemInfo.statusBarHeight || 0;
  windowHeight.value = systemInfo.windowHeight || 0;

  // 获取安全区域信息
  if (systemInfo.safeAreaInsets) {
    safeAreaBottom.value = systemInfo.safeAreaInsets.bottom || 0;
  } else if (systemInfo.safeArea) {
    // 兼容旧版本
    safeAreaBottom.value =
      systemInfo.screenHeight - systemInfo.safeArea.bottom || 0;
  }

  // 获取输入框容器的真实高度
  getChatInputHeight();

  console.log('安全区域信息:', {
    statusBarHeight: statusBarHeight.value,
    safeAreaBottom: safeAreaBottom.value,
    windowHeight: windowHeight.value,
  });
};

// 获取输入框容器的真实高度（仅用于键盘高度计算）
const getChatInputHeight = () => {
  nextTick(() => {
    const query = uni.createSelectorQuery().in(chatInputRef.value);
    query
      .select('.chat-input-container')
      .boundingClientRect((data) => {
        if (data) {
          chatInputHeight.value = data.height;
          console.log('输入框容器真实高度:', chatInputHeight.value);

          // 由于输入框使用相对定位，重新计算消息区域高度
          calculateChatMessagesHeight();
        }
      })
      .exec();
  });
};

// 计算聊天消息区域高度
const calculateChatMessagesHeight = () => {
  // 计算 scrollview 距离顶部的高度：状态栏高度 + 导航栏高度
  const topDistance =
    statusBarHeight.value + uni.upx2px(chatHeaderHeight.value);

  // 由于 ChatInput 组件使用 fixed 定位，脱离文档流
  // 消息列表可以占满整个可用空间，只需要减去顶部距离
  // 底部会被 fixed 的输入框覆盖，但这是期望的行为

  // scrollview 高度 = 窗口高度 - 顶部距离
  chatMessagesHeight.value = windowHeight.value - topDistance;

  console.log('高度计算 (ChatInput fixed定位):', {
    windowHeight: windowHeight.value,
    statusBarHeight: statusBarHeight.value,
    topDistance,
    chatMessagesHeight: chatMessagesHeight.value,
    note: 'ChatInput使用fixed定位，消息列表占满可用空间',
  });
};

// 初始化服务人员信息
const initServiceInfo = () => {
  const service = uni.getStorageSync('serviceInfo');
  if (service) {
    Object.assign(serviceInfo, service);
  } else {
    serviceInfo.name = '客服';
    serviceInfo.avatar = defaultServiceAvatar;
  }
};

// 加载客户信息
const loadCustomerInfo = async () => {
  // TODO: 实现客户信息加载逻辑
  console.log('客户信息加载功能待实现');
};

// 加载聊天历史
const loadChatHistory = async () => {
  try {
    if (!sessionInfo.conversationId) {
      console.warn('没有会话ID，无法加载聊天历史');
      return;
    }
    console.log('开始加载聊天历史...', sessionInfo.conversationId);

    // 使用分页 composable 加载第一页消息
    const result = await loadFirstPage();

    if (result.success) {
      console.log('聊天历史加载成功:', chatState.messages.length, '条消息');
      console.log('消息列表:', chatState.messages);
      console.log('isEmpty状态:', isEmpty);
      scrollToBottom();
    } else {
      console.warn('获取聊天历史失败:', result.error);
    }
  } catch (error) {
    console.error('加载聊天历史失败:', error);
    uni.showToast({
      title: '加载聊天记录失败',
      icon: 'none',
    });
  }
};

// 加入会话
const joinConversation = async () => {
  if (sessionInfo.conversationId) {
    // 设置当前会话ID到WebSocket管理器
    chatWebSocketManager.setCurrentConversationId(sessionInfo.conversationId);
    console.log('已加入会话:', sessionInfo.conversationId);
  }
};

// 下拉刷新处理
const handleRefresh = async () => {
  await onRefresh();
  // 刷新后滚动到底部
  nextTick(() => {
    scrollToBottom();
  });
};

// 滚动到顶部加载更多历史消息
const handleLoadMore = async () => {
  await onLoadMore();
};

// 处理发送消息
const handleSendMessage = async (content) => {
  if (!content || !content.trim()) {
    console.warn('消息内容为空，不发送');
    return;
  }

  // 检查WebSocket连接状态
  if (!chatWebSocketManager.websocketConnected) {
    uni.showToast({
      title: '连接已断开，请重试',
      icon: 'none',
    });
    return;
  }

  // 检查会话ID
  if (!sessionInfo.conversationId) {
    uni.showToast({
      title: '会话信息异常，请重新进入',
      icon: 'none',
    });
    return;
  }

  const trimmedContent = content.trim();

  // 创建临时消息对象（立即显示在界面上）
  const tempMessage = {
    id: `temp_${dayjs().valueOf()}`,
    type: 'service',
    content: trimmedContent,
    createdTime: dayjs().valueOf(),
    messageType: 'TEXT',
    status: 'sending', // 发送中状态
  };

  // 立即添加到消息列表
  addMessage(tempMessage);

  // 清空输入框
  inputText.value = '';

  // 滚动到底部
  await nextTick();
  scrollToBottom();

  try {
    // 使用WebSocket管理器发送客服消息
    const success = chatWebSocketManager.sendAgentMessage(
      trimmedContent,
      sessionInfo.conversationId,
    );

    if (!success) {
      throw new Error('WebSocket发送失败');
    }

    // 更新消息状态为已发送
    updateMessage(tempMessage.id, {
      status: 'sent',
      id: dayjs().valueOf().toString(), // 更新为正式ID
    });

    console.log('消息发送成功:', trimmedContent);
  } catch (error) {
    console.error('发送消息失败:', error);

    // 更新消息状态为发送失败
    updateMessage(tempMessage.id, {
      status: 'failed',
    });

    uni.showToast({
      title: '发送失败，请重试',
      icon: 'none',
    });
  }
};

// 处理输入框聚焦
const handleInputFocus = () => {
  inputFocus.value = true;

  // 延迟滚动到底部，等待键盘弹起
  setTimeout(() => {
    scrollToBottom();
  }, 300);
};

// 处理输入框失焦
const handleInputBlur = () => {
  inputFocus.value = false;
};

// 处理图片上传（来自ChatInput组件）
const handleImageUpload = async (filePath) => {
  console.log('开始处理图片上传:', filePath);

  // 显示上传进度
  uni.showLoading({
    title: '上传中...',
    mask: true,
  });

  try {
    // 上传图片
    const imageUrl = await uploadFile({
      filePath: filePath,
    });

    console.log('图片上传成功:', imageUrl);

    // 发送图片消息
    await sendImageMessage(imageUrl);

    uni.hideLoading();
    uni.showToast({
      title: '发送成功',
      icon: 'success',
      duration: 1500,
    });
  } catch (error) {
    console.error('图片上传失败:', error);
    uni.hideLoading();
    uni.showToast({
      title: '上传失败，请重试',
      icon: 'none',
      duration: 2000,
    });
  }
};

// 发送图片消息
const sendImageMessage = async (imageUrl) => {
  if (!imageUrl) return;

  const message = {
    id: dayjs().valueOf(),
    type: 'service',
    messageType: 'IMAGE',
    content: imageUrl,
    createdTime: dayjs().toISOString(),
    status: 'sending',
  };

  // 添加到消息列表
  addMessage(message);

  // 滚动到底部
  nextTick(() => {
    scrollToBottom();
  });

  try {
    // 通过WebSocket发送图片消息
    if (chatWebSocketManager.isConnected()) {
      const success = chatWebSocketManager.sendImageMessage(
        imageUrl,
        sessionInfo.conversationId,
        true, // 客服发送的消息
      );

      if (success) {
        // 更新消息状态为已发送
        updateMessage(message.id, { status: 'sent' });
      } else {
        throw new Error('发送图片消息失败');
      }
    } else {
      throw new Error('WebSocket连接已断开');
    }
  } catch (error) {
    console.error('发送图片消息失败:', error);
    // 更新消息状态为失败
    updateMessage(message.id, { status: 'failed' });
    uni.showToast({
      title: '发送失败',
      icon: 'none',
      duration: 2000,
    });
  }
};

// 图片预览
const previewImage = (imageUrl) => {
  uni.previewImage({
    urls: [imageUrl],
    current: imageUrl,
  });
};

// 图片加载错误处理
const onImageError = (e) => {
  console.error('图片加载失败:', e);
  uni.showToast({
    title: '图片加载失败',
    icon: 'none',
    duration: 1500,
  });
};

// 滚动到底部
const scrollToBottom = () => {
  nextTick(() => {
    // 获取聊天消息容器的滚动高度
    const query = uni.createSelectorQuery();
    query
      .select('.chat-messages')
      .scrollOffset((res) => {
        if (res) {
          // 设置 scrollTop 为最大滚动高度，实现滚动到底部
          scrollTop.value = res.scrollHeight;
        }
      })
      .exec();
  });
};

// 返回上一页
const goBack = () => {
  // 离开会话时关闭WebSocket连接
  if (sessionInfo.conversationId) {
    chatWebSocketManager.close();
  }
  uni.navigateBack({
    delta: 1,
    fail: () => {
      uni.switchTab({
        url: '/pageA/home',
      });
    },
  });
};

// 滚动事件
const onScroll = () => {
  // 可以在这里添加滚动相关的逻辑
};

// 删除重复的 onLoad 函数

onUnload(() => {
  // 关闭WebSocket连接
  chatWebSocketManager.close();
});

// 初始化WebSocket连接
const initWebSocket = async () => {
  try {
    // 初始化WebSocket管理器
    const success = await chatWebSocketManager.init({
      userType: 'AGENT',
    });

    if (!success) {
      console.error('WebSocket初始化失败');
      return;
    }

    // 注册事件监听器
    setupWebSocketEventListeners();

    console.log('WebSocket初始化成功');
  } catch (error) {
    console.error('WebSocket初始化异常:', error);
  }
};

// 设置WebSocket事件监听器
const setupWebSocketEventListeners = () => {
  // 先清理所有现有的监听器，避免重复监听
  chatWebSocketManager.messageHandlers.clear();

  // 连接成功
  chatWebSocketManager.on('connected', () => {
    console.log('WebSocket连接成功');
  });

  // 绑定成功
  chatWebSocketManager.on('bindSuccess', () => {
    console.log('WebSocket绑定成功');
    uni.showToast({
      title: '连接成功',
      icon: 'success',
      duration: 1000,
    });
  });

  // 收到聊天消息
  chatWebSocketManager.on('chatMessage', (message) => {
    console.log('收到聊天消息:', message);

    // 使用统一的消息类型判断函数
    const userType = getMessageType(message, 'user'); // 默认为用户消息（客服端接收）

    const formattedMessage = {
      id: message.id || message.messageId || dayjs().valueOf().toString(),
      type: userType, // 消息发送者类型：user/service/system
      userType: userType, // 用户类型字段，与type保持一致
      content: message.content,
      createdTime: message.createdTime || message.timestamp || dayjs().valueOf(),
      messageType: (
        message.format ||
        message.messageType ||
        'TEXT'
      ).toUpperCase(), // 消息内容类型：TEXT/IMAGE
      sender: message.sender,
      receiver: message.receiver,
      action: message.action,
    };

    addMessage(formattedMessage);

    // 如果收到用户消息，停止正在输入状态
    if (userType === 'user') {
      isUserTyping.value = false;
    }

    // 滚动到底部
    nextTick(() => {
      scrollToBottom();
    });
  });

  // 连接断开
  chatWebSocketManager.on('disconnected', () => {
    console.log('WebSocket连接断开');
    uni.showToast({
      title: '连接断开',
      icon: 'none',
      duration: 1000,
    });
  });

  // 连接错误
  chatWebSocketManager.on('error', (error) => {
    console.error('WebSocket连接错误:', error);
    uni.showToast({
      title: '连接错误',
      icon: 'none',
    });
  });

  // 用户正在输入
  chatWebSocketManager.on('userTyping', (data) => {
    console.log('用户正在输入:', data);
    isUserTyping.value = true;

    // 3秒后自动隐藏正在输入状态
    setTimeout(() => {
      isUserTyping.value = false;
    }, 3000);
  });

  // 用户停止输入
  chatWebSocketManager.on('userStopTyping', (data) => {
    console.log('用户停止输入:', data);
    isUserTyping.value = false;
  });
};

// 发送消息方法已集成到handleSendMessage中

// ==================== 转接相关方法 ====================

// 检查会话状态，判断是否显示输入框
const checkConversationStatus = async () => {
  try {
    if (!sessionInfo.conversationId) {
      canShowInput.value = false;
      return;
    }

    const res = await getConversationStatus(sessionInfo.conversationId);
    console.log('会话状态响应:', res);

    if (res.code === 200 && res.data) {
      // 只有状态为接入中(1)的会话才显示输入框
      canShowInput.value = res.data.status === 1;
      sessionInfo.status = res.data.status;

      // 更新用户昵称信息
      if (res.data.userNickname) {
        customerInfo.name = res.data.userNickname;
      }

      console.log(
        '会话状态:',
        res.data.status,
        '可显示输入框:',
        canShowInput.value,
      );
      console.log('用户昵称:', res.data.userNickname);
    } else {
      canShowInput.value = false;
    }
  } catch (error) {
    console.error('获取会话状态失败:', error);
    canShowInput.value = false;
  }
};

// 获取状态文本
const getStatusText = () => {
  if (!sessionInfo.conversationId) {
    return '会话信息加载中...';
  }

  switch (sessionInfo.status) {
    case 1:
      return '会话进行中';
    case 2:
      return '会话已结束，无法发送消息';
    default:
      return '会话状态未知';
  }
};
</script>

<style lang="scss" scoped>
.service-chat-container {
  height: 100vh;
  background-color: #f5f5f5;
}

.status-bar {
  background-color: #ffffff;
  width: 100%;
}

.chat-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 32rpx;
  background: linear-gradient(180deg, #ffffff 0%, #fafafa 100%);
  border-bottom: 1rpx solid #e5e5e5;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);

  .header-left,
  .header-right {
    width: 80rpx;
    height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .header-center {
    flex: 1;
    display: flex;
    justify-content: center;

    .user-info {
      text-align: center;

      .user-name {
        font-size: 34rpx;
        font-weight: 600;
        color: #333333;
        display: block;
      }

      .user-status {
        font-size: 24rpx;
        color: #52c41a;
        margin-top: 2rpx;
      }
    }
  }
}

.chat-messages {
  padding: 16rpx;
  padding-bottom: 200rpx; // 增加底部padding，避免被fixed输入框遮挡
  overflow: hidden;
  box-sizing: border-box;
}

.load-more-top,
.no-more,
.loading-more {
  text-align: center;
  padding: 20rpx;

  text {
    font-size: 24rpx;
    color: #999999;
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400rpx;

  text {
    font-size: 28rpx;
    color: #999999;
    margin-bottom: 16rpx;
  }
}

.time-display-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10rpx 0;
  margin-bottom: 24rpx;
}

.time-display-text {
  font-size: 24rpx;
  color: #999999;
}

.message-item {
  margin-bottom: 24rpx;
  animation: messageSlideIn 0.3s ease-out;

  &.user-message {
    display: flex;
    justify-content: flex-start;
  }

  &.service-message {
    display: flex;
    justify-content: flex-end;
  }
}

.user-msg,
.service-msg {
  display: flex;
  align-items: flex-end;
  max-width: 80%;
  gap: 16rpx;
}

.user-msg {
  .message-content {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
  }
}

.service-msg {
  .message-content {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
  }
}

.message-content {
  flex: 1;

  .sender-name {
    font-size: 26rpx;
    color: #999999;
    margin-bottom: 6rpx;
  }

  .message-bubble {
    padding: 16rpx 20rpx;
    border-radius: 20rpx;
    max-width: 100%;
    word-wrap: break-word;
    position: relative;
    transition: all 0.2s ease;

    &.user-bubble {
      background-color: #ffffff;
      border: 1rpx solid #e5e5e5;
      border-radius: 20rpx 20rpx 20rpx 8rpx;
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
    }

    &.service-bubble {
      background: linear-gradient(135deg, #007aff 0%, #0056d3 100%);
      color: #ffffff;
      border-radius: 20rpx 20rpx 8rpx 20rpx;
      box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.3);
    }
  }

  .message-text {
    font-size: 32rpx;
    line-height: 1.4;
  }

  .image-message {
    .message-image {
      width: auto;
      max-width: 400rpx;
      max-height: 400rpx;
      min-width: 200rpx;
      min-height: 200rpx;
      border-radius: 12rpx;
      background-color: #f5f5f5;
    }
  }

  .message-status {
    margin-top: 8rpx;
    text-align: right;

    .status-text {
      font-size: 24rpx;

      &.sending {
        color: #999999;
      }

      &.failed {
        color: #ff4d4f;
      }
    }
  }
}

.system-msg {
  display: flex;
  justify-content: center;
  margin-bottom: 24rpx;

  .system-content {
    background: rgba(0, 0, 0, 0.05);
    padding: 12rpx 24rpx;
    border-radius: 16rpx;

    .system-text {
      font-size: 24rpx;
      color: #666666;
    }
  }
}

.typing-indicator {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 24rpx;

  .typing-content {
    display: flex;
    align-items: center;
    background: #ffffff;
    padding: 16rpx 20rpx;
    border-radius: 20rpx;
    border: 1rpx solid #e5e5e5;

    .typing-text {
      font-size: 24rpx;
      color: #666666;
      margin-right: 16rpx;
    }

    .typing-dots {
      display: flex;
      align-items: center;

      .dot {
        width: 8rpx;
        height: 8rpx;
        background: #007aff;
        border-radius: 50%;
        margin-right: 6rpx;
        animation: typing 1.4s infinite ease-in-out;

        &:nth-child(1) {
          animation-delay: -0.32s;
        }

        &:nth-child(2) {
          animation-delay: -0.16s;
        }

        &:last-child {
          margin-right: 0;
        }
      }
    }
  }
}

@keyframes typing {
  0%,
  80%,
  100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}

@keyframes messageSlideIn {
  0% {
    opacity: 0;
    transform: translateY(20rpx);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.chat-status-tip {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #f8f9fa;
  border-top: 1rpx solid #e5e5e5;
  padding: 32rpx;
  text-align: center;
  z-index: 100;

  .status-text {
    font-size: 28rpx;
    color: #666666;
    line-height: 1.4;
  }
}
</style>
