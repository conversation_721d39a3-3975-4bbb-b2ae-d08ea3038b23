export const CLIENT_CONFIG = {
    CLIENT_ID: '428a8310cd442757ae699df5d894f051',
    GRANT_TYPE: 'xcx',
    TENANT_ID: '194338'
};
//开发环境配置
const develop = {
    ENV: "develop",
    VITE_BASE_API: "https://test-api.xiaodingdang1.com/",
    VITE_WSS_URL: "wss://test-socket.xiaodingdang1.com",
};
//体验环境配置
const trial = {
    ENV: "trial",
    VITE_BASE_API: "https://test-api.xiaodingdang1.com/",
    VITE_WSS_URL: "wss://test-socket.xiaodingdang1.com",
};
//生产配置
const pro = {
    ENV: "pro",
    VITE_BASE_API: "https://admin-api.xiaodingdang1.com/",
    VITE_WSS_URL: "wss://prod-socket.xiaodingdang1.com",
};

export const ENV_CONFIG = {
    develop,
    trial,
    pro
};


/**
 * 获取当前小程序运行环境
 * @returns {string} 环境名称: 'develop'、'trial' 或 'pro'
 */
export const getEnv = () => {

    // 可以根据微信小程序的环境进行判断
    // 这里可以使用wx.getAccountInfoSync().miniProgram.envVersion判断
    // develop: 开发版
    // trial: 体验版
    // release: 正式版
    try {
        const accountInfo = uni.getAccountInfoSync();
        const envVersion = accountInfo.miniProgram.envVersion;

        if (envVersion === 'release') {
            return 'pro';
        }
        return 'develop';
    } catch (e) {
        console.error('获取小程序环境信息失败', e);
        return 'develop'; // 默认使用开发环境
    }
};

export const getEnvConfig = () => {
    const env = getEnv();
    return {
        ENV: env,
        BASE_URL: ENV_CONFIG[env].VITE_BASE_API,
        WSS_URL: ENV_CONFIG[env].VITE_WSS_URL,
        ...CLIENT_CONFIG
    };
};