 .main {
        background-color: #ECEFF5;
        position: relative;
        // z-index: 1;
        // position: relative;
        .flex {
            display: flex;
            align-items: center;
        }

        .top {
            width: 100%;
            height: 749rpx;
            background-size: cover;
            /* 保持宽高比缩放图片，直到容器被完全覆盖 */
            background-position: center;
            /* 图片居中显示 */
            background-repeat: no-repeat;
            /* 防止背景图片重复 */
        }
        .img {
            width: 100%;
            height: 749rpx;
			background-size:100% 749rpx;
			background-repeat:no-repeat;
            position: absolute;
            top: 0;
            left: 0;
            z-index: 2;
        }

        .introduce {
            background-color: white;
            border-radius: 24rpx;
            margin: 128rpx 24rpx 24rpx;
            padding: 24rpx;

            .head1_1 {
                width: 10rpx;
                height: 30rpx;
                background: #ff4f61;
                border-radius: 5rpx;
            }

            .head1_2 {
                font-size: 34rpx;
                font-weight: bold;
                color: #333333;
                margin-left: 20rpx;
            }

            .head2_1 {
                color: #aaaaaa;
                font-size: 22rpx;
                margin-right: 10rpx;
            }

            .briefContent {
                margin-top: 16rpx;
                color: #777777;
                font-size: 28rpx;
            }
        }
    }

    .head {
        position: absolute;
        left: 0;
        top: 628rpx;
        z-index: 99;
        width: 705rpx;
        margin-left: 25rpx;
        padding: 25rpx;
        background: rgba(255, 255, 255, 1);
        border-radius: 10rpx 10rpx 10rpx 10rpx;
        border: 2rpx solid #FFFFFF;
        

        &-title {
            text-align: left;
            font-style: normal;
            text-transform: none;
            display: flex;
            justify-content: space-between;
            align-items: center;

            &-left {
                font-weight: 500;
                font-size: 37rpx;
                color: #333333;
            }

            &-right {
                font-weight: 400;
                font-size: 25rpx;
                color: #AAAAAA;
            }
        }

        &-tag {
            margin-top: 12rpx;
            margin-bottom: 24rpx;
            display: flex;
            align-items: center;

            &-enum {
                font-weight: 400;
                font-size: 24rpx;
                color: #777777;
                line-height: 28rpx;
                text-align: left;
                font-style: normal;
                text-transform: none;
                margin-right: 24rpx;
                position: relative;
            }

            &-enum:not(:last-child):after {
                content: '';
                position: absolute;
                right: -12rpx;
                top: 45%;
                height: 90%;
                width: 2rpx;
                background: #AAAAAA;
                transform: translateY(-50%);
            }

        }

        &-line {
            border: 1rpx solid #ECEFF5;
        }

        &-address {
            margin-top: 24rpx;
            margin-bottom: 8rpx;
            font-weight: 400;
            font-size: 24rpx;
            color: #333333;
            line-height: 32rpx;
            text-align: left;
            font-style: normal;
            text-transform: none;
            text-overflow: ellipsis;
            white-space: nowrap;
            overflow: hidden;

            image {
                display: inline-block;
                height: 24rpx;
                width: 24rpx;
                margin-right: 2rpx;
                position: relative;
                top: 2rpx;
            }
        }
    }

    .container {
        background-color: white;
        margin: 24rpx;
        border-radius: 20rpx;
        &-tabbar {
            .tab-h {
                width: 100%;
                box-sizing: border-box;
                font-size: 34rpx;
                white-space: nowrap;
                display: flex;
                padding: 14rpx 24rpx 26rpx 24rpx;
                background: #ffffff;
                border-radius: 20rpx 20px 0  0;
            }

            .title-sel-selected {
                color: #333333;
                font-size: 34rpx;
                display: inline-block;
                flex-direction: column;
                align-items: center;
                font-weight: bold;
                text-align: center;
                margin-right: 60rpx;
                position: relative;
            }

            .title-sel-selected .line-style {
                background: #ff4f61;
                height: 6rpx;
                width: 40rpx;
                position: absolute;
                margin-top: 10rpx;
                border-radius: 20rpx;
                left: 30%;
            }
            .title-sel {
                color: #aaaaaa;
                font-size: 28rpx;
                display: inline-block;
                align-items: center;
                font-weight: bold;
                height: 56rpx;
                margin-right: 60rpx;
            }
        }


        &-content {
            padding-bottom: 60rpx;
            .detail-banner-img {
               width: 638rpx;
               // height: 422rpx;
               margin: 0 34rpx 24rpx 34rpx;
               border-radius: 12rpx;
            }
        }
    }