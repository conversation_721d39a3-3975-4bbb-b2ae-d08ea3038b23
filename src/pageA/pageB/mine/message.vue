<template>
    <view>
        <view class="contentTab">
            <view :class="index == currentIndex ? 'title-sel-selected' : 'title-sel'" :data-index="index"
                @tap="titleClick" v-for="(item, index) in tabList" :key="index">
                {{ item.name }}
                <view class="badge" v-if="item.unreadCount > 0">{{ item.unreadCount }}</view>
                <hr class="line-style" />
            </view>
        </view>
        <view class="content" v-if="currentIndex == 0">
            <template v-if="communityCommentList.length > 0">
                <view class="contents" v-for="(item, index) in communityCommentList" :key="index"@click="nextDetails(item.postId,item.type,1)">
                    <view class="head">
                        <image :src="item.avatar || $defaultAvatar" mode=""
                            style="width: 80rpx; height: 80rpx; border-radius: 100rpx" />
                        <view class="heads">
                            <view class="head1">
                                <view class="head1_1">
                                    {{ item.nickname || $defaultName }}
                                </view>
                                <view class="head1_2">
                                    <image
                                        src="http://cdn.xiaodingdang1.com/1/120240125/5b82ebb2-4d5c-43ae-83b8-f4621e4f3336.png"
                                        mode="" style="width: 40rpx; height: 34rpx" class="head1Img" />
                                    <view class="head1_3">高级育婴师</view>
                                </view>
                            </view>
                            <view class="head2">{{ item.hisTimeStr }}</view>
                        </view>
                    </view>

                    <view class="content1" v-if="item.type == 2||item.type == 'COMMENT'">
                        <text class="content1_1">评论了我：</text>
                        <text class="content1_2">{{ item.commentContent }}</text>
                    </view>

                    <view class="content1" v-if="item.type == 3">
                        <text class="content1_1">回复我：</text>
                        <text class="content1_2">{{ item.commentContent }}</text>
                    </view>

                    <view class="content2" v-if="item.type == 2||item.type == 'COMMENT'">
                        <image :src="item.postUrl || $defaultAvatar" mode=""
                            style="width: 140rpx; height: 140rpx; border-radius: 10rpx" />
                        <view class="content2_2">
                            {{ item.content }}
                        </view>
                    </view>

                    <view class="content3" v-if="item.type == 3">
                        <view class="content3_1">
                            {{ item.meComment }}
                        </view>
                    </view>
                </view>
            </template>
            <empty-vue v-if="isFinshed && communityCommentList.length == 0"></empty-vue>
        </view>
        <view class="content" v-if="currentIndex == 1">
            <template v-if="communityLikeList.length > 0">
                <view class="contents" v-for="(item, index) in communityLikeList" :key="index" @click="nextDetails(item.postId,item.type,2)">
                    <view class="head">
                        <image :src="item.avatar || $defaultAvatar" mode=""
                            style="width: 80rpx; height: 80rpx; border-radius: 100rpx" />
                        <view class="heads">
                            <view class="head1">
                                <view class="head1_1">
                                    {{ item.nickname || $defaultName }}
                                </view>
                                <view class="head1_2">
                                    <image
                                        src="http://cdn.xiaodingdang1.com/1/120240125/5b82ebb2-4d5c-43ae-83b8-f4621e4f3336.png"
                                        mode="" style="width: 40rpx; height: 34rpx" class="head1Img" />
                                    <view class="head1_3">高级育婴师</view>
                                </view>
                            </view>
                            <view class="head2">{{ item.hisTimeStr }}</view>
                        </view>
                    </view>

                    <view class="content1_8">
                        <image src="http://cdn.xiaodingdang1.com/1/120240125/058cf0f5-7185-4671-961c-ee263c166795.png"
                            mode="" style="width: 24rpx; height: 24rpx" />
                        <view class="content1_9">赞了您的动态</view>
                    </view>

                    <view class="content2" v-if="item.type == 1||item.type=='LIKES'">
                        <image :src="item.postUrl || $defaultAvatar" mode="" style="width: 140rpx; height: 140rpx" />
                        <view class="content2_2">
                            {{ item.content }}
                        </view>
                    </view>

                    <view class="content3" v-if="item.type == 4">
                        <view class="content3_1">
                            {{ item.meComment }}
                        </view>
                    </view>
                </view>
            </template>
            <empty-vue v-if="isFinshed && communityLikeList.length == 0"></empty-vue>
        </view>
        <view class="content" v-if="currentIndex == 2">
            <template v-if="messageList.length > 0">
                <view class="chat-item" v-for="(item, index) in messageList" :key="index" @click="enterChat(item)">
                    <view class="chat-avatar">
                        <image :src="item.csAvatar || $defaultAvatar" mode="aspectFill" />
                        <view class="unread-badge" v-if="item.unreadCount > 0">{{ item.unreadCount }}</view>
                    </view>
                    <view class="chat-content">
                        <view class="chat-header">
                            <view class="chat-name">{{ item.csName || $defaultName }}</view>
                            <view class="chat-time">{{ formatTime(item.lastMessageTime) }}</view>
                        </view>
                        <view class="chat-footer">
                            <view class="chat-message">{{ item.lastMessage || '暂无消息' }}</view>
                            <view class="status-tag" :class="getStatusClass(item.status)">
                                {{ getStatusText(item.status) }}
                            </view>
                        </view>
                    </view>
                </view>
            </template>
            <empty-vue v-if="isFinshed && messageList.length == 0"></empty-vue>
        </view>
        <new-request-loading></new-request-loading>
    </view>
</template>

<script>
    import emptyVue from '@/components/empty.vue';
    import dayjs from 'dayjs';
    import 'dayjs/locale/zh-cn'; // 导入中文语言包
    
    // 设置dayjs为中文
    dayjs.locale('zh-cn');
    
    export default {
        components: {
            emptyVue
        },
        data() {
            return {
                communityLikeList: [],
                //查询宝妈社区动态我的消息点赞通知
                communityCommentList: [],
                //查询宝妈社区动态我的消息评论通知
                messageList: [], // 消息列表
                currentIndex: 0,
                tabList: [{
                        name: '评论',
                        unreadCount: 0
                    },
                    {
                        name: '点赞',
                        unreadCount: 0
                    },
                    {
                        name: '客服',
                        unreadCount: 0
                    }
                ],
                isFinshed: false
            };
        }
        /**
         * 生命周期函数--监听页面加载
         */
        ,
        onLoad(options) {
            if (this.currentIndex == 0) {
                this.communityComment();
            }
            // 获取所有Tab的未读消息数量
            this.getUnreadCounts();
        },
        /**
         * 生命周期函数--监听页面初次渲染完成
         */
        onReady() {},
        /**
         * 生命周期函数--监听页面显示
         */
        onShow() {},
        /**
         * 生命周期函数--监听页面隐藏
         */
        onHide() {},
        /**
         * 生命周期函数--监听页面卸载
         */
        onUnload() {},
        /**
         * 页面相关事件处理函数--监听用户下拉动作
         */
        onPullDownRefresh() {},
        /**
         * 页面上拉触底事件的处理函数
         */
        onReachBottom() {},
        /**
         * 用户点击右上角分享
         */
        onShareAppMessage() {},

        onShareTimeline() {},
        methods: {
			nextDetails(postId,type,ifTpye){
				console.log(type);
				if(ifTpye==1){
					if(type=='COMMENT'){
					uni.navigateTo({
						url:'/pageA/pageB/community/staffdetail?postId='+postId
					})	
					}else{
						uni.navigateTo({
							url:'/pageA/pageB/community/topicdetail/commentdetail?postId='+postId
						})
					}
				}else{
					if(type=='LIKES'){
						uni.navigateTo({
							url:'/pageA/pageB/community/staffdetail?postId='+postId
						})
					}else{
						uni.navigateTo({
							url:'/pageA/pageB/community/topicdetail/commentdetail?postId='+postId
						})
					}
				}
		
			},
            //用户点击tab时调用
            titleClick: function(e) {
                console.log(e.currentTarget.dataset.index);
                this.setData({
                    //拿到当前索引并动态改变
                    currentIndex: e.currentTarget.dataset.index
                });
                if (e.currentTarget.dataset.index == 0) {
                    this.communityComment(); //查询宝妈社区动态我的消息评论通知
                }
                if (e.currentTarget.dataset.index == 1) {
                    this.communityLike(); //查询宝妈社区动态我的消息点赞通知
                }
                if (e.currentTarget.dataset.index == 2) {
                    this.getMessages(); //获取消息列表
                }
            },

            communityLike() {
                //查询宝妈社区动态我的消息点赞通知

                let data = {
                    pageSize: 1,
                    pageNum: 1000,
                    type: 1
                };
                this.changeLoading(true)
                this.isFinshed = false
                this.$axios.get(this.$api.communityLike, data).then((res) => {
                    if (res.data.code == 200) {
                        this.setData({
                            communityLikeList: res.data.data
                        });
                        // 标记点赞消息为已读
                        if (this.tabList[1].unreadCount > 0) {
                            this.markLikeAsRead();
                        }
                    }
                }).finally(() => {
                    this.isFinshed = true
                    this.changeLoading(false)
                })
            },

            communityComment() {
                //查询宝妈社区动态我的消息评论通知

                let data = {
                    pageSize: 1,
                    pageNum: 1000,
                    type: 2
                };
                this.changeLoading(true)
                this.isFinshed = false
                this.$axios.get(this.$api.communityComment, data).then((res) => {
                    if (res.data.code == 200) {
                        this.setData({
                            communityCommentList: res.data.data
                        });
                        // 标记评论消息为已读
                        if (this.tabList[0].unreadCount > 0) {
                            this.markCommentAsRead();
                        }
                    }
                }).finally(() => {
                    this.isFinshed = true
                    this.changeLoading(false)
                })
            },

            getMessages() {
                // 获取用户客服会话列表
                this.changeLoading(true);
                this.isFinshed = false;

                // 调用用户会话列表API
                this.$axios.get(this.$api.getUserConversationList).then((res) => {
                    if (res.data.code === 200) {
                        // 处理API返回的会话数据
                        const conversations = res.data.data || [];

                        // 转换数据格式以适配现有UI组件
                        const messageList = conversations.map(conversation => {
                            return {
                                id: conversation.id,
                                conversationId: conversation.id,
                                csName: '客服', // 默认客服名称，可以后续从其他接口获取
                                csAvatar: '', // 默认头像，可以后续从其他接口获取
                                lastMessage: '点击查看聊天记录', // 默认消息，实际可能需要从最后一条消息获取
                                lastMessageTime: conversation.lastActiveAt || conversation.updatedTime,
                                unreadCount: conversation.userUnreadCount || 0,
                                status: this.convertConversationStatus(conversation.status),
                                source: conversation.source,
                                createdTime: conversation.createdTime
                            };
                        });

                        this.setData({
                            messageList: messageList
                        });

                        // 更新未读消息数量
                        let totalUnread = 0;
                        messageList.forEach(item => {
                            if (item.unreadCount) {
                                totalUnread += item.unreadCount;
                            }
                        });

                        this.tabList[2].unreadCount = totalUnread;
                        this.setData({
                            tabList: this.tabList
                        });
                    }
                }).catch((error) => {
                    console.error('获取用户会话列表失败:', error);
                    uni.showToast({
                        title: '获取消息列表失败',
                        icon: 'none'
                    });
                }).finally(() => {
                    this.isFinshed = true;
                    this.changeLoading(false);
                });
            },
            
            // 格式化时间显示 (改用dayjs)
            formatTime(timeStr) {
                if (!timeStr) return '';
                
                const messageDate = dayjs(timeStr);
                const now = dayjs();
                
                // 同一天显示时间
                if (messageDate.format('YYYY-MM-DD') === now.format('YYYY-MM-DD')) {
                    return messageDate.format('HH:mm');
                }
                
                // 一周内显示星期几
                const daysDiff = now.diff(messageDate, 'day');
                if (daysDiff < 7) {
                    return messageDate.format('ddd'); // 自动显示为周一、周二等
                }
                
                // 其他情况显示日期
                return messageDate.format('M月D日');
            },
            
            // 转换会话状态 (API状态 -> UI状态)
            convertConversationStatus(apiStatus) {
                // API状态: 0:待接入, 1:进行中, 2:用户结束, 3:客服结束, 4:系统结束
                // UI状态: 0:已结束, 1:进行中, 2:排队中
                switch (apiStatus) {
                    case 0:
                        return 2; // 待接入 -> 排队中
                    case 1:
                        return 1; // 进行中 -> 进行中
                    case 2:
                    case 3:
                    case 4:
                        return 0; // 各种结束状态 -> 已结束
                    default:
                        return 0; // 默认为已结束
                }
            },

            // 获取状态文本
            getStatusText(status) {
                const statusMap = {
                    0: '已结束',
                    1: '进行中',
                    2: '排队中'
                };
                return statusMap[status] || '未知状态';
            },

            // 获取状态样式类
            getStatusClass(status) {
                const classMap = {
                    0: 'status-ended',
                    1: 'status-active',
                    2: 'status-queue'
                };
                return classMap[status] || '';
            },

            // 进入聊天页面
            enterChat(item) {
                // 先标记消息为已读
                this.markMessageAsRead(item.conversationId);

                // 传递会话ID到聊天页面
                uni.navigateTo({
                    url: `/subPackages/customerService/pages/userChat?conversationId=${item.conversationId}`
                });
            },

            // 标记消息为已读
            markMessageAsRead(conversationId) {
                if (!conversationId) {
                    console.warn('conversationId 为空，无法标记消息为已读');
                    return;
                }

                const data = {
                    conversationId: conversationId,
                    receiverType: 'USER' // 用户端标记为已读
                };

                this.$axios.post(`${this.$api.markConversationAsRead}?conversationId=${conversationId}&receiverType=USER`).then((res) => {
                    if (res.data.code === 200) {
                        console.log('消息已标记为已读:', conversationId);

                        // 更新本地消息列表中对应项的未读数量
                        const messageIndex = this.messageList.findIndex(msg => msg.conversationId == conversationId);
                        if (messageIndex !== -1) {
                            // 减少该会话的未读数量
                            const oldUnreadCount = this.messageList[messageIndex].unreadCount || 0;
                            this.messageList[messageIndex].unreadCount = 0;

                            // 更新总的未读数量
                            this.tabList[2].unreadCount = Math.max(0, this.tabList[2].unreadCount - oldUnreadCount);

                            // 更新数据
                            this.setData({
                                messageList: this.messageList,
                                tabList: this.tabList
                            });
                        }

                        // 更新全局未读消息数量
                        this.updateGlobalUnreadCounts();
                    }
                }).catch((error) => {
                    console.error('标记消息为已读失败:', error);
                    // 不显示错误提示，避免影响用户体验
                });
            },

            // 更新全局未读消息数量
            updateGlobalUnreadCounts() {
                // 调用App.vue中的方法更新全局未读数量
                if (getApp().updateUnreadCounts) {
                    getApp().updateUnreadCounts();
                }
            },

            getUnreadCounts() {
                // TODO 获取所有未读消息数量
                // this.$axios.get(this.$axios.getUnreadCounts).then((res) => {
                //     if (res.data.code == 200) {
                //         let counts = res.data.data;
                //         this.setData({
                //             tabList: [
                //                 {
                //                     name: '评论',
                //                     unreadCount: counts.commentCount || 0
                //                 },
                //                 {
                //                     name: '点赞',
                //                     unreadCount: counts.likeCount || 0
                //                 },
                //                 {
                //                     name: '消息',
                //                     unreadCount: counts.messageCount || 0
                //                 }
                //             ]
                //         });
                //     }
                // });
            },

            markLikeAsRead() {
                this.$axios.post(this.$api.readLikes).then((res) => {
                    if (res.data.code == 200) {
                        this.tabList[1].unreadCount = 0;
                        this.setData({
                            tabList: this.tabList
                        });
                    }
                });
            },

            markCommentAsRead() {
                this.$axios.post(this.$api.readComments).then((res) => {
                    if (res.data.code == 200) {
                        this.tabList[0].unreadCount = 0;
                        this.setData({
                            tabList: this.tabList
                        });
                    }
                });
            }
        }
    };
</script>
<style lang="less" scoped>
    @import './message.less';

    .badge {
        position: absolute;
        top: -6rpx;
        right: -6rpx;
        min-width: 30rpx;
        height: 30rpx;
        border-radius: 15rpx;
        background-color: #ff4d4f;
        color: #ffffff;
        font-size: 20rpx;
        text-align: center;
        line-height: 30rpx;
        padding: 0 6rpx;
    }

    .title-sel, .title-sel-selected {
        position: relative;
    }

    /* 微信风格聊天列表样式 */
    .chat-item {
        display: flex;
        padding: 20rpx 30rpx;
        position: relative;
        background-color: #ffffff;
        transition: all 0.2s;
        border-bottom: 1rpx solid #f5f5f5;
        
        &:active {
            background-color: rgba(0,0,0,0.05);
        }
    }
    
    .chat-avatar {
        position: relative;
        margin-right: 20rpx;
        
        image {
            width: 96rpx;
            height: 96rpx;
            border-radius: 8rpx;
            background-color: #f5f5f7;
        }
    }
    
    .chat-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        overflow: hidden;
    }
    
    .chat-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12rpx;
    }
    
    .chat-name {
        font-size: 32rpx;
        color: #333333;
        font-weight: 500;
    }
    
    .chat-time {
        font-size: 24rpx;
        color: #999999;
    }
    
    .chat-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .chat-message {
        flex: 1;
        font-size: 28rpx;
        color: #888888;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        max-width: 450rpx;
    }
    
    .unread-badge {
        position: absolute;
        top: -10rpx;
        right: -10rpx;
        min-width: 36rpx;
        height: 36rpx;
        border-radius: 18rpx;
        background-color: #ff4d4f;
        color: #ffffff;
        font-size: 22rpx;
        text-align: center;
        line-height: 36rpx;
        padding: 0 6rpx;
        box-shadow: 0 2rpx 4rpx rgba(250, 81, 81, 0.2);
    }
    
    .status-tag {
        font-size: 20rpx;
        padding: 2rpx 12rpx;
        border-radius: 10rpx;
        margin-left: 10rpx;
    }
    
    .status-ended {
        background-color: #f5f5f5;
        color: #999999;
    }
    
    .status-active {
        background-color: #e6f7ff;
        color: #1890ff;
    }
    
    .status-queue {
        background-color: #fff7e6;
        color: #fa8c16;
    }
</style>