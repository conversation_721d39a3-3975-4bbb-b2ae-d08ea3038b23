// const baseUrl = process.env.config[process.env.UNI_SCRIPT].VITE_BASE_API; // uniapp方式 获取处于当前开发环境的url
import { getEnvConfig } from '@/utils/config';
const baseUrl = getEnvConfig().BASE_URL;

export default {
	login_wx: baseUrl + 'auth/login_wx',
	// 登录获取token
	clubName: baseUrl + 'mp/club/name',
	// 获取当前会所名称
	clubInfo: baseUrl + 'mp/club/info',
	// 获取当前会所基本信息
	clubHome: baseUrl + 'mp/club/home',
	// 查询会所主页信息
	hotList: baseUrl + 'mp/topic/hot_list',
	// 热门话题列表
	communityRecord: baseUrl + 'mp/community/record',
	// 查询宝妈社区动态列表
	postPage: baseUrl + 'mp/club/post_page',
	// 分页查询会所动态列表
	packagePage: baseUrl + 'mp/package/page',
	// 查询优惠套餐列表
	packageInfo: baseUrl + 'mp/package/info',
	// 查询优惠套餐详情
	staffPage: baseUrl + 'mp/staff/page',
	// 查询护理团队列表
	mealList: baseUrl + 'mp/meal/item_list',
	// 查询月子餐列表 主页月子餐
	homeBrand: baseUrl + 'mp/club/home_brand',
	//查询会所品牌介绍
	suitePage: baseUrl + 'mp/suite/page',
	//查询月子套房列表
	suiteInfo: baseUrl + 'mp/suite/info',
	//查询月子套房信息
	infoMerchants: baseUrl + 'mp/meal/info/merchants',
	//查询月子膳食信息
	getItemList: baseUrl + 'mp/meal/get_item_list',
	//查询菜品列表
	productDeliveryPage: baseUrl + 'mp/meal/product_delivery/page',
	//查询膳食套餐列表
	productDelivery: baseUrl + 'mp/meal/product_delivery',
	//查询膳食套餐详情
	staffInfo: baseUrl + 'mp/staff/info',
	//查询护理团队详情
	feedPostUserPage: baseUrl + 'mp/feed_post/user_page',
	//查询指定用户朋友圈列表
	postpartumRecoveryPage: baseUrl + 'mp/postpartum_recovery/page',
	//分页查询产后康复项目列表
	postpartumRecoveryInfo: baseUrl + 'mp/postpartum_recovery/info',
	//查询产后康复信息
	facilitiesList: baseUrl + 'mp/club/facilities_list',
	//查询会所设施列表
	reviewTagList: baseUrl + 'mp/review/tag_list',
	//查询商家总体评价标签列表
	reviewList: baseUrl + 'mp/review/list',
	//查询商家总体评价列表
	reviewInfo: baseUrl + 'mp/review/info',
	//查询商家总体评价详情
	mobileNannyPage: baseUrl + 'mp/mobile_nanny/page',
	//分页查询移动月嫂列表
	mobileNannyInfo: baseUrl + 'mp/mobile_nanny/info',
	//查询移动月嫂信息
	feedPostInfo: baseUrl + 'mp/feed_post/info',
	//查询动态详情
	hotList: baseUrl + 'mp/topic/hot_list',
	//热门话题列表
	topicInfo: baseUrl + 'mp/topic/info',
	//查询话题详情
	arriveUserList: baseUrl + 'mp/feed_post/arrive_user_list',
	//查询已到店入住客户列表
	dayList: baseUrl + 'mp/feed_post/day_list',
	//查询客户入住天数列表
	roomFeedbackList: baseUrl + 'mp/room_feedback/list',
	//查询指定用户评价列表
	communityInfo: baseUrl + 'mp/community/info',
	//查询宝妈社区动态详情
	listComment: baseUrl + 'mp/community/list_comment',
	//查询宝妈社区动态评论列表
	communitySave: baseUrl + 'mp/community/save',
	//新增宝妈社区动态
	communityHistory: baseUrl + 'mp/community/history',
	//查询宝妈社区我的动态列表
	pageView: baseUrl + 'mp/bookmark/page_view',
	//分页查询我收藏页面&界面列表
	favoritePage: baseUrl + 'mp/feed_post/favorite_page',
	//查询当前用户收藏的朋友圈动态
	pageTopic: baseUrl + 'mp/bookmark/page_topic',
	//分页查询我收藏的话题列表
	pagePost: baseUrl + 'mp/bookmark/page_post',
	//分页查询我收藏宝妈社区动态列表
	communityLike: baseUrl + 'mp/message/community_like',
	//查询宝妈社区动态我的消息点赞通知
	communityComment: baseUrl + 'mp/message/community_comment',
	//查询宝妈社区动态我的消息评论通知
	communityBookmark: baseUrl + 'mp/community/bookmark',
	//社区动态收藏
	topicBookmark: baseUrl + 'mp/topic/bookmark',
	//话题收藏&订阅
	feedPostFavorite: baseUrl + 'mp/feed_post/favorite',
	//朋友圈收藏
	feedPostLikes: baseUrl + 'mp/feed_post/likes',
	//朋友圈点赞
	feedPostShare: baseUrl + 'mp/feed_post/share',
	//朋友圈分享
	saveComment: baseUrl + 'mp/community/save_comment',
	//新增宝妈社区动态评论
	commentLike: baseUrl + 'mp/community/comment_like',
	//宝妈社区动态评论点赞
	recordsInfo: baseUrl + 'mp/check_in/records_info',
	//获取当前登录用户签到信息
	checkinSignIn: baseUrl + 'mp/check_in/signIn',
	//签到
	pregnancyTracker: baseUrl + 'mp/pregnancy_tracker',
	//获取孕期助手周数据
	gestationWeeks: baseUrl + 'mp/pregnancy_tracker/gestation_weeks',
	//获取当前用户预产期信息
	userPhone: baseUrl + 'mp/user/phone',
	//获取用户绑定手机号信息
	userUpdateInfo: baseUrl + 'mp/user/update_info',
	//更新微信用户信息
	saveLike: baseUrl + 'mp/community/save_like',
	//宝妈社区动态点赞
	reportStart: baseUrl + 'mp/event_tracking/report_start',
	//开始事件上报
	reportEnd: baseUrl + 'mp/event_tracking/report_end',
	//结束事件上报
	reportComStart: baseUrl + 'mp/event_tracking_community/report_start',
	//宝妈社区点击开始事件上报
	reportComEnd: baseUrl + 'mp/event_tracking_community/report_end',
	//宝妈社区点击结束事件上报
	reviewSave: baseUrl + 'mp/review/save_batch',
	//新增商家总体评价
	notificationNum: baseUrl + 'mp/feed_post/notification_num',
	//查询朋友圈我的未读消息数量(点赞和评论全部数量)
	userPost: baseUrl + 'mp/feed_post/user_post',
	suiteRrecommendation: baseUrl + 'mp/suite/recommendation',
	//查询套房热门推荐列表
	packagerRecommendation: baseUrl + 'mp/package/recommendation',
	//查询优惠套餐热门推荐列表
	recoveryRecommendation: baseUrl + 'mp/postpartum_recovery/recommendation',
	//查询产后康复热门推荐列表
	questionList: baseUrl + 'mp/question/list',
	//查询问题列表
	reviewPage: baseUrl + 'mp/review/page',
	//分页查询商家总体评价列表
	customerAllTasks: baseUrl + 'mp/customer_task/get_customer_all_tasks',
	//获取客户所有任务节点
	signing: baseUrl + 'mp/contract_gifts/signing',
	//签约礼品领取
	couponPage: baseUrl + 'mp/coupon/page',
	//分页查询优惠券列表
	getCustomerServiceStep: baseUrl + 'mp/customer_service_step/get_customer_service_step',
	//获取用户服务过程天数以及对应的节点
	customerServiceStep: baseUrl + 'mp/feed_post/customer_service_step',
	//通过服务过程查询朋友圈动态列表
	checkinInfo: baseUrl + 'mp/check_in/info',
	//查询礼品信息
	customerPage: baseUrl + 'mp/feed_post/grow_grass_page',
	//分页查询所有宝妈和护理人员动态列表 已经排除会所动态
	featuredList: baseUrl + 'mp/feed_post/featured_list',
	//查询精选动态列表
	geocodes: baseUrl + 'mp/club/geocodes',
	//获取地理/逆地理编码
	featuredNodList: baseUrl + 'mp/feed_post/featured_node_list',
	//查询精选节点列表
	//客户端
	feedPostAdd: baseUrl + 'mp/feed_post/add',
	// 动态发布
	feedPostPage: baseUrl + 'mp/feed_post/page',
	// 查询朋友圈列表
	feedPostComment: baseUrl + 'mp/feed_post/comment',
	// 评论
	feedPostLikes: baseUrl + 'mp/feed_post/likes',
	// 点赞
	uploadOSS: baseUrl + 'common/uploadOSS',
	// 文件上传oss
	userInfo: baseUrl + 'mp/user/user_info',
	// 获取登录用户信息
	notificationList: baseUrl + 'mp/feed_post/notification_list',
	// 查询我的消息 通知点赞列表
	messageList: baseUrl + 'mp/customer/message_list',
	// 查询客户端消息列表
	messageDetail: baseUrl + 'mp/customer/message_detail',
	// 查询客户端消息详情
	resultSatisfaction: baseUrl + 'mp/room_feedback/result_satisfaction',
	// 处理结果满意度
	staffList: baseUrl + 'mp/room_feedback/staff_list',
	// 查询房间护理人员列表
	roomFeedbackSave: baseUrl + 'mp/room_feedback/save',
	// 新增评价
	roomFeedbackHistory: baseUrl + 'mp/room_feedback/history',
	// 历史评价
	feedPostHistory: baseUrl + 'mp/feed_post/history',
	// 查询朋友圈历史动态列表
	feedPostRemove: baseUrl + 'mp/feed_post/remove',
	// 删除朋友圈动态
	//管理端
	roomList: baseUrl + 'mp/room/list',
	// 查询房间列表
	roomInfo: baseUrl + 'mp/room/info',
	// 查询房间详情
	feedPostRoomPage: baseUrl + 'mp/feed_post/room_page',
	// 查询房间的朋友圈列表（包括：房间服务人员的动态；入住客户的动态；）
	roomTodo: baseUrl + 'mp/room/todo',
	// 查询房间待办
	statusNumber: baseUrl + 'mp/room/status_number',
	// 查询房间入住状态数量
	roomStatus: baseUrl + 'mp/room/stats',
	// 获取房间待办状态数量统计
	roomTodoInfo: baseUrl + 'mp/room/todo/info',
	// 查询房间待办详情
	roomReply: baseUrl + 'mp/room/reply',
	// 回复投诉
	userStatsList: baseUrl + 'mp/user_stats/list',
	// 查询微信访问用户列表
	userStatsVisits: baseUrl + 'mp/user_stats/visits',
	// 统计用户访问详情 总访问次数、总访问时常、访问最多模块、访问时间最长模块、首次访问模块、最近访问模块
	customerList: baseUrl + 'mp/customer_service_step/get_customer_list',
	// 获取当前服务人员可用的客户列表
	customerTasks: baseUrl + 'mp/customer_service_step/get_customer_tasks',
	// 查询指定客户的待完成任务节点
	getstepnodeList: baseUrl + 'mp/customer_service_step/get_step_node_list',
	// 获取所有节点
	getCustomerServiceNode: baseUrl + 'mp/customer_service_step/get_customer_service_node',
	// 获取所有节
	lastList: baseUrl + 'mp/user_stats/last_list',
	// 查询最近访问用户列表
	mList: baseUrl + 'mp/user_stats/m_list',
	// 根据模块类型查询访问最多、访问最长用户列表
	//客资跟踪
	getWorkResult: baseUrl + 'mp/customer_track/get_work_result',
	// 获取个人工作成果
	getpendingCustomerCount: baseUrl + 'mp/customer_track/get_pending_customer_count',
	// 获取待跟进客户数量
	myPage: baseUrl + 'mp/customer_track/my_page',
	// 分页查询个人客资列表
	followedUpPage: baseUrl + 'mp/customer_track/followed_up_page',
	// 分页查询客户列表
	createCustomer: baseUrl + 'mp/customer_track/create_customer',
	// 新增客资线索
	changeStage: baseUrl + 'mp/customer_track/change_stage',
	// 更改阶段
	changeTag: baseUrl + 'mp/customer_track/change_tag',
	// 更改标签
	createReminder: baseUrl + 'mp/customer_track/create_reminder',
	// 创建提醒事项
	customerFollowUp: baseUrl + 'mp/customer_track/customer_follow_up',
	// 跟进客户
	getMyReminders: baseUrl + 'mp/customer_track/get_my_reminders',
	// 获取我的提醒事项
	operationLogs: baseUrl + 'mp/customer_track/operation_logs',
	// 分页查询操作记录列表
	batchChangeTag: baseUrl + 'mp/customer_track/batch_change_tag',
	// 批量更改标签
	batchChangeStage: baseUrl + 'mp/customer_track/batch_change_stage',
	// 批量更改阶段
	userPage: baseUrl + 'mp/customer_track/user_page',
	// 分页查询销售员列表
	assignLead: baseUrl + 'mp/customer_track/assign_lead',
	// 分配线索
	batchAssignLead: baseUrl + 'mp/customer_track/batch_assign_lead',
	// 批量分配线索
	getAssignmentStats: baseUrl + 'mp/customer_track/getAssignmentStats',
	// 查询客资分配统计 全部、待分配、已分配
	allPage: baseUrl + 'mp/customer_track/all_page',
	// 分页查询全部客资列表
	followedLogs: baseUrl + 'mp/customer_track/followed_logs',
	// 分页查询跟进记录列表
	customerDetail: baseUrl + 'mp/customer_track/customer_detail',
	// 查询客户详情
	tags: baseUrl + 'mp/customer_track/tags',
	// 查询客户标签列表
	createCustomerSale: baseUrl + 'mp/customer_track/create_customer',
	//销售端新增客户
	tagsList: baseUrl + 'mp/customer_track/tags',
	//查询客户标签列表
	myStats: baseUrl + 'mp/customer_track/my_stats',
	//我的客户统计
	notification: baseUrl + 'mp/customer_track/notification_list',
	//查询销售系统通知提醒列表
	leadStats: baseUrl + 'mp/customer_track/lead_stats',
	// 客户线索统计
	contractList: baseUrl + 'mp/customer_track/contract_list',
	// 查询客户签约列表
	contractDetail: baseUrl + 'mp/customer_track/contract_detail',
	// 查询客户签约详情
	packageList: baseUrl + 'mp/package/list',
	//套餐列表
	contract: baseUrl + 'mp/customer_track/contract',
	//客户签约
	customerOperate: baseUrl + 'mp/customer_track/customer_operate',
	//跟进客户
	contractList: baseUrl + 'mp/customer_track/contract_list',
	//查询客户签约列表
	confirmArrivedAtStore: baseUrl + 'mp/customer_track/confirmArrivedAtStore',
	//确认到店
	collectContractPayment: baseUrl + 'mp/customer_track/collectContractPayment',
	//收取合同金
	contractDetail: baseUrl + 'mp/customer_track/contract_detail',
	//查询客户签约详情
	createReminder: baseUrl + 'mp/customer_track/create_reminder',
	//创建或修改提醒事项
	getReminder: baseUrl + 'mp/customer_track/get_reminder_by_id',
	//获取提醒事项详情
	updateDetail: baseUrl + 'mp/customer_track/update_detail',
	//修改客户详情
	suitesList: baseUrl + 'mp/room/suites_list',
	//通过房型id查询套餐列表
	houseTypeList: baseUrl + 'mp/suite/list',
	//查询月子套房列表
	checkinStatus: baseUrl + 'mp/room/status',
	//查询房间入住状态
	occupationRange: baseUrl + 'mp/room/occupation_range',
	//查询房间排房数据
	getInviation: baseUrl + 'mp/wechat_inviation/getInfo',
	//查询请柬
	listByType: baseUrl + 'mp/taskNode/listByType',
	//通过类型与角色查询标签列表
	listByLabel: baseUrl + 'mp/templateDynamic/listByLabel',
	//通过标签名称查询模板
	goodMomList: baseUrl + 'mp/feed_post/good_mom_list',
	//分页查询所有宝妈和会所动态列表 已经排除工作人员动态
	queryRooms: baseUrl + 'mp/feed_post/query_rooms',
	//查询房间号
	getFeedPostPage: baseUrl + 'mp/feed_post/get_feed_post_page',
	//分页查询宝妈社区动态列表
	getpublish: baseUrl + 'mp/feed_post/publish',
	//分页查询宝妈社区动态列表
	styleDynamic: baseUrl + 'mp/styleDynamic',
	//queryShowStyle
	taskNodeSave: baseUrl + 'mp/taskNode/save',
	login_phone_wx: baseUrl + "auth/login_phone_wx",
	// 用户登录
	insert: baseUrl + 'mp/complaint/insert',
	//新增发布投诉
	userList: baseUrl + 'mp/complaint/userList',
	//获取用户投诉列表
	complaintDetail: baseUrl + 'mp/complaint/detail',
	//投诉id获取详情
	getClubInfo: baseUrl + 'mp/feed_post/club_info',
	//获取会所详情
	getActivityList: baseUrl + 'mp/club/activity_list',
	//获取活动列表
	getActivityDetail: baseUrl + 'mp/club/activity_detail',
	//获取活动详情
	getRoleList: baseUrl + 'mp/merchant/role_list',
	//新增员工
	employeeAdd: baseUrl + 'mp/employee/create',
	//销售发动态角色列表
	getRoomList: baseUrl + 'mp/room/role_code',
	//销售发动态房间列表
	pushSalesPublish: baseUrl + 'mp/feed_post/sales_publish',
	//销售发动态
	getAllTag: baseUrl + 'mp/taskNode/options',
	//获取所有标签
	getAllRoom: baseUrl + 'mp/room/options',
	//获取所有标签
	getMotherInfo: baseUrl + 'mp/mama/info',
	//查询宝妈信息
	getMotherList: baseUrl + 'mp/mama/post_list',
	//查询宝妈动态列表
	pushDiaryItem: baseUrl + 'mp/diary_item/create',
	//新增服务项
	changeDiaryItem: baseUrl + 'mp/diary_item/update',
	//修改服务项
	getDiaryItem: baseUrl + 'mp/diary_item/detail',
	//获取服务项详情
	pushDiaryList: baseUrl + 'mp/diary/publish',
	//发布笔记 
	changeDiaryList: baseUrl + 'mp/diary/update',
	//修改笔记
	getDiaryList: baseUrl + 'mp/diary/page',
	//分页查询笔记 
	getDiaryDetail: baseUrl + 'mp/diary/getDetail',
	//获取笔记详情 
	deleteDiaryList: baseUrl + 'mp/diary/delete',
	//删除宝妈笔记 
	getMomTeam: baseUrl + 'mp/diary_mom/getMomTeam',
	//根据角色获取姓名
	getMomDetail: baseUrl + 'mp/diary_mom/detail',
	//获取优质宝妈详情（包括医护团队）
	getMomPage: baseUrl + 'mp/diary_mom/page',
	//活动报名
	getSinUp: baseUrl + 'mp/club/activitySignUp',
	// 生成二维码
	getQrCode: baseUrl + 'mp/diary/qr_code_url',
	// 服务内容 
	getServicePost: baseUrl + 'mp/feed_post/myPage',
	// 服务笔记
	getServiceDiary: baseUrl + 'mp/diary/myPage',
	// 我的话题
	getServiceCommunity: baseUrl + 'mp/community/myPage',
	// 我的收藏 动态
	getPostFavorite: baseUrl + 'mp/feed_post/favorite_page',
	// 我的收藏 话题
	getPostBookmark: baseUrl + 'mp/bookmark/page_post',
	// 获取请帖列表
	getRehabilitationList: baseUrl + 'mp/card/getList',
	// 获取请帖详情
	getRehabilitationDetail: baseUrl + 'mp/card/getDetail',
	// 用户编辑请帖后先创建id
	getRehabiliteUserId: baseUrl + 'mp/card/create',
	// 用户编辑请帖后保存
	getRehabiliteUserSave: baseUrl + 'mp/card/saveUserCard',
	// 用户编辑保存后的列表
	getRehabiliteUserList: baseUrl + 'mp/card/getUserCardList',
	// 用户编辑保存后的详情
	getRehabiliteUserDetail: baseUrl + 'mp/card/getUserCardDetail',
	// 获取我的宝宝信息
	getMyDetail: baseUrl + 'mp/baby/info/getMyDetail',
	// 我的护理记录列表
	careMyList: baseUrl + 'mp/baby/care/myList',
	// 规记录列表
	routineMyList: baseUrl + 'mp/baby/routine/myList',
	// 我的喂奶记录列表(个人中心)
	feedingMyList: baseUrl + 'mp/baby/feeding/myList',
	// 我的尿布记录列表
	diaperMyList: baseUrl + 'mp/baby/diaper/myList',
	// 洗澡记录列表
	bathMyList: baseUrl + 'mp/baby/bath/myList',
	// 我的用药记录列表
	medicationMyList: baseUrl + 'mp/baby/medication/myList',
	// 护理记录列表
	motherCareMyList: baseUrl + 'mp/mother/care/myList',
	// 宝妈记录列表
	motherRoundsMyList: baseUrl + 'mp/mother/rounds/myList',
	// 获取当前登陆宝妈信息
	getLoginMomInfo: baseUrl + 'mp/mother/care/getLoginMomInfo',

	// 获取宝宝信息
	infoGetDetail: baseUrl + 'mp/baby/info/getDetail',
	// 宝宝护理详情
	careGetDetail: baseUrl + 'mp/baby/care/getDetail',
	// 宝宝记录详情
	routineGetDetail: baseUrl + 'mp/baby/routine/getDetail',
	// 宝宝喂奶详情
	feedingGetDetail: baseUrl + 'mp/baby/feeding/getDetail',
	// 宝宝尿布详情
	diaperGetDetail: baseUrl + 'mp/baby/diaper/getDetail',
	// 宝宝洗澡详情
	bathGetDetail: baseUrl + 'mp/baby/bath/getDetail',
	// 宝宝用药详情
	medicationGetDetail: baseUrl + 'mp/baby/medication/getDetail',
	// 宝妈护理详情
	motherCareGetDetail: baseUrl + 'mp/mother/care/getDetail',
	// 宝妈记录详情
	roundsGetDetail: baseUrl + 'mp/mother/rounds/getDetail',
	getHistoryMessageList: baseUrl + 'mp/session/user/history/{userId}',
	// 获取用户会话列表
	getUserConversationList: baseUrl + 'mp/conversation/user-list',
	// 标记消息为已读
	markConversationAsRead: baseUrl + 'mp/conversation/read',
	// 获取当前用户相关的所有未读消息数量
	getUnreadMessageCount: baseUrl + 'mp/conversation/unread-count',
	// 通过表明获取所有列
	configGetField: baseUrl + 'mp/field/config/getField',
	// 获取宝宝信息
	getMyDetailList: baseUrl + 'mp/baby/info/getMyDetailList',
	// 获取宝宝信息
	getBabyAnalysis: baseUrl + 'mp/baby/info/getBabyAnalysis',
	login_wx: baseUrl + 'auth/login_wx',
	// 登录获取token
	clubName: baseUrl + 'mp/club/name',
	// 获取当前会所名称
	clubInfo: baseUrl + 'mp/club/info',
	// 获取当前会所基本信息
	clubHome: baseUrl + 'mp/club/home',
	// 查询会所主页信息
	hotList: baseUrl + 'mp/topic/hot_list',
	// 热门话题列表
	communityRecord: baseUrl + 'mp/community/record',
	// 查询宝妈社区动态列表
	postPage: baseUrl + 'mp/club/post_page',
	// 分页查询会所动态列表
	packagePage: baseUrl + 'mp/package/page',
	// 查询优惠套餐列表
	packageInfo: baseUrl + 'mp/package/info',
	// 查询优惠套餐详情
	staffPage: baseUrl + 'mp/staff/page',
	// 查询护理团队列表
	mealList: baseUrl + 'mp/meal/item_list',
	// 查询月子餐列表 主页月子餐
	homeBrand: baseUrl + 'mp/club/home_brand',
	//查询会所品牌介绍
	suitePage: baseUrl + 'mp/suite/page',
	//查询月子套房列表
	suiteInfo: baseUrl + 'mp/suite/info',
	//查询月子套房信息
	infoMerchants: baseUrl + 'mp/meal/info/merchants',
	//查询月子膳食信息
	getItemList: baseUrl + 'mp/meal/get_item_list',
	//查询菜品列表
	productDeliveryPage: baseUrl + 'mp/meal/product_delivery/page',
	//查询膳食套餐列表
	productDelivery: baseUrl + 'mp/meal/product_delivery',
	//查询膳食套餐详情
	staffInfo: baseUrl + 'mp/staff/info',
	//查询护理团队详情
	feedPostUserPage: baseUrl + 'mp/feed_post/user_page',
	//查询指定用户朋友圈列表
	postpartumRecoveryPage: baseUrl + 'mp/postpartum_recovery/page',
	//分页查询产后康复项目列表
	postpartumRecoveryInfo: baseUrl + 'mp/postpartum_recovery/info',
	//查询产后康复信息
	facilitiesList: baseUrl + 'mp/club/facilities_list',
	//查询会所设施列表
	reviewTagList: baseUrl + 'mp/review/tag_list',
	//查询商家总体评价标签列表
	reviewList: baseUrl + 'mp/review/list',
	//查询商家总体评价列表
	reviewInfo: baseUrl + 'mp/review/info',
	//查询商家总体评价详情
	mobileNannyPage: baseUrl + 'mp/mobile_nanny/page',
	//分页查询移动月嫂列表
	mobileNannyInfo: baseUrl + 'mp/mobile_nanny/info',
	//查询移动月嫂信息
	feedPostInfo: baseUrl + 'mp/feed_post/info',
	//查询动态详情
	hotList: baseUrl + 'mp/topic/hot_list',
	//热门话题列表
	topicInfo: baseUrl + 'mp/topic/info',
	//查询话题详情
	arriveUserList: baseUrl + 'mp/feed_post/arrive_user_list',
	//查询已到店入住客户列表
	dayList: baseUrl + 'mp/feed_post/day_list',
	//查询客户入住天数列表
	roomFeedbackList: baseUrl + 'mp/room_feedback/list',
	//查询指定用户评价列表
	communityInfo: baseUrl + 'mp/community/info',
	//查询宝妈社区动态详情
	listComment: baseUrl + 'mp/community/list_comment',
	//查询宝妈社区动态评论列表
	communitySave: baseUrl + 'mp/community/save',
	//新增宝妈社区动态
	communityHistory: baseUrl + 'mp/community/history',
	//查询宝妈社区我的动态列表
	pageView: baseUrl + 'mp/bookmark/page_view',
	//分页查询我收藏页面&界面列表
	favoritePage: baseUrl + 'mp/feed_post/favorite_page',
	//查询当前用户收藏的朋友圈动态
	pageTopic: baseUrl + 'mp/bookmark/page_topic',
	//分页查询我收藏的话题列表
	pagePost: baseUrl + 'mp/bookmark/page_post',
	//分页查询我收藏宝妈社区动态列表
	communityLike: baseUrl + 'mp/message/community_like',
	//查询宝妈社区动态我的消息点赞通知
	communityComment: baseUrl + 'mp/message/community_comment',
	//查询宝妈社区动态我的消息评论通知
	communityBookmark: baseUrl + 'mp/community/bookmark',
	//社区动态收藏
	topicBookmark: baseUrl + 'mp/topic/bookmark',
	//话题收藏&订阅
	feedPostFavorite: baseUrl + 'mp/feed_post/favorite',
	//朋友圈收藏
	feedPostLikes: baseUrl + 'mp/feed_post/likes',
	//朋友圈点赞
	feedPostShare: baseUrl + 'mp/feed_post/share',
	//朋友圈分享
	saveComment: baseUrl + 'mp/community/save_comment',
	//新增宝妈社区动态评论
	commentLike: baseUrl + 'mp/community/comment_like',
	//宝妈社区动态评论点赞
	recordsInfo: baseUrl + 'mp/check_in/records_info',
	//获取当前登录用户签到信息
	checkinSignIn: baseUrl + 'mp/check_in/signIn',
	//签到
	pregnancyTracker: baseUrl + 'mp/pregnancy_tracker',
	//获取孕期助手周数据
	gestationWeeks: baseUrl + 'mp/pregnancy_tracker/gestation_weeks',
	//获取当前用户预产期信息
	userPhone: baseUrl + 'mp/user/phone',
	//获取用户绑定手机号信息
	userUpdateInfo: baseUrl + 'mp/user/update_info',
	//更新微信用户信息
	saveLike: baseUrl + 'mp/community/save_like',
	//宝妈社区动态点赞
	reportStart: baseUrl + 'mp/event_tracking/report_start',
	//开始事件上报
	reportEnd: baseUrl + 'mp/event_tracking/report_end',
	//结束事件上报
	reportComStart: baseUrl + 'mp/event_tracking_community/report_start',
	//宝妈社区点击开始事件上报
	reportComEnd: baseUrl + 'mp/event_tracking_community/report_end',
	//宝妈社区点击结束事件上报
	reviewSave: baseUrl + 'mp/review/save_batch',
	//新增商家总体评价
	notificationNum: baseUrl + 'mp/feed_post/notification_num',
	//查询朋友圈我的未读消息数量(点赞和评论全部数量)
	userPost: baseUrl + 'mp/feed_post/user_post',
	suiteRrecommendation: baseUrl + 'mp/suite/recommendation',
	//查询套房热门推荐列表
	packagerRecommendation: baseUrl + 'mp/package/recommendation',
	//查询优惠套餐热门推荐列表
	recoveryRecommendation: baseUrl + 'mp/postpartum_recovery/recommendation',
	//查询产后康复热门推荐列表
	questionList: baseUrl + 'mp/question/list',
	//查询问题列表
	reviewPage: baseUrl + 'mp/review/page',
	//分页查询商家总体评价列表
	customerAllTasks: baseUrl + 'mp/customer_task/get_customer_all_tasks',
	//获取客户所有任务节点
	signing: baseUrl + 'mp/contract_gifts/signing',
	//签约礼品领取
	couponPage: baseUrl + 'mp/coupon/page',
	//分页查询优惠券列表
	getCustomerServiceStep: baseUrl + 'mp/customer_service_step/get_customer_service_step',
	//获取用户服务过程天数以及对应的节点
	customerServiceStep: baseUrl + 'mp/feed_post/customer_service_step',
	//通过服务过程查询朋友圈动态列表
	checkinInfo: baseUrl + 'mp/check_in/info',
	//查询礼品信息
	customerPage: baseUrl + 'mp/feed_post/grow_grass_page',
	//分页查询所有宝妈和护理人员动态列表 已经排除会所动态
	featuredList: baseUrl + 'mp/feed_post/featured_list',
	//查询精选动态列表
	geocodes: baseUrl + 'mp/club/geocodes',
	//获取地理/逆地理编码
	featuredNodList: baseUrl + 'mp/feed_post/featured_node_list',
	//查询精选节点列表
	//客户端
	feedPostAdd: baseUrl + 'mp/feed_post/add',
	// 动态发布
	feedPostPage: baseUrl + 'mp/feed_post/page',
	// 查询朋友圈列表
	feedPostComment: baseUrl + 'mp/feed_post/comment',
	// 评论
	feedPostLikes: baseUrl + 'mp/feed_post/likes',
	// 点赞
	uploadOSS: baseUrl + 'common/uploadOSS',
	// 文件上传oss
	userInfo: baseUrl + 'mp/user/user_info',
	// 获取登录用户信息
	notificationList: baseUrl + 'mp/feed_post/notification_list',
	// 查询我的消息 通知点赞列表
	messageList: baseUrl + 'mp/customer/message_list',
	// 查询客户端消息列表
	messageDetail: baseUrl + 'mp/customer/message_detail',
	// 查询客户端消息详情
	resultSatisfaction: baseUrl + 'mp/room_feedback/result_satisfaction',
	// 处理结果满意度
	staffList: baseUrl + 'mp/room_feedback/staff_list',
	// 查询房间护理人员列表
	roomFeedbackSave: baseUrl + 'mp/room_feedback/save',
	// 新增评价
	roomFeedbackHistory: baseUrl + 'mp/room_feedback/history',
	// 历史评价
	feedPostHistory: baseUrl + 'mp/feed_post/history',
	// 查询朋友圈历史动态列表
	feedPostRemove: baseUrl + 'mp/feed_post/remove',
	// 删除朋友圈动态
	//管理端
	roomList: baseUrl + 'mp/room/list',
	// 查询房间列表
	roomInfo: baseUrl + 'mp/room/info',
	// 查询房间详情
	feedPostRoomPage: baseUrl + 'mp/feed_post/room_page',
	// 查询房间的朋友圈列表（包括：房间服务人员的动态；入住客户的动态；）
	roomTodo: baseUrl + 'mp/room/todo',
	// 查询房间待办
	statusNumber: baseUrl + 'mp/room/status_number',
	// 查询房间入住状态数量
	roomStatus: baseUrl + 'mp/room/stats',
	// 获取房间待办状态数量统计
	roomTodoInfo: baseUrl + 'mp/room/todo/info',
	// 查询房间待办详情
	roomReply: baseUrl + 'mp/room/reply',
	// 回复投诉
	userStatsList: baseUrl + 'mp/user_stats/list',
	// 查询微信访问用户列表
	userStatsVisits: baseUrl + 'mp/user_stats/visits',
	// 统计用户访问详情 总访问次数、总访问时常、访问最多模块、访问时间最长模块、首次访问模块、最近访问模块
	customerList: baseUrl + 'mp/customer_service_step/get_customer_list',
	// 获取当前服务人员可用的客户列表
	customerTasks: baseUrl + 'mp/customer_service_step/get_customer_tasks',
	// 查询指定客户的待完成任务节点
	getstepnodeList: baseUrl + 'mp/customer_service_step/get_step_node_list',
	// 获取所有节点
	getCustomerServiceNode: baseUrl + 'mp/customer_service_step/get_customer_service_node',
	// 获取所有节
	lastList: baseUrl + 'mp/user_stats/last_list',
	// 查询最近访问用户列表
	mList: baseUrl + 'mp/user_stats/m_list',
	// 根据模块类型查询访问最多、访问最长用户列表
	//客资跟踪
	getWorkResult: baseUrl + 'mp/customer_track/get_work_result',
	// 获取个人工作成果
	getpendingCustomerCount: baseUrl + 'mp/customer_track/get_pending_customer_count',
	// 获取待跟进客户数量
	myPage: baseUrl + 'mp/customer_track/my_page',
	// 分页查询个人客资列表
	followedUpPage: baseUrl + 'mp/customer_track/followed_up_page',
	// 分页查询客户列表
	createCustomer: baseUrl + 'mp/customer_track/create_customer',
	// 新增客资线索
	changeStage: baseUrl + 'mp/customer_track/change_stage',
	// 更改阶段
	changeTag: baseUrl + 'mp/customer_track/change_tag',
	// 更改标签
	createReminder: baseUrl + 'mp/customer_track/create_reminder',
	// 创建提醒事项
	customerFollowUp: baseUrl + 'mp/customer_track/customer_follow_up',
	// 跟进客户
	getMyReminders: baseUrl + 'mp/customer_track/get_my_reminders',
	// 获取我的提醒事项
	operationLogs: baseUrl + 'mp/customer_track/operation_logs',
	// 分页查询操作记录列表
	batchChangeTag: baseUrl + 'mp/customer_track/batch_change_tag',
	// 批量更改标签
	batchChangeStage: baseUrl + 'mp/customer_track/batch_change_stage',
	// 批量更改阶段
	userPage: baseUrl + 'mp/customer_track/user_page',
	// 分页查询销售员列表
	assignLead: baseUrl + 'mp/customer_track/assign_lead',
	// 分配线索
	batchAssignLead: baseUrl + 'mp/customer_track/batch_assign_lead',
	// 批量分配线索
	getAssignmentStats: baseUrl + 'mp/customer_track/getAssignmentStats',
	// 查询客资分配统计 全部、待分配、已分配
	allPage: baseUrl + 'mp/customer_track/all_page',
	// 分页查询全部客资列表
	followedLogs: baseUrl + 'mp/customer_track/followed_logs',
	// 分页查询跟进记录列表
	customerDetail: baseUrl + 'mp/customer_track/customer_detail',
	// 查询客户详情
	tags: baseUrl + 'mp/customer_track/tags',
	// 查询客户标签列表
	createCustomerSale: baseUrl + 'mp/customer_track/create_customer',
	//销售端新增客户
	tagsList: baseUrl + 'mp/customer_track/tags',
	//查询客户标签列表
	myStats: baseUrl + 'mp/customer_track/my_stats',
	//我的客户统计
	notification: baseUrl + 'mp/customer_track/notification_list',
	//查询销售系统通知提醒列表
	leadStats: baseUrl + 'mp/customer_track/lead_stats',
	// 客户线索统计
	contractList: baseUrl + 'mp/customer_track/contract_list',
	// 查询客户签约列表
	contractDetail: baseUrl + 'mp/customer_track/contract_detail',
	// 查询客户签约详情
	packageList: baseUrl + 'mp/package/list',
	//套餐列表
	contract: baseUrl + 'mp/customer_track/contract',
	//客户签约
	customerOperate: baseUrl + 'mp/customer_track/customer_operate',
	//跟进客户
	contractList: baseUrl + 'mp/customer_track/contract_list',
	//查询客户签约列表
	confirmArrivedAtStore: baseUrl + 'mp/customer_track/confirmArrivedAtStore',
	//确认到店
	collectContractPayment: baseUrl + 'mp/customer_track/collectContractPayment',
	//收取合同金
	contractDetail: baseUrl + 'mp/customer_track/contract_detail',
	//查询客户签约详情
	createReminder: baseUrl + 'mp/customer_track/create_reminder',
	//创建或修改提醒事项
	getReminder: baseUrl + 'mp/customer_track/get_reminder_by_id',
	//获取提醒事项详情
	updateDetail: baseUrl + 'mp/customer_track/update_detail',
	//修改客户详情
	suitesList: baseUrl + 'mp/room/suites_list',
	//通过房型id查询套餐列表
	houseTypeList: baseUrl + 'mp/suite/list',
	//查询月子套房列表
	checkinStatus: baseUrl + 'mp/room/status',
	//查询房间入住状态
	occupationRange: baseUrl + 'mp/room/occupation_range',
	//查询房间排房数据
	getInviation: baseUrl + 'mp/wechat_inviation/getInfo',
	//查询请柬
	listByType: baseUrl + 'mp/taskNode/listByType',
	//通过类型与角色查询标签列表
	listByLabel: baseUrl + 'mp/templateDynamic/listByLabel',
	//通过标签名称查询模板
	goodMomList: baseUrl + 'mp/feed_post/good_mom_list',
	//分页查询所有宝妈和会所动态列表 已经排除工作人员动态
	queryRooms: baseUrl + 'mp/feed_post/query_rooms',
	//查询房间号
	getFeedPostPage: baseUrl + 'mp/feed_post/get_feed_post_page',
	//分页查询宝妈社区动态列表
	getpublish: baseUrl + 'mp/feed_post/publish',
	//分页查询宝妈社区动态列表
	styleDynamic: baseUrl + 'mp/styleDynamic',
	//queryShowStyle
	taskNodeSave: baseUrl + 'mp/taskNode/save',
	login_phone_wx: baseUrl + "auth/login_phone_wx",
	// 用户登录
	insert: baseUrl + 'mp/complaint/insert',
	//新增发布投诉
	userList: baseUrl + 'mp/complaint/userList',
	//获取用户投诉列表
	complaintDetail: baseUrl + 'mp/complaint/detail',
	//投诉id获取详情
	getClubInfo: baseUrl + 'mp/feed_post/club_info',
	//获取会所详情
	getActivityList: baseUrl + 'mp/club/activity_list',
	//获取活动列表
	getActivityDetail: baseUrl + 'mp/club/activity_detail',
	//获取活动详情
	getRoleList: baseUrl + 'mp/merchant/role_list',
	//销售发动态角色列表
	getRoomList: baseUrl + 'mp/room/role_code',
	//销售发动态房间列表
	pushSalesPublish: baseUrl + 'mp/feed_post/sales_publish',
	//销售发动态
	getAllTag: baseUrl + 'mp/taskNode/options',
	//获取所有标签
	getAllRoom: baseUrl + 'mp/room/options',
	//获取所有标签
	getMotherInfo: baseUrl + 'mp/mama/info',
	//查询宝妈信息
	getMotherList: baseUrl + 'mp/mama/post_list',
	//查询宝妈动态列表
	pushDiaryItem: baseUrl + 'mp/diary_item/create',
	//新增服务项
	changeDiaryItem: baseUrl + 'mp/diary_item/update',
	//修改服务项
	getDiaryItem: baseUrl + 'mp/diary_item/detail',
	//获取服务项详情
	pushDiaryList: baseUrl + 'mp/diary/publish',
	//发布笔记 
	changeDiaryList: baseUrl + 'mp/diary/update',
	//修改笔记
	getDiaryList: baseUrl + 'mp/diary/page',
	//分页查询笔记 
	getDiaryDetail: baseUrl + 'mp/diary/getDetail',
	//获取笔记详情 
	deleteDiaryList: baseUrl + 'mp/diary/delete',
	//删除宝妈笔记 
	getMomTeam: baseUrl + 'mp/diary_mom/getMomTeam',
	//根据角色获取姓名
	getMomDetail: baseUrl + 'mp/diary_mom/detail',
	//获取优质宝妈详情（包括医护团队）
	getMomPage: baseUrl + 'mp/diary_mom/page',
	//活动报名
	getSinUp: baseUrl + 'mp/club/activitySignUp',
	// 生成二维码
	getQrCode: baseUrl + 'mp/diary/qr_code_url',
	// 服务内容 
	getServicePost: baseUrl + 'mp/feed_post/myPage',
	// 服务笔记
	getServiceDiary: baseUrl + 'mp/diary/myPage',
	// 我的话题
	getServiceCommunity: baseUrl + 'mp/community/myPage',
	// 我的收藏 动态
	getPostFavorite: baseUrl + 'mp/feed_post/favorite_page',
	// 我的收藏 话题
	getPostBookmark: baseUrl + 'mp/bookmark/page_post',
	// 获取请帖列表
	getRehabilitationList: baseUrl + 'mp/card/getList',
	// 获取请帖详情
	getRehabilitationDetail: baseUrl + 'mp/card/getDetail',
	// 用户编辑请帖后先创建id
	getRehabiliteUserId: baseUrl + 'mp/card/create',
	// 用户编辑请帖后保存
	getRehabiliteUserSave: baseUrl + 'mp/card/saveUserCard',
	// 用户编辑保存后的列表
	getRehabiliteUserList: baseUrl + 'mp/card/getUserCardList',
	// 用户编辑保存后的详情
	getRehabiliteUserDetail: baseUrl + 'mp/card/getUserCardDetail',
	// 删除用户保存的请帖
	delRehabiliteItem: baseUrl + 'mp/card/delete',
	// 我的收藏列表
	getMyRehabilite: baseUrl + 'mp/card/myCollect',
	// 我的评论列表
	myComment: baseUrl + 'mp/community/myComment',
	// 我的点赞(动态)
	myUpPost: baseUrl + 'mp/community/myUpPost',
	// 我的点赞(社区)
	myUpCommunity: baseUrl + 'mp/community/myUpCommunity',
	// 获取所有模板风格
	templateStyle: baseUrl + 'platform/template_style/list',
	// 服务笔记创建提醒事项
	diaryCreateReminder: baseUrl + 'mp/diary/create_reminder',
	// 宝妈说、工作人员、会所动态创建提醒事项
	feedCreateReminder: baseUrl + 'mp/feed_post/create_reminder',
	// 获取菜品详情
	itemDetail: baseUrl + 'mp/meal/itemDetail',
	// 菜品咨询
	mealConsultItem: baseUrl + 'mp/meal/consultItem',
	// 膳食套餐
	mealConsult: baseUrl + 'mp/meal/consult',
	// 产康咨询
	recoveryConsult: baseUrl + 'mp/postpartum_recovery/consult',
	// 套餐咨询
	packageConsult: baseUrl + 'mp/package/consult',
	// 获取所有商品基础信息
	getGoodsBasicInfo: baseUrl + 'mp/package/getGoodsBasicInfo',
	// 根据模板类型获取微信订阅消息模板
	getTemplatesByTypes: baseUrl + 'mp/wechat_template_config/getTemplatesByTypes',
	// 获取视频第一帧
	videoFirstFrame: baseUrl + 'common/videoFirstFrame',
	//用户收藏
	collect: baseUrl + 'mp/analysis/collect',
	//用户点赞
	like: baseUrl + 'mp/analysis/like',
	//用户评论
	comment: baseUrl + 'mp/analysis/comment',
	//客户轨迹分析-头部信息
	trajectoryTop: baseUrl + 'mp/analysis/trajectoryTop',
	//获取用户浏览记录-会所记录
	userHistory: baseUrl + 'mp/user/user_history',
	//获取指定日期的标准餐单
	getStandardMenu: baseUrl + 'mp/daily/menu/getStandardMenu',
	//获取当前登录客户的餐单
	menuMyList: baseUrl + 'mp/daily/menu/getMyMenu',
	//获取入住宝妈列表
	getRoomCustomer: baseUrl + 'mp/daily/menu/getRoomCustomer',
	//获取客户个性化餐单
	getCustomerMenu: baseUrl + 'mp/daily/menu/getCustomerMenu',
	//获取推送客资
	getCustomerTrack: baseUrl + 'mp/customer_track/getCustomer',
	//新增/修改部门动态
	getDeptSave: baseUrl + 'mp/dept_feed_post/save',
	//分页查询部门动态列表
	getDeptPage: baseUrl + 'mp/dept_feed_post/page',
	//删除部门动态
	getDeptRemove: baseUrl + 'mp/dept_feed_post/remove',
	//新增反馈
	cardFeedback: baseUrl + 'mp/card_feedback',
};