/**
 * WebSocket配置文件
 * 根据不同环境配置WebSocket服务器地址
 */

// 开发环境配置
const development = {
  baseUrl: 'wss://test-api.xiaodingdang1.com', // 本地开发服务器
  heartbeatInterval: 30000,
  reconnectAttempts: 5,
  reconnectInterval: 3000
}

// 测试环境配置
const testing = {
  baseUrl: 'wss://test-api.xiaodingdang1.com', // 测试服务器
  heartbeatInterval: 30000,
  reconnectAttempts: 5,
  reconnectInterval: 3000
}

// 生产环境配置
const production = {
  baseUrl: 'wss://admin-api.xiaodingdang1.com', // 生产服务器（使用wss安全连接）
  heartbeatInterval: 30000,
  reconnectAttempts: 3,
  reconnectInterval: 5000
}

// 根据环境变量或其他条件选择配置
function getWebSocketConfig() {
  // 可以根据实际需要修改环境判断逻辑
  const env = process.env.NODE_ENV || 'development'
  
  switch (env) {
    case 'production':
      return production
    case 'testing':
      return testing
    default:
      return development
  }
}

// 导出配置
export default getWebSocketConfig()

// 也可以导出所有配置供选择使用
export {
  development,
  testing,
  production,
  getWebSocketConfig
}
