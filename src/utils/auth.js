import api from './api.js'
import axios from './request.js'
import { getEnvConfig } from '@/utils/config';
const baseUrl = getEnvConfig().BASE_URL;
export async function checkSession() {
    return new Promise((resolve, reject) => {
        uni.checkSession({
            success() {
                return resolve(true);
            },
            fail() {
                return resolve(false);
            }
        });
    });
}

/**
 * 检测是否绑定手机号
 */
export async function checkBindPhone() {
    const hasBindPhone = uni.getStorageSync('hasBindPhone');
    if (!hasBindPhone) {
        uni.navigateTo({
            url: '/pageA/pageB/home/<USER>/login?identification=1'
        });
    }
}

// 最近使用的小程序切换
export async function getTenants() {
    const response = axios.get(`${baseUrl}mp/user/tenants`);
    return response;
}
export async function bindPhone(phone) {
    new Promise((resolve, reject) => {
        if (!phone) {
            return reject('手机号码不能为空');
        }
        const uid = uni.getStorageSync('uid');
        const data = {
            tel: phone,
            userId: uid
        };
        axios
            .post(api.userUpdateInfo, data)
            .then((res) => {
                if (res.data.code === 200) {
                    uni.setStorageSync('hasBindPhone', true);
                    resolve(res.data);
                } else {
                    reject(res.data.msg);
                }
            })
            .catch((err) => {
                reject(err);
            });
    });
}

// 检测登录状态，返回 true / false
export async function checkHasLogined() {
    const token = uni.getStorageSync('token');
    if (!token) {
        return false;
    }
    const loggined = await checkSession();
    if (!loggined) {
        uni.removeStorageSync('token');
        return false;
    }
    // const tenantId = uni.getStorageSync('tenantId');
    // const checkTokenRes = await axios.get(`${baseUrl}auth/checkToken?tenantId=${tenantId}`);
    return true;
}

export async function getUserInfo() {
    return new Promise((resolve, reject) => {
        axios.get(`${baseUrl}mp/user/info`).then(res => {
            // uni.setStorageSync('tenants', data.user)
            uni.setStorageSync('uid', res.data.data.user.userId)
            uni.setStorageSync('userInfo', res.data.data.user);
            uni.setStorageSync('permissions', res.data.data.permissions);
            uni.setStorageSync('roles', res.data.data.roles);
            uni.setStorageSync('hasBindPhone', res.data.data.user.hasBindPhone);
            uni.setStorageSync('clubName', res.data.data.user.clubName);
        }).finally(() => {
            resolve()
        });
        // reject();
    });
}
export async function wxaCode() {
    return new Promise((resolve, reject) => {
        uni.login({
            success(res) {
                return resolve(res.code);
            },
            fail() {
                return resolve('获取code失败');
            }
        });
    });
}
export async function login(page) {
    const _this = this;
    uni.login({
        success: function (res) {
            const componentAppid = uni.getStorageSync('componentAppid');
            if (componentAppid) {
                WXAPI.wxappServiceLogin({
                    componentAppid,
                    appid: uni.getStorageSync('appid'),
                    code: res.code
                }).then(function (res) {
                    if (res.code == 10000) {
                        // 去注册
                        return;
                    }
                    if (res.code != 0) {
                        // 登录错误
                        uni.showModal({
                            confirmText: $t.common.confirm,
                            cancelText: $t.common.cancel,
                            title: $t.common.loginFail,
                            content: res.msg,
                            showCancel: false
                        });
                        return;
                    }
                    uni.setStorageSync('token', res.data.token);
                    uni.setStorageSync('uid', res.data.uid);
                    _this.bindSeller();
                    if (page) {
                        page.onShow();
                    }
                });
            } else {
                WXAPI.login_wx(res.code).then(function (res) {
                    if (res.code == 10000) {
                        // 去注册
                        return;
                    }
                    if (res.code != 0) {
                        // 登录错误
                        uni.showModal({
                            confirmText: $t.common.confirm,
                            cancelText: $t.common.cancel,
                            title: $t.common.loginFail,
                            content: res.msg,
                            showCancel: false
                        });
                        return;
                    }
                    uni.setStorageSync('token', res.data.token);
                    uni.setStorageSync('uid', res.data.uid);
                    _this.bindSeller();
                    if (page) {
                        page.onShow();
                    }
                });
            }
        }
    });
}
export async function authorize() {
    return new Promise((resolve, reject) => {
        uni.login({
            success: function (res) {
                console.log('success', res)
                const code = res.code;
                let referrer = ''; // 来源
                let referrer_storge = uni.getStorageSync('referrer');
                if (referrer_storge) {
                    referrer = referrer_storge;
                }
                // 下面开始调用登录接口
                const tenantId = uni.getStorageSync('tenantId');
                console.log('tenantIdtenantId', tenantId)
                if (!tenantId) {
                    console.log('检测到商户编号为空', tenantId);
                    //跳转到公共页
                    // uni.navigateTo({
                    //     url: '/pages/public/public'
                    // });
                    //TODO 先默认我们的商户编号
                    // tenantId = '194338';
                }
                const data = {
                    code: code,
                    appid: uni.getAccountInfoSync().miniProgram.appId,
                    clientId: '428a8310cd442757ae699df5d894f051',
                    grantType: 'xcx',
                    tenantId: tenantId
                };
                axios.post(api.login_wx, data).then((res) => {
                    const data = res.data.data;
                    if (res.data.code == 200 || res.data.code == 202) {
                        uni.setStorageSync('token', data.access_token);
                        getUserInfo()
                        // uni.setStorageSync('uid', data?.userInfo.userId);
                        // uni.setStorageSync('tenants', data.tenants);
                        resolve(data);
                    } else {
                        // uni.showToast({
                        //     title: res.data.msg,
                        //     icon: 'none'
                        // });
                        reject(res.data.msg);
                    }
                });
            },
            fail: (err) => {
                console.log('errrorrrrrrrr', err)
                reject(err);
            }
        });
    });
}
export async function loginOut() {
    axios.post(`${baseUrl}auth/logout`).then((response) => {
        if (response.code === 200) {
            uni.removeStorageSync('token');
            uni.removeStorageSync('uid');
            uni.removeStorageSync('tenants');
        }
    });
}
export async function checkAndAuthorize(scope) {
    return new Promise((resolve, reject) => {
        uni.getSetting({
            success(res) {
                if (!res.authSetting[scope]) {
                    uni.authorize({
                        scope: scope,
                        success() {
                            resolve(); // 无返回参数
                        },
                        fail(e) {
                            console.error(e);
                            // if (e.errMsg.indexof('auth deny') != -1) {
                            //   wx.showToast({
                            //     title: e.errMsg,
                            //     icon: 'none'
                            //   })
                            // }
                            uni.showModal({
                                content: $t.common.authorizeRequired,
                                showCancel: false,
                                confirmText: $t.common.authorize,
                                confirmColor: '#e64340',
                                success(res) {
                                    uni.openSetting();
                                },
                                fail(e) {
                                    console.error(e);
                                    reject(e);
                                }
                            });
                        }
                    });
                } else {
                    resolve(); // 无返回参数
                }
            },
            fail(e) {
                console.error(e);
                reject(e);
            }
        });
    });
}
export default {
    checkHasLogined: checkHasLogined,
    wxaCode: wxaCode,
    login: login,
    loginOut: loginOut,
    checkAndAuthorize: checkAndAuthorize,
    authorize: authorize,
    bindPhone: bindPhone,
    checkBindPhone: checkBindPhone,
    getTenants: getTenants,
    getUserInfo: getUserInfo
};