/**
 * JSON 配置文件管理器
 * 提供项目配置的增删改查功能，基于 JSON 文件存储
 */

const fs = require('fs');
const path = require('path');

class JsonConfigManager {
  constructor() {
    this.configPath = path.resolve(__dirname, 'projects.json');
    this.backupDir = path.resolve(__dirname, 'backups');
    this.ensureBackupDir();
    this.ensureConfigFile();
  }

  /**
   * 确保备份目录存在
   */
  ensureBackupDir() {
    if (!fs.existsSync(this.backupDir)) {
      fs.mkdirSync(this.backupDir, { recursive: true });
    }
  }

  /**
   * 确保配置文件存在
   */
  ensureConfigFile() {
    if (!fs.existsSync(this.configPath)) {
      const defaultConfig = {
        projects: {},
        defaultConfig: {
          version: {
            auto: true,
            format: "YYYY.MM.DD.HHmm"
          },
          upload: {
            desc: "通过 miniprogram-ci 自动上传",
            robot: 1,
            timeout: 300000
          },
          preview: {
            pagePath: "pages/index/index",
            searchQuery: "",
            scene: 1001,
            qrcodeFormat: "image",
            qrcodeOutputDest: "../../preview-qrcode.jpg"
          }
        }
      };
      this.writeConfigFile(defaultConfig);
    }
  }

  /**
   * 读取配置文件
   */
  readConfig() {
    try {
      const configData = fs.readFileSync(this.configPath, 'utf8');
      const config = JSON.parse(configData);
      
      // 处理路径解析
      this.resolveConfigPaths(config);
      
      return {
        success: true,
        data: config
      };
    } catch (error) {
      return {
        success: false,
        error: `读取配置文件失败: ${error.message}`
      };
    }
  }

  /**
   * 写入配置文件
   */
  writeConfig(config) {
    try {
      // 创建备份
      this.createBackup();

      // 处理路径为相对路径
      const configToSave = this.normalizeConfigPaths(config);
      
      // 写入文件
      this.writeConfigFile(configToSave);
      
      return {
        success: true,
        message: '配置文件保存成功'
      };
    } catch (error) {
      return {
        success: false,
        error: `保存配置文件失败: ${error.message}`
      };
    }
  }

  /**
   * 写入配置文件（内部方法）
   */
  writeConfigFile(config) {
    const configJson = JSON.stringify(config, null, 2);
    fs.writeFileSync(this.configPath, configJson, 'utf8');
  }

  /**
   * 解析配置中的相对路径为绝对路径
   */
  resolveConfigPaths(config) {
    if (config.projects) {
      Object.values(config.projects).forEach(project => {
        if (project.projectPath && !path.isAbsolute(project.projectPath)) {
          project.projectPath = path.resolve(__dirname, project.projectPath);
        }
        if (project.privateKeyPath && !path.isAbsolute(project.privateKeyPath)) {
          project.privateKeyPath = path.resolve(__dirname, project.privateKeyPath);
        }
      });
    }
    
    if (config.defaultConfig && config.defaultConfig.preview && config.defaultConfig.preview.qrcodeOutputDest) {
      const qrcodePath = config.defaultConfig.preview.qrcodeOutputDest;
      if (!path.isAbsolute(qrcodePath)) {
        config.defaultConfig.preview.qrcodeOutputDest = path.resolve(__dirname, qrcodePath);
      }
    }
  }

  /**
   * 将配置中的绝对路径转换为相对路径
   */
  normalizeConfigPaths(config) {
    const normalizedConfig = JSON.parse(JSON.stringify(config));
    
    if (normalizedConfig.projects) {
      Object.values(normalizedConfig.projects).forEach(project => {
        if (project.projectPath && path.isAbsolute(project.projectPath)) {
          project.projectPath = path.relative(__dirname, project.projectPath);
        }
        if (project.privateKeyPath && path.isAbsolute(project.privateKeyPath)) {
          project.privateKeyPath = path.relative(__dirname, project.privateKeyPath);
        }
      });
    }
    
    if (normalizedConfig.defaultConfig && normalizedConfig.defaultConfig.preview && normalizedConfig.defaultConfig.preview.qrcodeOutputDest) {
      const qrcodePath = normalizedConfig.defaultConfig.preview.qrcodeOutputDest;
      if (path.isAbsolute(qrcodePath)) {
        normalizedConfig.defaultConfig.preview.qrcodeOutputDest = path.relative(__dirname, qrcodePath);
      }
    }
    
    return normalizedConfig;
  }

  /**
   * 创建配置文件备份
   */
  createBackup() {
    try {
      if (fs.existsSync(this.configPath)) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const backupPath = path.join(this.backupDir, `projects.${timestamp}.json`);
        fs.copyFileSync(this.configPath, backupPath);
        
        // 只保留最近 10 个备份
        this.cleanupBackups();
      }
    } catch (error) {
      console.warn('创建备份失败:', error.message);
    }
  }

  /**
   * 清理旧备份文件
   */
  cleanupBackups() {
    try {
      const files = fs.readdirSync(this.backupDir)
        .filter(file => file.startsWith('projects.') && file.endsWith('.json'))
        .map(file => ({
          name: file,
          path: path.join(this.backupDir, file),
          mtime: fs.statSync(path.join(this.backupDir, file)).mtime
        }))
        .sort((a, b) => b.mtime - a.mtime);

      // 删除超过 10 个的旧备份
      if (files.length > 10) {
        files.slice(10).forEach(file => {
          fs.unlinkSync(file.path);
        });
      }
    } catch (error) {
      console.warn('清理备份失败:', error.message);
    }
  }

  /**
   * 获取所有项目
   */
  getAllProjects() {
    const result = this.readConfig();
    if (!result.success) {
      return result;
    }

    return {
      success: true,
      data: Object.keys(result.data.projects).map(id => ({
        id,
        ...result.data.projects[id]
      }))
    };
  }

  /**
   * 获取单个项目
   */
  getProject(projectId) {
    const result = this.readConfig();
    if (!result.success) {
      return result;
    }

    const project = result.data.projects[projectId];
    if (!project) {
      return {
        success: false,
        error: `项目 "${projectId}" 不存在`
      };
    }

    return {
      success: true,
      data: {
        id: projectId,
        ...project
      }
    };
  }

  /**
   * 创建项目
   */
  createProject(projectData) {
    const result = this.readConfig();
    if (!result.success) {
      return result;
    }

    const { id, ...projectConfig } = projectData;
    
    // 检查项目 ID 是否已存在
    if (result.data.projects[id]) {
      return {
        success: false,
        error: `项目 "${id}" 已存在`
      };
    }

    // 验证必要字段
    const validation = this.validateProjectData(projectConfig);
    if (!validation.success) {
      return validation;
    }

    // 添加项目
    result.data.projects[id] = projectConfig;

    // 保存配置
    return this.writeConfig(result.data);
  }

  /**
   * 更新项目
   */
  updateProject(projectId, projectData) {
    const result = this.readConfig();
    if (!result.success) {
      return result;
    }

    // 检查项目是否存在
    if (!result.data.projects[projectId]) {
      return {
        success: false,
        error: `项目 "${projectId}" 不存在`
      };
    }

    // 验证必要字段
    const validation = this.validateProjectData(projectData);
    if (!validation.success) {
      return validation;
    }

    // 更新项目
    result.data.projects[projectId] = {
      ...result.data.projects[projectId],
      ...projectData
    };

    // 保存配置
    return this.writeConfig(result.data);
  }

  /**
   * 删除项目
   */
  deleteProject(projectId, options = {}) {
    const result = this.readConfig();
    if (!result.success) {
      return result;
    }

    // 检查项目是否存在
    if (!result.data.projects[projectId]) {
      return {
        success: false,
        error: `项目 "${projectId}" 不存在`
      };
    }

    const project = result.data.projects[projectId];
    const deletedFiles = [];

    // 如果选择删除相关文件
    if (options.deleteFiles) {
      try {
        // 删除私钥文件
        if (options.deletePrivateKey && fs.existsSync(project.privateKeyPath)) {
          fs.unlinkSync(project.privateKeyPath);
          deletedFiles.push('私钥文件');
        }

        // 删除预览二维码文件
        if (options.deletePreviewFiles) {
          try {
            const previewDir = path.resolve(__dirname, '../..');
            const files = fs.readdirSync(previewDir);
            const previewFiles = files.filter(file => 
              file.startsWith(`preview-${projectId}-`) && file.endsWith('.jpg')
            );
            previewFiles.forEach(file => {
              const filePath = path.join(previewDir, file);
              if (fs.existsSync(filePath)) {
                fs.unlinkSync(filePath);
                deletedFiles.push('预览文件');
              }
            });
          } catch (error) {
            console.warn('删除预览文件时出错:', error.message);
          }
        }
      } catch (error) {
        console.warn('删除相关文件时出错:', error.message);
      }
    }

    // 删除项目配置
    delete result.data.projects[projectId];

    // 保存配置
    const saveResult = this.writeConfig(result.data);
    
    if (saveResult.success && deletedFiles.length > 0) {
      saveResult.message += `，同时删除了：${deletedFiles.join('、')}`;
    }

    return saveResult;
  }

  /**
   * 验证项目数据
   */
  validateProjectData(projectData) {
    const required = ['name', 'appid', 'projectPath', 'privateKeyPath', 'environments'];
    
    for (const field of required) {
      if (!projectData[field]) {
        return {
          success: false,
          error: `缺少必要字段: ${field}`
        };
      }
    }

    // 验证 appid 格式
    if (!/^wx[a-f0-9]{16}$/.test(projectData.appid)) {
      return {
        success: false,
        error: 'AppID 格式不正确，应为 wx 开头的 18 位字符串'
      };
    }

    // 验证环境配置
    if (!projectData.environments || typeof projectData.environments !== 'object') {
      return {
        success: false,
        error: '环境配置不正确'
      };
    }

    const validEnvironments = ['develop', 'trial', 'release'];
    for (const env of Object.keys(projectData.environments)) {
      if (!validEnvironments.includes(env)) {
        return {
          success: false,
          error: `不支持的环境: ${env}`
        };
      }
    }

    return { success: true };
  }

  /**
   * 获取备份列表
   */
  getBackups() {
    try {
      const files = fs.readdirSync(this.backupDir)
        .filter(file => file.startsWith('projects.') && file.endsWith('.json'))
        .map(file => {
          const filePath = path.join(this.backupDir, file);
          const stats = fs.statSync(filePath);
          return {
            name: file,
            path: filePath,
            size: stats.size,
            mtime: stats.mtime
          };
        })
        .sort((a, b) => b.mtime - a.mtime);

      return {
        success: true,
        data: files
      };
    } catch (error) {
      return {
        success: false,
        error: `获取备份列表失败: ${error.message}`
      };
    }
  }

  /**
   * 恢复备份
   */
  restoreBackup(backupName) {
    try {
      const backupPath = path.join(this.backupDir, backupName);
      
      if (!fs.existsSync(backupPath)) {
        return {
          success: false,
          error: '备份文件不存在'
        };
      }

      // 创建当前配置的备份
      this.createBackup();

      // 恢复备份
      fs.copyFileSync(backupPath, this.configPath);

      return {
        success: true,
        message: '配置已恢复'
      };
    } catch (error) {
      return {
        success: false,
        error: `恢复备份失败: ${error.message}`
      };
    }
  }
}

module.exports = JsonConfigManager;
