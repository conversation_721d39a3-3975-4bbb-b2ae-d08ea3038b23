<template>
  <view>
    <view class="main">
      <view class="mainss">
        <view class="backContentRight">
          <view class="roll">
            <view
              class="rollImg"
              @tap="user"
              :data-userid="item.userId"
              :data-index="index"
              v-for="(item, index) in arriveUserList"
              :key="index"
            >
              <image
                :src="item.avatar || $defaultAvatar"
                class="rollImgs"
                mode=""
              />

              <view :class="index == userIndex ? 'rollTextActive' : 'rollText'">
                {{ item.nickname || $defaultName }}
              </view>
            </view>
          </view>
          <view class="tabList">
            <view :class="tabindex == -1 ? 'activeTab' : 'tabList1'" @tap="all"
              >全部</view
            >
            <template v-if="dayList[dayIndex]">
              <view
                :class="tabindex == index ? 'activeTab' : 'tabList1'"
                @tap="tab"
                :data-index="index"
                :data-tasknodeid="item.taskNodeId"
                v-for="(item, index) in dayList[dayIndex].stepList"
                :key="index"
              >
                <image
                  src="http://cdn.xiaodingdang1.com/2024/08/26/14347f63255245709eac8a7e157edf6b.png"
                  mode=""
                  v-if="item.isFeatured && tabindex != index"
                  style="width: 28rpx; height: 28rpx"
                />

                <image
                  src="http://cdn.xiaodingdang1.com/2024/08/26/d3ef6493b4f2461bbe3a58233ce2126e.png"
                  mode=""
                  v-if="item.isFeatured && tabindex == index"
                  style="width: 28rpx; height: 28rpx"
                />

                <view class="refined">
                  {{ item.nodeName }}
                </view>
              </view>
            </template>
          </view>
          <view
            class="preciousMother"
            v-for="(item, index) in userPageList"
            :key="index"
          >
            <image
              src="http://cdn.xiaodingdang1.com/2024/08/19/0e0811dd2839421cbdd5bed46a85a5d0.png"
              mode=""
              style="height: 48rpx; width: 100%"
              v-if="item.type == 'USER'"
            />

            <image
              src="http://cdn.xiaodingdang1.com/2024/08/19/6212dff4d4864ea7933a978eb8d576e0.png"
              mode=""
              style="height: 48rpx; width: 100%"
              v-if="item.type == 'CLUB'"
            />

            <image
              src="http://cdn.xiaodingdang1.com/2024/08/19/a4419237fb47479eaaa96ed79968bee3.png"
              mode=""
              style="height: 48rpx; width: 100%"
              v-if="item.type == 'STAFF'"
            />

            <view class="preciousMother1">
              <view class="mother1">
                <view class="mother3">
                  <image
                    :src="item.avatar || $defaultAvatar"
                    mode=""
                    style="width: 80rpx; height: 80rpx; border-radius: 100rpx"
                  />
                  <view class="mother1_2">
                    <view style="display: flex; align-items: center">
                      <view class="mother1Text2">{{
                        item.nickname || $defaultName
                      }}</view>
                      <image
                        src="http://cdn.xiaodingdang1.com/2024/08/20/9d5d73050d974a55b07f5982484179bf.png"
                        mode=""
                        style="width: 175rpx; height: 38rpx; margin-left: 10rpx"
                        v-if="item.type == 'STAFF'"
                      />
                      <image
                        src="http://cdn.xiaodingdang1.com/2024/08/19/b5f1bce08849448a959ba0571a2e5a34.png"
                        mode=""
                        style="width: 138rpx; height: 50rpx; margin-left: 10rpx"
                        v-if="item.type == 'USER'"
                      />
                      <image
                        src="http://cdn.xiaodingdang1.com/2024/08/19/4fb0a2dc7df44055b9d8bf7172d1d054.png"
                        mode=""
                        style="width: 72rpx; height: 38rpx; margin-left: 10rpx"
                        v-if="item.type == 'CLUB'"
                      />
                    </view>
                    <view class="mother1Text1">
                      {{ item.createTimes }}
                    </view>
                  </view>
                </view>

                <view class="lengthStay" v-if="item.type == 'USER'"
                  >入住第{{ item.liveDays }}天</view
                >
              </view>
              <view v-if="item.type == 'STAFF'">
                <view class="ageLimit">
                  从业年限
                  <text class="ageLimit1">{{
                    item.staffInfo.yearsEmployment
                  }}</text>
                  服务人员数量
                  <text class="ageLimit2"
                    >{{ item.staffInfo.serviceNum }}人</text
                  >
                </view>
                <view class="ageLimit3">
                  <view class="ageLimit5">人员标签</view>
                  <view
                    class="ageLimit4"
                    v-for="(item, index1) in item.staffInfo.staffTags"
                    :key="index1"
                  >
                    {{ item }}
                  </view>
                </view>
              </view>
              <view
                :class="item.moreBtns ? 'beyond' : 'mother2'"
                @tap="momDetails(item)"
              >
                {{ item.content }}
              </view>
              <view
                class="packUp"
                v-if="item.moreBtn"
                @tap="packUp"
                :data-index="index"
              >
                {{ item.packUpShow ? "收回" : "展开" }}
              </view>
              <!-- 视频 -->
              <view
                class="img_box"
                v-if="item.videos && item.videos.length > 0"
              >
                <view
                  class="videos"
                  v-for="(res, index1) in item.videos"
                  :key="index1"
                >
                  <video
                    class="video"
                    :src="res"
                    @tap="preview"
                    :data-src="item.videos[0]"
                    data-index="0"
                    :ontrols="false"
                  ></video>
                </view>
              </view>
              <!-- 图片 -->
              <view class="img_box" v-else>
                <template v-if="item.contentPhoto">
                  <view
                    :class="item.contentPhotos.length > 1 ? 'many_img' : ''"
                  >
                    <view
                      :class="
                        'img_item ' +
                        (item.contentPhotos.length == 1 ||
                        item.contentPhotos.length == 2 ||
                        item.contentPhotos.length == 4
                          ? 'many'
                          : item.contentPhotos.length >= 3
                          ? 'four'
                          : '')
                      "
                      v-for="(res, index1) in item.contentPhotos"
                      :key="index1"
                    >
                      <image
                        class="img"
                        :src="res"
                        @tap="previewImage"
                        :data-url="item.contentPhotos"
                        :data-src="res"
                        :data-sources="item.contentPhotos"
                        :data-index="index"
                        mode="aspectFill"
                      ></image>
                    </view>
                  </view>
                </template>
              </view>
              <view class="comment1">
                <view class="comment2" v-for="(e, j) in item.comments" :key="j">
                  <text class="mmcomment">{{ e.nickname }}:</text>
                  <text class="mmcomments">{{ e.comment }}</text>
                </view>
              </view>
              <view>
                <view class="discuss4"></view>

                <view class="discusss5">
                  <functionbutton-vue :item="item"></functionbutton-vue>
                </view>
              </view>
            </view>
          </view>
          <view class="toLoad" @tap="toLoad" v-if="toLoadShow"
            >加载更多数据</view
          >
          <view class="text" v-if="tabindex == -1">妈妈评分</view>
          <view
            class="preciousMothers"
            v-if="tabindex == -1"
            v-for="(item, index) in roomFeedbackList"
            :key="index"
          >
            <view class="mother1">
              <view class="mother3">
                <image
                  :src="item.avatar || $defaultAvatar"
                  mode=""
                  style="width: 80rpx; height: 80rpx; border-radius: 100rpx"
                />
                <view class="mother1_2">
                  <view style="display: flex">
                    <view class="mother1Text2">{{
                      item.nickname || $defaultName
                    }}</view>
                    <view class="head1_2">
                      <image
                        src="http://cdn.xiaodingdang1.com/1/120240125/5b82ebb2-4d5c-43ae-83b8-f4621e4f3336.png"
                        mode=""
                        style="width: 40rpx; height: 34rpx"
                        class="head1Img"
                      />
                      <view class="head1_3">{{ item.staffPost }}</view>
                    </view>
                  </view>
                  <view class="mother1Text1">
                    {{ item.clubName }}
                  </view>
                </view>
              </view>
            </view>

            <view class="score">
              <view class="score1">宝妈给她的标签：</view>
              <view class="score2" v-for="(res, i) in item.tags" :key="i">
                {{ res }}
              </view>
            </view>

            <view class="score3">
              {{ item.content }}
            </view>
          </view>
          <view class="toLoads" @tap="toLoads" v-if="toLoadShows"
            >加载更多数据</view
          >
        </view>
      </view>
    </view>
    <new-request-loading></new-request-loading>
    <popUp v-if="notLogShow"></popUp>
  </view>
</template>

<script>
import functionbuttonVue from "@/components/functionbutton.vue";
export default {
  components: {
    functionbuttonVue,
  },
  data() {
    return {
      toLoadShows: true,
      isLoading: false,
      notLogShow: false,
      bgColor: "",
      menu: "",
      navBarHeight: "",
      navStatusBarHeight: "",
      feedbackType: "",
      pageSizes: 2,
      pageNums: 1,
      toLoadShow: false,
      userIndex: 0,
      startDate: "",
      endDate: "",
      dayList: [],
      dayIndex: 0,
      types: "",
      pageSize: 10,
      pageNum: 1,
      tabindex: -1,
      listTab: [],
      userPageList: [],
      roomFeedbackList: [],
      userId: "",
      taskNodeId: "",
      arriveUserList: [],
      stepList: [],
      res: [],
      j: "",

      e: {
        nickname: "",
        comment: "",
      },

      i: "",
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    var newDateTime = Date.parse(new Date());
    let data = {
      eventType: "PAGE_VIEW",
      pageUrl: "/pageA/pageB/community/motherspeak/momcommunity",
      module: "community",
      eventTime: newDateTime,
    };
    let iftoken = uni.getStorageSync("token");
    if (iftoken) {
      this.$point.basePoint(data);
    }
    //  this.getCustomerServiceNode(options.userId)
    // this.roomFeedbackList(options.userId)
    // this.getCustomerServiceStep(options.userId)

    //  this.setData({
    //   userId:options.userId
    //  })
    this.arriveUserListFun();
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},
  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    const hasBindPhone = uni.getStorageSync("hasBindPhone");
    if (!hasBindPhone) {
      this.setData({
        notLogShow: !this.notLogShow,
      });
    } else {
      this.setData({
        notLogShow: !hasBindPhone,
      });
    }
    // AUTH.checkBindPhone().then(() => { });
    // 获取手机系统信息
    uni.getSystemInfo({
      success: (res) => {
        // 手机系统状态栏高度
        uni.setStorageSync("statusBarHeight", res.statusBarHeight);
        const platform = res.platform;
        const menu = uni.getMenuButtonBoundingClientRect();
        //menu为胶囊，判断是否能读到胶囊位置，读不到则用具体一般数值表示
        if (menu) {
          this.setData({
            menu: menu,
            navBarHeight: menu.height + (menu.top - res.statusBarHeight) * 2,
            navStatusBarHeight:
              res.statusBarHeight +
              menu.height +
              (menu.top - res.statusBarHeight) * 2,
          });
          // wx.setStorageSync('menu', menu)
          // // 导航栏高度
          // wx.setStorageSync('navBarHeight', menu.height+(menu.top-res.statusBarHeight) *2 )
          // // 状态栏加导航栏
          // wx.setStorageSync('navStatusBarHeight', res.statusBarHeight+ menu.height+(menu.top-res.statusBarHeight) *2 )
        } else {
          this.setData({
            menu: null,
            navBarHeight: platform === "android" ? 48 : 44,
            navStatusBarHeight:
              res.statusBarHeight + (platform === "android" ? 48 : 44),
          });
          // wx.setStorageSync('menu', null)
          // // 导航栏高度
          // wx.setStorageSync('navBarHeight', platform === 'android' ? 48 : 44)
          //  // 状态栏加导航栏
          //  wx.setStorageSync('navStatusBarHeight', res.statusBarHeight+ (platform === 'android' ? 48 : 44) )
        }
      },
      fail(err) {
        console.log(err);
      },
    });
  },
  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},
  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    var newDateTime = Date.parse(new Date());
    let data = {
      eventId: uni.getStorageSync("eventId"),
      leaveTime: newDateTime,
    };
    this.$point.reportEnd(data);
  },
  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},
  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    // if(this.data.feedbackType==1){
    // 	this.setData({
    // 		pageNums: this.data.pageNums + 1,
    //   })
    //   console.log(this.data.pageNums);
    // 	this.roomFeedbackList(this.data.userId)
    //   }
  },
  /**
   * 用户点击右上角分享
   */
  onPageScroll: function (e) {
    if (e.scrollTop > 0) {
      this.setData({
        bgColor: "#fff",
      });
    } else if (e.scrollTop < 2) {
      this.setData({
        bgColor: "",
      });
    }
  },
  onPullDownRefresh() {},
  onShareTimeline() {},
  methods: {
    toLoads() {
      if (this.feedbackType == 1) {
        this.setData({
          pageNums: this.pageNums + 1,
        });
        console.log(this.pageNums);
        this.roomFeedbackListFun(this.userId);
      }
    },

    toLoad() {
      if (this.types == 1) {
        this.setData({
          pageNum: this.pageNum + 1,
        });
        this.feedPostUserPage(this.userId);
      }
    },

    feedPostUserPage(userId) {
      //查询指定用户朋友圈列表

      this.changeLoading(true);
      // this.setData({
      //     isLoading: true
      // });

      let data = {
        pageSize: this.pageSize,
        pageNum: this.pageNum,
        userId: userId,
      };
      //  if(this.data.date){
      //    data.date=this.data.date
      //  }
      if (this.endDate && this.startDate) {
        data.endDate = this.endDate;
        data.startDate = this.startDate;
      } else {
        data.endDate =
          this.dayList.length > 0 && this.dayList[0].endDate
            ? this.dayList[0].endDate
            : "";
        data.startDate =
          this.dayList.length > 0 && this.dayList[0].startDate
            ? this.dayList[0].startDate
            : "";
      }
      if (this.taskNodeId) {
        data.taskNodeId = this.taskNodeId;
      }
      this.$axios
        .get(this.$api.customerServiceStep, data)
        .then((res) => {
          if (res.data.code == 200) {
            let data = res.data.rows;
            if (data != null) {
              let types = data != null ? (data.length == 10 ? 1 : 2) : "";
              let dtnow;
              if (this.pageNum == 1) {
                dtnow = [];
              } else {
                dtnow = this.userPageList;
              }
              data.forEach((item) => {
                if (item.content.length > 115) {
                  item.packUpShow = false;
                  item.moreBtns = true;
                  item.moreBtn = true;
                } else {
                  item.packUpShow = false;
                  item.moreBtns = false;
                  item.moreBtn = false;
                }
                var timearr = item.createTime
                  .replace(" ", ":")
                  .replace(/\:/g, "-")
                  .split("-");
                item.createTimes =
                  timearr[1] +
                  "/" +
                  timearr[2] +
                  " " +
                  timearr[3] +
                  ":" +
                  timearr[4];
              });
              let dtnows = dtnow.concat(data);
              this.setData({
                userPageList: dtnows,
                types: types,
              });
              if (dtnows.length == res.data.total) {
                this.setData({
                  toLoadShow: false,
                });
              }
              if (dtnows.length < res.data.total) {
                this.setData({
                  toLoadShow: true,
                });
              }
              // this.setData({
              //     isLoading: false
              // });
            }
          }
        })
        .finally(() => {
          this.changeLoading(false);
        });
    },

    roomFeedbackListFun(userId) {
      //查询指户定用评列价表

      let data = {
        pageSize: this.pageSizes,
        pageNum: this.pageNums,
        userId: userId,
      };
      this.$axios.get(this.$api.roomFeedbackList, data).then((res) => {
        if (res.data.code == 200) {
          let data = res.data.data.rows;
          if (data != null) {
            let types = data.length == 2 ? 1 : 2;
            let dtnow;
            if (this.pageNums == 1) {
              dtnow = [];
            } else {
              dtnow = this.roomFeedbackList;
            }
            let dtnows = dtnow.concat(data);
            this.setData({
              roomFeedbackList: dtnows,
              feedbackType: types,
            });
            if (data.length == 0) {
              this.setData({
                toLoadShows: false,
              });
            }
          }
        }
      });
    },

    likesCount(e) {
      //点赞
      let postId = e.currentTarget.dataset.postid;

      let data = {
        postId: postId,
      };
      this.$axios.get(this.$api.feedPostLikes, data).then((res) => {
        if (res.data.code == 200) {
          this.feedPostUserPage(this.userId);
        }
      });
    },

    favoriteCount(e) {
      //收藏
      let postId = e.currentTarget.dataset.postid;

      let data = {
        postId: postId,
      };
      this.$axios.get(this.$api.feedPostFavorite, data).then((res) => {
        if (res.data.code == 200) {
          this.feedPostUserPage(this.userId);
        }
      });
    },

    previewImage: function (e) {
      console.log(e);
      var current = e.currentTarget.dataset.src;
      var url = e.currentTarget.dataset.url;
      uni.previewImage({
        current: current,
        // 当前显示图片的http链接
        urls: url, // 需要预览的图片http链接列表
      });
    },

    preview(event) {
      let src = event.currentTarget.dataset.src;
      let maparr = [];
      maparr.push({
        type: "video",
        url: src,
      });
      let index = event.currentTarget.dataset.index;
      // 既有视频又有图片用这个
      console.log(maparr);
      uni.previewMedia({
        sources: maparr,
        // 官方文档 : 素材展示列表 是个数组对象 一个url链接 一个type 判断这是视频还是图片
        // 接收image 和 video 所以处理数据时 最好给个type 指定 image 或者 video
        current: index,
        // 当前显示的资源序号
      });
    },

    next() {
      uni.navigateBack({
        delta: 1,
      });
    },

    momDetails(item) {
      let postId = item.postId;
      let userId = item.userId;
      uni.navigateTo({
        url:
          "/pageA/pageB/community/staffdetail?postId=" +
          postId +
          "&userId=" +
          userId,
      });
    },

    // getCustomerServiceNode(userId){
    //   let this=this
    //    let data={
    //     userId:userId,
    //    }
    //    this.$axios.get(api.getCustomerServiceNode,data).then(res => {
    //        if(res.data.code==200){
    //          console.log(res.data.data);
    //          let listTab=[
    //            {
    //             nodeName:'全部',
    //             taskNodeId:''
    //            }
    //          ]
    //          res.data.data.forEach(item=>{
    //           listTab.push({
    //             taskNodeId:item.taskNodeId,
    //             nodeName:item.nodeName
    //           })
    //          })
    //          this.setData({
    //           listTab:listTab
    //          })
    //          console.log(listTab);
    //        }else{
    //          wx.showToast({
    //            title:  res.data.msg,
    //            icon: 'none',
    //            duration: 2000//持续的时间
    //          })
    //        }
    //    })
    // },
    tab(e) {
      let index = e.currentTarget.dataset.index;
      let taskNodeId = e.currentTarget.dataset.tasknodeid;
      this.setData({
        tabindex: index,
        taskNodeId: taskNodeId,
        pageNum: 1,
      });
      this.feedPostUserPage(this.userId);
    },

    packUp(e) {
      //收起全文
      let index = e.currentTarget.dataset.index;
      this.setData({
        [`userPageList[${index}].packUpShow`]:
          !this.userPageList[index].packUpShow,
        [`userPageList[${index}].moreBtns`]: !this.userPageList[index].moreBtns,
      });
    },

    arriveUserListFun() {
      //查询已到店入住客户列表

      this.$axios.get(this.$api.arriveUserList).then((res) => {
        if (res.data.code == 200) {
          this.setData({
            arriveUserList: res.data.data,
            userId: res.data.data.length > 0 ? res.data.data[0].userId : "",
          });
          this.roomFeedbackListFun(
            res.data.data.length > 0 ? res.data.data[0].userId : null
          );
          this.feedPostUserPage(
            res.data.data.length > 0 ? res.data.data[0].userId : null
          );
          this.getCustomerServiceStep(
            res.data.data.length > 0 ? res.data.data[0].userId : null
          );
        }
      });
    },

    user(e) {
      let userId = e.currentTarget.dataset.userid;
      let index = e.currentTarget.dataset.index;
      // this.getCustomerServiceNode(userId)
      // this.feedPostUserPage(userId)
      this;
      this.setData({
        userIndex: index,
        userId: userId,
        dayIndex: 0,
        tabindex: -1,
        taskNodeId: "",
        pageNum: 1,
        pageNums: 1,
      });
      this.feedPostUserPage(userId);
      this.getCustomerServiceStep(userId);
      this.roomFeedbackListFun(userId);
    },

    getCustomerServiceStep(userId) {
      //获取用户服务过程天数以及对应的节点

      let data = {
        userId: userId,
        group: "week",
      };
      this.$axios.get(this.$api.getCustomerServiceStep, data).then((res) => {
        if (res.data.code == 200) {
          let data = res.data.data;
          this.setData({
            dayList: data,
            endDate: data.length > 0 ? data[0].endDate : "",
            startDate: data.length > 0 ? data[0].startDate : "",
          });
          this.feedPostUserPage(this.userId);
        }
      });
    },

    swichNav(e) {
      let current = e.currentTarget.dataset.current;
      let endDate = e.currentTarget.dataset.enddate;
      let startDate = e.currentTarget.dataset.startdate;
      let taskName = e.currentTarget.dataset.taskname;
      this.setData({
        dayIndex: current,
        tabindex: -1,
        taskNodeId: "",
        endDate: endDate,
        startDate: startDate,
        pageNum: 1,
      });
      this.feedPostUserPage(this.userId);
    },

    all() {
      this.setData({
        tabindex: -1,
        taskNodeId: "",
        pageNum: 1,
      });
      this.feedPostUserPage(this.userId);
    },
  },
};
</script>
<style scoped>
@import "./momcommunity.css";
</style>
