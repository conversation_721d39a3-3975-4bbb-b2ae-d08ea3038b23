<template>
  <view class="request-loading-view" v-if="user_loading">
    <view class="loading-view">
      <u-loading mode="circle" size="32" color="red"></u-loading>
    </view>
  </view>
</template>

<script>
// 接口请求loading
</script>

<style scoped>
.request-loading-view {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 999999;
  display: flex;
  justify-content: center;
  align-items: center;
}

.loading-view {
  width: 60rpx;
  height: 60rpx;
  /* background-color: rgba(0, 0, 0, 0.2); */
  border-radius: 8rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
