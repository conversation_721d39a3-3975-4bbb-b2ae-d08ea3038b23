<template>
  <view>
    <view class="nexts">
      <view class="headImg" @tap="next">
        <image
          src="http://cdn.xiaodingdang1.com/2024/05/16/44a1d120512d48e2afe102d90517d71fpng"
          mode=""
          style="width: 18rpx; height: 34rpx"
        />
      </view>
      <view class="headTitle"></view>
    </view>
    <!-- 轮播图 S -->
    <swiper
      class="detail-banner"
      :indicator-dots="indicatorDots"
      :autoplay="autoplay"
      :interval="interval"
      :duration="duration"
      :circular="circular"
      :indicator-color="beforeColor"
      :indicator-active-color="afterColor"
      style="background: white"
    >
      <swiper-item v-for="(item, index) in banner_list" :key="index">
        <video
          class="detail-banner-img"
          v-if="item.type == 'video'"
          :src="item.url"
          @tap="preview"
          :data-index="index"
        ></video>

        <image
          class="detail-banner-img"
          v-else
          :src="item.url"
          @tap="preview"
          :data-index="index"
        ></image>
      </swiper-item>
    </swiper>
    <!-- 轮播图 E -->
    <view class="main">
      <view class="main1">
        <view class="titleName">
          {{ infoList.productName }}
        </view>
        <view class="content">
          <view
            class="content1"
            v-for="(item, index) in infoList.tag"
            :key="index"
          >
            <view>
              {{ item }}
            </view>

            <view class="fgxTag" v-if="index != tagList.length - 1"></view>
          </view>
        </view>
        <view class="price">
          <view class="price1"
            >￥{{
              infoList.productPrice ? infoList.productPrice : '----'
            }}</view
          >
          <!-- <view class="price2">
				咨询报价
			</view> -->
          <button
            @click="handleContact"
            style="
              width: 200rpx;
              padding: 3rpx 14rpx;
              border-radius: 100rpx;
              background: #ff4f61;
              color: #ffffff;
              font-size: 28rpx;
              font-weight: bold;
            "
          >
            咨询
          </button>
        </view>
        <view class="fgx"></view>
        <!-- <view class="head" id="head">
			<view class="head1">
				<view class="head1_1">
				</view>
				<view class="head1_2">
					购买详情
				</view>
			</view>
			<view class="head2">
			</view>
		</view> -->
        <!-- <view class="serve">
			<view class="serve1">
				<view class="serveTitle">
					购买分类
				</view>
				<view class="serveNaame">
					{{infoList.groupType}}
				</view>
			</view>
			<view class="serve1">
				<view class="serveTitle">
					使用次数
				</view>
				<view class="serveNaame">
					{{infoList.useCount}}次
				</view>
			</view>
			<view class="serve1">
				<view class="serveTitle">
					服务方式
				</view>
				<view class="serveNaame">
					{{infoList.serviceMode}}
				</view>
			</view>
		</view> -->
        <!-- <view class="distribution">
			<image src="http://cdn.xiaodingdang1.com/2024/04/24/08a3401a64c34442b2cfaad496d508e8png" mode="" style="width: 26rpx;height: 26rpx;" />
			<view class="distribution1">
				小Tips：五公里内免费配送哦！
			</view>
		</view> -->
      </view>

      <view
        class="signatory"
        @tap="claim"
        :data-contractgiftid="infoList?.contractGift?.contractGiftId"
        :data-isexist="infoList.isExistContractGift"
      >
        <image
          src="http://cdn.xiaodingdang1.com/2024/07/05/3971907104854f0089fad0bc3603e50c.png"
          mode=""
          style="width: 702rpx; height: 178rpx"
        />
      </view>
      <!-- <view class="main2">
		<view class="signingGift">
			<view class="signingGift1">
				<image src="http://cdn.xiaodingdang1.com/2024/05/30/78b4e690b3fe43d3aededb607e4d8364png" mode="" style="width: 120rpx;height: 35rpx;"/>
			</view>
			<view class="signingGift2" wx:if="{{!infoList.contractGift.description&&!infoList.isExistContractGift}}">
				<view>
					<image src="http://cdn.xiaodingdang1.com/2024/05/30/8c0c27c7bbdc45e791aab27f90661ad6png" mode="" style="width: 160rpx;height: 35rpx;"/>
				</view>
				<view class="signingGift2_2_1">
					立即领取
				</view>
			</view>
			<view class="signingGift2" wx:if="{{infoList.contractGift.description}}">
				<view class="signingGift2_1">
					<view class="prices">
						<view>
							价值：
						</view>
						<view class="prices1">
							￥{{infoList.contractGift.price}}
						</view>
					</view>
					<view class="rule">
					</view>
					<view class="description">
						{{infoList.contractGift.description}}
					</view>
				</view>
				<view class="signingGift2_2" bindtap="claim" data-contractgiftid="{{infoList.contractGift.contractGiftId}}" wx:if="{{!infoList.isExistContractGift}}">
					立即领取
				</view>
				<view class="signingGift2_2_1" wx:if="{{infoList.isExistContractGift}}">
					已领取
				</view>
			</view>
		</view>
	</view> -->
      <!-- <view class="main2"> 
	  <view class="head">
			 <view class="head1">
				 <view class="head1_1">
				 </view>
				 <view class="head1_2">
					膳食评论
				 </view>
			 </view>
			 <view class="head2">
				 <view class="head2_1">
				  更多
				 </view>
				 <image src="http://cdn.xiaodingdang1.com/1/120240115/2ee8ae21-7a8f-45d9-a490-fc0879a0d56b.png" mode="" style="width: 8rpx;height: 18rpx;"/>
			 </view>
		</view>
		<view class="roomComment">
			<view class="roomComment1">
				<view class="roomComment1_1">
					<image src="../../images/icon/Frame <EMAIL>" mode="" style="width: 78rpx;height: 78rpx;"/>
					<view class="roomComment1_2">
						<view class="roomComment1_3">
							师**和
						</view>
						<image src="http://cdn.xiaodingdang1.com/2024/09/04/b376457b2da04b42baa5e9f98baae515.png" mode="" style="width: 28rpx; height: 28rpx;" wx:for="{{[1,2,3,4,5]}}"/>
					</view>
				</view>
				<view class="roomComment1_4">
					2024-10-05
				</view>
			</view>
			<view class="roomComment2">
				<view class="roomComment2_1">
					<view class="roomComment2_2">
						房间很好
					</view>
					<view class="roomComment2_2">
						设施很棒
					</view>
					<view class="roomComment2_2">
						服务很好
					</view>
				</view>
				<view class="roomComment2_3">
					房间内的装饰和家具都是经过精心挑选的，既美观又实用，营造出一种温馨而宁静的氛围，非常适合产后休养。
				</view>
			</view>
		</view>
		<view class="fgx">
		</view>
	 </view> -->
      <view class="main2">
        <view class="head">
          <view class="head1">
            <view class="head1_1"></view>
            <view class="head1_2">大家都在问</view>
          </view>
          <view class="head2"></view>
        </view>
        <view class="roomComments" v-if="questionList.length == 0"
          >暂无数据</view
        >
        <view
          class="next"
          v-if="questionList.length > 0"
          v-for="(item, index) in questionList"
          :key="index"
        >
          <button
            @click="handleContact"
            style="
              width: 100%;
              background: #f4f6ff;
              padding: 0 0;
              text-align: right;
            "
          >
            <view class="next3">
              <view class="next1">
                <view style="width: 40rpx; height: 40rpx">
                  <image
                    src="http://cdn.xiaodingdang1.com/2024/05/31/d81edcb9874047b183f6fa193260019cpng"
                    mode=""
                    style="width: 100%; height: 100%"
                  />
                </view>
                <view class="next1_1">
                  {{ item.questionText }}
                </view>
              </view>
              <view class="next2">
                <image
                  src="http://cdn.xiaodingdang1.com/1/120240115/2ee8ae21-7a8f-45d9-a490-fc0879a0d56b.png"
                  mode=""
                  style="width: 12rpx; height: 18rpx"
                />
              </view>
            </view>
          </button>
        </view>
      </view>
      <view class="main2">
        <view class="image-text">
          <view class="leftFgx"></view>
          <view class="charactersTitle">图文详情</view>
          <view class="rightFgx"></view>
        </view>
        <view>
          <image
            :src="item"
            mode=""
            style="width: 100%; margin-top: 20rpx"
            v-for="(item, index) in infoList.productDetails"
            :key="index"
          ></image>
        </view>
      </view>
      <view class="main2">
        <view class="head">
          <view class="head1">
            <view class="head1_1"></view>
            <view class="head1_2">为您推荐</view>
          </view>
          <view class="head2"></view>
        </view>
        <view class="recommend">
          <view
            class="recommend1"
            @tap="nextDetails"
            :data-projectid="item.projectId"
            v-for="(item, index) in recoveryList"
            :key="index"
          >
            <view style="width: 340rpx; height: 340rpx">
              <image
                :src="item.displayPhotos"
                mode=""
                style="
                  width: 100%;
                  height: 100%;
                  border-top-left-radius: 10rpx;
                  border-top-right-radius: 10rpx;
                "
              />
            </view>

            <view class="recommend1_1">
              <view class="recommend2">{{ item.projectName }}</view>
              <view class="tag2_1">
                <view class="tag2" v-for="(res, i) in item.tag" :key="i">
                  {{ res }}
                </view>
              </view>
            </view>

            <view class="tagOne1">
              <image
                src="http://cdn.xiaodingdang1.com/2024/09/04/9468d471ef2c4cbfb79a62336ff0300f.png"
                mode=""
                style="width: 26rpx; height: 26rpx"
              />
              <view class="tagName">最热产康</view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="blank"></view>
    <new-bottom :from="SessionFrom"></new-bottom>
  </view>
</template>

<script>
export default {
  data() {
    return {
      deliveryId: '',
      signingShow: false,
      recoveryList: [],
      imgList: [],
      questionList: [],

      infoList: {
        productName: '',
        tag: [],
        productPrice: false,

        contractGift: {
          contractGiftId: '',
        },

        isExistContractGift: '',
        productDetails: [],
      },

      tagList: ['补中益气', '补肾益精'],

      // banner 轮播图
      banner_list: [
        // { type: 1, video: "http://cdn.xiaodingdang1.com/2024/04/24/e8c8ae4071884810b8ac0ef33fcfe86fmp4", img: "../../images/icon/<EMAIL>" },
        // { type: 0, url: "../../images/icon/Rectangle <EMAIL>" },
        // { type: 0, url: "../../images/icon/Rectangle <EMAIL>" },
        // { type: 0, url: "../../images/icon/Rectangle <EMAIL>" },
        // { type: 0, url: "../../images/icon/Rectangle <EMAIL>" }
      ],

      indicatorDots: true,
      autoplay: false,

      // 自动播放
      interval: 5000,

      //轮播时间
      duration: 300,

      // 滑动速度越大越慢
      circular: false,

      //是否循环
      beforeColor: 'lightgray',

      //指示点颜色
      afterColor: 'red',

      //当前选中的指示点颜色
      // 轮播数据 + 效果 E
      controls: false,

      SessionFrom: '',
      i: '',
      res: [],
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.suiteInfo(options.deliveryId);
    this.setData({
      deliveryId: options.deliveryId,
      SessionFrom:
        uni.getStorageSync('tenantId') +
        ',' +
        uni.getStorageSync('userInfo').nickname +
        ',' +
        '月子膳食',
    });
    this.questionListFun();
    this.recoveryRecommendation();
    var newDateTime = Date.parse(new Date());
    let data = {
      eventType: 'PAGE_VIEW',
      pageUrl: '/pageA/pageB/home/<USER>/detail',
      module: 'meal',
      eventTime: newDateTime,
      pageTitle: '膳食详情',
    };

    let iftoken = uni.getStorageSync('token');
    if (iftoken) {
      this.$point.basePoint(data);
    }
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},
  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {},
  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},
  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    var newDateTime = Date.parse(new Date());
    let data = {
      eventId: uni.getStorageSync('eventId'),
      leaveTime: newDateTime,
    };
    this.$point.reportEnd(data);
  },
  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},
  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {},

  onShareTimeline() {},
  methods: {
    //预览图片
    previewImage: function (e) {
      console.log(e.target.dataset.src);
      var current = e.target.dataset.src;
      uni.previewImage({
        current: current,
        // 当前显示图片的http链接
        urls: this.imgList,
        // urls: this.data.imgUrls // 需要预览的图片http链接列表
      });
    },

    // 播放
    videoPlay: function () {
      console.log('开始播放');
      var videoplay = uni.createVideoContext('video');
      videoplay.play();
      this.setData({
        controls: true,
      });
    },

    next() {
      uni.navigateBack({
        delta: 1,
      });
    },

    suiteInfo(deliveryId) {
      //查询优惠套餐列表
      let data = {
        deliveryId: deliveryId,
      };
      this.$axios.get(this.$api.productDelivery, data).then((res) => {
        if (res.data.code == 200) {
          let data = res.data.data;
          let list = [];
          data.videos.forEach((item) => {
            let data = {
              type: 'video',
              url: item,
            };
            list.push(data);
          });
          data.productPhotoUrl.forEach((item) => {
            let data = {
              type: 'image',
              url: item,
            };
            list.push(data);
          });
          console.log(data);
          this.setData({
            infoList: data,
            banner_list: list,
            imgList: data.productPhotoUrl,
          });
        }
      });
    },

    // reviewList(){//查询商家总体评价列表
    //   let this=this
    //   let data={
    //     pageSize:2,
    //     pageNum:1,
    //     reviewType:'room'
    //   }
    //   this.$axios.get(this.$api.reviewPage,data).then(res => {
    //       if(res.data.code==200){
    //         this.setData({
    //           reviewList:res.data.rows,
    //         })
    //       }else{
    //         wx.showToast({
    //           title:  res.data.msg,
    //           icon: 'none',
    //           duration: 2000//持续的时间
    //         })
    //       }
    //   })
    // },
    recoveryRecommendation() {
      //查询产后康复热门推荐列表
      let data = {
        limit: 2,
      };
      this.$axios.get(this.$api.recoveryRecommendation, data).then((res) => {
        if (res.data.code == 200) {
          this.setData({
            recoveryList: res.data.data,
          });
        }
      });
    },

    questionListFun() {
      //查询问题列表
      let data = {
        pageSize: 1000,
        pageNum: 1,
        category: 'meal',
      };
      this.$axios.get(this.$api.questionList, data).then((res) => {
        if (res.data.code == 200) {
          this.setData({
            questionList: res.data.data,
          });
        }
      });
    },

    nextDetails(e) {
      let projectId = e.currentTarget.dataset.projectid;
      uni.navigateTo({
        url: '/pageA/pageB/home/<USER>/detail?projectId=' + projectId,
      });
    },

    claim(e) {
      let contractGiftId = e.currentTarget.dataset.contractgiftid;
      let isExistContractGift = e.currentTarget.dataset.isexist;
      uni.setStorageSync('isExistContractGift', isExistContractGift);
      if (!isExistContractGift) {
        this.signing(contractGiftId);
      } else {
        this.setData({
          signingShow: true,
        });
      }
    },

    signing(contractGiftId) {
      let data = {
        contractGiftId: contractGiftId,
      };
      this.$axios.get(this.$api.signing, data).then((res) => {
        if (res.data.code == 200) {
          this.setData({
            signingShow: true,
          });
          this.suiteInfo(this.deliveryId);
        }
      });
    },

    preview(event) {
      console.log(event);
      let maparr = this.banner_list;
      let index = event.currentTarget.dataset.index;
      // 既有视频又有图片用这个
      console.log(maparr);
      uni.previewMedia({
        sources: maparr,
        // 官方文档 : 素材展示列表 是个数组对象 一个url链接 一个type 判断这是视频还是图片
        // 接收image 和 video 所以处理数据时 最好给个type 指定 image 或者 video
        current: index,
        // 当前显示的资源序号
      });
    },

    onChildEvent(event) {
      this.setData({
        signingShow: event.detail.signingShow,
      });
    },

    handleContact() {
      uni.navigateTo({
        url: '/subPackages/customerService/pages/userChat',
      });
    },
  },
};
</script>
<style scoped>
@import './detail.css';
</style>
