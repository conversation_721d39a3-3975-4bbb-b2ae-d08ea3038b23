<template>
	<view class="main">
		<view class="content">
				<view class="content1">
				<view class="content1_1">
					接收客户
				</view>
				<!-- <view class="content1_2" @click="show1 = true">
					<view class="content1_3">
						{{receivingTypes?selector[receivingTypes]:'全部'}}
					</view>
					<view class="content1_4">
						<image src="http://cdn.xiaodingdang1.com/2025/04/24/9147277a60f841439048695c24029c3c.png"
							mode="" style="width: 28rpx;height: 28rpx;"></image>
					</view>
				</view> -->
				<view class="content1_2" @click="selectClient()">
				<!-- 	<view class="content1_3">
						{{receivingTypes?selector[receivingTypes]:'全部'}}
					</view>
					<view class="content1_4">
						<image src="http://cdn.xiaodingdang1.com/2025/04/24/9147277a60f841439048695c24029c3c.png"
							mode="" style="width: 28rpx;height: 28rpx;"></image>
					</view> -->
					请选择客户
				</view>
			</view>
			<view class="content1">
				<view class="content1_1">
					发送时间
				</view>
				<view class="content1_2" @click="show = true">
					<view class="content1_3">
						{{sendTime?sendTime:'请选择'}}
					</view>
					<view class="content1_4">
						<image src="http://cdn.xiaodingdang1.com/2025/04/24/02a1c7a6d89749ea9ea196ad93d8e693.png"
							mode="" style="width: 32rpx;height: 32rpx;"></image>
					</view>
				</view>
			</view>
			<view class="content1">
				<view class="content1_1">
					提醒内容
				</view>
				<view class="content1_2">
				</view>
			</view>
			<view class="textarea">
				<textarea name="" id="" cols="30" rows="10" v-model="from.description"
					placeholder="我们发布了一条新笔记，欢迎查看。"></textarea>
			</view>
		</view>
		<view class="addBtn" @click="publish">
			提交
		</view>
		<u-picker mode="time" v-model="show" :params="params" @confirm="handleConfirm"></u-picker>
		<u-picker mode="selector" v-model="show1" :default-selector="[0]" :range="selector" @confirm="handleConfirm1"></u-picker>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				userList:[],
				params: {
					year: true,
					month: true,
					day: true,
					hour: true,
					minute: true,
					second: true,
					// 选择时间的时间戳
					timestamp: true,
				},
				show: false,
				show1: false,
				selector: ['全部', '客资宝妈'],
				sendTime: '',
				from: {
					description: '',
					date: '',
					time: '',
					momId: '',
					diaryId: ''
				},
				receivingType:'',
				postId:'',
				receivingTypes:''
			}
		},
		/**
		 * 生命周期函数--监听页面加载
		 */
		onLoad(options) {
			this.from.momId=options.momId
			this.from.diaryId=options.diaryId
			this.receivingType=options.receivingType
			this.postId=options.postId
			},
			// 在上一页
			onShow() {
			    let globalData = getApp().globalData;
			    this.userList = globalData.useridList?globalData.useridList:[];
			    globalData.useridList = null;
			},
		methods: {
			selectClient(){
				uni.navigateTo({
					url:'/pageA/pageB/community/pushMessage/pushMessageDetails'
				})
			},
			tagIndex(e){
				console.log(e);
			},
			handleConfirm1(e){
				this.receivingTypes=e[0]
				console.log(this.receivingTypes);
				if(e[0]==1){
					uni.navigateTo({
						url:'/pageA/pageB/community/pushMessage/pushMessageDetails'
					})
				}
			},
			handleConfirm(e) {
				console.log(e);
				let date = e.month + '-' + e.day
				let time = e.hour + ':' + e.minute
				let sendTime = e.year + '-' + e.month + '-' + e.day + ' ' + e.hour + ':' + e.minute
				this.sendTime = sendTime
				this.from.date = date
				this.from.time = time
			},
			async publish() {
				if (this.from.date == ''&&this.from.time=='') {
					uni.showToast({
						title: '请选择发送时间',
						icon: 'none',
						duration: 3000 //持续的时间
					});
					return;
				}
				console.log(this.userList);
				let from={
					description:this.from.description,
					date:this.from.date,
					time:this.from.time,
					receivingType:this.receivingType
				}
				if(this.userList.length>0){ 
					from.userList=this.userList
				}
				if(this.receivingType==3){
					from.momId=this.from.momId
					from.diaryId=this.from.diaryId
				}else{
					from.postId=this.postId
				}
				this.changeLoading(true)
				const res = await this.$axios.post(this.receivingType==3?this.$api.diaryCreateReminder:this.$api.feedCreateReminder, from)
				if (res?.data?.code == 200) {
					uni.showToast({
						title: '消息推送成功',
						icon: 'none',
						duration: 2000 //持续的时间
					});
					 setTimeout(() => {
						uni.navigateBack({
							delta: 1
						});
					},2000)
				
				} else {
					uni.showToast({
						title: res.data.msg,
						icon: 'none',
						duration: 3000 //持续的时间
					});
				}
			},
		}
	}
</script>

<style lang="less" scoped>
	@import './pushMessage.less';
</style>