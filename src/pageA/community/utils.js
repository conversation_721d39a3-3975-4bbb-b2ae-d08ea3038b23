import { SERVICE_TYPES, LOAD_STATUS } from './constants.js'

/**
 * 根据服务类型获取对应的列表引用
 * @param {string} serviceType - 服务类型
 * @param {Object} refs - 包含各种列表引用的对象
 * @returns {Ref} 对应的列表引用
 */
export function getServiceListByType(serviceType, refs) {
  const {
    postpartumRecoveryList,
    confinementMealList,
    nursingServiceList,
    otherServiceList
  } = refs

  switch (serviceType) {
    case SERVICE_TYPES.POSTPARTUM_RECOVERY:
      return postpartumRecoveryList
    case SERVICE_TYPES.CONFINEMENT_MEAL:
      return confinementMealList
    case SERVICE_TYPES.NURSING_SERVICE:
      return nursingServiceList
    case SERVICE_TYPES.OTHER_SERVICE:
      return otherServiceList
    default:
      return null
  }
}

/**
 * 根据服务标题获取对应的列表引用
 * @param {string} serviceTitle - 服务标题
 * @param {Object} refs - 包含各种列表引用的对象
 * @returns {Ref} 对应的列表引用
 */
export function getServiceListByTitle(serviceTitle, refs) {
  const {
    postpartumRecoveryList,
    confinementMealList,
    nursingServiceList,
    otherServiceList
  } = refs

  switch (serviceTitle) {
    case '产后康复':
      return postpartumRecoveryList
    case '月子膳食':
      return confinementMealList
    case '护理服务':
      return nursingServiceList
    case '其他服务':
      return otherServiceList
    default:
      return null
  }
}

/**
 * 合并所有服务列表
 * @param {Object} refs - 包含各种列表引用的对象
 * @returns {Array} 合并后的列表
 */
export function mergeAllServiceLists(refs) {
  const {
    postpartumRecoveryList,
    confinementMealList,
    nursingServiceList,
    otherServiceList
  } = refs

  return [
    ...postpartumRecoveryList.value,
    ...confinementMealList.value,
    ...nursingServiceList.value,
    ...otherServiceList.value,
  ]
}

/**
 * 处理员工信息的就业年限显示
 * @param {Object} item - 包含员工信息的项目
 */
export function processStaffEmploymentInfo(item) {
  if (item.staffInfo) {
    item.staffInfo.yearsEmployment = item.staffInfo.yearsEmployment
      ? `服务年限：${item.staffInfo.yearsEmployment}`
      : '从业年限：10年'
  }
}

/**
 * 格式化URL参数
 * @param {string} baseUrl - 基础URL
 * @param {Object} params - 参数对象
 * @returns {string} 格式化后的URL
 */
export function formatUrlWithParams(baseUrl, params) {
  const queryString = Object.entries(params)
    .map(([key, value]) => `${key}=${value}`)
    .join('&')
  
  return `${baseUrl}?${queryString}`
}

/**
 * 检查是否有更多数据
 * @param {number} dataLength - 当前数据长度
 * @param {number} pageSize - 页面大小
 * @returns {number} 加载状态
 */
export function checkHasMoreData(dataLength, pageSize) {
  return dataLength === pageSize ? LOAD_STATUS.HAS_MORE : LOAD_STATUS.NO_MORE
}
