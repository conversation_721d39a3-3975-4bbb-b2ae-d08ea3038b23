<template>
    <view class="main">
        <view class="mains" @tap="consult" :data-nurseid="item.nurseId" v-for="(item, index) in mobileNannyPage"
            :key="index">
            <view style="width: 702rpx; height: 702rpx;border-top-left-radius: 20rpx; border-top-right-radius: 20rpx">
                <!-- <image :src="item.nursePhotoUrl[0]" mode=""
                    style="width: 100%; height: 100%; border-top-left-radius: 20rpx; border-top-right-radius: 20rpx" /> -->
					<u-lazy-load :duration="0" :image="item.nursePhotoUrl[0]" effect="fade" loading-img="" error-img=""></u-lazy-load>
            </view>

            <view class="coment">
                <view class="title">{{ ittem.packageName }}</view>
                <text class="title1">简介：</text>
                <text class="title2">{{ item.nurseProfile }}</text>
                <view class="coment1">
                    <image src="http://cdn.xiaodingdang1.com/2024/05/15/75cc5000749f41ada2f5baa6978339e4png" mode=""
                        style="width: 200rpx; height: 40rpx" />
                    <view class="btn">咨询</view>
                </view>
            </view>
        </view>
    </view>
	<rightMenuBarVue :scrollTop="scrollTop"></rightMenuBarVue>
	<mask-dialog></mask-dialog>
</template>

<script>
	import rightMenuBarVue from "@/components/rightMenuBar.vue";
    export default {
		components: {
			rightMenuBarVue
		},
        data() {
            return {
				scrollTop:0,
                mobileNannyPage: [],

                ittem: {
                    packageName: ''
                }
            };
        }
        /**
         * 生命周期函数--监听页面加载
         */
        ,
		onPageScroll(e) {
				this.scrollTop = e.scrollTop;
			},
        onLoad(options) {},
        /**
         * 生命周期函数--监听页面初次渲染完成
         */
        onReady() {},
        /**
         * 生命周期函数--监听页面显示
         */
        onShow() {
            this.mobileNannyPageFun();
        },
        /**
         * 生命周期函数--监听页面隐藏
         */
        onHide() {},
        /**
         * 生命周期函数--监听页面卸载
         */
        onUnload() {},
        /**
         * 页面相关事件处理函数--监听用户下拉动作
         */
        onPullDownRefresh() {},
        /**
         * 页面上拉触底事件的处理函数
         */
        onReachBottom() {},
        /**
         * 用户点击右上角分享
         */
        onShareAppMessage() {},

        onShareTimeline() {},
        methods: {
            consult(e) {
                let nurseId = e.currentTarget.dataset.nurseid;
                uni.navigateTo({
                    url: 'maternitymatron/detail?nurseId=' + nurseId
                });
            },

            mobileNannyPageFun() {
                //分页查询移动月嫂列表
                let data = {
                    pageSize: 1000,
                    pageNum: 1
                };
                this.$axios.get(this.$api.mobileNannyPage, data).then((res) => {
                    if (res.data.code == 200) {
                        this.setData({
                            mobileNannyPage: res.data.rows
                        });
                    }
                });
            }
        }
    };
</script>
<style scoped>
    @import './maternitymatron.css';
</style>