<template>
    <view>
        <view class="bg"></view>
    <navigation
      title="详情"
      :background="bgColor"
      :isSowArrow="true"
    ></navigation>
        <view class="main">
            <view class="top">
        <view
          class="top-img"
          v-if="
            staffInfoList.staffPhotos && staffInfoList.staffPhotos.length > 0
          "
        >
                    <image :src="staffInfoList.staffPhotos[0]"></image>
                </view>
                <view class="top-header">
                    <view class="top-header-name">{{ staffInfoList.staffName }}</view>
          <view class="top-header-log">
            {{ staffInfoList.staffPost ? staffInfoList.staffPost : '护士长' }}
          </view>
                </view>
        <view class="top-subname"
          >{{ clubName }}{{ staffInfoList.staffPost }}</view
        >
                <scroll-view :scroll-x="true" class="top-tag">
          <view
            class="top-tag-num"
            v-for="(res, i) in staffInfoList.tag"
            :key="i"
            >{{ res }}</view
          >
                </scroll-view>
                <view class="top-state">
                    <view class="top-state-label">从业年限:</view>
          <view class="top-state-value">{{
            staffInfoList.yearsEmployment
          }}</view>
                    <view class="top-state-label">服务人员数量:</view>
                    <view class="top-state-value">{{ staffInfoList.serviceNum }}</view>
                </view>
        <view class="top-desc" v-html="staffInfoList.staffDesc"></view>
            </view>
            <view class="content">
        <view
          class="card"
          v-for="(item, index) in userPageList"
          :key="index"
          :id="`post-${index}`"
        >
                    <view class="card-img">
            <image
              src="http://cdn.xiaodingdang1.com/2024/11/22/a8c6233952c1465caa22245d2c1ac9be.png"
            >
                        </image>
                    </view>
                    <view class="card-header">
                        <view class="card-header-img">
              <image :src="item.avatar || defaultAvatar" mode="" />
                        </view>
                        <view class="card-header-right">
                            <view class="flex">
                                <view class="card-header-right-name">
                  {{ item.nickname ? item.nickname : staffInfoList.staffName }}
                                </view>
                                <view class="card-header-right-log" v-if="item.staffPost">
                                    <view class="card-header-right-log-img">
                                        <image
                      src="http://cdn.xiaodingdang1.com/2024/11/22/4b7f9589a4b24bb9b91c533d0ea7af17.png"
                    >
                                        </image>
                                    </view>
                                    <view class="card-header-right-log-sign">
                                        {{ item.staffPost }}
                                    </view>
                                </view>
                            </view>
                            <view>
                                <view class="card-header-right-date">
                                    {{ item.createTimes }}
                                </view>
                            </view>
                        </view>
            <scroll-view
              :scroll-x="true"
              class="card-tag"
              v-if="item.staffInfo && item.staffInfo.staffTags"
            >
              <view
                class="card-tag-enum"
                v-for="(res, i) in item.staffInfo.staffTags"
                :key="i"
              >
                {{ res }}
                            </view>
                        </scroll-view>
                    </view>
                    <view class="card-content">
            <img-content-area-vue
              :listData="item"
              :index="index"
              @preloadNext="handlePreloadNext"
            ></img-content-area-vue>
                        <view class="discuss4"></view>
                        <view class="discusss5">
              <functionbutton-vue
                :item="item"
                @collect="collect"
                @like="like"
                @share="shareCallback"
              ></functionbutton-vue>
                        </view>
                    </view>
                </view>
            </view>
        </view>
        <new-request-loading></new-request-loading>
    <mask-dialog></mask-dialog>
    <rightMenuBarVue :scrollTop="scrollTop"></rightMenuBarVue>
    </view>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, getCurrentInstance } from 'vue';
import {
  onLoad,
  onReachBottom,
  onPageScroll,
  onShareAppMessage,
  onShareTimeline,
} from '@dcloudio/uni-app';
import imgContentAreaVue from '@/components/imgContentArea.vue';
    import functionbuttonVue from '@/components/functionbutton.vue';
import rightMenuBarVue from '@/components/rightMenuBar.vue';
import utils from '@/utils/util.js';

// 获取实例
const { proxy } = getCurrentInstance();
const defaultAvatar = proxy.$defaultAvatar;

// 响应式数据
const staffInfoList = ref({});
const userId = ref('');
const pageSize = ref(10);
const pageNum = ref(1);
const userPageList = ref([]);
const clubName = ref(uni.getStorageSync('clubName'));
const hasMoreData = ref(true);
const bgColor = ref('');
const observer = ref(null);
const scrollTop = ref(0);
// 获取护理团队信息
const staffInfo = async (staffId) => {
  try {
    const res = await proxy.$axios.get(proxy.$api.staffInfo, {
      staffId: staffId,
    });

    if (res.data.code == 200) {
      userId.value = res.data.data.userId;
      const template = `<span style="font-weight: 500; color: #FF4F61;margin-right: 12rpx">人员简介：</span>`;
      res.data.data.staffDesc = template + res.data.data.staffDesc;
      staffInfoList.value = res.data.data;
      feedPostUserPage(); // 查询指定用户朋友圈列表
    }
  } catch (error) {
    console.error('获取护理团队信息失败', error);
  }
};

// 获取用户朋友圈列表
const feedPostUserPage = async () => {
  changeLoading(true);

  try {
    const data = {
      pageSize: pageSize.value,
      pageNum: pageNum.value,
    };

    if (userId.value) {
      data.userId = userId.value;
    }

    const res = await proxy.$axios.get(proxy.$api.feedPostUserPage, data);

                if (res.data.code == 200) {
      const newData = processPostData(res.data.rows);

      // 更新列表数据和分页状态
      if (pageNum.value === 1) {
        userPageList.value = newData;
      } else {
        userPageList.value = userPageList.value.concat(newData);
      }

      // 判断是否还有更多数据
      hasMoreData.value = newData.length === 10;

      // 设置可见性观察
      setupIntersectionObserver();
    }
  } catch (error) {
    console.error('获取朋友圈列表失败', error);
  } finally {
    changeLoading(false);
  }
};

// 处理帖子数据
const processPostData = (data) => {
  return data.map((item) => {
    // 处理内容显示状态
                            if (item.content.length > 120) {
                                item.packUpShow = false;
                                item.moreBtns = true;
                                item.moreBtn = true;
                            } else {
                                item.packUpShow = false;
                                item.moreBtns = false;
                                item.moreBtn = false;
                            }

    // 格式化文本和时间
    item.createTimes = formatDateTime(item.createTime);
    item.listShow = false; // 初始化可见性状态

    return item;
  });
};

// 格式化日期时间
const formatDateTime = (dateTimeStr) => {
  const timearr = dateTimeStr.replace(' ', ':').replace(/\:/g, '-').split('-');
  return `${timearr[1]}/${timearr[2]} ${timearr[3]}:${timearr[4]}`;
};

// 设置交叉观察器
const setupIntersectionObserver = () => {
  // 延迟执行以确保DOM已渲染
  setTimeout(() => {
    userPageList.value.forEach((item, index) => {
      const observer = uni.createIntersectionObserver(proxy);
      observer.relativeToViewport().observe(`#post-${index}`, (result) => {
        if (result.intersectionRatio > 0) {
          item.listShow = true;
        }
      });
    });
  }, 100);
};

// 切换加载状态
const changeLoading = (status) => {
  uni.$emit('requestLoading', status);
};

// 收藏操作
const collect = (item, index) => {
  const post = userPageList.value[index];
  post.isFavorite = !post.isFavorite;
  post.favoriteCount = post.isFavorite
    ? post.favoriteCount + 1
    : post.favoriteCount - 1;

  proxy.$axios
    .get(proxy.$api.feedPostFavorite, {
      postId: item.postid,
    })
    .catch((error) => {
      console.error('收藏操作失败', error);
      // 还原状态
      post.isFavorite = !post.isFavorite;
      post.favoriteCount = post.isFavorite
        ? post.favoriteCount + 1
        : post.favoriteCount - 1;
    });
};

// 点赞操作
const like = (item, index) => {
  const post = userPageList.value[index];
  post.isLike = !post.isLike;
  post.likesCount = post.isLike ? post.likesCount + 1 : post.likesCount - 1;

  proxy.$axios
    .get(proxy.$api.feedPostLikes, {
      postId: item.postid,
    })
    .catch((error) => {
      console.error('点赞操作失败', error);
      // 还原状态
      post.isLike = !post.isLike;
      post.likesCount = post.isLike ? post.likesCount + 1 : post.likesCount - 1;
    });
};

// 分享回调
const shareCallback = (index) => {
  userPageList.value[index].shareCount++;
};

// 处理预加载下一项
const handlePreloadNext = (nextIndex) => {
  // 检查nextIndex是否有效且在列表范围内
  if (nextIndex >= 0 && nextIndex < userPageList.value.length) {
    // 预加载下一个项目
    userPageList.value[nextIndex].listShow = true;

    // 如果接近列表末尾，考虑加载更多数据
    if (nextIndex >= userPageList.value.length - 3 && hasMoreData.value) {
      // 如果用户即将滚动到底部，提前加载下一页数据
      pageNum.value++;
      feedPostUserPage();
    }
  }
};

// 页面初始化
const pageInit = (options) => {
  if (options.staffId) {
    staffInfo(options.staffId);
  }
};

// 生命周期钩子
onLoad((options) => {
  console.log('页面加载参数', options);
  pageInit(options);
});

onReachBottom(() => {
  if (hasMoreData.value) {
    pageNum.value++;
    feedPostUserPage();
  }
});

onPageScroll((e) => {
  bgColor.value = e.scrollTop > 0 ? '#ffffff' : '';
  scrollTop.value = e.scrollTop;
});

onMounted(() => {
  observer.value = uni.createIntersectionObserver(proxy, {
    thresholds: [0],
    initialRatio: 0,
  });
});

onUnmounted(() => {
  observer.value && observer.value.disconnect();
});

// 分享函数
onShareAppMessage(() => {
  return {
    title: staffInfoList.value.staffName,
    path: `/pageA/pageB/home/<USER>/detail?staffId=${staffInfoList.value.staffId}`,
  };
});

onShareTimeline(() => {
  return {
    title: staffInfoList.value.staffName,
    query: `staffId=${staffInfoList.value.staffId}`,
  };
});
</script>

<style lang="less" scoped>
@import './detail.less';
</style>
