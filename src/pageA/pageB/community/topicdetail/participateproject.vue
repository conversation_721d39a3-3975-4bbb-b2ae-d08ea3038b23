<template>
    <view class="main">
        <view class="head">
            <view class="cancel" @tap="cancel">取消</view>
            <view class="publish" @tap="publish">发表</view>
        </view>
        <view class="topicName">#{{ topicInfo.topicName }}</view>
        <textarea :value="content" placeholder="这一刻你想说些什么？" class="textarea" @input="searchInput" />
		<view class="uploading" v-if="videosShow">
		    <view class="uploadingImg" v-for="(item, index) in videos" :key="index">
		        <video :src="item" mode="" @tap="preview"
		            style="width: 198rpx; height: 198rpx; border: 1rpx solid #c2bfbf" />
		
		        <icon class="imgCancel" type="clear" size="25" :data-index="index" @tap.stop.prevent="deleteImgs">
		        </icon>
		    </view>
		    <view class="addImg1" id="addImg1" @tap="uploadVideo" v-if="videos.length == 0">
		        <image src="http://cdn.xiaodingdang1.com/2024/05/07/f8f4369ed4404343b36f48bb080e3c51png" mode=""
		            style="width: 84rpx; height: 84rpx; margin-top: 40rpx" />
		        <view class="addImg">添加视频</view>
		    </view>
		</view>
        <view class="uploading" v-if="photosShow">
      <!--      <view class="uploadingImg" v-for="(item, index) in imgUrl" :key="index">
                <image :src="item" mode="" style="width: 198rpx; height: 198rpx; border: 1rpx solid #c2bfbf"
                    @tap="previewImage" :data-src="item" :data-url="imgUrl" />

                <icon class="imgCancel" type="clear" size="25" :data-index="index" @tap.stop.prevent="deleteImg"></icon>
            </view> -->
			<view @touchmove.stop.prevent="moveHandle" class="moveWrap">
			
				<movable-area class="movarea" ref="areaBox" id="areaBox" :style="{height:imgSize*rowNum  + 'rpx'}">
			
					<view class="imgBox" :style="{height:imgSize*rowNum  + 'rpx'}">
						<view :id="'img' + idx" class="imgItem" v-for="(item, idx) in imgUrl" :key="idx"
							:style="{transition:addJump?' all 0.5s':'',opacity:idx===selectIdx?'0':'1', width: imgSize + 'rpx', height: imgSize + 'rpx', borderRadius:imgRadius+'rpx',left:(hoverImgIdx==='img'+idx?curHoverBoxMove.x+'rpx':''),top:(hoverImgIdx==='img'+idx?curHoverBoxMove.y+'rpx':'')}">
							<image :style="{borderRadius:imgRadius+'rpx' }" :ref="'img' + idx" :src="item.url" mode="aspectFill"
								@touchstart="tstr(idx, $event)" @touchmove="tsmo" @touchend="toend"></image>
							<!-- 删除对应图片按钮 -->
							<!-- <view class="closeBtn" v-if="enableDel"
								:style="{borderRadius:imgRadius+'rpx',fontSize: (imgSize/5) + 'rpx'}" @click="closeImg(idx)">
								删除
							</view> -->
							<icon class="imgCancel" type="clear" size="25" :data-index="index" @tap.stop.prevent="deleteImg"></icon>
						</view>
							<view class="addImg1" @tap="chooseImg" v-if="imgUrl.length < 6">
										<image src="http://cdn.xiaodingdang1.com/2024/05/07/866cb0bda2354e2e99cbe6c6f66572e7png" mode=""
											style="width: 84rpx; height: 84rpx; margin-top: 40rpx" />
										<view class="addImg">添加图</view>
									</view>
					</view>
					<movable-view v-if="moveSrc" :animation="false" class="moveV" :x="x" :y="y" direction="all"
						@change="onChange"
						:style="{ width: imgSize + 'rpx', height: imgSize + 'rpx',padding:imgPadding+'rpx' }">
						<image :style="{borderRadius:imgRadius+'rpx' }" :src="moveSrc" mode="aspectFill"></image>
					</movable-view>
				</movable-area>
			</view>
            <!-- <image src="http://cdn.xiaodingdang1.com/Group%2048098148%403x.png" mode="" style="width: 200rpx;height: 200rpx;" bindtap="chooseImg"/> -->
          <!--  <view class="addImg1" @tap="chooseImg" v-if="imgUrl.length < 6">
                <image src="http://cdn.xiaodingdang1.com/2024/05/07/866cb0bda2354e2e99cbe6c6f66572e7png" mode=""
                    style="width: 84rpx; height: 84rpx; margin-top: 40rpx" />
                <view class="addImg">添加图</view>
            </view> -->
        </view>
      
    </view>
</template>

<script>
    export default {
        data() {
            return {
				areaBoxInfo: {},
				x: 0,
				y: 0,
				selectIdx: null,
				moveSrc: '',
				areaBoxTop: 0,
				hoverImgIdx: '',
				inBoxXY: {},
				curHoverBoxMove: {
					x: 20,
					y: 20
				},
				imgSize:200,
				rowNum:1,
				screenWidth:0,
                videosList: [],
                videosShow: true,
                photosShow: true,
                videos: [],
                content: '',
                //动态内容
                contentPhotos: [],
                //动态图片
                imgUrl: [],
                topicInfo: {
                    topicName: ''
                }
            };
        },
			watch: {
					hoverImgIdx(e) {
						let idx = this.selectIdx
						let hoverIdx = parseInt(e.split('img')[1]);
						if (this.imgUrl[idx]) {
							let selectRow = this.imgUrl[idx].y / uni.upx2px(this.imgSize)
							let selectColum= this.imgUrl[idx].x / uni.upx2px(this.imgSize)
							let hoverRow = this.imgUrl[hoverIdx].y / uni.upx2px(this.imgSize)
							let hoverColum= this.imgUrl[hoverIdx].x / uni.upx2px(this.imgSize)
							let left =  -(this.imgSize * (hoverColum - selectColum))
							let top= -(this.imgSize * (hoverRow - selectRow))
							this.curHoverBoxMove = {
								x: left,
								y: top,
							}
						}
					},
					imgList(e) {
						this.imgUrl = e;
						this.rowNum=Math.ceil((e.length+1)/this.colNum) 
						let _this = this;
						this.$nextTick(function() {
							_this.getDomInfo('areaBox', info => {
								_this.areaBoxInfo = info;
								// 设置区域内所有图片的左上角坐标
								_this.imgUrl.forEach((item, idx) => {
									_this.getDomInfo('img' + idx, res => {
										item.x = res.left - info.left;
									});
									_this.getDomInfo('img' + idx, res => {
										item.y = res.top - info.top;
									});
								});
							});
						})
					}
				}
        /**
         * 生命周期函数--监听页面加载
         */
        ,
        onLoad(options) {
            let topicInfo = JSON.parse(options.topicInfo);
            this.setData({
                topicInfo: topicInfo
            });
        },
        /**
         * 生命周期函数--监听页面初次渲染完成
         */
        onReady() {},
        /**
         * 生命周期函数--监听页面显示
         */
        onShow() {},
        /**
         * 生命周期函数--监听页面隐藏
         */
        onHide() {},
        /**
         * 生命周期函数--监听页面卸载
         */
        onUnload() {},
        /**
         * 页面相关事件处理函数--监听用户下拉动作
         */
        onPullDownRefresh() {},
        /**
         * 页面上拉触底事件的处理函数
         */
        onReachBottom() {},
        /**
         * 用户点击右上角分享
         */
        onShareAppMessage() {},

        onShareTimeline() {},
        methods: {
			moveHandle() {
				return;
			},
			onChange(e) {
				// console.log(e.detail)
			},
			getWindowWidth(){
				uni.getSystemInfo({
					success:(e)=>{
						let screenWidth=parseInt(e.windowWidth/(uni.upx2px(100)/100));
						this.screenWidth  = screenWidth;
						this.imgSize=(screenWidth/this.colNum)-1
					}
				})
			},
			tstr(e, s) {
				const _this = this;
				_this.addJump=true
				_this.getDomInfo('areaBox', info => {
					_this.areaBoxInfo = info;
					// 设置区域内所有图片的左上角坐标
					_this.imgUrl.forEach((item, idx) => {
						_this.getDomInfo('img' + idx, res => {
							item.x = res.left - info.left;
						});
						_this.getDomInfo('img' + idx, res => {
							item.y = res.top - info.top;
						});
					});
				});
				//获取拖拽区域的上边距和下边距
				let areaBoxTop = this.areaBoxInfo.top;
				let areaBoxLeft = this.areaBoxInfo.left;
				// console.log(this.areaBoxInfo)
			
				// 设置可移动方块的初始位置为当前所选中图片的位置坐标
				this.x = this.imgUrl[e].x;
				this.y = this.imgUrl[e].y;
				//显示可移动方块
				this.moveSrc = this.imgUrl[e].url;
				//保存当前所选择的图片索引
				this.selectIdx = e;
				var x = s.changedTouches[0].clientX - areaBoxLeft;
				var y = s.changedTouches[0].clientY - areaBoxTop;
				// 保存鼠标在图片内的坐标
				this.inBoxXY = {
					x: x - this.imgUrl[e].x,
					y: y - this.imgUrl[e].y,
				}
			},
			tsmo(e) {
				const _this = this;
				let areaBoxTop = this.areaBoxInfo.top;
				let areaBoxLeft = this.areaBoxInfo.left;
				let imgSize = this.imgSize;
				//重置为以拖拽盒子左上角为坐标原点
				var x = e.changedTouches[0].clientX - areaBoxLeft;
				var y = e.changedTouches[0].clientY - areaBoxTop;
				this.x = x - this.inBoxXY.x;
				this.y = y - this.inBoxXY.y;
			
				this.imgUrl.forEach((item, idx) => {
					if (x > item.x && x < item.x + imgSize && y > item.y && y < item.y + imgSize) {
						_this.hoverImgIdx = 'img' + idx
					}
				});
			},
			toend(e) {
				const _this = this;
				_this.addJump=false
				// 移动结束隐藏可移动方块
				let beforeIdx = _this.selectIdx;
				let afterIdx = parseInt(_this.hoverImgIdx.split('img')[1]);
				if (_this.hoverImgIdx !== '' && beforeIdx !== afterIdx) {
					_this.imgUrl[beforeIdx].url = _this.imgUrl[afterIdx].url;
					_this.imgUrl[afterIdx].url = _this.moveSrc;
					this.$emit('moveEndList', this.imgUrl);
				}
				this.moveSrc = '';
				this.hoverImgIdx = ''
				this.selectIdx = null
			},
			addImgBtn() {
				this.$emit('addImg');
			},
			closeImg(e) {
				this.$emit('delImg', e);
			},
			getDomInfo(id, callBack) {
				const query = uni.createSelectorQuery().in(this);
				// console.log(query)
				query.select('#' + id)
					.boundingClientRect()
					.exec(function(res) {
						callBack(res[0]);
					});
			},
            // 上传图片
            chooseImg: function(e) {
                let left = this
                var contentPhotos = left.contentPhotos;
                if (contentPhotos.length >= 6) {
                    uni.showToast({
                        icon: 'none',
                        title: '最多上传6张图片'
                    });
                    return false;
                }
                uni.chooseImage({
                    // count: 1, // 默认9
                    sizeType: ['compressed'],
                    // 可以指定是原图还是压缩图，默认二者都有
                    sourceType: ['album', 'camera'],
                    // 可以指定来源是相册还是相机，默认二者都有
                    success: function(res) {
                        var tempFilePaths = res.tempFilePaths;
                        var contentPhotos = left.contentPhotos;
                        let picNum = tempFilePaths.length;
                        uni.showLoading({
                            title: '图片上传中'
                        });
                        for (var i = 0; i < tempFilePaths.length; i++) {
                            if (contentPhotos.length >= 6) {
                                left.setData({
                                    contentPhotos: contentPhotos
                                });
                                return false;
                            } else {
                                contentPhotos.push(tempFilePaths[i]);
                                uni.uploadFile({
                                    filePath: tempFilePaths[i],
                                    name: 'file',
                                    url: left.$api.uploadOSS,
                                    formData: {},
                                    header: {
                                        'Content-Type': 'multipart/form-data; charset=UTF-8',
                                        Authorization: 'Bearer ' + uni.getStorageSync('token'),
                                        'Accept-Encoding': 'gzip',
                                        Clientid: '428a8310cd442757ae699df5d894f051'
                                    },
                                    success: function(rests) {
                                        let data = JSON.parse(rests.data);
                                        console.log('resssss', rests, data)
                                        if (data.code == 200) {
                                            uni.hideLoading();
                                            let datas = left.imgUrl;
                                            datas.push({url:data.data.url});
                                            left.setData({
                                                imgUrl: datas
                                            });
                                        } else {
                                            uni.showToast({
                                                icon: 'none',
                                                title: data.msg
                                            });
                                        }
                                    }
                                });
                            }
                        }
                        // 返回选定照片的本地文件路径列表，tempFilePath可以作为img标签的src属性显示图片
                        left.setData({
                            contentPhotos: contentPhotos,
                            videosShow: false
                        });
                    }
                });
            },

            deleteImgs(e) {
                //删除视频
                var videos = this.videos;
                var index = e.currentTarget.dataset.index;
                videos.splice(index, 1);
                if (videos.length == 0) {
                    this.setData({
                        photosShow: true,
                        videosShow: true
                    });
                }
                this.setData({
                    videos: videos
                });
            },

            // 删除图片
            deleteImg: function(e) {
                var contentPhotos = this.contentPhotos;
                var imgUrl = this.imgUrl;
                var index = e.currentTarget.dataset.index;
                contentPhotos.splice(index, 1);
                imgUrl.splice(index, 1);
                if (imgUrl.length == 0) {
                    this.setData({
                        photosShow: true,
                        videosShow: true
                    });
                }
                this.setData({
                    contentPhotos: contentPhotos,
                    imgUrl: imgUrl
                });
            },

            publish() {
                //发布
                let left = this
                if (left.content == '') {
                    uni.showToast({
                        title: '请填写发布内容',
                        icon: 'none',
                        duration: 3000 //持续的时间
                    });
                    return;
                }
                let data = {
                    topicId: left.topicInfo.topicId,
                    content: left.content
                };
                if (left.imgUrl.length > 0) {
					let imageUrl=[]
					left.imgUrl.forEach(item=>{
						imageUrl.push(item.url)
					})
                    data.imageUrl =imageUrl;
                }
                if (left.videos.length > 0) {
                    data.videos = left.videos;
                }
                left.$axios.put(left.$api.communitySave, data).then((res) => {
                    if (res.data.code == 200) {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000 //持续的时间
                        });
                        setTimeout(() => {
                            uni.navigateBack({
                                delta: 1
                            });
                        }, 3000);
                    }
                });
            },

            searchInput(e) {
                //输入框获取值
                this.setData({
                    content: e.detail.value
                });
            },

            cancel() {
                //取消
                uni.navigateBack({
                    delta: 1
                });
            },

            uploadVideo() {
                let left = this
                uni.chooseMedia({
                    count: 9,
                    mediaType: ['video'],
                    sourceType: ['album', 'camera'],
                    maxDuration: 30,
                    camera: 'back',
                    success(res) {
                        uni.showLoading({
                            title: '视频上传中'
                        });
                        left.setData({
                            photosShow: false
                        });
                        var tempFiles = res.tempFiles[0].tempFilePath;
                        let videos = [];
                        uni.uploadFile({
                            filePath: tempFiles,
                            name: 'file',
                            url: left.$api.uploadOSS,
                            formData: {},
                            header: {
                                'Content-Type': 'multipart/form-data',
                                Authorization: 'Bearer ' + uni.getStorageSync('token'),
                                'Accept-Encoding': 'gzip',
                                Clientid: '428a8310cd442757ae699df5d894f051'
                            },
                            success: function(rests) {
                                let data = JSON.parse(rests.data);
                                if (data.code == 200) {
                                    let videosList = [];
                                    uni.hideLoading();
                                    videos.push(data.data.url);
                                    videosList.push({
                                        type: 'video',
                                        url: data.data.url
                                    });
                                    left.setData({
                                        videos: videos,
                                        photosShow: false,
                                        videosList: videosList
                                    });
                                }
                            }
                        });
                    }
                });
            },

            previewImage: function(e) {
                var current = e.currentTarget.dataset.src;
                var url = e.currentTarget.dataset.url;
                uni.previewImage({
                    current: current,
                    // 当前显示图片的http链接
                    urls: url // 需要预览的图片http链接列表
                });
            },

            preview(event) {
                let maparr = this.videosList;
                let index = 0;
                // 既有视频又有图片用这个
                uni.previewMedia({
                    sources: maparr,
                    // 官方文档 : 素材展示列表 是个数组对象 一个url链接 一个type 判断这是视频还是图片
                    // 接收image 和 video 所以处理数据时 最好给个type 指定 image 或者 video
                    current: index
                    // 当前显示的资源序号
                });
            }
        }
    };
</script>
<style scoped>
    @import './participateproject.less';
</style>