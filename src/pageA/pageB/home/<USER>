<template>
    <view>
        <view class="main">
            <view class="prosonal">
                <view class="next">
                    <view class="headImg" @tap="next">
                        <image src="http://cdn.xiaodingdang1.com/1/120240201/183e1847-20bc-461e-aecf-444fe898e91f.png"
                            mode="" style="width: 18rpx; height: 34rpx" />
                    </view>
                </view>
                <view class="message">
                    <view class="message1">
                        <view>
                            <image :src="recordsInfoList.avatar || $defaultAvatar" mode=""
                                style="width: 100rpx; height: 100rpx; border-radius: 100rpx" />
                        </view>
                        <view class="message1_2">
                            <view>已连续签到{{ recordsInfoList.checkinCount }}天</view>
                            <view class="message1_4">连续签到七天获得免费福利</view>
                        </view>
                    </view>
                    <view class="message1_3">{{ recordsInfoList.checkinCount }}天</view>
                </view>
                <view class="sign">
                    <view class="title">签到得好礼</view>
                    <view class="sign1">
                        <template v-for="(item, index) in recordsInfoList.checkInRecords" :key="index">
                            <view :class="item.isCheckin ? 'signAtive' : 'sign1_1'" @tap="tab" :data-index="index"
                                v-if="index < 6">
                                <view>
                                    {{ item.day }}
                                </view>

                                <image
                                    src="http://cdn.xiaodingdang1.com/1/120240129/75ab1863-f607-42a3-b20b-578ee17892db.png"
                                    mode="" style="width: 62rpx; height: 54rpx" class="signImg" v-if="item.isCheckin" />
                            </view>
                        </template>

                        <view :class="recordsInfoList.checkInRecords[6]?.isCheckin ? 'signAtive1' : 'sign1_2'"
                            @tap="preview">
                            <view class="sign1_3">
                                <view
                                    :class="recordsInfoList.checkInRecords[6]?.isCheckin ? 'sign1_3_1_1' : 'sign1_3_1'">
                                    {{ recordsInfoList.checkInRecords[6]?.day }}
                                </view>
                                <view
                                    :class="recordsInfoList.checkInRecords[6]?.isCheckin ? 'sign1_3_2_1' : 'sign1_3_2'">
                                    神秘好礼</view>
                            </view>
                            <view class="sign1_4">
                                <image
                                    src="http://cdn.xiaodingdang1.com/2024/09/04/c6122a8eb729426a8dfb6ffeb362946a.png"
                                    mode="" style="width: 136rpx; height: 136rpx" />
                            </view>
                        </view>
                    </view>
                </view>
                <view class="sifnBtn" @tap="checkinSignIn">立即签到</view>
            </view>
        </view>
        <view class="popUp" v-if="getShow" @tap="closegetShow">
            <view class="popUp1">
                <view class="popImg">
                    <image src="http://cdn.xiaodingdang1.com/Group%2048098321%403x.png" mode=""
                        style="width: 394rpx; height: 266rpx" />
                </view>
                <view class="popTitle1">恭喜获得</view>
                <view class="popTitle2_1">
                    {{ checkinInfoList.giftName }}
                </view>
                <image :src="checkinInfoList.giftPhotoUrl[0]" mode=""
                    style="width: 284rpx; height: 284rpx; margin-top: 16rpx" />
                <view class="price">
                    <view class="price1">价值：</view>
                    <view class="price2">￥{{ checkinInfoList.giftPrice }}元</view>
                </view>
            </view>
        </view>

        <view class="popUps" v-if="signinShow" @tap="lsee">
            <view class="popUp2">
                <view class="popImg">
                    <image src="http://cdn.xiaodingdang1.com/2024/04/11/a28a957dcec54ed5830248e25e19af3bpng" mode=""
                        style="width: 308rpx; height: 308rpx" />
                </view>
                <view class="popTitle2">太棒了，签到成功！</view>
                <view class="getBtn1" @tap="lsee">我知道了</view>
            </view>
        </view>
    </view>
</template>

<script>
    export default {
        data() {
            return {
                checkinInfoList: {
                    giftName: '',
                    giftPhotoUrl: '',
                    giftPrice: ''
                },

                signinShow: false,

                recordsInfoList: {
                    avatar: '',
                    checkinCount: '',
                    checkInRecords: []
                },

                getShow: false,
                tabIndex: 0,

                tabList: [{
                        text: '1月1日',
                        check: true
                    },
                    {
                        text: '1月2日',
                        check: true
                    },
                    {
                        text: '1月3日',
                        check: true
                    },
                    {
                        text: '1月4日',
                        check: true
                    },
                    {
                        text: '1月5日'
                    },
                    {
                        text: '1月6日'
                    }
                ],

                isCheckin: false,
                day: ''
            };
        }
        /**
         * 生命周期函数--监听页面加载
         */
        ,
        onLoad(options) {},
        /**
         * 生命周期函数--监听页面初次渲染完成
         */
        onReady() {},
        /**
         * 生命周期函数--监听页面显示
         */
        onShow() {
            this.recordsInfo();
        },
        /**
         * 生命周期函数--监听页面隐藏
         */
        onHide() {},
        /**
         * 生命周期函数--监听页面卸载
         */
        onUnload() {},
        /**
         * 页面相关事件处理函数--监听用户下拉动作
         */
        onPullDownRefresh() {},
        /**
         * 页面上拉触底事件的处理函数
         */
        onReachBottom() {},
        /**
         * 用户点击右上角分享
         */
        onShareAppMessage() {},

        onShareTimeline() {},
        methods: {
            tab(e) {
                let index = e.currentTarget.dataset.index;
                this.setData({
                    tabIndex: index
                });
            },

            next() {
                uni.navigateBack({
                    delta: 1
                });
            },

            recordsInfo() {
                //获取当前登录用户签到信息

                this.$axios.get(this.$api.recordsInfo).then((res) => {
                    if (res.data.code == 200) {
                        this.setData({
                            recordsInfoList: res.data.data
                        });
                    }
                });
            },

            checkinSignIn() {
                //签到

                this.$axios.post(this.$api.checkinSignIn).then((res) => {
                    if (res.data.code == 200) {
                        this.setData({
                            signinShow: true
                        });
                        this.recordsInfo();
                    }
                });
            },

            lsee() {
                this.setData({
                    signinShow: false
                });
            },

            preview() {

                if (this.recordsInfoList.checkInRecords[6].isGifts) {
                    let data = {
                        giftId: this.recordsInfoList.checkInRecords[6].giftsId
                    };
                    this.$axios.get(this.$api.checkinInfo, data).then((res) => {
                        if (res.data.code == 200) {
                            this.setData({
                                checkinInfoList: res.data.data,
                                getShow: !this.getShow
                            });
                        }
                    });
                }
            },

            closegetShow() {
                this.setData({
                    getShow: !this.getShow
                });
            }
        }
    };
</script>
<style scoped>
    @import './signin.css';
</style>