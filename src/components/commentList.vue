<template>
    <view class="comment" v-if="commentList && commentList.length > 0">
        <view class="top flex" v-if="item.commentsCount">
            <view :class="[pageType == 'mom'? 'top-red': 'top-blue']"></view>
            <view class="top-title">评论</view>
            <view class="top-num">
                {{item.commentsCount}}条评论
            </view>
            <view class="top-line"></view>
        </view>
        <view v-for="(comment, index) in commentList" :key="index">
            <view class="main">
                <view class="main-img">
                    <image :src="comment.avatar || $defaultAvatar" mode=""></image>
                </view>
                <view class="main-content">
                    <view class="main-content-name">
                        {{ comment.nickname }}
                    </view>
                    <view class="main-content-sub">
                        {{ comment.comment }}
                    </view>
                    <view class="main-content-bottom flex">
                        <view class="main-content-bottom-date">
                            {{ comment.createTime || utils.formatTime(new Date()) }}
                        </view>
                        <view class="main-content-bottom-recover" @tap="onComment(index, comment)">
                            回复
                        </view>
                    </view>
                </view>
                <view class="main-right">
                    <!-- <image src="http://cdn.xiaodingdang1.com/2024/12/31/6bb4463717ba467595e886bea2eb1f66.png">
                    </image> -->
                    <!-- 0 -->
                </view>
            </view>
            <template v-if="comment.childrenList && comment.childrenList.length > 0">
                <template v-for="(item, index) in comment.childrenList" :key="index">
                    <view class="submain">
                        <view class="submain-grid">
                            <view class="submain-img">
                                <image :src="item.avatar || $defaultAvatar" mode=""></image>
                            </view>
                            <view class="submain-content">
                                <view class="submain-content-name">
                                    {{ item.nickname }}
                                    <view class="submain-content-bottom-recover" style="margin-left: 0;">
                                        <!--   回复
                                <text class="main-content-name">
                                    胡哥哥
                                </text> -->
                                    </view>
                                </view>
                                <view class="submain-content-sub">
                                    {{ item.comment }}
                                </view>
                                <view class="submain-content-bottom flex">
                                    <view class="submain-content-bottom-date">
                                        {{ item.createTime || utils.formatTime(new Date()) }}
                                    </view>
                                    <!-- <view class="main-content-bottom-recover" @tap="onComment">
                                回复
                            </view>
                            <view class="main-content-bottom-recover">
                                删除
                            </view> -->
                                </view>
                            </view>
                            <view class="submain-right">
                                <!-- <image
                                    src="http://cdn.xiaodingdang1.com/2024/12/31/6bb4463717ba467595e886bea2eb1f66.png">
                                </image> -->
                                <!-- <image
                                    src="http://cdn.xiaodingdang1.com/2024/12/31/2899047a49fd47fe97e0ec71c5d21635.png">
                                </image> -->
                                <!-- 88 -->
                            </view>
                        </view>
                        <!-- <view class="line"></view> -->
                        <!-- <view class="more">
                            展示更多回复
                        </view> -->
                    </view>
                </template>
            </template>
            <view class="line"></view>
        </view>
        <view class="all-more" @tap="lookAllData" v-if="commentList.length > 3">
            {{showMore? '收起': '展开'}}全部{{item.commentsCount}}条评论
        </view>
        <view class="fixed">
            <bottom-input :inputBottomHeight="inputBottomHeight" :show="show" @callback="repay"
                @close="close"></bottom-input>
        </view>
    </view>
    <view v-else class="comment">
        <view class="top flex">
            <view :class="[pageType == 'mom'? 'top-red': 'top-blue']"></view>
            <view class="top-title">评论</view>
            <view class="top-num">
                0条评论
            </view>
            <view class="top-line"></view>
        </view>
        <view class="empty-content">
            <view class="empty">
                <image src="http://cdn.xiaodingdang1.com/2025/01/18/59ee172ce881443a97510ed50777e0ae.png"></image>
                <view class="text">还没有评论哦</view>
            </view>
        </view>
    </view>
</template>

<script setup>
    import bottomInput from "./bottomInput.vue";
    import utils from '@/utils/util.js'
    import {
        onMounted,
        ref,
        computed,
        reactive,
        getCurrentInstance,
        inject,
        watch
    } from "vue";
    const instance = getCurrentInstance();
    const App = instance.appContext.config.globalProperties;
    const props = defineProps({
        inputBottomHeight: {
            type: Number,
            default: 0
        },
        item: {
            type: Object,
            default: () => {}
        },
        pageType: {
            type: String,
            default: 'mom'
        }
    })
    const content = ref('')
    const show = ref(false)
    const emits = defineEmits(['input'])
    const repayIndex = ref('')
    const parentCommentId = ref('')
    const commentList = ref([])
    const originList = ref([])
    const postId = computed(() => props.item.postId)
    const showMore = ref(false)
    const list = computed(() => {
        if (props.item.comments && props.item.comments.length > 0) {
            originList.value = JSON.parse(JSON.stringify(props.item.comments))
            return props.item.comments
        } else {
            return []
        }
    })
    watch(list, (newVal, oldVal) => {
        console.log('newvallll', newVal)
        commentList.value = newVal
        commentList.value = showMore.value ? originList.value : originList.value.slice(0, 4)
    })
    const lookAllData = () => {
        showMore.value = !showMore.value
        commentList.value = showMore.value ? originList.value : originList.value.slice(0, 4)
    }
    const onComment = (index, comment) => {
        console.error('oncomment', comment)
        repayIndex.value = index
        parentCommentId.value = comment.commentId
        show.value = true
    }
    const repay = (value) => {
        // 评论回复
        const userInfo = uni.getStorageSync("userInfo")
        const currentDate = utils.formatTime(new Date())
        const childrenList = {
            comment: value,
            avatar: userInfo.avatar,
            createTime: currentDate,
            nickname: userInfo.nickname
        }
        if (commentList.value[repayIndex.value].childrenList) {
            commentList.value[repayIndex.value].childrenList.push(childrenList)
        } else {
            commentList.value[repayIndex.value].childrenList = [childrenList]
        }
        if (value) {
            App.$axios.post(App.$api.feedPostComment, {
                comment: value,
                postId: postId.value,
                parentCommentId: parentCommentId.value
            }).then((res) => {

            })
        }
        emits('repay', value)
        // close()
    }
    const close = () => {
        show.value = false
    }
    const like = () => {

    }
    const addComment = (value) => {

    }
    defineExpose({
        addComment
    })
</script>

<style lang="less" scoped>
    .comment {
        margin-top: 20rpx;
        background-color: white;
        padding: 37rpx 24rpx 1rpx 24rpx;

        .flex {
            display: flex;
            align-items: center;
        }

        .line {
            margin-left: 98rpx;
            width: calc(100vw - 98rpx);
            height: 2rpx;
            transform: scaleY(.5);
            background: #F0F0F0;
            margin-top: 20rpx;
            margin-bottom: 30rpx;
        }

        .more {
            font-weight: 500;
            font-size: 26rpx;
            color: #002B74;
            line-height: 44rpx;
            // padding-left: 160rpx;
            margin-bottom: 22rpx;
            text-align: center;
        }

        .all-more {
            font-weight: 500;
            font-size: 26rpx;
            color: #002B74;
            line-height: 44rpx;
            text-align: center;
            margin-top: 8rpx;
            margin-bottom: 32rpx;
        }


    }

    .top {
        margin-bottom: 23rpx;

        &-red {
            width: 6rpx;
            height: 30rpx;
            background: #FF4F61;
            border-radius: 28rpx;
            margin-right: 12rpx;
        }

        &-blue {
            width: 6rpx;
            height: 30rpx;
            border-radius: 28rpx;
            margin-right: 12rpx;
            background-color: #3994FF;
        }

        &-title {
            font-weight: 500;
            font-size: 32rpx;
            color: #333333;
            line-height: 38rpx;
            margin-right: 8rpx;
        }

        &-num {
            font-weight: 500;
            font-size: 26rpx;
            color: #ABABAB;
            line-height: 30rpx;
        }

        &-line {
            flex: 1;
            height: 1rpx;
            background: #F0F0F0;
        }
    }

    .main {
        display: grid;
        grid-template-columns: 108rpx 1fr 80rpx;

        &-img {
            width: 80rpx;
            height: 80rpx;

            image {
                width: 100%;
                height: 100%;
                border-radius: 80rpx;
            }
        }

        &-content {
            text-align: left;

            &-name {
                font-weight: 500;
                font-size: 26rpx;
                color: #ABABAB;
                line-height: 30rpx;
                margin: 8rpx 0;
            }

            &-sub {
                font-weight: 500;
                font-size: 26rpx;
                color: #313131;
                line-height: 40rpx;
                margin-bottom: 12rpx;
            }

            &-bottom {
                font-size: 22rpx;
                line-height: 44rpx;

                &-date {
                    color: #ACACAC;

                }

                &-recover {
                    color: #565656;
                    margin-left: 16rpx;
                }
            }
        }

        &-right {
            width: 80rpx;
            display: flex;
            flex-direction: column;
            align-items: center;

            image {
                width: 32rpx;
                height: 32rpx;
                display: block;
            }
        }

    }

    .submain {
        padding: 24rpx 0 0rpx 96rpx;
        width: 706rpx;

        &-grid {
            display: grid;
            grid-template-columns: 70rpx 1fr 80rpx;
        }

        &-img {
            width: 52rpx;
            height: 52rpx;
            border-radius: 52rpx;

            image {
                width: 100%;
                height: 100%;
                border-radius: 80rpx;
            }
        }

        &-content {
            text-align: left;

            &-name {
                font-weight: 500;
                font-size: 26rpx;
                color: #ABABAB;
                line-height: 30rpx;
                margin: 4rpx 0;
            }

            &-sub {
                font-weight: 500;
                font-size: 26rpx;
                color: #313131;
                line-height: 40rpx;
                margin-bottom: 12rpx;
            }

            &-bottom {
                font-size: 22rpx;
                line-height: 44rpx;

                &-date {
                    color: #ACACAC;

                }

                &-recover {
                    color: #565656;
                    margin-left: 16rpx;
                }
            }
        }

        &-right {
            width: 80rpx;
            display: flex;
            flex-direction: column;
            align-items: center;

            image {
                width: 32rpx;
                height: 32rpx;
                display: block;
            }
        }

        // .main-content {
        //     width: 434rpx;
        // }

    }

    .fixed {
        background: #FFFFFF;
        border: 1rpx solid #F0F0F0;
        position: fixed;
        bottom: 0;
        left: 0;
        z-index: 9;
        background-color: white;
        width: 100%;
        display: flex;
        align-items: center;
        padding: 24rpx 32rpx 44rpx 32rpx;
    }

    .empty-content {
        height: 290rpx;
        width: 100%;
        position: relative;
    }

    .empty {
        position: absolute;
        top: 40%;
        left: 50%;
        transform: translate(-50%, -50%);
    }

    .empty image {
        width: 260rpx;
        height: 150rpx;
    }

    .text {
        font-weight: 400;
        font-size: 22rpx;
        color: #ABABAB;
        line-height: 26rpx;
        text-align: center;
    }
</style>