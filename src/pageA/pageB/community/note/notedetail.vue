<template>
  <view>
    <navigation
      :background="bgColor"
      title="服务笔记"
      :isSowArrow="true"
    ></navigation>
    <scroll-view
      :using-sticky="true"
      class="note"
      :scroll-y="true"
      :scroll-x="false"
      :scroll-into-view="scrollIntoView"
      :scroll-with-animation="true"
      :show-scrollbar="false"
      @scroll="handleScroll"
    >
      <view class="note-fixed">
        <view class="top">
          <view class="top-img">
            <image :src="momDetail.avatar || $defaultAvatar"></image>
          </view>
          <view class="top-right">
            <view class="top-right-name">
              {{ momDetail.name }}
              <image
                src="http://cdn.xiaodingdang1.com/2025/01/03/75913fa5ed3341b1ac610947dca44b9f.png"
              >
              </image>
            </view>
            <view class="top-right-age"> 年龄：&nbsp;{{ momDetail.age }} </view>
            <view class="top-right-weight">
              宝宝：&nbsp; {{ momDetail.babyWeight }}
            </view>
            <view class="top-right-time">
              入住时间：&nbsp; {{ momDetail.serviceStartDate }}
            </view>
          </view>
        </view>
        <view
          class="nurse"
          v-if="momDetail.employees && momDetail.employees.length > 0"
        >
          <view class="nurse-title"> 护理团队 </view>
          <scroll-view :scroll-x="true" class="nurse-list">
            <view
              class="nurse-list-enum"
              v-for="(item, index) in momDetail.employees"
              :key="index"
              @tap="nurseDetail(item)"
            >
              <view class="nurse-list-enum-img">
                <image :src="item.photos || $defaultAvatar"></image>
              </view>
              <view class="nurse-list-enum-name">
                {{ item.name }}
              </view>
              <view class="nurse-list-enum-tag">
                <image
                  src="http://cdn.xiaodingdang1.com/2024/12/31/7ab70df46904400aae76508164f5a0ac.png"
                >
                </image>
                {{ item.roleName }}
              </view>
            </view>
          </scroll-view>
        </view>
      </view>
      <view class="border">
        <view class="content">
          <view
            :id="`day-${index + 1}`"
            v-for="(item, index) in showData"
            :key="index"
            class="card"
          >
            <view v-show="index == 0" class="tab-day">
              <view class="tab-list">
                <scroll-view class="tab" :scroll-x="true">
                  <view
                    :style="{
                      color: currentIndex == index ? '#1FA2FF' : '#333333',
                    }"
                    class="tab-enum"
                    v-for="(item, index) in checkInDayList"
                    :key="index"
                    @click="checkedDay(index)"
                  >
                    {{ item.label }}
                  </view>
                </scroll-view>
              </view>
            </view>
            <view class="tab-line"></view>
            <view class="content-radius">
              <view class="card-top">
                {{ item.serviceNumName }}
              </view>
              <view
                class="card-header"
                v-if="item.serviceList && item.serviceList.length > 0"
              >
                <view class="card-header-title">今日服务项</view>
                <view
                  class="card-header-subtitle"
                  v-for="(service, index) in item.serviceList"
                  :key="index"
                >
                  <template v-if="service">
                    <view class="card-header-subtitle-dot"></view>
                    {{ service }}
                  </template>
                  <view class="subtitle-line"></view>
                </view>
              </view>
              <!-- <view class="card-line"></view> -->
              <view class="card-detail">
                <view class="card-detail-title">宝妈记录</view>
                <template v-if="item.showMore">
                  <view
                    class="card-detail-main"
                    v-for="(list, index) in item.pageList"
                    :key="index"
                  >
                    <!-- <service-card-vue :item="list"></service-card-vue> -->
                    <view class="headPortrait">
                      <view class="headPortraitLeft">
                        <image
                          :src="list.staffPhotos || $defaultAvatar"
                        ></image>
                      </view>
                      <view class="headPortraitRight">
                        <view class="headPortraitRight_1">
                          {{ list.staffName }}
                        </view>
                        <view class="headPortraitRight_2">
                          <image
                            src="http://cdn.xiaodingdang1.com/2024/12/31/7ab70df46904400aae76508164f5a0ac.png"
                            style="width: 24rpx; height: 24rpx"
                          >
                          </image>
                          {{ list.staffPost }}
                        </view>
                      </view>
                    </view>
                    <scroll-view scroll-x="true" class="headPortraitTag">
                      <view class="headPortraitTag1">
                        {{ list.nodeName }}
                      </view>
                    </scroll-view>
                    <view style="height: 20rpx"></view>
                    <img-content-area-vue
                      :listData="list"
                      :index="index"
                    ></img-content-area-vue>
                    <dynamic-button-vue
                      @onDelete="onDelete"
                      :buttonArray="buttonArray"
                      @onPoster="onPoster"
                      :dyData="list"
                    ></dynamic-button-vue>
                  </view>
                </template>
                <template v-else>
                  <view
                    class="card-detail-main"
                    v-for="(list, index) in item.pageList.slice(0, 1)"
                    :key="index"
                  >
                    <view v-show="item.pageList[0].createTime">
                      <!-- <service-card-vue :item="list"></service-card-vue> -->
                      <view class="headPortrait">
                        <view class="headPortraitLeft">
                          <image
                            :src="list.staffPhotos || $defaultAvatar"
                          ></image>
                        </view>
                        <view class="headPortraitRight">
                          <view class="headPortraitRight_1">
                            {{ list.staffName }}
                          </view>
                          <view class="headPortraitRight_2">
                            <image
                              src="http://cdn.xiaodingdang1.com/2024/12/31/7ab70df46904400aae76508164f5a0ac.png"
                              style="width: 24rpx; height: 24rpx"
                            >
                            </image>
                            {{ list.staffPost }}
                          </view>
                        </view>
                      </view>
                      <scroll-view scroll-x="true" class="headPortraitTag">
                        <view class="headPortraitTag1">
                          {{ list.nodeName }}
                        </view>
                      </scroll-view>
                      <view style="height: 20rpx"></view>
                      <img-content-area-vue
                        :listData="list"
                        :index="index"
                      ></img-content-area-vue>
                      <dynamic-button-vue
                        @onDelete="onDelete"
                        :buttonArray="buttonArray"
                        @onPoster="onPoster"
                        :dyData="list"
                      ></dynamic-button-vue>
                    </view>
                    <view
                      class="notAvailable"
                      v-show="!item.pageList[0].createTime"
                    >
                      暂无发布笔记
                    </view>
                  </view>
                </template>
              </view>
              <view
                class="card-more"
                @tap="moreClick(index)"
                v-if="item.pageList && item.pageList.length > 1"
              >
                {{ item.showMore ? '收起全部' : '点击展开更多' }}
                <u-icon name="arrow-up" size="20" v-if="item.showMore"></u-icon>
                <u-icon name="arrow-down" size="20" v-else></u-icon>
              </view>
            </view>
          </view>
        </view>
      </view>
      <!-- <u-back-top @topClick="topClick" :scroll-top="scrollTop" top="1000"></u-back-top> -->
      <!-- <rightMenuBarVue :scrollTop="scrollTop"></rightMenuBarVue> -->
      <view class="btn">
        <button open-type="share" class="image-button">
          <image
            src="http://cdn.xiaodingdang1.com/2025/05/23/e4c88700d42747ba826408937b7177c7.png"
            mode=""
            style="width: 100%; height: 100%"
          />
        </button>
        <button class="image-button" @click="nextIndex()">
          <image
            src="http://cdn.xiaodingdang1.com/2025/05/23/3b004cce4ef54533900ef914db531cf8.png"
            mode=""
            style="width: 100%; height: 100%"
          />
        </button>
        <u-back-top
          style="border-radius: 100rpx"
          @topClick="topClick"
          :scroll-top="scrollTop"
          top="1000"
          :mode="mode"
          :icon-style="iconStyle"
          :custom-style="customStyle"
        ></u-back-top>
      </view>
    </scroll-view>
    <u-popup
      v-model="showPop"
      :closeable="false"
      mode="bottom"
      border-radius="40"
    >
      <view class="pop-bottom">
        <view class="li" @tap="popClick('service')">
          <image
            src="http://cdn.xiaodingdang1.com/2025/01/06/30abb06bd14e480198cba65762a36817.png"
          ></image>
          服务项设置
        </view>
        <view class="li" @tap="popClick('publish')">
          <image
            src="http://cdn.xiaodingdang1.com/2025/01/06/237e3940339a4ed9aaddbcf264c29851.png"
          ></image>
          发布笔记
        </view>
        <view class="bottom" @tap="popClick()">
          <image
            src="http://cdn.xiaodingdang1.com/2025/01/06/f45e5d6fedae47cfaf507d21b30435ab.png"
          ></image>
        </view>
      </view>
    </u-popup>
    <publishbutton-vue
      ref="publishRef"
      roleIf="5"
      permissions="diary:post:create"
      @publishnote="popClick"
      v-if="rolesShow1 == 1"
    ></publishbutton-vue>
  </view>
</template>

<script setup>
import publishbuttonVue from '@/components/publishbutton.vue';
import imgContentAreaVue from '@/components/imgContentArea.vue';
import dynamicButtonVue from '@/components/dynamicButton.vue';
import {
  onShow,
  onLoad,
  onUnload,
  onShareAppMessage,
  onShareTimeline,
} from '@dcloudio/uni-app';
import { ref, getCurrentInstance } from 'vue';
import { useStore } from 'vuex';
import utils from '@/utils/util.js';
const dayList = utils.getCheckInDayList();
const store = useStore();
const instance = getCurrentInstance();
const App = instance.appContext.config.globalProperties;
onShareAppMessage(() => {});
onShareTimeline(() => {});
const mode = ref('');
const iconStyle = ref({});
const customStyle = ref({});
const bgColor = ref('#F0F0F0');
const publishRef = ref(null);
const posterGeneratorRef = ref(null);
const momDetail = ref({});
const rolesShow1 = ref(0);
const shopInfo = ref({});
const momId = ref('');
const showPop = ref(false);
// const allData = ref([])
const showData = ref([]);
const checkInDayList = ref([]);
// const checkListNum = ref([])
// const checked = ref(null)
const scrollIntoView = ref(''); // 需要滑动到的视图的id
const currentIndex = ref(0); // 当前选中的锚点索引
// const setPad = ref(false)
const scrollTop = ref(0);
const stickyIndex = ref(0);
const buttonArray = ref(['poster']);
const uuid = ref(''); // 生成海报需要的id
onShow(() => {
  setTimeout(() => {
    if (publishRef.value) {
      publishRef.value.publishInit();
    }
  }, 200);
  // 刷新页面列表接口
  uni.$on('publishon', () => {
    pageInit();
    uni.$off('publishon');
  });
  // uni.$on('publishservice', () => {
  //     pageInit()
  //     uni.$off('publishservice');
  // });
});
onUnload(() => {
  var newDateTime = Date.parse(new Date());
  let data = {
    eventId: uni.getStorageSync('eventId'),
    leaveTime: newDateTime,
  };
  let iftoken = uni.getStorageSync('token');
  if (iftoken) {
    App.$point.reportEnd(data);
  }
});
const moreClick = (index) => {
  showData.value[index].showMore = !showData.value[index].showMore;
};
const topClick = () => {
  scrollIntoView.value = checkInDayList.value[0].id;
};
const nextIndex = () => {
  uni.switchTab({
    url: '/pageA/home',
  });
};
const handleScroll = (e) => {
  // if (e.detail.scrollTop > 0) {
  //     bgColor.value = '#F8F9F9'
  // } else if (e.detail.scrollTop < 2) {
  //     bgColor.value = '#F0F0F0'
  // }
  scrollTop.value = e.detail.scrollTop;
  // setPad.value = e.detail.scrollTop > 320 ? true : false
  scrollIntoView.value = '';

  // 触发全局滚动事件，通知 dynamicButton 组件关闭菜单
  uni.$emit('pageScroll', e);
};
onLoad((options) => {
  mode.value = 'square';
  (iconStyle.value = {
    fontSize: '35rpx',
    color: '#4A4A4A',
  }),
    (customStyle.value = {
      backgroundColor: '#ffffff',
      borderRadius: '100rpx',
      width: '70rpx',
      height: '70rpx',
      bottom: '230rpx',
      right: '24rpx',
      boxShadow: '2px 2px 5px rgba(0,0,0,0.1)',
    });

  let roles = uni.getStorageSync('roles');
  let rolesShow = roles[0] == 'USER' || roles[0] == 'CUSTOMER' ? 0 : 1;
  rolesShow1.value = rolesShow;
  momId.value = options.momId || '';
  uuid.value = options.uuid || '';
  if (options.scene) {
    let params = {};
    let pairs = options.scene.split('%26');
    for (let i = 0; i < pairs.length; i++) {
      let pair = pairs[i].split('-');
      params[pair[0]] = pair[1];
    }
    uuid.value = params.uuid;
  }
  let permissions = uni.getStorageSync('permissions');
  if (permissions.includes('diary:post:create')) {
    buttonArray.value.push('delete');
  }
  pageInit();
});
const pageInit = async () => {
  // allData.value = []
  // checkListNum.value = []
  // checked.value = []
  await getMomDetail();
  await getMomList();
};
const checkedDay = (index) => {
  // if (checked.value.includes(num)) {
  //     checked.value.splice(checked.value.findIndex((item) => item == num), 1)
  //     if (checked.value.length == 0) {
  //         showData.value = []
  //         showData.value = JSON.parse(JSON.stringify(allData.value))
  //     } else {
  //         showData.value = []
  //         allData.value.forEach((list) => {
  //             if (checked.value.includes(list.serviceNum)) {
  //                 showData.value.push(list)
  //             }
  //         })
  //     }
  // } else {
  //     checked.value = []
  //     checked.value.push(num)
  //     showData.value = []
  //     allData.value.forEach((list) => {
  //         if (checked.value.includes(list.serviceNum)) {
  //             showData.value.push(list)
  //         }
  //     })
  // }

  // nextTick(() => {
  //     setTimeout(() => {

  //     }, 500)
  // })

  scrollIntoView.value = checkInDayList.value[index].id;
  stickyIndex.value = index;
  // uni.createSelectorQuery().select('#' + scrollIntoView.value).boundingClientRect(data => {
  //     console.log('dataaaa', data)
  //     if (data) {
  //         uni.createSelectorQuery().select('#' + scrollIntoView.value).scrollOffset(
  //             scrollData => {
  //                 scrollTop.value = 200
  //                 console.log('scrollDatascrollData', scrollData, scrollTop.value)
  //                 // scrollTop.value = scrollData.scrollTop + 200
  //                 uni.pageScrollTo({
  //                     scrollTop: scrollData.scrollTop + 200,
  //                     duration: 300
  //                 });
  //             }).exec();
  //     }
  // }).exec();
  currentIndex.value = index;
};
const onDelete = (item) => {
  App.$axios
    .get(App.$api.deleteDiaryList, {
      diaryId: item.diaryId,
    })
    .then((res) => {
      if (res.data.code == '500') {
        uni.showToast({
          title: res.data.msg || '权限不足',
          icon: 'none',
          duration: 2000, //持续的时间
        });
      } else {
        uni.showToast({
          title: '删除成功',
          icon: 'none',
          duration: 2000, //持续的时间
        });
        pageInit();
      }
    });
};
const getMomDetail = async () => {
  const res = await App.$axios.get(App.$api.getMomDetail, {
    id: momId.value,
    uuid: uuid.value,
  });
  try {
    if (res.data.code == 200) {
      const data = res?.data?.data;
      if (data?.serviceStartDate) {
        data.serviceStartDate = data.serviceStartDate.slice(0, 10);
      }
      const employees = data.employees;
      let roles = uni.getStorageSync('roles');
      if (roles[0] == 'BOSS' || roles[0] == 'SALES') {
        employees.push({
          photos:
            'http://cdn.xiaodingdang1.com/2025/03/19/853ff26d43ab40ab9151622ee2cffd9e.png',
          name: '服务人员',
          ifId: 1,
        });
      }
      momDetail.value = data;
      var newDateTime = Date.parse(new Date());
      let datas = {
        eventType: 'PAGE_VIEW',
        pageUrl: '/pageA/pageB/community/note/notedetail',
        module: 'diary',
        eventTime: newDateTime,
        platform: data.name,
        pageTitle: '服务笔记',
        additionalData: { momId: data.momId },
      };
      let iftoken = uni.getStorageSync('token');
      if (iftoken) {
        App.$point.basePoint(datas);
      }
    }
  } catch (e) {}
};
const getMomList = async () => {
  let data = {
    pageSize: 999,
    pageNum: 1,
    momId: momId.value,
    uuid: uuid.value,
  };
  showData.value = [];
  checkInDayList.value = [];
  store.commit('m_user/changeLoading', true);
  const res = await App.$axios.get(App.$api.getDiaryList, data);
  try {
    if (res.data.code == 200) {
      let data = res.data.rows;
      // 根据timeNumber 分类
      let timeNumberList = [];
      data.forEach((list) => {
        if (list?.createTime) {
          list.endTime = list.createTime.slice(11, list.createTime.length);
          list.createTime = list.createTime.slice(0, 10);
        }
        if (list.imgs && list.imgs.length > 0) {
          list.contentPhotos = list.imgs;
        } else {
          // list.contentPhotos = [
          //     'http://cdn.xiaodingdang1.com/2024/12/06/2638525a978d4d93905491269f790474.png'
          // ]
        }
        // if (list.content.length > 115) {
        //     list.packUpShow = false;
        //     list.moreBtns = true;
        //     list.moreBtn = true;
        // } else {
        //     list.packUpShow = false;
        //     list.moreBtns = false;
        //     list.moreBtn = false;
        // }
        const number = list.timeNumber;
        let serviceNumName = dayList.find((item) => item.value == number).label;
        if (timeNumberList.includes(number)) {
          let index = timeNumberList.findIndex((el) => el == number);
          showData.value[index].serviceNum = number;
          showData.value[index].serviceNumName = serviceNumName;
          showData.value[index].pageList.push(list);
        } else {
          if (list.itemName && list.itemName.length > 0) {
            if (list.itemName && list.itemName.length > 0) {
              // 新增一个空数组 添加border
              if (list.itemName.length % 2 == 1) {
                list.itemName.push('');
              }
            }
            showData.value.push({
              serviceNum: number,
              serviceNumName,
              pageList: [list],
              serviceList: list.itemName,
              showMore: false,
            });
          } else {
            showData.value.push({
              serviceNum: number,
              serviceNumName,
              pageList: [list],
              showMore: false,
            });
          }
          const len = checkInDayList.value.length;
          checkInDayList.value.push({
            label: serviceNumName.slice(2, serviceNumName.length),
            value: number,
            id: 'day-' + (len + 1),
          });
          timeNumberList.push(number);
        }
      });

      // checkListNum.value = timeNumberList
      // allData.value = JSON.parse(JSON.stringify(showData.value))
    }
  } catch (e) {}
  store.commit('m_user/changeLoading', false);
};
// const shareNote = () => {
// console.log('shareNoteshareNoteshareNote')
// if (posterGeneratorRef.value) {
//     posterGeneratorRef.value.getPoster()
// }
// App.$jumpPage('/pageA/pageB/home/<USER>/detail', {
//     staffId: item.userId
// })
// }
const onPoster = (item) => {
  console.log('item', item);
  uni.navigateTo({
    url: `/pageA/poster/poster?type=create&diaryId=${item.diaryId}&momId=${momId.value}&uuid=${item.uuid}`,
  });
  // if (posterGeneratorRef.value) {
  //     posterGeneratorRef.value.getPoster()
  // }
  // App.$jumpPage('/pageA/pageB/home/<USER>/detail', {
  //     staffId: item.userId
  // })
};
const nurseDetail = (item) => {
  if (item.ifId == 1) {
    App.$jumpPage('/pageA/pageB/community/servicestaff/servicestafflist', {
      momId: momDetail._rawValue.momId,
    });
  } else {
    App.$jumpPage('/pageA/pageB/home/<USER>/detail', {
      staffId: item.staffId,
    });
  }
};
const popClick = (type) => {
  if (type == 'service') {
    showPop.value = false;
    App.$jumpPage('/pageA/pageB/community/note/publishservice');
  } else if (type == 'publish') {
    showPop.value = false;
    const params = {
      momId: momId.value,
    };
    App.$jumpPage('/pageA/pageB/community/note/publishnote', params);
  } else {
    showPop.value = !showPop.value;
  }
};
</script>

<style scoped lang="less">
@import './notedetail.less';
</style>
