<template>
    <view class="dynamics">
        <view class="header" v-if="activeList.length > 0">
            <u-tabs ref="tabs1" bar-width="32" :list="activeList" active-color="#FF4F61" inactive-color="#AAAAAA"
                font-size="32" :gutter="gutter" v-model="current" @change="change"></u-tabs>
        </view>
        <view class="more">
            <template v-if="pageList.length > 0">
                <view class="devide-content" v-if="activeList.length > 0"></view>
                <template v-if="current == 0 || current == 2">
                    <view class='content-main'>
                        <waterfall-card-vue class="waterfall-card" ref="waterfallRef" :list="pageList" :title="current">
                        </waterfall-card-vue>
                    </view>
                </template>
                <template v-else>
                    <view class="more-card" v-for="(item, index) in pageList" :key="index">
                        <view class="pd-24">
                            <userdesc-vue :item="item" pageType="detail"></userdesc-vue>
                            <view style="margin-top: 16rpx">
                                <img-content-area-vue :listData="item" :index="index"></img-content-area-vue>
                            </view>
                        </view>
                        <view class="devide"></view>
                        <!-- <view class="discusss5 pd-24">
                            <functionbutton-vue :item="item"></functionbutton-vue>
                        </view> -->
                        <view class="devide-content"></view>
                    </view>
                </template>
            </template>
            <empty-vue v-if="isFinshed && pageList.length == 0"></empty-vue>
        </view>
        <u-back-top :scroll-top="scrollTop" top="800"></u-back-top>
    </view>
</template>

<script>
    import emptyVue from '@/components/empty.vue';
    import functionbuttonVue from '@/components/functionbutton.vue';
    import waterfallCardVue from "@/components/waterfallCard.vue";
    import imgContentAreaVue from "@/components/newimgContentArea.vue";
    import userdescVue from '@/components/newuserdesc.vue';
    export default {
        components: {
            emptyVue,
            functionbuttonVue,
            waterfallCardVue,
            imgContentAreaVue,
            userdescVue
        },
        data() {
            return {
                gutter: 64,
                pageSize: 10,
                pageNum: 1,
                scrollTop: 0,
                types: 1,
                // 0:宝妈社区 1:热门话题 2:服务内容 3:服务笔记 4:我的话题
                current: 0,
                isFinshed: false,
                activeList: [],
                pageList: []
            }
        },
        onPageScroll: function(e) {
            this.scrollTop = e.scrollTop
        },
        onReachBottom() {
            if (this.types == 1 && (this.current == 0 || this.current == 2)) {
                this.pageNum = this.pageNum + 1
                this.getList();
            }
            if (this.types == 1 && (this.current == 1 || this.current == 3 || this.current == 4)) {
                this.pageNum = this.pageNum + 1
                this.getPageList()
            }
        },
        onLoad(options) {
            let roles = uni.getStorageSync('roles') || ''
            if (roles == 'USER') {
                this.activeList = []
                this.getPageList()
            } else if (roles == 'CUSTOMER') {
                this.activeList = [{
                        name: '宝妈社区',
                        current: 0
                    },
                    {
                        name: '热门话题',
                        current: 1
                    },
                ]
                this.gutter = 120
                this.getList()
            } else {
                this.activeList = [{
                        name: '服务内容',
                        current: 2
                    },
                    {
                        name: '服务笔记',
                        current: 3
                    },
                    {
                        name: '我的话题',
                        current: 4
                    },
                ]
                this.gutter = 64
                this.getList()
            }
        },
        methods: {
            detail(item) {
                uni.navigateTo({
                    url: "/pageA/pageB/community/sending?userId=" +
                        item.userId +
                        "&postId=" +
                        item.postId,
                });
            },
            change(index) {
                this.current = this.activeList[index].current
                this.pageNum = 1
                this.pageList = []
                if (this.current == 0 || this.current == 2) {
                    this.getList()
                } else {
                    this.getPageList()
                }
            },
            getPageList() {
				let current=this.current
                let data = {
                    pageSize: this.pageSize,
                    pageNum: this.pageNum,
                };
                this.isFinshed = false
                this.changeLoading(true)
                const api = current == 3 ? this.$api.getServiceDiary : this.$api.getServiceCommunity
                this.$axios.get(api, data).then((res) => {
					console.log(current);
                    if (res.data.code == 200) {
                        let data = current == 3 ? res.data.rows : res.data.data.rows;
                        if (data != null) {
                            let types = data != null ? (data.length == 10 ? 1 : 2) : '';
                            let dtnow = this.pageNum == 1 ? [] : this.pageList;
                            data.forEach((item) => {
                                if (item.content.length > 65) {
                                    item.packUpShow = false;
                                    item.moreBtns = true;
                                    item.moreBtn = true;
                                } else {
                                    item.packUpShow = false;
                                    item.moreBtns = false;
                                    item.moreBtn = false;
                                }
                                var timearr = item.createTime.replace(' ', ':').replace(/\:/g, '-')
                                    .split('-');
                                item.createTimes = timearr[1] + '/' + timearr[2] + ' ' + timearr[3]
								 +
                                    ':' + timearr[4];
                            });
                            this.setData({
                                pageList: dtnow.concat(data),
                                types: types
                            });
                        }

                    }
                }).finally(() => {
                    this.isFinshed = true
                    this.changeLoading(false)
                });
            },
            getList() {
                let data = {
                    pageSize: this.pageSize,
                    pageNum: this.pageNum,
                };
                if (this.postId) {
                    data.postId = this.postId
                }
                this.isFinshed = false
                this.changeLoading(true)
                this.$axios.get(this.$api.getServicePost, data).then((res) => {
                    if (res.data.code == 200) {
                        let data = res.data.rows;
						 console.log(data);
                        let types = data.length == 10 ? 1 : 2;
                        let dtnow = this.pageNum == 1 ? [] : this.pageList;
                        this.setData({
                            pageList: dtnow.concat(data),
                            types: types
                        });
                        this.$nextTick(() => {
                            setTimeout(() => {
                                this.$refs.waterfallRef.visualizeLoadingImages();
                            }, 400)
                        })
                    }
                }).finally(() => {
                    this.isFinshed = true
                    this.changeLoading(false)
                });
            },
        }
    }
</script>

<style lang="less" scoped>
    @import './dynamics.less';
</style>