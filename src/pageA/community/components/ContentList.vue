<template>
  <template v-if="contentList.length > 0">
    <waterfall-card-vue
      class="content-waterfall"
      ref="waterfallListRef"
      :list="contentList"
      @detail="handleContentDetail"
    >
      <template #header>
        <slot name="header"></slot>
      </template>
    </waterfall-card-vue>
  </template>
</template>

<script setup>
import { ref, getCurrentInstance } from 'vue'
import waterfallCardVue from '@/components/waterfallCard.vue'
import { USER_TYPES, PAGE_ROUTES } from '../constants.js'

const { proxy } = getCurrentInstance()

const props = defineProps({
  contentList: {
    type: Array,
    default: () => []
  }
})

const waterfallListRef = ref(null)

// 内部处理详情跳转逻辑
const handleContentDetail = (item) => {
  const params = {
    postId: item.postId,
    userId: item.userId,
  }

  const url = item.type === USER_TYPES.USER
    ? PAGE_ROUTES.USER_DETAIL
    : PAGE_ROUTES.STAFF_DETAIL

  proxy.$jumpPage(url, params)
}

defineExpose({
  waterfallListRef
})
</script>

<style scoped lang="less">
.content-waterfall {
  width: 100%;
}
</style>
