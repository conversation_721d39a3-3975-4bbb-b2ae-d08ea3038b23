<template>
  <view class="tenant-menu-container">
    <view class="tenant-menu">
      <view class="menu-main">
        <!-- 月子套餐 -->
        <view class="menu-left" @click="handleMenuClick(0)">
          <view class="menu-title">
            <text>月子套餐</text>
            <text>专业舒适的房间</text>
          </view>
          <image src="@/static/images/home/<USER>" />
        </view>
        <view class="menu-right">
          <!-- 月子膳食 -->
          <view class="menu-top" @click="handleMenuClick(1)">
            <view class="menu-title">
              <text>月子膳食</text>
              <text>精选食材烹饪</text>
            </view>
            <image src="@/static/images/home/<USER>" />
          </view>
          <!-- 护理团队 -->
          <view class="menu-bottom" @click="handleMenuClick(2)">
            <view class="menu-title">
              <text>护理团队</text>
              <text>贴心专业服务</text>
            </view>
            <image src="@/static/images/home/<USER>" />
          </view>
        </view>
      </view>
      <view class="menu-tabs">
        <view
          class="menu-tab"
          v-for="tab in visibleTabs"
          :key="tab.key"
          @click="handleMenuClick(tab.key)"
        >
          <view class="tab-img">
            <image :src="tab.icon" />
            <image
              v-if="tab.hot"
              src="http://cdn.xiaodingdang1.com/2025/06/20/fd5edb3e2404422ca7d199f848374bad.png"
              class="hot-tag"
              >HOT</image
            >
          </view>
          <view class="tab-text">{{ tab.name }}</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { onMounted, ref, computed } from 'vue';

const tenantId = ref('');

// 统一的菜单配置
const menuItems = {
  // 主菜单项
  0: {
    name: '月子套餐',
    path: '/pageA/pageB/home/<USER>',
    type: 'navigate',
  },
  1: { name: '月子膳食', path: '/pageA/pageB/home/<USER>', type: 'navigate' },
  2: {
    name: '护理团队',
    path: '/pageA/pageB/home/<USER>',
    type: 'navigate',
  },

  // 底部菜单项
  introduce: {
    name: '品牌介绍',
    path: '/pageA/pageB/home/<USER>',
    type: 'navigate',
    icon: 'http://cdn.xiaodingdang1.com/2025/06/20/7e78bc885a74418fa740d3d44dc0ea1c.png',
    showCondition: (tenantId) => !['148757', '705382'].includes(tenantId),
  },
  happyEver: {
    name: '馨喜如初',
    appId: 'wx9869e3ffafa073e2',
    path: 'pages/home/<USER>/index',
    type: 'miniProgram',
    icon: 'http://cdn.xiaodingdang1.com/2025/05/21/9a04614ca5384e978267082144fc9a8e.png',
    showCondition: (tenantId) => ['148757', '705382'].includes(tenantId),
  },
  device: {
    name: '会所设施',
    path: '/pageA/pageB/home/<USER>',
    type: 'navigate',
    icon: 'http://cdn.xiaodingdang1.com/2025/06/20/2177f2713a644e3a87c5e2be9de86c5d.png',
    showCondition: () => true,
  },
  invite: {
    name: '产后康复',
    path: '/pageA/pageB/home/<USER>',
    type: 'navigate',
    icon: 'http://cdn.xiaodingdang1.com/2025/06/20/bed0a270fbec4116b707e61867d296b9.png',
    showCondition: () => true,
  },
  maternitymatron: {
    name: '移动月嫂',
    path: '/pageA/pageB/home/<USER>',
    type: 'navigate',
    icon: 'http://cdn.xiaodingdang1.com/2025/06/20/9b69fcee25314fc9bbc519fc8adf5f07.png',
    showCondition: () => true,
  },
  rehabilitation: {
    name: '宝宝请帖',
    path: '/pageA/pageB/home/<USER>',
    type: 'navigate',
    icon: 'http://cdn.xiaodingdang1.com/2025/06/20/c363de4d152f4fce99eb126110ce1eca.png',
    showCondition: () => true,
    hot: true,
  },
};

// 计算属性：根据tenantId过滤显示的tabs
const visibleTabs = computed(() => {
  const tabKeys = [
    'introduce',
    'happyEver',
    'device',
    'invite',
    'maternitymatron',
    'rehabilitation',
  ];
  return tabKeys
    .map((key) => ({ key, ...menuItems[key] }))
    .filter((tab) =>
      tab.showCondition ? tab.showCondition(tenantId.value) : true,
    );
});

onMounted(() => {
  tenantId.value = uni.getStorageSync('tenantId');
});

// 内部跳转处理函数
const handleMenuClick = (menuKey) => {
  const menuItem = menuItems[menuKey];

  if (!menuItem) {
    console.warn(`未找到菜单配置: ${menuKey}`);
    return;
  }

  if (menuItem.type === 'miniProgram') {
    // 小程序跳转
    uni.navigateToMiniProgram({
      appId: menuItem.appId,
      path: menuItem.path,
      success: () => console.log(`跳转${menuItem.name}成功`),
      fail: (err) => console.error(`跳转${menuItem.name}失败:`, err),
    });
  } else {
    // 普通页面跳转
    uni.navigateTo({
      url: menuItem.path,
      success: () => console.log(`跳转${menuItem.name}成功`),
      fail: (err) => console.error(`跳转${menuItem.name}失败:`, err),
    });
  }
};
</script>

<style lang="scss" scoped>
.tenant-menu-container {
  width: 100%;
  padding: 0 24rpx;
  margin-bottom: 20rpx;

  .tenant-menu {
    border-radius: 20rpx;
    border: 3rpx solid #fff;
    background: rgba(255, 255, 255, 0.6);
    box-shadow: -8rpx -8rpx 8rpx 0px rgba(246, 246, 246, 0.5) inset,
      8rpx 8rpx 8rpx 0px rgba(246, 246, 246, 0.5) inset;
    backdrop-filter: blur(10rpx);
    padding: 14rpx;
  }

  .menu-main {
    display: flex;
    margin-bottom: 20rpx;
  }

  .menu-left {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    justify-content: space-between;
    padding: 16rpx 48rpx 16rpx 24rpx;
    filter: drop-shadow(0px 0px 6px rgba(178, 178, 178, 0.25));

    background: url(@/static/images/home/<USER>/100% 100%
      no-repeat;
    .menu-title {
      width: 100%;
    }
    image {
      width: 134rpx;
      height: 100rpx;
    }
  }
  .menu-right {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    row-gap: 12rpx;
    .menu-top {
      display: flex;
      justify-content: space-between;
      width: 312rpx;
      height: 110rpx;
      padding: 20rpx 30rpx;
      background: url(@/static/images/home/<USER>/100% 100%
        no-repeat;
      filter: drop-shadow(0px 0px 6px rgba(178, 178, 178, 0.25));
      image {
        width: 76rpx;
        height: 88rpx;
      }
    }
    .menu-bottom {
      display: flex;
      justify-content: space-between;
      width: 326rpx;
      height: 110rpx;
      padding: 20rpx 30rpx;
      filter: drop-shadow(0px 0px 6px rgba(178, 178, 178, 0.25));
      background: url(@/static/images/home/<USER>/100% 100%
        no-repeat;
      image {
        width: 90rpx;
        height: 72rpx;
      }
    }
  }

  .menu-title {
    display: flex;
    flex-direction: column;
    text:nth-child(1) {
      color: #333;
      font-size: 32rpx;
      font-weight: 500;
    }
    text:nth-child(2) {
      color: #333;
      font-size: 24rpx;
    }
  }
}

.menu-tabs {
  display: flex;
  justify-content: space-between;
  .menu-tab {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 10rpx;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10rpx;

    gap: 10rpx;
  }

  .tab-img {
    width: 60rpx;
    height: 60rpx;
    position: relative;
    image {
      width: 100%;
      height: 100%;
    }
    .hot-tag {
      position: absolute;
      width: 48rpx;
      height: 32rpx;
      right: -26rpx;
      top: -10rpx;
    }
  }
  .tab-text {
    color: #5c5c5c;
    font-size: 22rpx;
  }
}
</style>
