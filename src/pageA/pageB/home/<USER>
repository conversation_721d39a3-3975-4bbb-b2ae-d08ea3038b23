.main {
    background: #F8F9F9;
    min-height: 100vh;
    .content {
        background-image: url('https://cdn.xiaodingdang1.com/2024/11/09/c1e8a3924cef4dc38e2765ef8cd1f36e.png');
        background-size: cover;
        // background: linear-gradient(to bottom, rgba(255,255,255,1), rgba(255,255,255,0)), url('https://cdn.xiaodingdang1.com/2024/11/09/c1e8a3924cef4dc38e2765ef8cd1f36e.png') no-repeat;
        overflow-x: hidden;
        padding: 184rpx 24rpx 28rpx 24rpx;
        margin-bottom: 28rpx;
          ::-webkit-scrollbar {
              display: none;
          }
        .top-container::-webkit-scrollbar {
            display: none;
        }
        .top-container::after {
            content: '';
            display: block;
            width: 28rpx;
        }
        .top-container {
                 overflow-x: scroll;
                 overflow-y: hidden;
                 white-space: nowrap;
                 .top {
                    height: 574rpx;
                    background-color: white;
                    border-radius: 20rpx
                 }
                 .top1 {
                     // width: 792rpx;
                     // margin-right: 30rpx;
                 }
                 .top2 {
                     width: 668rpx;
                     display: inline-block;
                     margin-right: 28rpx;
                 }
                 .top2:last-child{
                    margin-right: 0;
                }
            }
            .top-container > .top2:last-child::after {
                display: none;
            }
        }

}