import { getEnv, ENV_CONFIG } from '@/utils/config';
import * as TextEncoding from 'text-encoding-shim';

/**
 * SSE (Server-Sent Events) 工具类
 * 用于处理流式数据传输
 */
class SSEClient {
  constructor() {
    this.eventSource = null;
    this.isConnected = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 3;
    this.reconnectDelay = 1000;
  }

  /**
   * 创建SSE连接
   * @param {string} url - SSE接口地址
   * @param {Object} options - 配置选项
   * @param {Function} options.onMessage - 消息回调
   * @param {Function} options.onError - 错误回调
   * @param {Function} options.onOpen - 连接打开回调
   * @param {Function} options.onClose - 连接关闭回调
   * @param {Object} options.headers - 请求头
   */
  connect(url, options = {}) {
    const { onMessage, onError, onOpen, onClose, headers = {} } = options;

    // 关闭已有连接
    this.close();

    try {
      // 构建完整URL
      const baseUrl = ENV_CONFIG[getEnv()].VITE_BASE_API;
      const fullUrl = url.startsWith('http') ? url : `${baseUrl}${url}`;

      // 添加认证头
      const authHeaders = getAuthHeaders();
      const allHeaders = { ...authHeaders, ...headers };

      // 构建带参数的URL（因为SSE不支持自定义headers，需要通过URL参数传递）
      const urlWithAuth = this.buildUrlWithAuth(fullUrl, allHeaders);

      console.log('SSE连接URL:', urlWithAuth);

      // 创建EventSource连接
      this.eventSource = new EventSource(urlWithAuth);

      // 监听连接打开
      this.eventSource.onopen = (event) => {
        console.log('SSE连接已打开', event);
        this.isConnected = true;
        this.reconnectAttempts = 0;
        onOpen && onOpen(event);
      };

      // 监听消息
      this.eventSource.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          onMessage && onMessage(data, event);
        } catch (error) {
          console.error('SSE消息解析失败:', error);
          // 如果不是JSON格式，直接传递原始数据
          onMessage && onMessage(event.data, event);
        }
      };

      // 监听错误
      this.eventSource.onerror = (event) => {
        console.error('SSE连接错误:', event);
        this.isConnected = false;

        if (onError) {
          onError(event);
        } else {
          // 默认错误处理：尝试重连
          this.handleReconnect(url, options);
        }
      };

      // 监听特定事件类型
      this.eventSource.addEventListener('error', (event) => {
        console.error('SSE错误事件:', event);
        this.isConnected = false;
        onClose && onClose(event);
      });

      this.eventSource.addEventListener('close', (event) => {
        console.log('SSE连接关闭:', event);
        this.isConnected = false;
        onClose && onClose(event);
      });
    } catch (error) {
      console.error('创建SSE连接失败:', error);
      onError && onError(error);
    }
  }

  /**
   * 关闭SSE连接
   */
  close() {
    if (this.eventSource) {
      this.eventSource.close();
      this.eventSource = null;
      this.isConnected = false;
    }
  }

  /**
   * 处理重连逻辑
   */
  handleReconnect(url, options) {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      console.log(
        `SSE重连尝试 ${this.reconnectAttempts}/${this.maxReconnectAttempts}`,
      );

      setTimeout(() => {
        this.connect(url, options);
      }, this.reconnectDelay * this.reconnectAttempts);
    } else {
      console.error('SSE重连失败，已达到最大重试次数');
      options.onError && options.onError(new Error('连接失败，请检查网络'));
    }
  }

  /**
   * 构建带认证参数的URL
   * 因为EventSource不支持自定义headers，需要通过URL参数传递认证信息
   */
  buildUrlWithAuth(url, headers) {
    const urlObj = new URL(url);

    // 将headers转换为URL参数
    Object.keys(headers).forEach((key) => {
      urlObj.searchParams.set(key, headers[key]);
    });

    return urlObj.toString();
  }

  /**
   * 检查连接状态
   */
  isConnectedState() {
    return (
      this.isConnected &&
      this.eventSource &&
      this.eventSource.readyState === EventSource.OPEN
    );
  }
}

/**
 * 创建SSE客户端实例
 */
export const createSSEClient = () => {
  return new SSEClient();
};

/**
 * 解析SSE数据流
 * @param {string} sseData - SSE原始数据
 * @returns {Array} 解析后的事件数组
 */
const parseSSEData = (sseData) => {
  try {
    const events = [];
    const chunks = sseData.split('\n\n'); // SSE事件由双换行分隔

    for (const chunk of chunks) {
      if (!chunk.trim()) continue;

      const lines = chunk.split('\n');
      let eventData = {};

      for (const line of lines) {
        const trimmedLine = line.trim();
        if (trimmedLine.startsWith('id:')) {
          eventData.id = trimmedLine.substring(3).trim();
        } else if (trimmedLine.startsWith('event:')) {
          eventData.event = trimmedLine.substring(6).trim();
        } else if (trimmedLine.startsWith('data:')) {
          const dataStr = trimmedLine.substring(5).trim();
          try {
            eventData.data = JSON.parse(dataStr);
          } catch (e) {
            eventData.data = dataStr;
          }
        }
      }

      if (Object.keys(eventData).length > 0) {
        events.push(eventData);
      }
    }

    return events;
  } catch (error) {
    console.error('解析SSE数据失败:', error);
    return [];
  }
};

/**
 * 提取消息内容
 * @param {Object} parsedData - 解析后的SSE数据
 * @returns {string} 消息内容
 */
const extractMessageContent = (parsedData) => {
  try {
    if (!parsedData || !parsedData.data) {
      return '';
    }

    const data = parsedData.data;

    // 根据您提供的数据结构解析
    if (
      data.choices &&
      Array.isArray(data.choices) &&
      data.choices.length > 0
    ) {
      const choice = data.choices[0];
      if (choice.delta && choice.delta.content !== undefined) {
        return choice.delta.content;
      }
    }

    // 兼容其他可能的数据结构
    if (typeof data === 'string') {
      return data;
    }

    if (data.content) {
      return data.content;
    }

    return '';
  } catch (error) {
    console.error('提取消息内容失败:', error);
    return '';
  }
};

/**
 * 检查是否完成
 * @param {Object} parsedData - 解析后的SSE数据
 * @returns {boolean} 是否完成
 */
const isMessageComplete = (parsedData) => {
  try {
    if (!parsedData || !parsedData.data) {
      return false;
    }

    const data = parsedData.data;

    if (
      data.choices &&
      Array.isArray(data.choices) &&
      data.choices.length > 0
    ) {
      const choice = data.choices[0];
      return choice.finish_reason === 'STOP' || choice.finish_reason === 'stop';
    }

    return false;
  } catch (error) {
    console.error('检查完成状态失败:', error);
    return false;
  }
};

/**
 * 发送SSE聊天消息 (使用uni.request的onChunkReceived实现真正的流式响应)
 * @param {Object} data - 消息数据
 * @param {Function} onMessage - 消息回调
 * @param {Function} onError - 错误回调
 * @param {Function} onComplete - 完成回调
 */
export const sendSSEChatMessage = (
  data,
  { onMessage, onError, onComplete },
) => {
  const baseUrl = ENV_CONFIG[getEnv()].VITE_BASE_API;
  const authHeaders = getAuthHeaders();

  let messageBuffer = '';
  let chunkBuffer = '';
  let isComplete = false;
  let requestTask = null;

  console.log('开始发送SSE流式请求，使用onChunkReceived实现真正的流式响应');

  // 创建SSE客户端
  const sseClient = {
    close: () => {
      if (requestTask) {
        console.log('关闭SSE请求');
        requestTask.abort();
        requestTask = null;
      }
      isComplete = true;
    },
  };

  try {
    // 发送请求
    requestTask = uni.request({
      url: `${baseUrl}mp/ai/chat`,
      method: 'POST',
      header: {
        ...authHeaders,
        'Content-Type': 'application/json',
        'Accept': 'text/event-stream',
        'Cache-Control': 'no-cache',
      },
      data: data,
      responseType: 'arraybuffer',
      enableChunked: true, // 启用分块传输

      success: (res) => {
        console.log('SSE流式请求完成:', res.statusCode);
        if (!isComplete) {
          if (res.statusCode === 200) {
            // 处理最后的数据块
            if (chunkBuffer.trim()) {
              processChunkData(chunkBuffer, true);
            }
            // 如果还没有完成标志，手动完成
            if (!isComplete) {
              console.log('流式响应结束，最终内容长度:', messageBuffer.length);
              console.log('最终内容:', messageBuffer);
              onComplete && onComplete(messageBuffer);
            }
          } else {
            console.error('SSE流式请求失败:', res.statusCode, res.data);
            onError && onError(new Error(`请求失败: ${res.statusCode}`));
          }
        }
      },

      fail: (error) => {
        console.error('SSE请求失败:', error);
        if (!isComplete) {
          onError && onError(error);
        }
      },
    });

    // 使用onChunkReceived监听数据块接收 - 实现真正的流式响应
    requestTask.onChunkReceived((chunk) => {
      if (isComplete) return;

      try {
        // 将ArrayBuffer转换为字符串
        const decoder = new TextEncoding.TextDecoder('utf-8'); // 使用text-encoding-shim
        const chunkText = decoder.decode(new Uint8Array(chunk.data));

        console.log('接收到流式数据块:', chunkText.length, '字符');
        console.log('数据块内容:', chunkText);

        // 累积数据块
        chunkBuffer += chunkText;

        // 实时处理完整的SSE事件
        processChunkData(chunkBuffer, false);
      } catch (error) {
        console.error('处理流式数据块失败:', error);
        onError && onError(error);
      }
    });

    // 处理数据块的函数
    const processChunkData = (buffer, isLast = false) => {
      // SSE事件由双换行符分隔
      const events = buffer.split('\n\n');

      if (!isLast && events.length > 0) {
        // 保留最后一个可能不完整的事件
        chunkBuffer = events.pop();
      } else {
        chunkBuffer = '';
      }

      // 处理完整的事件
      for (const eventText of events) {
        if (!eventText.trim()) continue;

        console.log('处理SSE事件:', eventText);

        const eventData = parseSSEEvent(eventText);
        if (eventData) {
          // 提取消息内容
          const content = extractMessageContent(eventData);
          console.log('提取的内容:', content);

          if (content) {
            messageBuffer += content;
            // 实时回调
            onMessage && onMessage(content, messageBuffer);
          }

          // 检查是否完成
          if (isMessageComplete(eventData)) {
            console.log('检测到完成标志');
            isComplete = true;
            onComplete && onComplete(messageBuffer);
            return;
          }
        }
      }
    };
  } catch (error) {
    console.error('创建SSE请求失败:', error);
    onError && onError(error);
  }

  return sseClient;
};

/**
 * 解析单个SSE事件
 * @param {string} eventText - 单个SSE事件文本
 * @returns {Object|null} 解析后的事件数据
 */
const parseSSEEvent = (eventText) => {
  try {
    const lines = eventText.split('\n');
    let eventData = {};

    for (const line of lines) {
      const trimmedLine = line.trim();
      if (trimmedLine.startsWith('id:')) {
        eventData.id = trimmedLine.substring(3).trim();
      } else if (trimmedLine.startsWith('event:')) {
        eventData.event = trimmedLine.substring(6).trim();
      } else if (trimmedLine.startsWith('data:')) {
        const dataStr = trimmedLine.substring(5).trim();
        try {
          eventData.data = JSON.parse(dataStr);
        } catch (e) {
          eventData.data = dataStr;
        }
      }
    }

    return Object.keys(eventData).length > 0 ? eventData : null;
  } catch (error) {
    console.error('解析SSE事件失败:', error);
    return null;
  }
};

/**
 * 获取认证头
 */
const getAuthHeaders = () => {
  const headers = {
    'Clientid': '428a8310cd442757ae699df5d894f051',
    'X-Tenant-ID': '194338',
  };

  if (uni.getStorageSync('token')) {
    headers['Authorization'] = 'Bearer ' + uni.getStorageSync('token');
  }

  if (uni.getStorageSync('tenantId')) {
    headers['X-Tenant-ID'] = uni.getStorageSync('tenantId');
  }

  return headers;
};

/**
 * 测试SSE数据解析
 * @param {string} testData - 测试数据
 */
export const testSSEParsing = (testData) => {
  console.log('=== SSE解析测试 ===');
  console.log('原始数据:', testData);

  const events = parseSSEData(testData);
  console.log('解析后的事件:', events);

  let messageBuffer = '';
  let isComplete = false;

  for (const event of events) {
    const content = extractMessageContent(event);
    const complete = isMessageComplete(event);

    console.log(`事件ID: ${event.id}, 内容: "${content}", 完成: ${complete}`);

    messageBuffer += content;
    if (complete) {
      isComplete = true;
    }
  }

  console.log('最终消息:', messageBuffer);
  console.log('是否完成:', isComplete);
  console.log('=== 测试结束 ===');

  return { messageBuffer, isComplete, events };
};

export default {
  createSSEClient,
  sendSSEChatMessage,
  testSSEParsing,
};
