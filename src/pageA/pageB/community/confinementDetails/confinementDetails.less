// 主容器
.confinement-details {
  background: #f6f6f6;
}

// 通用样式
.section-title {
  color: #333333;
  font-size: 32rpx;
  font-weight: bold;

  &--bold {
    font-weight: bold;
  }
}

// 图片画廊
.image-gallery {
  .gallery-swiper {
    // swiper样式保持原有功能
  }
}

// 价格区域
.price-section {
  background: #fff;
  padding: 32rpx 24rpx;

  .price-info {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .price-amount {
      color: #fd2d39;
      font-size: 56rpx;
      font-weight: bold;
    }

    .consult-count {
      color: #aaaaaa;
      font-size: 24rpx;
    }
  }

  .package-info {
    margin-top: 32rpx;

    .package-header {
      width: 100%;

      .package-title-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;

        .package-name {
          color: #333333;
          font-size: 36rpx;
          font-weight: bold;
        }

        .share-button-wrapper {
          width: 20%;
          height: 50rpx;

          .share-button {
            display: flex;
            align-items: center;
            background: #ffffff;
            font-size: 24rpx;
            color: #777777;

            .share-icon {
              margin-right: 10rpx;
              width: 26rpx;
              height: 26rpx;
            }
          }
        }
      }

      .service-tags {
        display: flex;
        width: 100%;
        margin-top: 20rpx;
        align-items: center;

        .service-label {
          font-weight: 400;
          font-size: 28rpx;
          color: #aaaaaa;
          margin-right: 32rpx;
          padding: 4rpx 12rpx;
        }

        .tags-scroll {
          flex: 1;
          display: flex;
          justify-content: center;
          overflow-x: scroll;
          overflow-y: hidden;
          white-space: nowrap;

          .tag-item {
            background-color: #f8f8f8;
            display: inline-block;
            font-weight: 400;
            font-size: 24rpx;
            color: #333333;
            padding: 4rpx 12rpx;
            margin-right: 24rpx;
            border-radius: 6rpx;
          }
        }
      }
    }
  }
}
// 设施区域
.facility-section {
  padding: 24rpx 24rpx;
  background: #fff;
  margin-top: 20rpx;

  .facility-grid {
    width: 100%;
    display: flex;
    flex-wrap: wrap;

    .facility-item {
      display: flex;
      width: 50%;
      align-items: center;
      height: 108rpx;

      .facility-icon {
        width: 47rpx;
        height: 47rpx;
      }

      .facility-content {
        margin-left: 20rpx;

        .facility-label {
          color: #929292;
          font-size: 20rpx;
        }

        .facility-value {
          color: #333333;
          font-size: 22rpx;
          display: flex;
        }
      }
    }
  }
}
// 配套设施区域
.ancillary-section {
  background: #fff;
  padding: 24rpx 24rpx;
  margin-top: 20rpx;

  .ancillary-list {
    .ancillary-category {
      margin-top: 22rpx;

      .category-header {
        display: flex;
        align-items: center;

        .category-icon {
          width: 30rpx;
          height: 30rpx;
        }

        .category-title {
          color: #333333;
          font-size: 26rpx;
          font-weight: bold;
          margin-left: 6rpx;
        }
      }

      .feature-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        margin-top: 22rpx;
        gap: 10px;
        width: 90%;
        margin: 10px auto;

        .feature-item {
          text-align: left;
          color: #666666;
          font-size: 22rpx;
        }
      }
    }
  }
}

.serviceContent {
  background: #fff;
  padding: 24rpx 24rpx;
  .serviceContent1 {
    .serviceContent2 {
      border: 1rpx solid #f1f1f1;
      border-radius: 6rpx;
      margin-top: 20rpx;
      .serviceContent2_1 {
        color: #333333;
        font-size: 24rpx;
        font-weight: bold;
        padding: 16rpx 20rpx;
        background: #f6f6f6;
      }
      .serviceContent2_2 {
        display: flex;
        font-size: 22rpx;
        padding: 16rpx 20rpx;
        border-top: 1rpx solid #f1f1f1;
        .serviceContent2_2_1 {
          color: #666666;
          width: 20%;
        }
        .serviceContent2_2_2 {
          color: #333333;
        }
      }
      .serviceContent3 {
        display: flex;
        font-size: 22rpx;
        padding: 12rpx 20rpx;
        width: 100%;
        .serviceContent3_1 {
          width: 25%;
          color: #666666;
        }
        .serviceContent4 {
          width: 75%;
          display: flex;
          justify-content: space-between;
          color: #333333;
          .serviceContent4_1 {
            display: flex;
            align-items: center;
            .serviceContent4_3 {
              background: #fdf1ed;
              color: #eb5735;
              width: 24rpx;
              height: 24rpx;
              border-radius: 100rpx;
              font-size: 20rpx;
              text-align: center;
              line-height: 24rpx;
              margin-right: 10rpx;
            }
          }
        }
      }
    }
  }
}
#serviceContent3 {
  border-top: 1rpx solid #f1f1f1;
}
.serviceContent5 {
  display: flex;
  justify-content: space-between;
  padding: 16rpx 20rpx;
  .serviceContent5_1 {
    display: flex;
    align-items: center;
    .serviceContent5_2 {
      background: #fdf1ed;
      color: #eb5735;
      width: 24rpx;
      height: 24rpx;
      border-radius: 100rpx;
      font-size: 20rpx;
      text-align: center;
      line-height: 24rpx;
      margin-right: 10rpx;
    }
    .serviceContent5_3 {
      color: #333333;
      font-size: 22rpx;
    }
  }
  .serviceContent5_4 {
    color: #666666;
    font-size: 22rpx;
  }
}
.imageText {
  image {
    width: 100%;
    border-radius: 12rpx;
    margin-top: 20rpx;
  }
}
.brandTitle {
  text-align: center;
  color: #5c5c5c;
  font-size: 32rpx;
  font-weight: bold;
  margin: 0rpx 0 20rpx 0;
}
.brandComent {
  margin-top: 28rpx;
  .brandComent1 {
    display: flex;
    align-items: center;
    padding: 10rpx 0;
    width: 100%; /* 宽度占满容器 */
    flex-wrap: wrap;
    .brandComent1_3 {
      width: 2%;
      .brandComent1_1 {
        width: 10rpx;
        height: 10rpx;
        border-radius: 100rpx;
        background: #fd2d39;
      }
    }
    .brandComent1_2 {
      color: #333333;
      font-size: 24rpx;
      margin-left: 10rpx;
      width: 98%;
    }
  }
}
.brandImg {
  display: flex;
  margin: 28rpx 0;
  justify-content: space-between;
  .brandImg1 {
    width: 48%;
    image {
      width: 100%;
      border-radius: 8rpx;
    }
  }
}
.viewMore {
  border-radius: 100rpx;
  background: #ff4f61;
  width: 156rpx;
  height: 52rpx;
  text-align: center;
  line-height: 52rpx;
  color: #fff;
  font-size: 22rpx;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
}
.recommend {
  display: flex;
  align-items: center;
  .recommend_1 {
    background: #ff4f61;
    height: 28rpx;
    width: 8rpx;
    border-radius: 10rpx;
  }
  .recommend_2 {
    color: #333333;
    font-size: 34rpx;
    font-weight: bold;
    margin-left: 20rpx;
  }
}
.recommend1 {
  width: 100%;
  margin: 20rpx 0;
  display: flex;
  justify-content: space-between;
  .recommend1_1 {
    width: 48%;
    text-align: center;
    .recommend1_2 {
      width: 340rpx;
      height: 340rpx;
      image {
        width: 100%;
        height: 340rpx;
      }
    }
    .recommend1_3 {
      color: #333333;
      font-size: 30rpx;
      font-weight: bold;
      margin: 10rpx 0;
    }
    .recommend1_4 {
      color: #777777;
      font-size: 24rpx;
    }
    .recommend1_5 {
      width: 136rpx;
      height: 58rpx;
      border-radius: 100rpx;
      color: #fff;
      background: #ff4f61;
      font-size: 24rpx;
      line-height: 58rpx;
      margin: 20rpx auto;
    }
  }
}
.blank {
  height: 240rpx;
}
.fit-img {
  width: 100%;
  height: 100%;
  border-radius: 10rpx;
  background-size: cover;
  background-position: center;
}
.my_text {
  word-break: break-word; //可以避免文字超出限制
}
.serveTab {
  display: flex;
  width: 100%;
  margin-top: 20px;
  align-items: center;
  .serveTab1 {
    font-weight: 400;
    font-size: 28rpx;
    color: #aaaaaa;
    margin-right: 32rpx;
    padding: 4rpx 12rpx;
  }
  .serveTab2 {
    flex: 1;
    display: flex;
    justify-content: center;
    overflow-x: scroll;
    overflow-y: hidden;
    white-space: nowrap;
    .serveTab2_1 {
      background-color: #f8f8f8;
      display: inline-block;
      font-weight: 400;
      font-size: 24rpx;
      color: #333333;
      padding: 4rpx 12rpx;
      margin-right: 24rpx;
    }
  }
}
.roomMessage2_6 {
  width: 100%;
  display: flex;
  justify-content: space-between;
  .roomMessage2_4 {
    width: 20%;
    height: 50rpx;
    .image-text-button {
      display: flex; /* 使用flex布局 */
      align-items: center; /* 垂直居中 */
      background: #ffffff;
      font-size: 24rpx;
      color: #777777;
      .button-icon {
        margin-right: 10rpx; /* 图片与文字的间距 */
        width: 26rpx; /* 图片宽度 */
        height: 26rpx; /* 图片高度 */
      }
    }
  }
}

.scroll-container {
  display: flex;
  white-space: nowrap;
  height: 110rpx;
  .scroll-item {
    display: inline-block;
    background: #fdf1ed;
    border-radius: 6rpx;
    width: 172rpx;
    height: 72rpx;
    color: #5c5c5c;
    font-size: 26rpx;
    margin-right: 20rpx;
    text-align: center;
    line-height: 72rpx;
  }
}
.box-with-arrow {
  position: relative;
  display: inline-block;
  background: #ff4f61;
  border-radius: 6rpx;
  width: 172rpx;
  height: 72rpx;
  color: #5c5c5c;
  font-size: 26rpx;
  margin-right: 20rpx;
  text-align: center;
  line-height: 72rpx;
  color: #fff;
}

/* 尖角实现 */
.box-with-arrow1 {
  content: '';
  position: absolute;
  left: 50%;
  bottom: -8px; /* 调整尖角位置 */
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-top: 10px solid #ff4f61; /* 尖角颜色与边框一致 */
}
.menuTab {
  display: flex;
  width: 100%;
  margin-top: 20rpx;
  .menuTab1 {
    padding: 20rpx 0;
    display: flex;
    width: 50%;
    background: #fff;
    align-items: center;
    justify-content: space-around;
    border-top-left-radius: 50rpx;
    border-top-right-radius: 50rpx;
    .menuTab1_1 {
      color: #333333;
      font-size: 30rpx;
      font-weight: bold;
    }
  }
  .menuTab2 {
    padding: 20rpx 0;
    display: flex;
    width: 50%;
    background: #ededed;
    align-items: center;
    justify-content: space-around;
    border-top-left-radius: 50rpx;
    border-top-right-radius: 50rpx;
    .menuTab1_2 {
      color: #9c9c9c;
      font-size: 30rpx;
      font-weight: bold;
    }
  }
}
