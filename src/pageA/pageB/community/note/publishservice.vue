<template>
    <view class="publish-service">
        <view class="content">
            <view class="title item">
                创建设置
            </view>
            <view class="item">
                <view class="left">
                    服务天数
                </view>
                <view class="right top-service">
                    <u-input placeholder="请选择服务天数" v-model="timeNumber" type="select"
                        @click="() => selectShow = true" />
                </view>
            </view>
            <view class="item">
                <view class="left">
                    服务项
                </view>
                <view class="right">
                    <view class="list" v-for="(item, index) in inputList" :key="index">
                        <u-input maxlength="10" placeholder="请输入服务项" v-model="form.names[index]" type="input" />
                        <u-icon name="minus-circle" size="36" color="#AAAAAA" @tap="operate(index)"></u-icon>
                    </view>
                </view>
            </view>
            <view class="add" @tap="operate('add')">
                <image src="http://cdn.xiaodingdang1.com/2025/01/06/4e5fa6829d7c496a985c71516f900c70.png"></image>
            </view> 
            <u-select v-model="selectShow" :list="dayList" @confirm="onSelected"></u-select>
        </view>
        <view class="bottom">
            <view>
                <u-button :plain="true" :ripple="true" shape="circle" type="primary" @click="back">取消</u-button>
            </view>
            <view>
                <u-button :ripple="true" shape="circle" type="primary" @click="submit">提交</u-button>
            </view>
        </view>
        <new-request-loading></new-request-loading>
    </view>
</template>

<script setup>
    import utils from '@/utils/util.js'
    import {
        onShow,
        onHide,
        onLoad,
        onPageScroll,
        onReachBottom,
        onShareAppMessage,
        onShareTimeline
    } from "@dcloudio/uni-app";
    import {
        ref,
        nextTick,
        reactive,
        getCurrentInstance,
        onMounted
    } from "vue";
    const instance = getCurrentInstance();
    const App = instance.appContext.config.globalProperties
    onShareAppMessage(() => {

    })
    onShareTimeline(() => {

    })
    const dayList = utils.getCheckInDayList()
    const selectShow = ref(false)
    const form = ref({
        timeNumber: '',
        names: ['', '', '', '']
    })
    const timeNumber = ref('')
    const inputList = ref(['', '', '', ''])
    const onSelected = (options) => {
        timeNumber.value = options[0].label
        form.value.timeNumber = options[0].value
        getNoteItem()
    }
    const operate = (type) => {
        if (type == 'add') {
            inputList.value.push('')
        } else {
            form.value.names.splice(type, 1)
            inputList.value.splice(type, 1)
        }
    }
    const submit = async () => {
        const res = await App.$axios.post(App.$api.pushDiaryItem, form.value)
        if (res?.data) {
            uni.$emit('publishon')
            back()
        }
    }
    const back = () => {
        uni.navigateBack({
            delta: 1
        })
    }
    const getNoteItem = async () => {
        const res = await App.$axios.get(App.$api.getDiaryItem, {
            'timeNumber': form.value.timeNumber
        })
        const list = res.data?.data?.names
        if (list && list.length > 0) {
            form.value.names = list
            inputList.value = list
        } else {
            form.value.names = ['', '', '', '']
            inputList.value = ['', '', '', '']
        }
    }
</script>
<style scoped lang="less">
    @import './publishservice.less';
</style>