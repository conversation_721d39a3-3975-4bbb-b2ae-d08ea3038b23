<script>
import $api from './utils/api.js';
import $axios from './utils/request';
import $auth from './utils/auth.js';
import store from '@/store/index';

// 常量定义
const DEFAULT_TENANT_ID = uni.getStorageSync("tenantId")
  ? uni.getStorageSync("tenantId")
  : "194338";
const SUCCESS_CODE = 200;

// 小程序场景值常量
const SCENE_CODES = {
  QR_CODE_SCAN: [1047, 1048], // 小程序码扫码进入
  RECENT_USED: 1089, // 微信聊天主界面下拉「最近使用」
  CARD_MESSAGE: 1036, // 小程序中的卡片消息
  SINGLE_CHAT: 1007, // 单人聊天会话中的小程序消息卡片
  GROUP_CHAT: 1008, // 群聊会话中的小程序消息卡片
  QR_SCAN: 1011, // 扫描二维码
  DISCOVER_LIST: 1001, // 发现页小程序「最近使用」列表
  SEARCH_RESULT: 1005, // 微信首页顶部搜索框的搜索结果页
  DISCOVER_SEARCH: 1006, // 发现栏小程序主入口搜索框的搜索结果页
};

// 分享白名单路径
const SHARE_WHITELIST = [
  'pageA/pageB/home/<USER>/share',
  'pageA/pageB/home/<USER>/preview',
  'pageA/aiService/aiService',
];

export default {
  globalData: {
    tenantId: DEFAULT_TENANT_ID,
    system: null,
    capsule: null,
    callback: () => {},

    // ==================== 初始化相关方法 ====================

    /**
     * 初始化应用
     */
    async initializeApp() {
      try {
        // 处理入口逻辑
        this.handleAppEntry();

        // 检查版本更新
        this.checkAppUpdate();

        // 设置全局分享
        this.setupGlobalShare();

        // 获取系统信息
        await this.getSystemInfo();

        // 获取胶囊信息
        this.getCapsuleInfo();
      } catch (error) {
        console.error('应用初始化失败:', error);
      }
    },

    // ==================== 数据获取相关方法 ====================

    /**
     * 获取会所名称
     */
    async fetchClubName() {
      try {
        const res = await $axios.get($api.clubName);
        if (res.data.code === SUCCESS_CODE) {
          uni.setStorageSync('clubName', res.data.data);
          return res.data.data;
        }
      } catch (error) {
        console.error('获取会所名称失败:', error);
      }
    },

    /**
     * 获取会所信息
     */
    async fetchClubInfo() {
      try {
        const res = await $axios.get($api.clubInfo);
        if (res.data.code === SUCCESS_CODE) {
          uni.setStorageSync('clubInfo', res.data.data);
          return res.data.data;
        }
      } catch (error) {
        console.error('获取会所信息失败:', error);
      }
    },

    /**
     * 获取系统信息
     */
    async getSystemInfo() {
      return new Promise((resolve) => {
        uni.getSystemInfo({
          success: (res) => {
            this.system = res;
            resolve(res);
          },
          fail: (error) => {
            console.error('获取系统信息失败:', error);
            resolve(null);
          },
        });
      });
    },

    /**
     * 获取胶囊信息
     */
    getCapsuleInfo() {
      try {
        this.capsule = uni.getMenuButtonBoundingClientRect();
      } catch (error) {
        console.error('获取胶囊信息失败:', error);
      }
    },

    // ==================== 入口处理相关方法 ====================

    /**
     * 解析小程序码参数
     */
    parseSceneQuery(query) {
      const params = {};
      if (!query) return params;

      try {
        const pairs = query.split('&');
        pairs.forEach((pair) => {
          const [key, value] = pair.split('-');
          if (key && value) {
            params[key] = decodeURIComponent(value);
          }
        });
      } catch (error) {
        console.error('解析场景参数失败:', error);
      }

      return params;
    },

    /**
     * 处理小程序入口逻辑
     */
    handleAppEntry() {
      try {
        const options = uni.getEnterOptionsSync();
        const currentTenantId = uni.getStorageSync('tenantId');
        let newTenantId = '';

        if (options?.query?.scene) {
          // 扫码进入
          console.log('扫码进入:', options);
          const scene = decodeURIComponent(options.query.scene);
          const params = this.parseSceneQuery(scene);
          newTenantId = params.tenantId;
        } else if (options?.query?.tenantId) {
          // 分享进入
          console.log('分享进入:', options);
          newTenantId = options.query.tenantId;
        } else if (!currentTenantId) {
          // 默认进入
          console.log('默认进入:', options);
          newTenantId = DEFAULT_TENANT_ID;
        }

        // 如果租户ID发生变化，重置状态
        if (newTenantId && newTenantId !== currentTenantId) {
          this.resetUserStatus();
          uni.setStorageSync('tenantId', newTenantId);
          console.log('租户ID已更新:', newTenantId);
        }
      } catch (error) {
        console.error('处理入口逻辑失败:', error);
      }
    },

    /**
     * 检查场景值是否需要处理入口逻辑
     */
    shouldHandleEntry(scene) {
      const entryScenes = [
        ...SCENE_CODES.QR_CODE_SCAN,
        SCENE_CODES.SINGLE_CHAT,
        SCENE_CODES.GROUP_CHAT,
      ];
      return entryScenes.includes(scene);
    },

    // ==================== 分享相关方法 ====================

    /**
     * 设置全局分享监听
     */
    setupGlobalShare() {
      uni.onAppRoute((res) => {
        console.log('页面路由切换:', res);

        // 检查是否在分享白名单中
        if (SHARE_WHITELIST.includes(res.path)) {
          return;
        }

        try {
          const currentPage = getCurrentPages().pop();
          if (!currentPage) return;

          const tenantId = uni.getStorageSync('tenantId');
          const shareUrl = this.buildShareUrl(currentPage, tenantId);

          // 设置页面分享配置
          currentPage.onShareAppMessage = () => ({
            withShareTicket: true,
            title: uni.getStorageSync('clubName') || '仟辰科技',
            path: shareUrl,
            success: (shareRes) => {
              console.log('分享成功:', shareRes);
            },
            fail: (error) => {
              console.error('分享失败:', error);
            },
          });
        } catch (error) {
          console.error('设置分享配置失败:', error);
        }
      });
    },

    /**
     * 构建分享URL
     */
    buildShareUrl(currentPage, tenantId) {
      const baseUrl = `/${currentPage.route}`;
      const options = currentPage.options || {};

      // 构建查询参数
      const params = Object.keys(options)
        .map((key) => `${key}=${encodeURIComponent(options[key])}`)
        .join('&');

      // 添加 tenantId 参数
      const finalParams = params
        ? `${params}&tenantId=${tenantId}`
        : `tenantId=${tenantId}`;

      return `${baseUrl}?${finalParams}`;
    },

    // ==================== 更新相关方法 ====================

    /**
     * 检查应用更新
     */
    checkAppUpdate() {
      try {
        const updateManager = uni.getUpdateManager();

        updateManager.onCheckForUpdate((res) => {
          if (res.hasUpdate) {
            updateManager.onUpdateReady(() => {
              this.showUpdateDialog();
            });
          }
        });
      } catch (error) {
        console.error('检查更新失败:', error);
      }
    },

    /**
     * 显示更新对话框
     */
    showUpdateDialog() {
      uni.showModal({
        title: '更新提示',
        content: '新版本已经准备好，是否重启应用？',
        success: (res) => {
          if (res.confirm) {
            uni.getUpdateManager().applyUpdate();
          } else {
            this.showForceUpdateDialog();
          }
        },
      });
    },

    /**
     * 显示强制更新对话框
     */
    showForceUpdateDialog() {
      uni.showModal({
        title: '温馨提示~',
        content: '本次版本更新涉及到新的功能添加，旧版本无法正常访问的哦~',
        confirmText: '确定更新',
        cancelText: '取消更新',
        success: (res) => {
          if (res.confirm) {
            uni.getUpdateManager().applyUpdate();
          }
        },
      });
    },

    // ==================== 用户状态管理相关方法 ====================

    /**
     * 清除用户存储信息
     */
    clearUserStorage() {
      const keysToRemove = [
        'uid',
        'userInfo',
        'permissions',
        'roles',
        'hasBindPhone',
        'clubName',
      ];

      keysToRemove.forEach((key) => {
        uni.removeStorageSync(key);
      });
    },

    /**
     * 重置用户状态
     */
    resetUserStatus() {
      console.log('重置用户登录状态');

      try {
        // 清除认证相关信息
        uni.removeStorageSync('token');
        uni.removeStorageSync('tenantId');

        // 更新 store 状态
        store.commit('m_user/setLoginStatus', false);

        // 清除用户信息
        this.clearUserStorage();
      } catch (error) {
        console.error('重置用户状态失败:', error);
      }
    },

    /**
     * 更新未读消息数量
     */
    async updateUnreadCounts() {
      console.log('updateUnreadCounts 方法开始执行');
      try {
        // 检查是否已登录
        const token = uni.getStorageSync('token');
        console.log('当前token:', token ? '已登录' : '未登录');

        if (!token) {
          // 未登录时清空未读数量
          console.log('未登录，清空未读数量');
          store.commit('m_user/clearUnreadCounts');
          return;
        }

        // 调用API获取未读消息数量
        console.log('准备调用API:', $api.getUnreadMessageCount);
        const res = await $axios.get($api.getUnreadMessageCount);
        console.log('API调用完成，响应:', res.data);

        if (res.data.code === 200 && res.data.data) {
          const counts = res.data.data;
          console.log('API返回的未读消息数量:', counts);

          // 计算总未读数量
          const userCount = counts.userUnreadCount || 0;
          const agentCount = counts.agentUnreadCount || 0;
          const totalCount = userCount + agentCount;

          // 更新Vuex状态
          store.commit('m_user/setUnreadCounts', {
            userUnreadCount: userCount,
            agentUnreadCount: agentCount,
            totalUnreadCount: totalCount,
          });

          console.log('未读消息数量已更新到Vuex:', {
            userUnreadCount: userCount,
            agentUnreadCount: agentCount,
            totalUnreadCount: totalCount,
          });

          // 验证Vuex状态
          const currentState = store.getters['m_user/getTotalUnreadCount'];
          console.log('Vuex中的总未读数量:', currentState);
        } else {
          console.log('API响应异常:', res.data);
        }
      } catch (error) {
        console.error('获取未读消息数量失败:', error);
      }
    },
  },

  // ==================== 生命周期方法 ====================

  /**
   * 应用启动时的处理
   */
  async onLaunch() {
    try {
      // 记录启动场景
      this.logLaunchScene();

      // 初始化应用
      await this.globalData.initializeApp();

      // 获取会所信息
      await this.globalData.fetchClubInfo();

      // 将更新未读数量方法暴露给全局
      getApp().updateUnreadCounts = this.globalData.updateUnreadCounts;
    } catch (error) {
      console.error('应用启动失败:', error);
    }
  },

  /**
   * 应用显示时的处理
   */
  onShow() {
    try {
      console.log('App.vue onShow 开始执行');

      // 检查登录状态
      this.checkLoginStatus();

      // 处理场景值
      this.handleSceneOnShow();

      // 更新未读消息数量
      console.log('准备调用 updateUnreadCounts');
      this.globalData.updateUnreadCounts();
    } catch (error) {
      console.error('onShow 处理失败:', error);
    }
  },

  // ==================== 方法 ====================

  methods: {
    /**
     * 记录启动场景
     */
    logLaunchScene() {
      const options = uni.getEnterOptionsSync();
      const scene = options.scene;

      console.log('应用启动场景值:', scene);
      console.log('启动参数:', options);

      // 记录各种场景的详细信息
      const sceneDescriptions = {
        [SCENE_CODES.QR_CODE_SCAN[0]]: '小程序码扫码进入',
        [SCENE_CODES.QR_CODE_SCAN[1]]: '小程序码识别进入',
        [SCENE_CODES.RECENT_USED]: '微信聊天主界面下拉「最近使用」栏',
        [SCENE_CODES.CARD_MESSAGE]: '小程序中的卡片消息',
        [SCENE_CODES.SINGLE_CHAT]: '单人聊天会话中的小程序消息卡片',
        [SCENE_CODES.GROUP_CHAT]: '群聊会话中的小程序消息卡片',
        [SCENE_CODES.QR_SCAN]: '扫描二维码',
        [SCENE_CODES.DISCOVER_LIST]: '发现页小程序「最近使用」列表',
        [SCENE_CODES.SEARCH_RESULT]: '微信首页顶部搜索框的搜索结果页',
        [SCENE_CODES.DISCOVER_SEARCH]: '发现栏小程序主入口搜索框的搜索结果页',
      };

      const description = sceneDescriptions[scene] || `未知场景(${scene})`;
      console.log('场景描述:', description);
    },

    /**
     * 检查登录状态
     */
    checkLoginStatus() {
      const token = uni.getStorageSync('token');
      if (token) {
        store.commit('m_user/setLoginStatus', true);
      } else {
        this.globalData.clearUserStorage();
      }
    },

    /**
     * 处理 onShow 时的场景值
     */
    handleSceneOnShow() {
      const options = uni.getEnterOptionsSync();
      const scene = options.scene;

      console.log('应用显示场景值:', scene);

      if (this.globalData.shouldHandleEntry(scene)) {
        console.log('处理入口场景:', this.getSceneDescription(scene));
        this.globalData.handleAppEntry();
      }
    },

    /**
     * 获取场景描述
     */
    getSceneDescription(scene) {
      const sceneMap = {
        [SCENE_CODES.QR_CODE_SCAN[0]]: '小程序码扫码进入',
        [SCENE_CODES.QR_CODE_SCAN[1]]: '小程序码识别进入',
        [SCENE_CODES.SINGLE_CHAT]: '单人聊天会话中的小程序消息卡片',
        [SCENE_CODES.GROUP_CHAT]: '群聊会话中的小程序消息卡片',
      };
      return sceneMap[scene] || `未知场景(${scene})`;
    },
  },
};
</script>
<style lang="scss">
@import './uni_modules/vk-uview-ui/index.scss';
</style>

<style>
@import '@/styles/theme.css';
@import './app.css';
@import './static/iconfont/iconfont.css';
</style>
