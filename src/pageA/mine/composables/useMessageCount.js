import { ref, computed } from 'vue'
import { getCurrentInstance } from 'vue'

export function useMessageCount() {
  const { proxy } = getCurrentInstance()

  const unreadMessageCount = ref(0) // 客服未读消息数量
  const messageUnreadCount = ref(0) // 普通消息未读数量

  // 获取未读客服消息数量
  const getUnreadMessageCount = () => {
    // 从 Vuex 获取客服未读消息数量
    const agentCount = proxy.getAgentUnreadCount
    console.log('mine.vue - 客服未读数量:', agentCount)
    unreadMessageCount.value = agentCount
  }

  // 获取未读普通消息数量
  const getMessageUnreadCount = () => {
    // 从 Vuex 获取用户未读消息数量
    const userCount = proxy.getUserUnreadCount
    console.log('mine.vue - 用户未读数量:', userCount)
    messageUnreadCount.value = userCount
  }

  // 重置消息未读数量
  const resetMessageUnreadCount = () => {
    messageUnreadCount.value = 0
  }

  return {
    unreadMessageCount,
    messageUnreadCount,
    getUnreadMessageCount,
    getMessageUnreadCount,
    resetMessageUnreadCount
  }
}
