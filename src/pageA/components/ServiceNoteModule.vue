<template>
  <view class="service-note-module" v-if="goodMomList?.length">
    <!-- 标题区域 -->
    <view class="packageText" @click="handleMoreClick">
      <view class="packageText1">宝妈服务笔记</view>
      <view class="packageText2">
        <view class="more">更多</view>
        <u-icon name="arrow-right" color="#aaaaaa" size="26"></u-icon>
      </view>
    </view>

    <!-- 宝妈列表 -->
    <scroll-view
      :show-scrollbar="false"
      :scroll-x="true"
      class="tab-h1"
      :scroll-this="scrollthis"
    >
      <view
        class="superiorBM1"
        v-for="(item, index) in goodMomList"
        :key="index"
      >
        <view class="superiorBM" @tap="homepage(item)">
          <image :src="item.avatar || $defaultAvatar" mode="" />
          <view class="superiorBMTitle">
            {{ item.name || $defaultName }}
          </view>
          <view class="discover-title">优质动态宝妈</view>
          <view class="homepage">主页</view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted, getCurrentInstance } from 'vue';

// 响应式数据
const goodMomList = ref([]);
const scrollthis = ref(false);

// 获取当前实例
const instance = getCurrentInstance();

// 生命周期
onMounted(() => {
  initServiceNoteData();
});

// 初始化宝妈服务笔记数据
const initServiceNoteData = () => {
  customerPageFun();
};

// 获取优质宝妈数据
const customerPageFun = () => {
  instance.proxy.$axios
    .get(instance.proxy.$api.getMomPage, {
      pageSize: 9999,
      pageNum: 1,
      isShow: true,
    })
    .then((res) => {
      if (res.data.code == 200) {
        goodMomList.value = res.data.rows || [];
      }
    })
    .catch((error) => {
      console.error('获取优质宝妈数据失败:', error);
    });
};

// 事件处理 - 内部实现页面跳转
const handleMoreClick = () => {
  instance.proxy.$jumpPage('/pageA/pageB/community/sending/list');
};

const homepage = (item) => {
  // 宝妈主页跳转逻辑
  let params = {
    userId: item.userId,
    momId: item.momId,
    uuid: item.uuid,
  };
  instance.proxy.$jumpPage('/pageA/pageB/community/note/notedetail', params);
};
</script>

<style scoped>
/* ===== 宝妈服务笔记模块样式 ===== */
.service-note-module {
  width: 100%;
  margin: 40rpx 0;
}

/* 标题区域 - 与原型保持一致 */
.packageText {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 40rpx 0 20rpx 0;
}

.packageText1 {
  font-weight: bold;
  font-size: 34rpx;
  color: #333333;
}

.packageText2 {
  display: flex;
  align-items: center;
}

.more {
  color: #aaaaaa;
  font-size: 28rpx;
}

.packageImg {
  padding-left: 8rpx;
}

.packageImg image {
  width: 8rpx;
  height: 18rpx;
}

/* 宝妈列表 - 精确还原原型样式 */
.tab-h1 {
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
  font-size: 16px;
  white-space: nowrap;
}

.superiorBM1 {
  display: inline-block;
  margin-right: 12rpx;
}

.superiorBM {
  padding: 28rpx 0;
  border-radius: 20rpx;
  width: 272rpx;
  text-align: center;
  position: relative;
  background: linear-gradient(to bottom, #ffefef 0%, #ffffff 100%);
  border: 2rpx solid #ffd6d7;
}

.superiorBM image:first-child {
  width: 110rpx;
  height: 110rpx;
  border-radius: 110rpx;
}

.superiorBMTitle {
  color: #333333;
  font-size: 30rpx;
}

.discover-title {
  color: #777777;
  font-size: 24rpx;
  margin-top: 8rpx;
  margin-bottom: 20rpx;
}

.homepage {
  font-weight: 400;
  font-size: 28rpx;
  color: #ffffff;
  line-height: 47rpx;
  background: #ff4f61;
  text-align: center;
  margin: 0 auto;
  width: 136rpx;
  height: 47rpx;
  border-radius: 8rpx;
}
</style>
