<template>
	<view @tap="dynamic" v-if="btnShow">
		<view class="note" v-if="roleIf == 5">
			<image src="http://cdn.xiaodingdang1.com/2024/10/30/971c5c151c6b4f7ab09a4639106c7157.png" mode=""
				style="width: 22rpx; height: 22rpx; margin-right: 10rpx" />
			<view>发布笔记</view>
		</view>
		<view class="dynamic" v-else>
			<image src="http://cdn.xiaodingdang1.com/2024/10/30/971c5c151c6b4f7ab09a4639106c7157.png" mode=""
				style="width: 22rpx; height: 22rpx; margin-right: 10rpx" />
			<view>发动态</view>
		</view>
	</view>
</template>

<script setup>
	// 发布按钮
	import {
		ref,
		reactive,
		onMounted,
		computed,
		getCurrentInstance
	} from "vue";
	const btnShow = ref(false);
	const isSale = ref(false);
	const props = defineProps({
		// 1宝妈USER 2.员工(宝妈社区)STAFF 3.会所 CLUB 4.销售 5.发布笔记
		roleIf: {
			type: [String, Number],
			default: 1,
		},
		permissions: {
			type: String,
			default: "",
		},
	});
	const emits = defineEmits(["publishnote"]);
	const publishInit = () => {
		let permissions = uni.getStorageSync("permissions");
		btnShow.value = permissions.includes(props.permissions);
		// 销售特殊处理
		if (permissions.includes("feed:post:sales:create")) {
			isSale.value = true;
			if (props.roleIf == 2 || props.roleIf == 3) {
				btnShow.value = true;
			}
		}
	};
	const dynamic = () => {
		// 发布
		let roleIf = "";
		if (props.roleIf == "5") {
			roleIf = "5";
		} else {
			if (isSale.value) {
				//     // 销售区分会所和动态
				//     if (props.permissions == "feed:post:club:create") {
				//       roleIf = "3";
				// console.log('11111111');
				//     } else {
				//       roleIf = "2";
				// console.log('44444444');
				//     }
				roleIf = props.roleIf;
			} else {
				roleIf = props.roleIf;
			}
		}
		if (roleIf == "5") {
			emits("publishnote");
		} else {
			uni.navigateTo({
				url: "/pageA/pageB/dynamics/publish?roleIf=" + roleIf,
			});
		}
	};
	defineExpose({
		publishInit,
	});
</script>

<style scoped>
	.dynamic {
		padding: 20rpx 36rpx;
		background: #ff4f61;
		border-radius: 110rpx;
		display: flex;
		align-items: center;
		color: #fff;
		font-size: 32rpx;
		position: fixed;
		bottom: 200rpx;
		left: 38%;
		z-index: 99;
	}

	.note {
		padding: 20rpx 36rpx;
		border-radius: 110rpx;
		display: flex;
		align-items: center;
		color: #fff;
		font-size: 32rpx;
		position: fixed;
		bottom: 50rpx;
		left: 38%;
		z-index: 99;
		background: #1fa2ff;
	}
</style>