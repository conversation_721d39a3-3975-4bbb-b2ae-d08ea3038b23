.main {
    background: #F8F9F9;
    padding-top: 184rpx;
}
.top {
    background: white;
    position: relative;
    &-price {
        display: flex;
        &-icon {
            margin-left: 24rpx;
            font-weight: 600;
            font-size: 24rpx;
            color: #333333;
            line-height: 24rpx;
            text-align: left;
            font-style: normal;
            text-transform: none;
            margin-top: 40rpx;
        }
        &-text {     
            font-weight: bold;
            font-size: 44rpx;
            color: #333333;
            line-height: 36rpx;
            text-align: left;
            font-style: normal;
            text-transform: none;
            margin: 28rpx 16rpx 20rpx 0rpx;
        }
        &-qes {
            font-weight: 400;
            font-size: 24rpx;
            color: #AAAAAA;
            line-height: 24rpx;
            text-align: left;
            font-style: normal;
            text-transform: none;
            margin-top: 40rpx;
        }
    }
    &-des {
        font-weight: 500;
        font-size: 34rpx;
        color: #333333;
        line-height: 40rpx;
        text-align: left;
        font-style: normal;
        text-transform: none;
        margin-left: 24rpx;
        margin-bottom: 12rpx;
        width: 60%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap; 
    }
    &-list {
        margin-left: 24rpx;
        padding-bottom: 26rpx;
        display: flex;
        align-items: center;
        &-enum {
            font-weight: 400;
            font-size: 24rpx;
            color: #777777;
            line-height: 28rpx;
            text-align: left;
            font-style: normal;
            text-transform: none;
            margin-right: 24rpx;
            position: relative;
        }
        &-enum:not(:last-child):after {
            content: '';
            position: absolute;
            right: -12rpx; 
            top: 45%; 
            height: 90%; 
            width: 2rpx;
            background: #AAAAAA; 
            transform: translateY(-50%);
        }
    }
    
    &-button {
        position: absolute;
        right: 24rpx;
        bottom: 26rpx;
    }
}
    
.tabbar {
    background: white;
    margin-top: 20rpx;
    // margin-bottom: 20rpx;
}
 .scroll-h {
        width: 750rpx;
		/* #ifdef H5 */
		width: 100%;
		/* #endif */
		height: 80rpx;
        /* #ifndef APP-PLUS */
        white-space: nowrap;
        /* #endif */
    }
    .uni-tab-item {
        display: inline-block;
        width: calc(100% / 3); 
        text-align: center;
        margin-top: 24rpx;
        margin-bottom: 38rpx;
    }
    .uni-tab-item-title {
       font-weight: 400;
       font-size: 28rpx;
       color: #AAAAAA;
       line-height: 33rpx;
       text-align: left;
       font-style: normal;
       text-transform: none;
    }
    
    .uni-tab-item-title-active {
        font-weight: 600;
        color: #333333;
    }
.content {
    overflow-x: hidden;
    overflow-y: auto;
    background: #F8F9F9;
    padding-bottom: 200rpx;
}
.top-img {
    height: 750rpx;
    width: 100%;
}
