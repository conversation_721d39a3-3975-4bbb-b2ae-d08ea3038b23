<template>
	<view>
		<view class='poster postercanvas' data-type="view">
			<view class="box postercanvas">
				<exportImgCanvas ref="exportCanvas" :canvas-width="canvasWidth" :canvas-height="canvasHeight"
					@sharePicture="sharePicture" @finshed="finshed" />
				<!-- data-type="image" data-src='http://cdn.xiaodingdang1.com/2025/01/20/0046e46896cb4f3bb334e003cedb4037.png' -->
				<view class="main postercanvas" data-type="image"
					data-src='http://cdn.xiaodingdang1.com/2025/01/20/0046e46896cb4f3bb334e003cedb4037.png'>
					<view class="top postercanvas" data-type="view">
						<image data-radius="100" class="top-image postercanvas" data-type="image"
							:data-src="pageList.logo || clubLogo || $defaultAvatar"
							:src="pageList.logo || clubLogo || $defaultAvatar"></image>
						<view class="top-right postercanvas" data-type="view">
							<text class="top-right-title postercanvas" data-weight="bold" :data-word="clubName"
								data-size="40" data-color="black" data-type="text">{{clubName}}</text>
							<text class="top-right-subtitle postercanvas"
								:data-word="pageList.posterSlogan || defaultLogan" data-size="26" data-color="#33333369"
								data-type="text">{{ pageList.posterSlogan || defaultLogan }}</text>
						</view>
					</view>
					<!-- <view class="header postercanvas" data-type="view" v-if="posterIf!=1">
                        <view class="header-image postercanvas" data-type="image"
                            data-src='http://cdn.xiaodingdang1.com/2025/01/20/0b86ed83621045cea5a084d6f56de44d.png'>
                            <image class="postercanvas" data-type="image" data-radius="152"
                                :data-src="pageList.avatar || $defaultAvatar" :src="pageList.avatar || $defaultAvatar">
                            </image>
                        </view>
                        <view class="header-right postercanvas" data-type="view">
                            <view class="flex postercanvas" data-type="view">
                                <text class="postercanvas" data-type="text" data-word="宝妈：" data-size="26"
                                    data-color="#333">宝妈：</text>
                                <text class="postercanvas" data-type="text" :data-word="pageList.momName" data-size="26"
                                    data-color="#333">{{pageList.momName}}</text>
                            </view>
                            <view class="flex postercanvas" data-type="view">
                                <text class="postercanvas" data-type="text" data-word="年龄：" data-size="26"
                                    data-color="#333">年龄：</text>
                                <text class="postercanvas" data-type="text" :data-word="pageList.age || ''"
                                    data-size="26" data-color="#333">{{pageList.age}}</text>
                            </view>
                            <view class="flex postercanvas" data-type="view">
                                <text class="postercanvas" data-type="text" data-word="宝宝：" data-size="26"
                                    data-color="#333">宝宝：</text>
                                <text class="postercanvas" data-type="text" :data-word="pageList.babyWeight || ''"
                                    data-size="26" data-color="#333">{{pageList.babyWeight}}</text>
                            </view>
                            <view class="flex postercanvas" data-type="view">
                                <text class="postercanvas" data-type="text" data-word="入住时间：" data-size="26"
                                    data-color="#333">入住时间：</text>
                                <text class="postercanvas" data-type="text" :data-word="pageList.serviceStartDate || ''"
                                    data-size="26" data-color="#333">{{ pageList.serviceStartDate }}</text>
                            </view>
                        </view>
                    </view>
					 -->
					<view class="content postercanvas" data-type="view">
						<view class="service-card postercanvas" data-type="view" data-bgcolor="white" data-radius="40">
							<view class="bg postercanvas" data-radius="40" data-type="image"
								data-src="http://cdn.xiaodingdang1.com/2025/01/20/3aa0331c80f54166a71fb26dc9b07ac7.png">
								<view class="detail postercanvas" data-type="view">
									<view class="detail-left postercanvas" data-type="view">
										<image class="postercanvas" data-type="image" data-radius="80"
											:data-src="pageList.avatar || $defaultAvatar"
											:src="pageList.avatar || $defaultAvatar"></image>
									</view>
									<view class="right-name postercanvas">
										<text class="postercanvas" data-type="text" :data-word="pageList.nickname"
											data-size="36" data-color="#FF6E00">
											{{pageList.nickname}}
										</text>
									</view>
									<view class="detail-right-content-staf postercanvas" data-radius="60"
										data-bgcolor="#FFE7C0" data-type="view">
										<image class="postercanvas" data-type="image"
											data-src="http://cdn.xiaodingdang1.com/2024/12/31/7ab70df46904400aae76508164f5a0ac.png"
											src="http://cdn.xiaodingdang1.com/2024/12/31/7ab70df46904400aae76508164f5a0ac.png">
										</image>
										<view class="detail-right-name postercanvas" data-type="view">
											<text class="postercanvas" data-weight="bold" data-type="text"
												:data-word="pageList.nodeName" data-size="26" data-color="#7A4E2B">
												{{ pageList.nodeName }}
											</text>
										</view>
									</view>
								</view>
								<view class="detail-tag postercanvas" data-type="view" v-if="pageList.staffPost">
									<view class="detail-tag-enum postercanvas" data-radius="20" data-type="view"
										data-bgcolor="#1FA2FF">
										<text class="postercanvas" data-type="text" :data-word="pageList.staffPost"
											data-size="26" data-color="#E9F2FF">
											{{pageList.staffPost}}
										</text>
									</view>
								</view>
								<template v-if="pageList.contentArray && pageList.contentArray.length > 0">
									<view class="content-note postercanvas" data-type="view"
										v-for="(text, index) in pageList.contentArray" :key='index'>
										<text class="postercanvas" data-type="text" :data-word="text" data-size="26"
											data-color="#333333">
											{{text}}
										</text>
									</view>
								</template>
							</view>
							<template v-if="pageList.contentPhotos && pageList.contentPhotos.length > 0">
								<view v-for="(img, index) in pageList.contentPhotos" :key="index"
									class="content-image postercanvas" data-type="view">
									<image class="postercanvas" id="fit-img" data-type="image" data-radius="40"
										:data-src="img" mode="aspectFill" :src="img">
									</image>
									<!-- 			<view class="postercanvas" id="fit-img" :style="{'backgroundImage': 'url('+ img +')'}">
														</view> -->
								</view>
							</template>
							<view class="bottom-left postercanvas" data-radius="40" data-type="view">
								<view class="bottom-comment postercanvas" data-type="view">
								<!-- 	<text class="postercanvas" data-size="28" data-type="text"
										:data-word="pageList.count" data-color="#333333">
										{{pageList.count}}
									</text> -->
								</view>
								<view class="bottom-line postercanvas" data-type="view" data-bgcolor="#333333"></view>
								<view class="bottom-subtitle postercanvas" data-type="view">
									<text class="postercanvas" data-type="text" data-word="扫码即刻开启" data-size="30"
										data-color="#333333" data-weight="bold">
										扫码即刻开启
									</text>
								</view>
								<view class="bottom-title postercanvas" data-type="view">
									<text class="postercanvas" data-type="text" data-word="不用来回体验" data-size="24"
										data-color="#33333369">
										{{ str1 }}
									</text>
								</view>
								<view class="bottom-title postercanvas" data-type="view">
									<text class="postercanvas" data-type="text" data-word="在线上就能查看合适再选择" data-size="24"
										data-color="#33333369">
										{{ str2 }}
									</text>
								</view>
							</view>
							<view class="bottom-right postercanvas" data-radius="40" data-type="view" v-if="qrCode">
								<image class="postercanvas bottom-image" data-type="image" data-radius="80"
									:data-src="qrCode" :src="qrCode">
								</image>
								<view class="bottom-title postercanvas" data-type="view">
									<text class="postercanvas" data-type="text" data-word="宝妈小叮当" data-size="24"
										data-color="#33333369">
										宝妈小叮当
									</text>
								</view>
								<view class="bottom-title postercanvas" data-type="view">
									<text class="postercanvas" data-type="text" data-word="专注母婴系统开发" data-size="24"
										data-color="#33333369">
										专注母婴系统开发
									</text>
								</view>
							</view>
							<view class="bottom-clear postercanvas"></view>
						</view>
						<view class="zhen postercanvas" data-type="view">
							<image class="postercanvas" data-type="image"
								data-src="http://cdn.xiaodingdang1.com/2025/01/20/3fe0ddf2cb4c4c88a9a9592050ec6ac3.png"
								src="http://cdn.xiaodingdang1.com/2025/01/20/3fe0ddf2cb4c4c88a9a9592050ec6ac3.png">
							</image>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="note" @click="productPoster">
			<image src="http://cdn.xiaodingdang1.com/2024/10/30/971c5c151c6b4f7ab09a4639106c7157.png" mode=""
				style="width: 22rpx; height: 22rpx; margin-right: 10rpx" />
			<view>生成海报</view>
		</view>
		<new-request-loading></new-request-loading>
	</view>
</template>
<script>
	import exportImgCanvas from '@/components/exportImgCanvas'
	import html2canvas from 'html2canvas';
	export default {
		components: {
			exportImgCanvas
		},
		data() {
			return {
				canvasWidth: 0,
				canvasHeight: 0,
				test: 'huge',
				diaryId: '',
				clubName: uni.getStorageSync('clubName'),
				clubLogo: uni.getStorageSync('clubInfo')?.logo,
				pageList: {},
				qrCode: '',
				momId: '',
				uuid: '',
				defaultLogan: '专业呵护新起点',
				str1: '不用来回体验',
				str2: '在线上就能查看合适再选择',
				posterIf: '',
				postId: '',
				taskNodeId:''
			}
		},
		onShareAppMessage() {},

		onShareTimeline() {},
		onLoad(options) {
			this.diaryId = options.diaryId
			this.momId = options.momId
			this.uuid = options.uuid
			// this.getList()
			this.postId = options.postId
			this.taskNodeId=options.taskNodeId
			this.getQrCode()
			this.getfeedPostInfo()
		},
		methods: {
			async getvideoFirstFrame(videoUrl) {
							let data = {
								videoUrl: videoUrl,
							};
							const res = await this.$axios.get(this.$api.videoFirstFrame, data);
							if (res.data.code == 200) {
								let contentPhotos=[]
								contentPhotos.push(res.data.data.frameUrl)
								this.pageList.contentPhotos=contentPhotos
								this.frameUrl = res.data.data.frameUrl
							} else {
								uni.showToast({
									title: res.data.msg,
									icon: "none",
									duration: 3000, //持续的时间
								});
							}
						},
			getfeedPostInfo() {
				let data = {
					postId: this.postId,
				};
				this.$axios.get(this.$api.feedPostInfo, data).then((res) => {
					if (res.data.code == 200) {
						let data = res.data.data;
						data.contentArray = this.handlerText(data.content, 24)
						if (data.videos||data.videos!==null) {
							let videoUrl = data.videos[0]
							this.getvideoFirstFrame(videoUrl)
						}
						this.pageList = data
					}
				});
			},
			finshed() {
				this.changeLoading(false)
			},
			async getList() {
				this.changeLoading(true)
				const res = await this.$axios.get(this.$api.getDiaryDetail, {
					diaryId: this.diaryId
				})
				if (res.data.code == 200) {
					const data = res?.data?.data
					if (data?.serviceStartDate) {
						data.serviceStartDate = data.serviceStartDate.slice(0, 10)
					}
					if (data?.content) {
						data.contentArray = this.handlerText(data.content, 24)
					}
					data.count = data?.userCount ? `该用户还有${data.userCount}条精彩评论` : '该用户还有1条精彩评论'
					if (data.chefPosterSlogan) {
						let arr = data.chefPosterSlogan.split('，')
						this.str1 = arr[0] || '不用来回体验'
						this.str2 = arr[1] || '在线上就能查看合适再选择'
					}
					this.pageList = data;
				}
				this.changeLoading(false)
			},
			handlerText(str, length) {
				let result = [];
				for (let i = 0; i < str.length; i += length) {
					result.push(str.slice(i, i + length));
				}
				console.log('resulttttt', result)
				return result;
			},
			getQrCode() {
				const pathUrl = 'pageA/pageB/community/sending'
				this.$axios.get(this.$api.getQrCode, {
					pathUrl,
					uuid: this.postId,
					diaryId: ''
				}).then((res) => {
					console.log('resssss', res.data)
					if (res?.data.code == 200) {
						this.qrCode = res?.data?.msg
					}
				})
			},
			// 分享图片
			sharePicture({
				canvas,
				path
			}) {
				const fileData = canvas.toDataURL()
				const fs = uni.getFileSystemManager()
				let that = this
				fs.writeFile({
					filePath: path,
					data: fileData.replace(/^data:image\/\w+;base64,/, ""),
					encoding: 'base64',
					success(res) {
						uni.hideLoading()
						wx.showShareImageMenu({
							withShareTicket: true,
							path: path,
							success: async res => {
								console.log(res);
								uni.navigateBack()
							}
						})
					},
					complete(res) {
						that.finshed()
						console.log('调用结束');
					}
				})
			},
			// 获取屏幕宽高
			getSystemInfo(height) {
				let that = this
				uni.getSystemInfo({
					success: function(info) {
						that.canvasWidth = info.windowWidth * info.pixelRatio
						that.canvasHeight = height * info.pixelRatio
						// that.canvasHeight = height
					}
				});
			},
			productPoster() {
				this.changeLoading(true)
				let that = this
				let arrayInfo = []
				uni.pageScrollTo({
					scrollTop: 0,
					duration: 0
				})
				try {
					uni.createSelectorQuery().in(that).selectAll('.postercanvas').boundingClientRect().exec(
						async (
							res) => {
							console.log('获取到信息：', res[0])
							let resArray = res[0]
							await that.getSystemInfo(resArray[0].height)
							for (let i = 0; i < resArray.length; i++) {
								const {
									width,
									height,
									left,
									top
								} = resArray[i]

								if (resArray[i].dataset.type == 'view') {
									console.log('view')
									let radius = resArray[i].dataset.radius
									let bgColor = resArray[i].dataset.bgcolor
									let shadow = resArray[i].dataset.shadow
									let drawRect = resArray[i].dataset.drawRect

									arrayInfo.push({
										width,
										height,
										posX: left,
										posY: top,
										type: 'view',
										radius: radius,
										shadow: shadow ? true : false,
										bgColor: bgColor ? bgColor : false,
										drawRect: drawRect ? true : false,
									})
								} else if (resArray[i].dataset.type == 'image') {
									console.log('imageIndex', resArray[i])
									let radius = resArray[i].dataset.radius
									let src = resArray[i].dataset.src
									arrayInfo.push({
										width,
										height,
										posX: left,
										posY: top,
										type: 'image',
										src,
										radius,
									})
								} else if (resArray[i].dataset.type == 'text') {
									console.log('text')
									const {
										color,
										word,
										size,
										weight
									} = resArray[i].dataset
									arrayInfo.push({
										color,
										word,
										size,
										weight,
										posX: left,
										posY: top,
										type: 'text',
									})
								}
							}
							// 处理归纳后将数组arrayInfo，传给组件的drawCanvas()方法
							console.log('that.$refs.exportCanvas', that.$refs.exportCanvas)
							that.$refs.exportCanvas.drawCanvas(arrayInfo)
						})
				} catch (e) {
					this.changeLoading(false)
				}
			}
		}
	}
</script>
<style scoped lang="less">
	@import "./sendingPoster.less";
</style>