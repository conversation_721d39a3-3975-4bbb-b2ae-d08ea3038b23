<template>
	<view class="main">


		<view class="calendar-container">
			<view class="calendar-title">基础信息</view>
			<view class="fgx"></view>
			<view class="form-item">
				<label class="label">客户信息</label>
				<text class="value">{{customerMenu.customerName?customerMenu.customerName:'--'}}</text>
			</view>
			<view class="fgx"></view>
			<view class="form-item">
				<label class="label">入住房号</label>
				<text class="value">{{roomNumber}}</text>
			</view>
			<view class="fgx"></view>
			<view class="form-item">
				<label class="label">联系电话</label>
				<text class="value">{{customerMenu.phone?customerMenu.phone:'--'}}</text>
			</view>
			<view class="fgx"></view>
			<view class="form-item">
				<label class="label">用餐日期</label>
				<text class="value">{{date}}</text>
			</view>
		</view>


		<view class="mealType">
			<view class="mealType1">
				<view class="mealType-1">
					<image src="http://cdn.xiaodingdang1.com/2025/06/25/e0ade65201284f299c70b4a84123f32a.png" mode=""
						style="width: 24rpx;height: 25rpx;" />
				</view>
				<view class="mealType-2">早餐</view>
			</view>
			<view class="mealType-3">
				<textarea :auto-height="true" placeholder="暂无数据" :value="customerMenu.breakfast" class="textarea" disabled="true"></textarea>
			</view>
		</view>
		<view class="mealType">
			<view class="mealType1">
				<view class="mealType-1">
					<image src="http://cdn.xiaodingdang1.com/2025/06/25/f4e91d464b27475eba5b6edc472d8ba0.png" mode=""
						style="width: 24rpx;height: 25rpx;" />
				</view>
				<view class="mealType-2">午餐</view>
			</view>
			<view class="mealType-3">
				<textarea :auto-height="true" placeholder="暂无数据" :value="customerMenu.lunch" class="textarea" disabled="true"></textarea>
			</view>
		</view>
		<view class="mealType">
			<view class="mealType1">
				<view class="mealType-1">
					<image src="http://cdn.xiaodingdang1.com/2025/06/25/4834040060d445108bfd5bc0698846c7.png" mode=""
						style="width: 24rpx;height: 25rpx;" />
				</view>
				<view class="mealType-2">下午茶</view>
			</view>
			<view class="mealType-3">
				<textarea :auto-height="true" placeholder="暂无数据" :value="customerMenu.afternoonTea" class="textarea" disabled="true"></textarea>
			</view>
		</view>
		<view class="mealType">
			<view class="mealType1">
				<view class="mealType-1">
					<image src="http://cdn.xiaodingdang1.com/2025/06/25/5fb06e8f8b2f47259324439440d689e2.png" mode=""
						style="width: 24rpx;height: 25rpx;" />
				</view>
				<view class="mealType-2">晚餐</view>
			</view>
			<view class="mealType-3">
				<textarea :auto-height="true" placeholder="暂无数据" :value="customerMenu.dinner" class="textarea" disabled="true"></textarea>
			</view>
		</view>
		<view class="mealType">
			<view class="mealType1">
				<view class="mealType-1">
					<image src="http://cdn.xiaodingdang1.com/2025/06/25/3b2d575060cd4b9987a51653558db53a.png" mode=""
						style="width: 24rpx;height: 25rpx;" />
				</view>
				<view class="mealType-2">夜宵</view>
			</view>
			<view class="mealType-3">
				<textarea  :auto-height="true" placeholder="暂无数据" :value="customerMenu.nightSnack" class="textarea" disabled="true"></textarea>
			</view>
		</view>
	</view>

</template>

<script>
	export default {
		data() {
			return {
				customerId: '',
				date: '',
				roomNumber: '',
				customerMenu:''
			}
		},
		onLoad(options) {
			console.log(options);
			this.customerId = options.customerId
			this.date = options.date
			this.roomNumber = options.roomNumber
			this.getCustomerMenus(options.customerId,options.date)
		},
		methods: {
			getCustomerMenus(id,date) {
				//获取客户个性化餐单
				let data = {
					customerId: id,
					date: date
				};
				this.$axios.get(this.$api.getCustomerMenu, data).then((res) => {
					if (res.data.code == 200) {
						let data = res.data.data;
						this.setData({
							customerMenu: data,
						});
					}
				});
			},
		}
	}
</script>

<style lang="less" scoped>
	@import './details.less';
</style>