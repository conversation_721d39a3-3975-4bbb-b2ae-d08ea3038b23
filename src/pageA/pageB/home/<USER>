<template>
	<view class="mains">
		<view class="headTab" v-if="tenantId=='469752'">
			<u-tabs :list="list" active-color="#FF4F61" inactive-color="#9A9A9A" font-size="30" :is-scroll="false"
				:current="current" @change="change"></u-tabs>
		</view>
		<view class="black" v-if="tenantId=='469752'"></view>
		<view v-if="current==0">
			<view class="main">
				<view class="team" @tap="nurseryTeamDetails" :data-staffid="item.staffId"
					v-for="(item, index) in staffPageList" :key="index">
					<view class="teamImg">
						<image :src="item.staffPhotos"></image>
					</view>

					<view class="teamContent">
						<!-- <view class="teamContent1">
				<view class="text1">王沁轻</view>
				<view class="text2">专业</view>
				<view class="text3">资质认证</view>
			</view> -->
						<view class="teamContent1">
							<view class="teamContent1">
								<view class="discuss1_3">
									<view class="text1">{{ item.staffName }}</view>
									<view class="head1_2">
										<image
											src="http://cdn.xiaodingdang1.com/1/120240125/5b82ebb2-4d5c-43ae-83b8-f4621e4f3336.png"
											mode="" style="width: 40rpx; height: 34rpx" class="head1Img" />
										<view class="head1_3">
											<!-- {{
                      index == 0 && clubName == "澜悦湾月子会所"
                        ? "护士长"
                        : item.staffPost
                    }} -->
											{{ item.staffPost}}
										</view>
									</view>
								</view>
							</view>
						</view>
						<view class="text4">{{ clubName }}{{ item.staffPost }}</view>
						<view class="experience">
							<text class="experience1">从业年限：</text>
							<text class="experience2">{{ item.yearsEmployment }}</text>
							<text class="experience1">服务人员数量：</text>
							<text class="experience2">{{ item.serviceNum }}人</text>
						</view>
						<view class="introductory">
							<text class="introductoryText">简介：</text>
							<text class="introductoryContent">{{ item.staffDesc }}</text>
						</view>
						<view class="teamContent1">
							<view class="text2" v-for="(res, i) in item.tag" :key="i">{{
              res
            }}</view>
						</view>
						<!-- <view class="evaluate">
				<view >
				<view class="evaluateText">
					用户评分：5.0分
				</view>
				<view class="score">
					<image src="http://47.106.72.143:9000/tools/files/1746773107865055234/download" mode=""/>
				</view>
				</view>
				<view class="evaluateFgx"></view>
				<view class="evaluateText">
				<view>
				用户评分：5.0分
				</view>
				<view class="score">
					<image src="http://47.106.72.143:9000/tools/files/1746773107865055234/download" mode=""/>
				</view>
				</view>
			</view> -->
					</view>
				</view>
			</view>
			<rightMenuBarVue :scrollTop="scrollTop"></rightMenuBarVue>
			<mask-dialog></mask-dialog>
			<!-- <view class="blank"></view>
	<bottom></bottom> -->
			<!-- <view class="tag">
		<view>
			<image src="http://cdn.xiaodingdang1.com/1/120240115/1896701e-aa88-4619-8521-8a498b5e22c5.png" mode="" style="width: 118rpx;height: 118rpx;" />
		</view>
		<view>
			<image src="http://cdn.xiaodingdang1.com/1/120240115/8a35145f-a9e0-402c-be52-deb8f1a40d3a.png" mode="" style="width: 118rpx;height: 118rpx;" />
		</view>
	</view> -->
		</view>
		<view v-if="current==1" style="background-color: #fff;">
			<view v-for="item,index in deptPageList" :key="index">
				<view class="container">
					<!-- 页面标题 -->
					<view class="header">
						<image :src="item.avatar || $defaultAvatar" alt="" lazy-load></image>
						<view class="header1">
							<view class="header1_1">{{item.publisher || $defaultName}}</view>
							<view class="header1_2">{{item.createTime}}</view>
						</view>
						<image src="http://cdn.xiaodingdang1.com/2025/07/10/114ec43d6cdb4342ba19a4c656655046.png"
							mode="aspectFill" style="width: 28rpx;height: 30rpx;" @tap="remove(item.id)" class="delBtn"
							v-if="pushMessageIf"></image>
					</view>

					<!-- 内容区域 -->
					<view class="content">
						{{item.content}}
					</view>

					<!-- 图片展示区域 -->
					<view class="image-gallery" v-if="item.images && item.images.length > 0">
						<view class="image-item" v-for="res,i in item.images" :key="i">
							<image :src="res + '?x-oss-process=image/quality,q_60'" @tap="previewImage" alt="" lazy-load
								:data-url="item.images" :data-src="res" :data-sources="item.images" :data-index="index1"
								mode="aspectFill"></image>
						</view>
					</view>

					<view class="image-gallery" v-if="item.videos && item.videos.length > 0">
						<view class="image-item" v-for="res,i in item.videos" :key="i">
							<video @touchstart="onTouchStart" @touchend="onTouchEnd" class="video" :src="res"
								@tap="previewVideo" :data-url="item.videos" :data-src="res" :data-index="i"
								:controls="true"></video>
						</view>
					</view>
				</view>
				<view class="cutoffrule"></view>
			</view>
			<view class="dynamic" @tap="campaign" v-if="pushMessageIf">
				<image src="http://cdn.xiaodingdang1.com/2024/10/30/971c5c151c6b4f7ab09a4639106c7157.png" mode=""
					style="width: 22rpx; height: 22rpx; margin-right: 10rpx" />
				<view>发动态</view>
			</view>
		</view>
	</view>
</template>

<script>
	import rightMenuBarVue from "@/components/rightMenuBar.vue";
	import imgContentAreaVue from '@/components/imgContentArea.vue';
	export default {
		components: {
			rightMenuBarVue,
			imgContentAreaVue
		},
		data() {
			return {
				types:'',
				tenantId: '',
				list: [{
						name: '护理团队'
					},
					{
						name: '部门动态'
					},
				],
				current: 0,
				scrollTop: 0,
				staffPageList: [],
				pushMessageIf: false,

				//会所名字
				clubName: "",

				i: "",
				res: [],
				deptPageList: [],
				pageNum:1
			};
		},
		/**
		 * 生命周期函数--监听页面加载
		 */
		onLoad(options) {
			let tenantId = uni.getStorageSync('tenantId')
			let roles = uni.getStorageSync('roles');
			//BOSS("老板"),ADMIN("管理员"),APP_SUPER_ADMIN("超级管理员"),SALES_MANAGER("销售主管"),SALES("销售"),
			this.pushMessageIf =
				roles[0] == 'BOSS' || roles[0] == 'ADMIN' || roles[0] == 'APP_SUPER_ADMIN' || roles[0] ==
				'SALES_MANAGER' || roles[0] == 'SALES' ? true : false;
			this.setData({
				clubName: uni.getStorageSync("clubName"),
				tenantId: tenantId
			});
		},
		/**
		 * 生命周期函数--监听页面初次渲染完成
		 */
		onReady() {},
		onPageScroll(e) {
			this.scrollTop = e.scrollTop;
		},
		/**
		 * 生命周期函数--监听页面显示
		 */
		onShow() {
			this.staffPage();
			this.deptPage()
			var newDateTime = Date.parse(new Date());
			let data = {
				eventType: "PAGE_VIEW",
				pageUrl: "pageA/pageB/home/<USER>",
				module: "staff",
				eventTime: newDateTime,
				pageTitle: "护理团队",
			};
			let iftoken = uni.getStorageSync("token");
			if (iftoken) {
				this.$point.basePoint(data);
			}
		},
		/**
		 * 生命周期函数--监听页面隐藏
		 */
		onHide() {},
		/**
		 * 生命周期函数--监听页面卸载
		 */
		onUnload() {
			var newDateTime = Date.parse(new Date());
			let data = {
				eventId: uni.getStorageSync("eventId"),
				leaveTime: newDateTime,
			};
			this.$point.reportEnd(data);
		},
		/**
		 * 页面相关事件处理函数--监听用户下拉动作
		 */
		onPullDownRefresh() {},
		/**
		 * 页面上拉触底事件的处理函数
		 */
		onReachBottom() {
			if (this.types == 1) {
			  this.pageNum = this.pageNum + 1;
			  this.changeLoading(true);
			  this.deptPage();
			}
		},
		/**
		 * 用户点击右上角分享
		 */
		onShareAppMessage() {},

		onShareTimeline() {},
		methods: {
			previewVideo(event) {
				this.preview('video', event);
			},
			previewImage(event) {
				this.preview('image', event);
			},
			preview(type, event) {
				let that = this;
				let url = event.currentTarget.dataset.url;
				let src = event.currentTarget.dataset.src;
				let index = event.currentTarget.dataset.index;
				let maparr = [];
				if (type == 'video') {
					maparr.push({
						type,
						url: src,
					});
				} else {
					url.forEach((item) => {
						maparr.push({
							type,
							url: item,
						});
					});
				}
				// 既有视频又有图片用这个
				uni.previewMedia({
					sources: maparr,
					// 官方文档 : 素材展示列表 是个数组对象 一个url链接 一个type 判断这是视频还是图片
					// 接收image 和 video 所以处理数据时 最好给个type 指定 image 或者 video
					current: index,
					// 当前显示的资源序号
					autoplay: true,
				});
			},
			onTouchStart() {
				this.isZoomed = true; // 触摸开始时设置为缩放状态
			},
			onTouchEnd() {
				this.isZoomed = false; // 触摸结束时设置为非缩放状态
				if (!this.isZoomed) {
					this.pauseVideo(); // 如果不是缩放状态，暂停视频播放
				}
			},
			pauseVideo() {
				if (this.videoContext) {
					this.videoContext.pause();
				}
			},
			campaign() {
				uni.navigateTo({
					url: '/pageA/pageB/community/branchdynamic/branchdynamic'
				})
			},
			change(index) {
				if (index == 0) {
					this.staffPage();
				} else if (index == 1) {
					this.deptPage()
				}
				this.current = index;
			},
			nurseryTeamDetails(e) {
				let staffId = e.currentTarget.dataset.staffid;
				console.log("eeeeee", e);
				this.$jumpPage("nurseteam/detail", {
					staffId,
				});
			},

			staffPage() {
				//查询护理团队列表
				let data = {
					pageSize: 100,
					pageNum: 1,
					auditStatus: 1,
					isShow: true
				};
				this.$axios.get(this.$api.staffPage, data).then((res) => {
					if (res.data.code == 200) {
						this.setData({
							staffPageList: res.data.rows,
						});
					}
				});
			},
			deptPage() {
				//分页查询部门动态列表
				let data = {
					pageSize: 3,
					pageNum: this.pageNum,
				};
				this.$axios.get(this.$api.getDeptPage, data).then((res) => {
					if (res.data.code == 200) {
						let item=res.data.rows
						let typesValue = item.length == 3 ? 1 : 2;
						let dtnow;
						if (this.pageNum == 1) {
						  dtnow = [];
						} else {
						  dtnow = this.deptPageList;
						}
						let dtnows = dtnow.concat(item);
						this.deptPageList = dtnows;
						this.types = typesValue;
					}
				});
			},
			remove(id) {
				uni.showModal({
					title: '提示',
					content: '是否确定删除该部门动态?',
					success: (res) => {
						uni.hideLoading();
						if (res.cancel) {
							console.log('用户点击取消');
						} else if (res.confirm) {
							console.log('用户点击确定');
							this.getdelStaff(id);
						}
					},
				});
			},
			getdelStaff(id) {
				//删除部门动态
				let data = {
					id: id,
				};
				this.$axios.get(this.$api.getDeptRemove, data).then((res) => {
					if (res.data.code == 200) {
						this.deptPageList=[]
						this.pageNum=1
						uni.showToast({
							title: '删除成功',
							icon: 'none',
							duration: 3000, //持续的时间
						});
						this.deptPage()
					}
				});
			}
		},
	};
</script>
<style scoped>
	@import "./nurseteam.css";
</style>