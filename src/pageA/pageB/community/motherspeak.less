.main {
  background: #f8f9f9;
  overflow-x: hidden;
}

.next {
  display: flex;
  width: 100%;
  justify-content: center;
  z-index: 10000;
  margin-bottom: 30rpx;
  position: fixed;
  top: 0;
}

.activeNext {
  display: flex;
  width: 100%;
  justify-content: center;
  z-index: 10000;
  margin-bottom: 30rpx;
  position: fixed;
}

.headImg {
  position: absolute;
  left: 24rpx;
  bottom: 20rpx;
}

.headTitle {
  font-size: 34rpx;
  color: #333333;
  font-weight: bold;
  position: absolute;
  bottom: 20rpx;
}

.mainss {
  background-size: 100% 478rpx;
  background-image: url('https://cdn.xiaodingdang1.com/2024/11/09/c1e8a3924cef4dc38e2765ef8cd1f36e.png');
  padding: 184rpx 0rpx 0rpx;
}

.text {
  color: hsla(0, 0%, 20%, 1);
  font-size: 34rpx;
  padding: 20rpx 24rpx;
}

.roll {
  display: flex;
  padding: 0rpx 24rpx;
  overflow-x: auto;
}

.motherImg {
  width: 120rpx;
  text-align: center;
  margin-right: 42rpx;
}

.rollText {
  margin: 12rpx 0 20rpx 0;
  color: #333333;
}

.rollTextActive {
  margin: 12rpx 0 20rpx 0;
  color: #fe5b86;
  font-weight: 600;
}

.mains {
  padding: 0 24rpx;
}

.text1 {
  color: hsla(0, 0%, 20%, 1);
  font-size: 34rpx;
  padding: 20rpx 0rpx;
}

.packageText {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 24rpx;
}

.packageText1 {
  font-size: 34rpx;
  color: hsla(0, 0%, 20%, 1);
  font-weight: bold;
}

.packageText2 {
  display: flex;
  color: #aaaaaa;
  font-size: 28rpx;
  align-items: center;
}

.more {
  font-size: 22rpx;
  color: hsla(0, 0%, 67%, 1);
}

.packageImg {
  padding-left: 8rpx;
}

.packageImgs {
  width: 8rpx;
  height: 18rpx;
}

.topic {
  background: #ffffff;
  border-radius: 20rpx;
  margin: 0rpx 24rpx 20rpx 24rpx;
  padding: 24rpx 0;
  position: relative;
}

.topic1 {
  width: 96%;
  display: flex;
  align-items: flex-end;
  margin: 0rpx auto;
}

.topic1Text1 {
  margin-bottom: 12rpx;
  height: 42rpx;
}

.topic1Text1_1 {
  color: #ff4f61;
  font-size: 30rpx;
  font-weight: bold;
}

.topic1Text1_2 {
  color: hsla(0, 0%, 20%, 1);
  font-size: 30rpx;
  margin-left: 10rpx;
}

.topic1Text2 {
  margin-right: 30rpx;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  margin-bottom: 38rpx;
  height: 68rpx;
}

.topicImg {
  width: 30%;
  height: 200rpx;
  border-radius: 10rpx;
}

.topic2 {
  width: 68%;
  margin-left: 2%;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.topic1Text2 {
  line-height: 30rpx;
}

.topic1Text2_1 {
  color: hsla(0, 0%, 20%, 1);
  font-size: 24rpx;
}

.topic1Text2_2 {
  color: hsla(0, 0%, 47%, 1);
  font-size: 24rpx;
}

.topic1Text3 {
  display: flex;
  justify-content: space-between;
  margin-right: 12rpx;
}

.topic1Text3_1 {
  color: hsla(0, 0%, 67%, 1);
  font-size: 24rpx;
}

.topic1Text3_2 {
  display: flex;
  align-items: center;
}

/* .topic1Text3topic1Text3{
	width: 40rpx;
	height: 40rpx;
	border: none;
	border-radius: 100rpx;
} */
.title {
  position: absolute;
  right: 0;
  top: 0;
  background: hsla(44, 100%, 83%, 1);
  color: hsla(7, 100%, 50%, 1);
  border-top-right-radius: 20rpx;
  border-bottom-left-radius: 20rpx;
  width: 74rpx;
  height: 44rpx;
  text-align: center;
  line-height: 44rpx;
  font-size: 24rpx;
}

/*宝妈讨论*/
.discuss {
  background: #ffffff;
  margin-bottom: 16rpx;
}

.discus {
  padding: 24rpx 24rpx 0 24rpx;
}

.discuss1 {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.discuss1_1 {
  display: flex;
}

.discuss1_2 {
  margin-left: 16rpx;
}

.discuss1_3 {
  color: hsla(26, 100%, 50%, 1);
  font-size: 30rpx;
}

.discuss1_4 {
  font-size: 24rpx;
  color: hsla(0, 0%, 67%, 1);
  margin-top: 4rpx;
}

.discussName {
  margin-left: 24rpx;
}

.discussTitle {
  color: hsla(0, 0%, 47%, 1);
  font-size: 24rpx;
  margin-left: 6rpx;
}

.discuss2 {
  margin-top: 16rpx;
  font-size: 28rpx;
}

.beyonds {
  margin-top: 16rpx;
  font-size: 28rpx;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 5;
}

.discuss2_1 {
  color: hsla(228, 25%, 48%, 1);
}

.discuss2_2 {
  color: hsla(0, 0%, 20%, 1);
}

.discuss3 {
  margin: 20rpx 0 24rpx 0;
  display: flex;
  flex-wrap: wrap;
}

.discuss3_1 {
  width: 220rpx;
  height: 220rpx;
}

.discuss3 image {
  margin-left: 15rpx;
  margin-bottom: 15rpx;
}

.discuss1_5 {
  display: flex;
  align-items: center;
}

.discuss4 {
  width: 100%;
  height: 1rpx;
  background: hsla(0, 0%, 92%, 1);
}

.discuss5 {
  display: flex;
  justify-content: space-around;
  padding: 26rpx 0 32rpx 0;
  color: hsla(0, 0%, 47%, 1);
  font-size: 24rpx;
}

.discuss5_2 {
  margin-left: 4rpx;
}

.blank {
  height: 150rpx;
}

.tab {
  display: flex;
  margin-top: 20rpx;
  position: relative;
}

.tab view {
  width: 50%;
  text-align: center;
}

.tabActivLeft {
  color: #7e6dfc;
  font-size: 36rpx;
  font-weight: bold;
  height: 84rpx;
  border-top-right-radius: 80rpx;
  border-top-left-radius: 50rpx;
  background: -webkit-gradient(
    linear,
    left top,
    left bottom,
    color-stop(0%, #ecf4fa),
    color-stop(100%, #f9fafa)
  );
  background: -webkit-linear-gradient(180deg, #ecf4fa 0%, #f9fafa 100%);
  background: -o-linear-gradient(180deg, #ecf4fa 0%, #f9fafa 100%);
  background: -ms-linear-gradient(180deg, #ecf4fa 0%, #f9fafa 100%);
  background: linear-gradient(180deg, #ecf4fa 0%, #f9fafa 100%);
  line-height: 84rpx;
}

.tabActivRight {
  color: #7e6dfc;
  font-size: 36rpx;
  font-weight: bold;
  height: 84rpx;
  border-top-right-radius: 50rpx;
  border-top-left-radius: 80rpx;
  line-height: 84rpx;
}

.underline {
  background: #7e6dfc;
  height: 10rpx;
  border-radius: 60rpx;
  position: absolute;
  bottom: 0;
}

.tabs {
  color: #aaaaaa;
  font-size: 36rpx;
  height: 84rpx;
  line-height: 84rpx;
  font-weight: bold;
}

.backContentLeft {
  background: -webkit-gradient(
    linear,
    left top,
    left bottom,
    color-stop(0%, #ecf4fa),
    color-stop(100%, #f9fafa)
  );
  background: -webkit-linear-gradient(180deg, #ecf4fa 0%, #f9fafa 100%);
  background: -o-linear-gradient(180deg, #ecf4fa 0%, #f9fafa 100%);
  background: -ms-linear-gradient(180deg, #ecf4fa 0%, #f9fafa 100%);
  background: linear-gradient(180deg #ecf4fa 0%, #f9fafa 100%);
  width: 100%;
  border-top-right-radius: 30rpx;
  padding-bottom: 40rpx;
}

.backContentRight {
  width: 100%;
  border-top-left-radius: 30rpx;
  padding-bottom: 40rpx;
}

.mains {
  background: url('http://cdn.xiaodingdang1.com/1/120240129/7670640b-a0e7-4e4d-a06c-14f2115b17d8.png')
    no-repeat;
  background-size: 100% 478rpx;
}

.mains {
  padding: 174rpx 0rpx 0rpx;
}

.text {
  color: hsla(0, 0%, 20%, 1);
  font-size: 34rpx;
  padding: 20rpx 24rpx;
  font-weight: bold;
}

.roll {
  display: flex;
  padding: 0rpx 24rpx;
  overflow-x: auto;
}

.rollImg {
  width: 120rpx;
  text-align: center;
  margin-right: 42rpx;
}

.rollText {
  margin: 12rpx 0 20rpx 0;
}

.tag {
  display: flex;
  align-items: center;
  margin-left: 24rpx;
}

.tabList {
  display: flex;
  align-items: center;
  padding: 0rpx 24rpx;
  overflow-x: auto;
  margin: 10rpx 0 20rpx 0;
}

.tab-h {
  width: 100%;
  box-sizing: border-box;
  font-size: 34rpx;
  white-space: nowrap;
}

.tab-h1 {
  white-space: nowrap;
  color: #333333;
  font-size: 28rpx;
  padding: 10rpx 0;
}

.tab-item {
  display: inline-block;
  padding: 20rpx 30rpx;
  border-radius: 66rpx;
  color: #7e6dfc;
  font-size: 32rpx;
  border: 1px solid #7e6dfc;
  margin-right: 28rpx;
  background: #fff;
}

.border-triangle-bottom {
  padding: 20rpx 30rpx;
  position: relative;
  border-radius: 1px;
  border-radius: 66rpx;
  color: #fff;
  font-size: 32rpx;
  margin-right: 28rpx;
  background: #7e6dfc;
}

.tab-item1 {
  display: inline-block;
  padding: 10rpx 20rpx;
  border-radius: 10rpx;
  color: #777777;
  font-size: 28rpx;
  margin-right: 28rpx;
  background: #e6e5e5;
}

.border-triangle-bottom1 {
  padding: 10rpx 20rpx;
  position: relative;
  border-radius: 1px;
  border-radius: 10rpx;
  color: #fff;
  font-size: 28rpx;
  margin-right: 28rpx;
  background: #7e6dfc;
}

/* 
.border-triangle-bottom:after,
.border-triangle-bottom:before {
	content: "";
	position: absolute;
	width: 0;
	height: 0;
	border: 4px solid transparent;
	border-top-color: #7E6DFC;
	left: 50%;
	margin-left: -4px;
	bottom: -12px;
} */

.border-triangle-bottom:after {
  border-top-color: #fff;
  bottom: -7px;
}

.text1 {
  color: hsla(0, 0%, 20%, 1);
  font-size: 34rpx;
  padding: 20rpx 24rpx;
  font-weight: bold;
}

/*今日餐食*/
.endure {
  background: #fff;
  width: 250rpx;
  margin-right: 20rpx;
  border-bottom-left-radius: 10rpx;
  border-bottom-right-radius: 10rpx;
}

.endure1 {
  display: flex;
  padding: 0rpx 24rpx 20rpx 24rpx;
  overflow-x: auto;
}

.endureTitle {
  padding: 16rpx 16rpx;
  font-size: 28rpx;
  color: #333333;
}

/*宝妈说*/

.preciousMothers {
  padding: 24rpx 24rpx;
  background: #fff;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  min-height: 316rpx;
  margin: 0 24rpx;
}

.preciousMother {
  /* padding: 24rpx 24rpx; */
  background: #fff;
  /* border-radius: 20rpx; */
  min-height: 316rpx;
  margin: 24rpx 24rpx;
}

.preciousMother1 {
  padding: 24rpx 24rpx;
}

.mother1 {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.lengthStay {
  background: url('http://cdn.xiaodingdang1.com/2024/08/20/c707e0287940456787f1cef4f1dec273.png')
    no-repeat;
  width: 190rpx;
  height: 88rpx;
  background-size: 100% 100%;
  color: #ff4f61;
  font-size: 26rpx;
  font-weight: bold;
  text-align: center;
  line-height: 100rpx;
}

.lengthStays {
  background: url('http://cdn.xiaodingdang1.com/2024/08/20/46a44753a8844ba1880758c8fc1cbfc0.png')
    no-repeat;
  width: 190rpx;
  height: 88rpx;
  background-size: 100% 100%;
  color: #ff6c11;
  font-size: 26rpx;
  font-weight: bold;
  text-align: center;
  line-height: 100rpx;
}

.mother3 {
  display: flex;
}

.mother1_2 {
  margin-left: 10rpx;
}

.mother1Text1 {
  color: hsla(0, 0%, 67%, 1);
  font-size: 24rpx;
  margin-top: 8rpx;
}

.mother1Text2 {
  color: hsla(26, 100%, 50%, 1);
  font-size: 30rpx;
}

.mother1Text3 {
  background: hsla(40, 100%, 90%, 1);
  color: hsla(15, 97%, 50%, 1);
  border-radius: 10rpx;
  padding: 4rpx 8rpx;
  font-size: 24rpx;
  font-weight: bold;
}

.motherImgs {
  margin: 20rpx 0 24rpx 0;
  display: flex;
  flex-wrap: wrap;
}

.motherImgs image {
  width: 220rpx;
  height: 220rpx;
  margin-left: 10rpx;
  margin-bottom: 15rpx;
}

/* .motherImg image:nth-of-type(1){
  margin-right: 20rpx;
}
.motherImg image:nth-of-type(2){
  margin-right: 20rpx;
} */
.discuss4 {
  width: 100%;
  height: 1rpx;
  background: hsla(0, 0%, 92%, 1);
}

.discuss5 {
  display: flex;
  justify-content: space-around;
  padding: 26rpx 0 32rpx 0;
  color: hsla(0, 0%, 47%, 1);
  font-size: 24rpx;
}

.discuss5_2 {
  margin-left: 4rpx;
}

.discuss5Active_2 {
  color: #ff4f61;
  font-size: 24rpx;
}

.score {
  display: flex;
  margin-top: 12rpx;
  align-items: center;
  flex-wrap: wrap;
}

.score1 {
  color: #333333;
  font-size: 28rpx;
  font-weight: bold;
}

.score2 {
  padding: 4rpx 8rpx;
  color: #fff;
  background: #b0b0ff;
  font-size: 24rpx;
  border-radius: 4rpx;
  margin-right: 10rpx;
}

.score3 {
  color: #777777;
  font-size: 24rpx;
  margin: 20rpx 0 0 0;
}

.blank {
  height: 150rpx;
}

.mmcomment {
  color: #7e6dfc;
}

.mmcomments {
  color: #777777;
  margin-left: 8rpx;
}

.comment2 {
  padding: 6rpx 0;
}

.comment1 {
  margin: 24rpx 0;
  font-size: 24rpx;
}

.head1_2 {
  display: flex;
  position: relative;
  margin-left: 16rpx;
}

.head1_3 {
  background: #ffeecc;
  padding: 0 8rpx 0 30rpx;
  border-radius: 10rpx;
  color: #fb4105;
  font-size: 24rpx;
  height: 34rpx;
}

.head1Img {
  position: absolute;
  left: -10rpx;
}

.avatar-list-stacked .avatar {
  box-shadow: 0 0 0 1px #fff;
}

.avatar-list-stacked .avatar {
  margin-right: -0.8em !important;
}

.avatar {
  width: 1.5rem;
  height: 1.5rem;
  line-height: 5rem;
  display: inline-block;
  background: #2ddcd3 no-repeat center/cover;
  position: relative;
  text-align: center;
  color: #fff;
  font-weight: 600;
  vertical-align: bottom;
  font-size: 0.875rem;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  border-radius: 50%;
}

.contentTabs {
  display: flex;
}

.contentTab {
  display: flex;
  align-items: center;
  padding: 24rpx 20rpx;
  margin-top: 50rpx;
  justify-content: space-around;
}

.title-sel-selected {
  color: #ff4f61;
  font-size: 36rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  font-weight: bold;
  margin-right: 36rpx;
  width: 50vw;
}

.title-sel-selected .line-style {
  background: #ff4f61;
  height: 8rpx;
  width: 30rpx;
  position: relative;
  margin-top: 10rpx;
  border-radius: 20rpx;
}

.title-sel {
  color: #aaaaaa;
  font-size: 36rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  font-weight: Regular;
  height: 56rpx;
  margin-right: 36rpx;
  width: 50vw;
}

.main2 {
  margin-top: 20rpx;
  padding: 20rpx 24rpx;
  background: #ffffff;
  border-radius: 10px 10px 0px 0px;
}

.head {
  display: flex;
  justify-content: space-between;
}

.head1 {
  display: flex;
  align-items: center;
}

.head2 {
  display: flex;
  align-items: center;
}

.head1_1 {
  width: 10rpx;
  height: 30rpx;
  background: #ff4f61;
  border-radius: 5rpx;
}

.head1_2_1 {
  font-size: 34rpx;
  font-weight: bold;
  color: #333333;
  margin-left: 20rpx;
}

.head2_1 {
  color: #aaaaaa;
  font-size: 22rpx;
  margin-right: 10rpx;
}

.nexts {
  background: #fff3f7;
  padding: 12rpx 20rpx;
  border-radius: 10rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20rpx;
}

.next1 {
  display: flex;
  align-items: center;
}

.next1_1 {
  width: 75%;
  margin-left: 12rpx;
  color: #333333;
  font-size: 28rpx;
  word-break: break-all;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.nextImg {
  position: relative;
}

.serialNumber {
  position: absolute;
  left: 13rpx;
  top: 5rpx;
  font-size: 24rpx;
  color: #ffffff;
}

.tagName {
  padding: 4rpx 16rpx;
  background: #ff4f61;
  border-radius: 2px 2px 2px 2px;
  color: #ffffff;
  font-size: 24rpx;
  margin-left: 10rpx;
}

.img_box {
  margin-top: 20rpx;
  padding-left: 4rpx;
}

.videos {
  width: 340rpx;
  height: 340rpx;
}

.videos video {
  width: 100%;
  height: 100%;
  border-radius: 10rpx;
}

.img_box .many_img {
  display: flex;
  justify-self: start;
  flex-wrap: wrap;
}

.img_item.four {
  width: 32%;
  height: 220rpx;
  margin-bottom: 10rpx;
  border-radius: 16rpx;
  overflow: hidden;
}

.img_item.four image {
  width: 100%;
  height: 100%;
}

.img_item.four:nth-child(2) {
  margin: 0 1%;
}

.img_item.four:nth-child(5) {
  margin: 0 1%;
}

.img_item.four:nth-child(8) {
  margin: 0 1%;
}

.img_item.many {
  width: 48%;
  height: 340rpx;
  margin-bottom: 10rpx;
  border-radius: 10px;
  overflow: hidden;
}

.img_item.many image {
  width: 100%;
  height: 100%;
}

.img_item.many:nth-child(2n) {
  margin-left: 1%;
}

.discusss5 {
  display: flex;
  justify-content: space-around;
  align-items: center;
  color: hsla(0, 0%, 47%, 1);
  font-size: 28rpx;
}

.discusss6 {
  color: hsla(0, 0%, 47%, 1);
  font-size: 28rpx;
}

.loadImg.active {
  transition: all 3s ease-in-out;
  opacity: 1;
}

.custom-button {
  background: #fff;
  color: hsla(0, 0%, 47%, 1);
  font-size: 24rpx;
  margin: 0;
}

.discuss5_1 {
  display: flex;
  align-items: center;
  justify-content: center;
}

.packUp {
  color: #4d669b;
  font-size: 30rpx;
}

.mother2 {
  font-size: 30rpx;
  color: hsla(0, 0%, 20%, 1);
  margin: 16rpx 0 10rpx 0;
}

.beyond {
  font-size: 30rpx;
  color: hsla(0, 0%, 20%, 1);
  margin: 16rpx 0 10rpx 0;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 5;
}

/**服务节点**/
.node {
  padding: 28rpx 28rpx;
  border: 2rpx solid #ff4f61;
  margin: 0rpx 24rpx 0 24rpx;
  border-radius: 20rpx;
  background: -webkit-gradient(
    linear,
    left top,
    left bottom,
    color-stop(10%, #f5c3c9),
    color-stop(100%, #f9fafa)
  );
  background: -webkit-linear-gradient(180deg, #f5c3c9 10%, #f9fafa 100%);
  background: -o-linear-gradient(180deg, #f5c3c9 10%, #f9fafa 100%);
  background: -ms-linear-gradient(180deg, #f5c3c9 10%, #f9fafa 100%);
  background: linear-gradient(180deg, #f5c3c9 10%, #f9fafa 100%);
  position: relative;
}

.nodeTitle {
  color: #ff4f61;
  font-size: 32rpx;
  font-weight: bold;
}

.nodeTitle1 {
  color: #ff4f61;
  font-size: 20rpx;
  margin: 8rpx 0 40rpx 0;
}

.nodeImg {
  position: absolute;
  top: 0;
  right: 32rpx;
}

.nodeNext {
  border-radius: 10rpx;
  margin-top: 20rpx;
}

.nodeNext2 {
  width: 85%;
  margin-left: 12rpx;
  color: #333333;
  font-size: 24rpx;
  word-break: break-all;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.nodeNext1 {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nodeFGX {
  background: #ebebeb;
  margin-top: 24rpx;
  height: 1rpx;
}

.newTab {
  display: flex;
  margin: 40rpx 24rpx;
  align-items: center;
}

.newTab1 {
  margin-right: 56rpx;
}

.newTab2 {
  color: #333333;
  font-size: 28rpx;
  height: 51rpx;
}

.headTab {
  height: 140rpx;
  display: flex;
  justify-content: space-around;
  align-items: center;
  // margin-top: 70rpx;
  // background: -o-linear-gradient(180deg, #9e9e9e1f 0%, #9e9e9e30 100%);
  // background: -ms-linear-gradient(180deg, #9e9e9e1f 0%, #9e9e9e30 100%);
  // background: linear-gradient(180deg, #9e9e9e1f 0%, #9e9e9e30 100%);
}
.contentBg {
  // background-color: #9e9e9e30;
}
.relativeBottom {
  position: relative;
  bottom: 10rpx;
}
.headTab1 {
  color: #aaaaaa;
  font-size: 36rpx;
  font-weight: bold;
  height: 58rpx;
}

.ageLimit {
  color: #aaaaaa;
  font-size: 28rpx;
  font-weight: bold;
  margin: 16rpx 0;
}

.ageLimit1 {
  color: #ff6c11;
  margin: 0 26rpx 0 8rpx;
}

.ageLimit2 {
  color: #ff6c11;
  margin-left: 8rpx;
}

.ageLimit3 {
  display: flex;
  align-items: center;
  padding: 8rpx 8rpx;
  border-radius: 10rpx;
  background: #fff1ed;
}

.ageLimit4 {
  color: #ff6c11;
  font-size: 24rpx;
  margin: 0 12rpx;
  font-weight: bold;
}

.ageLimit5 {
  padding: 4rpx 8rpx;
  border-radius: 10rpx;
  background: -webkit-gradient(
    linear,
    left top,
    left bottom,
    color-stop(0%, #ffaa00),
    color-stop(100%, #f96323)
  );
  background: -webkit-linear-gradient(180deg, #ffaa00 0%, #f96323 100%);
  background: -o-linear-gradient(180deg, #ffaa00 0%, #f96323 100%);
  background: -ms-linear-gradient(180deg, #ffaa00 0%, #f96323 100%);
  background: linear-gradient(180deg #ffaa00 0%, #f96323 100%);
  font-size: 24rpx;
  color: #ffffff;
  font-weight: bold;
}

.content {
  .content-tab {
    // background-color: #9e9e9e30;

    /deep/.u-tabs {
      border-radius: 40rpx 40rpx 0 0;
      padding-bottom: 8rpx;
      background: linear-gradient(
        to bottom,
        #ffffff,
        0%,
        #fffffc 100%
      ) !important;
    }

    /deep/.u-tab-item {
      height: 60rpx !important;
      line-height: 60rpx !important;
    }

    /deep/.u-tab-bar {
      background: linear-gradient(270deg, #fd997b 0%, #ff4f61 100%) !important;
    }
  }

  .content-main {
    padding: 0 12rpx 80rpx 12rpx;
  }
}
