.mains {
	background: hsl(180, 8%, 97%);
}

.main {
	margin: 20rpx auto;
}

.headImg image {
	display: inline-block;
	width: 100%;
	min-height: 500rpx;
}

.title {
	font-size: 44rpx;
	font-weight: bold;
	color: hsla(0, 0%, 20%, 1);
	margin-top: 20rpx;
}

.titles {
	font-size: 32rpx;
	font-weight: bold;
	margin: 20rpx 0;
}

.content {
	color: #777777;
	font-size: 26rpx;
	margin-top: 10rpx;
}

.contentTabs {
	width: 100%;
	background: #ffffff;
	border-radius: 20rpx;
	margin-top: 36rpx;
}

.contentTab1 {
	width: 94%;
	margin: 0rpx auto;
	padding: 24rpx 0;
}

.contentTab {
	display: flex;
	justify-content: space-around;
	align-items: center;
}

/*tab*/
.title-sel {
	color: #333333;
	font-size: 32rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	font-weight: bold;
	height: 56rpx;
}

.title-sel-selected {
	color: #ff4f61;
	font-size: 32rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	font-weight: bold;
}

.title-sel-selected .line-style {
	background: #ff4f61;
	height: 6rpx;
	width: 40rpx;
	position: relative;
	margin-top: 10rpx;
	border-radius: 20rpx;
}

#headImg {
	margin: 20rpx 0;
}

.foodIngredient {
	display: flex;
	width: 100%;
	overflow-x: auto;
}

.foodIngredientImg {
	text-align: center;
}

.foodIngredientImg image {
	width: 160rpx;
	height: 160rpx;
}

.tag {
	position: fixed;
	left: 10rpx;
	bottom: 400rpx;
}

.tag view {
	margin-bottom: 10rpx;
}

.title1 {
	color: hsla(0, 0%, 20%, 1);
	font-size: 34rpx;
	font-weight: bold;
	margin: 20rpx 0;
}

.tabList {
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
	margin-top: 20rpx;
}

.tabList1 {
	width: 48.5%;
	margin-bottom: 20rpx;
}

.tabList1_1 {
	color: hsla(0, 0%, 20%, 1);
	font-size: 28rpx;
	margin-top: 10rpx;
}

.tabList1_2 {
	color: hsla(0, 0%, 47%, 1);
	font-size: 24rpx;
}

/*轮播图*/
.head {
	background: #ffffff;
	padding: 20rpx 24rpx;
}

#head {
	padding: 20rpx 24rpx 0 24rpx;
}

.parent {
	position: relative;
	height: 342rpx;
}

.dots {
	position: absolute;
	right: 20rpx;
	bottom: 20rpx;
	width: 70rpx;
	height: 35rpx;
	line-height: 35rpx;
	text-align: center;
	font-size: 24rpx;
	border-radius: 20rpx;
	background-color: rgba(0, 0, 0, 0.4);
	color: #fff;
}

/*膳食套餐*/
.food {
	padding: 24rpx 24rpx;
	border-radius: 20rpx;
	display: flex;
	margin-bottom: 20rpx;
	height: 200rpx;
}

.foodss {
	position: relative;
}

.foods {
	padding: 24rpx 24rpx;
	background: #ffffff;
	margin-top: 20rpx;
}

.foodTitle {
	display: flex;
	align-items: center;
	margin-top: 16rpx;
}

.foodTitle1 {
	color: #aaaaaa;
	font-size: 24rpx;
}

.foodTitle2 {
	font-size: 30rpx;
	color: #333333;
	font-weight: bold;
}

.fgx {
	width: 2rpx;
	height: 20rpx;
	background: #ebebeb;
	margin: 0 8rpx;
}

.food2 {
	margin-left: 24rpx;
	width: 430rpx;
	text-align-last: auto;
}

.price {
	display: flex;
	justify-content: space-between;
	margin-top: 22rpx;
	align-items: flex-end;
}

.price1 {
	color: #ff7a00;
	font-size: 32rpx;
	width: 50%;
	overflow: hidden;
	font-weight: bold;
}

.priceBtn {
	border-radius: 54rpx;
	width: 136rpx;
	height: 56rpx;
	text-align: center;
	line-height: 56rpx;
	background: #ff4f61;
	color: #ffffff;
	font-size: 28rpx;
}

.priceBtns {
	border-radius: 54rpx;
	width: 136rpx;
	height: 56rpx;
	text-align: center;
	line-height: 56rpx;
	background: #ff7a00;
	color: #ffffff;
	font-size: 28rpx;
}

.tabContent {
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
	padding: 0 24rpx;
}

.tabContent1 {
	margin-bottom: 20rpx;
}

.tabTitle1 {
	color: #333333;
	font-size: 28rpx;
}

.tabTitle2 {
	color: #777777;
	font-size: 24rpx;
}

.heads {
	display: flex;
	justify-content: space-between;
}

.head1 {
	display: flex;
	align-items: center;
}

.head2 {
	display: flex;
	align-items: center;
}

.head1_1 {
	width: 10rpx;
	height: 30rpx;
	background: #ff4f61;
	border-radius: 5rpx;
}

.head1_2 {
	font-size: 34rpx;
	font-weight: bold;
	color: #333333;
	margin-left: 20rpx;
}

#head1_1 {
	width: 10rpx;
	height: 30rpx;
	background: #ff4f61;
	border-radius: 5rpx;
}

.main2 {
	margin-top: 20rpx;
	padding: 20rpx 24rpx;
	background: #ffffff;
}

.next1 {
	display: flex;
	align-items: center;
	width: 90%;
}

.next1_1 {
	width: 90%;
	margin-left: 12rpx;
	color: #333333;
	font-size: 28rpx;
	overflow: hidden;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 1;
	text-align: left;
}

.next {
	background: #fff3f7;
	padding: 12rpx 20rpx;
	border-radius: 10rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-top: 10rpx;
}

.next2 {
	width: 20%;
}

.next3 {
	display: flex;
	justify-content: space-between;
}

#heads {
	margin-bottom: 20rpx;
}

/* .btn{
	position: fixed;
	bottom: 0;
	display: flex;
	align-items: center;
	background: #ffffff;
	width: 100%;
	padding: 20rpx 0 68rpx 0;
	justify-content: space-around;
}
.consult{
	display: flex;
	padding: 20rpx 114rpx;
	background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #FFD9DF), color-stop(100%, #FFF5D8));
  background: -webkit-linear-gradient(top, #FFD9DF 0%, #FFF5D8 100%);
  background: -o-linear-gradient(top, #FFD9DF 0%, #FFF5D8 100%);
	background: -ms-linear-gradient(top, #FFD9DF 0%, #FFF5D8 100%);
	border-radius: 100rpx;
}
.consultTitle{
	margin-left: 10rpx;
	color: #F84343;
	font-size: 28rpx;
}
.btn1{
	text-align: center;
	color: #333333;
	font-size: 24rpx;
} */
.blank {
	height: 150rpx;
}

.detail-banner-img {
	width: 100%;
	height: 342rpx;
}

.roomComments {
	color: #aaaaaa;
	font-size: 28rpx;
	font-weight: Medium;
	text-align: center;
	padding: 60rpx 0;
}

.consultTitle {
	margin-left: 10rpx;
	color: #ffffff;
	font-size: 28rpx;
}

.todayMenu {
	background: #fff;
	padding: 26rpx 24rpx;
	margin-top: 20rpx;
}

.todayMenuTitle {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.todayMenufgx {
	width: 10rpx;
	height: 28rpx;
	border-radius: 50rpx;
	background: #FF4F61;
}
.todayMenuName {
	color: #333333;
	font-size: 34rpx;
	margin-left: 20rpx;
	font-weight: bold;
}
.todayMenuComent{
	display: flex;
	margin: 24rpx 0;
}
.todayMenuComent1{
	background: #FF4F61;
	width: 60rpx;
	height: 60rpx;
	text-align: center;
	line-height: 80rpx;
	border-radius: 100rpx;
}
.todayMenuComent1_1{
	width: 10%;
}
.todayMenuComent2{
	margin-left: 32rpx;
	width: 90%;
}
.todayMenuComent2_1{
	color: #333333;
	font-size: 28rpx;
	font-weight: bold;
}
.todayMenuComent2_2{
	color: #5C5C5C;
	font-size: 24rpx;
}
.cutoffrule{
	width: 100%;
	background: #F0F0F0;
	height: 1rpx;
}

.todayMenuTitle1{
	display: flex;
	align-items: center;
}
.todayMenuTitle2{
	width: 150rpx;
	height: 42rpx;
}
.todayMenuTitle2 image{
	width: 100%;
	height: 100%;
}
