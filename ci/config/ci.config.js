/**
 * 小程序 CI 多项目配置
 * 支持多个 appid 项目的构建和上传
 * 
 * 此文件由配置管理器自动生成，请勿手动编辑
 * 生成时间: 2025/7/30 18:38:01
 */

const path = require('path');

// 项目配置
const projects = {
  main: {
    name: "艾尔母婴商家管理平台",
    appid: "wx1df89e340decbc7a",
    projectPath: "/Users/<USER>/Documents/槿墨工作区/aier-merchant-uniapp/dist/build/mp-weixin",
    privateKeyPath: "/Users/<USER>/Documents/槿墨工作区/aier-merchant-uniapp/ci/private/private.wx1df89e340decbc7a.key",
    defaultTenantId: "194338",
    ignores: [
      "node_modules/**/*"
    ],
    environments: {
      develop: {
        name: "开发版",
        desc: "开发环境版本",
        buildCommand: "npx uni build -p mp-weixin --mode test",
        tenantId: "194338"
      },
      trial: {
        name: "体验版",
        desc: "体验环境版本",
        buildCommand: "npx uni build -p mp-weixin --mode trial",
        tenantId: "194338"
      },
      release: {
        name: "正式版",
        desc: "生产环境版本",
        buildCommand: "npx uni build -p mp-weixin --mode production",
        tenantId: "194338"
      }
    }
  },
  xinxiyue: {
    name: "馨喜月月子会所平台",
    appid: "wxc05ce1faaa835bb2",
    projectPath: "/Users/<USER>/Documents/槿墨工作区/aier-merchant-uniapp/dist/build/mp-weixin",
    privateKeyPath: "/Users/<USER>/Documents/槿墨工作区/aier-merchant-uniapp/ci/private/private.wxc05ce1faaa835bb2.key",
    defaultTenantId: "123456",
    environments: {
      develop: {
        name: "开发版",
        desc: "开发环境版本",
        buildCommand: "npx uni build -p mp-weixin --mode test",
        tenantId: "123456"
      },
      trial: {
        name: "体验版",
        desc: "体验环境版本",
        buildCommand: "npx uni build -p mp-weixin --mode trial",
        tenantId: "123456"
      }
    },
    ignores: [
      "node_modules/**/*"
    ]
  }
};

// 默认配置
const defaultConfig = {
  version: {
    auto: true,
    format: "YYYY.MM.DD.HHmm"
  },
  upload: {
    desc: "通过 miniprogram-ci 自动上传",
    robot: 1,
    timeout: 300000
  },
  preview: {
    pagePath: "pages/index/index",
    searchQuery: "",
    scene: 1001,
    qrcodeFormat: "image",
    qrcodeOutputDest: "/Users/<USER>/Documents/槿墨工作区/aier-merchant-uniapp/preview-qrcode.jpg"
  }
};

module.exports = {
  projects,
  defaultConfig,
  
  // 获取项目配置
  getProject: (projectName) => {
    const project = projects[projectName];
    if (!project) {
      throw new Error(`项目 "${projectName}" 不存在，可用项目：${Object.keys(projects).join(', ')}`);
    }
    return project;
  },

  // 获取所有项目名称
  getProjectNames: () => Object.keys(projects),

  // 验证项目配置
  validateProject: (projectName) => {
    const project = projects[projectName];
    if (!project) {
      return { valid: false, error: `项目 "${projectName}" 不存在` };
    }

    // 检查必要的配置
    if (!project.appid) {
      return { valid: false, error: `项目 "${projectName}" 缺少 appid 配置` };
    }

    if (!project.privateKeyPath) {
      return { valid: false, error: `项目 "${projectName}" 缺少私钥路径配置` };
    }

    return { valid: true };
  }
};
