.main{
		background: linear-gradient(to bottom, #CBEDFF, #CBEDFF);
		margin-bottom: 100rpx;
		.blacks{
			height: 180rpx;
		}
		.content {
		    background-image: url('http://cdn.xiaodingdang1.com/2025/04/08/8221ad07db514c4a85bca98aa9dd6544.png');
		    background-size: cover;
			margin: 20rpx 20rpx;
			padding: 0 24rpx 10rpx 24rpx;
			
			}
}
.headimg{
	display: flex;
	justify-content: space-between;
	padding: 80rpx 40rpx 0 40rpx;
}
.contentImg1{
	position: relative;
	width: 30%;  
}
.contentImg1_1{
	width: 170rpx;
	height: 170rpx;
	border-radius: 100rpx;
}
.contentImg1_2{
	height: 56rpx;
	width: 56rpx;
	position: absolute;
	right: 10rpx;
	bottom: 40rpx;

}
.contentImg1_3{
	width: 290rpx;
	height: 200rpx;
}
.basics{
		background: #ffffff;
		padding: 20rpx 20rpx;
		border-radius: 20rpx;
		margin-bottom: 30rpx;
		.basics1{
			display: flex;
			padding: 30rpx 0rpx;
			.basics1_1{
				width: 30%;
			}
			.basics1_2{
				display: flex;
				width: 70%;
				color: #303133;
			}
		}
		.fgx{
			width: 100%;
			height: 1rpx;
			background: #F5F5F5;
		}
		.publish-form{
		}
}
.title{
	display: flex;
	align-items: center;
	color: #333333;
	.title1{
		font-size: 30rpx;
	}
	.title2{
		font-size: 20rpx;
		margin-left: 20rpx;
	}
}
.tagAbb{
 padding: 14rpx 20rpx;
	background: #E7F4FF;
	color: #1FA2FF;
	font-size: 22rpx;
	border-radius: 8rpx;
	margin-bottom: 40rpx;
	margin-left: 10rpx;
}

.tagAbbs{
	display: flex;
	flex-wrap: wrap;
	padding-top: 40rpx;
}
.tag{
   position: relative;
}
.tags{
	padding: 12rpx 20rpx 8rpx 20rpx;
	border: 1rpx solid #1FA2FF;
	font-size: 22rpx;
	margin-right: 10rpx;
	background: #EEF2FF;
	border-radius: 8rpx;
	color: #1FA2FF;
	font-size: 22rpx;
	display: flex;
	margin-bottom: 10rpx;
}
.delimg{
	width: 30rpx;
	height: 30rpx;
	margin-left: 15rpx;
}
.textarea{
	background: #F9F9F9;
	padding: 20rpx 20rpx;
	border-radius: 20rpx;
	width: 100%;
	margin-top: 40rpx;
}
.bootBtn{
	width: 100%;
	padding: 20rpx 0 62rpx 0;
	background: #ffffff;
	position: fixed;
	bottom: 0;
	z-index: 999;
}
.bootBtn1{
	width: 90%;
	height: 90rpx;
	background: #1FA2FF;
	border-radius: 108rpx;
	color: #ffffff;
	font-size: 30rpx;
	text-align: center;
	line-height: 90rpx;
	margin-left: 5%;
}
.black{
	height: 150rpx;
}
.tagAbbInp input{
	width: 200rpx;
	border: 1rpx solid #cccccc;
	height: 53rpx;
	border-radius: 8rpx;
	padding: 0 10rpx;
}
.publish-upload {
    display: block;
}