<template>
    <u-popup :zoom="false" :closeable="true" :mask-close-able="false" v-model="avatorName" mode="center"
        border-radius="20" @close="close">
        <view class="popUp2">
            <view class="popUpsContent">
                <image src="http://cdn.xiaodingdang1.com/2025/01/23/3c0c17411133461db0cd0b450e22671e.png" mode=""
                    class="pop-img" />
                <view class="lbox1">
                    <text>头像:</text>
                    <button class="avatar-wrapper" open-type="chooseAvatar" @chooseavatar="onChooseAvatar" slot="right">
                        <u-avatar :src="avatar" mode="square"></u-avatar>
                    </button>
                </view>
                <view class="lbox1">
                    <text>昵称:</text>
                    <input type="nickname" class="weui-input" :value="nickname" @blur="userNameInput"
                        placeholder="请输入昵称" />
                </view>
                <view class="surebtn" @tap="confirm">确定</view>
            </view>
        </view>
    </u-popup>
</template>

<script>
    export default {
        data() {
            return {
                avatar: '',
                nickname: '',
                secIcon: 'man'
            }
        },
        /**
         * 生命周期函数--监听页面加载
         */
        onLoad(options) {},
        /**
         * 生命周期函数--监听页面初次渲染完成
         */
        onReady() {},
        /**
         * 生命周期函数--监听页面显示
         */
        onShow() {},
        /**
         * 生命周期函数--监听页面隐藏
         */
        onHide() {},
        /**
         * 生命周期函数--监听页面卸载
         */
        onUnload() {},
        /**
         * 页面相关事件处理函数--监听用户下拉动作
         */
        onPullDownRefresh() {},
        /**
         * 页面上拉触底事件的处理函数
         */
        onReachBottom() {},
        /**
         * 用户点击右上角分享
         */
        onShareAppMessage() {},
        methods: {
            onChooseAvatar(e) {
                let that = this
                uni.uploadFile({
                    filePath: e.detail.avatarUrl,
                    name: 'file',
                    url: that.$api.uploadOSS,
                    formData: {},
                    header: {
                        'Content-Type': 'multipart/form-data',
                        Authorization: 'Bearer ' + uni.getStorageSync('token'),
                        'Accept-Encoding': 'gzip',
                        Clientid: '428a8310cd442757ae699df5d894f051'
                    },
                    success: function(rests) {
                        let data = JSON.parse(rests.data);
                        if (data.code == 200) {
                            // uni.hideLoading();
                            that.setData({
                                avatar: data.data.url
                            });
                        }
                    }
                })
            },
            //获取昵称输入内容
            userNameInput(e) {
                this.nickname = e.detail.value
            },
            confirm() {
                const data = {
                    avatar: this.avatar,
                    nickname: this.nickname,
                    userId: uni.getStorageSync('uid')
                }
                this.$axios.post(this.$api.userUpdateInfo, data).then((res) => {
                    this.$auth.getUserInfo().then(() => {
                        this.close()
                    })
                })
            },
            close() {
                this.$emit('updateInfo')
                this.setAvatorName(false)
            }
        }
    };
</script>
<style>
    @import './popUp.css';
</style>