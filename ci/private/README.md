# 私钥文件说明

此目录用于存放小程序上传所需的私钥文件。

## 私钥文件获取方式

1. 登录微信公众平台：https://mp.weixin.qq.com
2. 进入对应的小程序账号
3. 导航到：开发 -> 开发管理 -> 开发设置
4. 在"小程序代码上传"部分，点击"生成"按钮生成私钥
5. 下载私钥文件（.key 格式）

## 文件命名规范

请按照以下规范命名私钥文件：

- `main.key` - 主项目私钥文件（AppID: wx1df89e340decbc7a）
- `test.key` - 测试项目私钥文件
- `demo.key` - 演示项目私钥文件

## 安全注意事项

⚠️ **重要提醒**：

1. 私钥文件包含敏感信息，请勿提交到版本控制系统
2. 请妥善保管私钥文件，避免泄露
3. 建议定期更换私钥文件
4. 此目录已添加到 .gitignore 中，确保不会被意外提交

## 文件权限

建议设置私钥文件权限为 600（仅所有者可读写）：

```bash
chmod 600 private/*.key
```

## 使用说明

配置好私钥文件后，即可使用上传和预览脚本：

```bash
# 上传到主项目开发环境
node scripts/upload.js main develop

# 预览主项目
node scripts/preview.js main develop

# 上传到测试项目
node scripts/upload.js test develop
```
