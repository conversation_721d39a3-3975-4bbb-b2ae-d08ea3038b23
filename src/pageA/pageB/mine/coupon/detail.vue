<template>
    <view class="main">
        <view class="head">
            <image src="http://cdn.xiaodingdang1.com/Time_light%403x.png" mode="" style="width: 70rpx; height: 70rpx" />
            <view class="headTitle">待到店使用</view>
        </view>
        <view class="content">
            <view class="content1">
                <image src="http://cdn.xiaodingdang1.com/Rectangle%207084%403x.png" mode=""
                    style="width: 160rpx; height: 160rpx" />
                <view class="content2">
                    <view class="content1_3">宝宝游泳优惠券</view>
                    <view class="content1_1">有效期至2024年12月21日</view>
                    <view class="content1_3">￥99</view>
                </view>
            </view>
            <view class="content1_4">X <text class="sum">1</text></view>
        </view>
        <view class="cancel">
            <view class="cancelTitle">我的券码</view>
            <view class="cancel1">22 2584 125</view>
            <view class="cancel2">22 2584 125</view>
            <view class="status">
                <view class="status1">订单状态：</view>
                <view>未核销</view>
            </view> 
        </view>
    </view>
</template>

<script>
    export default {
        data() {
            return {};
        }
        /**
         * 生命周期函数--监听页面加载
         */
        ,
        onLoad(options) {},
        /**
         * 生命周期函数--监听页面初次渲染完成
         */
        onReady() {},
        /**
         * 生命周期函数--监听页面显示
         */
        onShow() {},
        /**
         * 生命周期函数--监听页面隐藏
         */
        onHide() {},
        /**
         * 生命周期函数--监听页面卸载
         */
        onUnload() {},
        /**
         * 页面相关事件处理函数--监听用户下拉动作
         */
        onPullDownRefresh() {},
        /**
         * 页面上拉触底事件的处理函数
         */
        onReachBottom() {},
        /**
         * 用户点击右上角分享
         */
        onShareAppMessage() {},

        onShareTimeline() {},
        methods: {}
    };
</script>
<style>
    @import './detail.css';
</style>