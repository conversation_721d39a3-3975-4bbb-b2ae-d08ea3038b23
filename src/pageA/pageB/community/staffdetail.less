.main {
    background: #f8f9f9;
	margin-top: 20rpx;
}

.mom-detail {
    position: relative;
}

.custom-button {
    background: #fff;
    color: hsla(0, 0%, 47%, 1);
    font-size: 24rpx;
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    line-height: 0;
}

.mainss {
    // background-color: white;
    // background: url('http://cdn.xiaodingdang1.com/2024/09/09/ef7454c13aca4a3da4fcad1dc69b1319.png') no-repeat;
    // background-size: 100% 988rpx;
    padding: 164rpx 0rpx 154rpx 0;
}

.swipper1 {
    height: 900rpx;
}

.u-wrp-bnr {
    width: 100%;
    height: 100%;
    display: block;
    position: relative;
    background: #f0f0f0;
}
.swiper-item-box{
	 display: flex;
	  justify-content: center; /* 水平居中 */
	  align-items: center;     /* 垂直居中 */
	  height: 100%;            /* 继承swiper高度 */
}
.lengthStay {
    background: url('http://cdn.xiaodingdang1.com/2024/08/20/c707e0287940456787f1cef4f1dec273.png') no-repeat;
    width: 190rpx;
    height: 88rpx;
    background-size: 100% 100%;
    color: #ff4f61;
    font-size: 26rpx;
    font-weight: bold;
    text-align: center;
    line-height: 100rpx;
}

.content {
    padding: 40rpx 24rpx;
    background: #fff;
}

.listHesds1 {
    display: flex;
    justify-content: space-between;
}

.listHesds {
    display: flex;
    align-items: center;
}

.listHesds_1 {
    margin-left: 20rpx;
}

.listHesds_2 {
    display: flex;
    align-items: center;
}

.listHesds_3 {
    color: #ff6e00;
    font-size: 30rpx;
}

.listHesds_4 {
    color: #aaaaaa;
    font-size: 24rpx;
}

// .title {
//     display: flex;
//     margin-top: 20rpx;
//     align-items: center;
// }

.title_1 {
    font-weight: bold;
    font-size: 38rpx;
    color: #ff6955;
}

.title_2 {
    // margin-bottom: 32rpx;
    font-weight: 500;
    font-size: 40rpx;
    color: #333333;
    margin-left: 10rpx;
    overflow: hidden;
    /* 确保超出容器的文本会被裁剪 */
    white-space: nowrap;
    /* 确保文本在一行内显示，避免换行 */
    text-overflow: ellipsis;
    /* 使用省略号表示被裁剪的文本 */
    max-width: 24em;
    /* 设置最大宽度，确保不会超过十个字符的宽度 */
}

.title_3 {
    color: #333333;
    font-size: 30rpx;
    margin-top: 10rpx;
    white-space: pre-wrap;
}

.issue {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 40rpx;
}

.issue_1 {
    color: #777777;
    font-size: 24rpx;
}

.issue1 {
    display: flex;
    align-items: center;
}

.issue1_1 {
    display: flex;
    align-items: center;
}

.issue1_2 {
    font-size: 28rpx;
    color: #777777;
    margin-left: 8rpx;
}

.dot {
    width: 8rpx;
    height: 8rpx;
    background: #777777;
    border-radius: 100rpx;
    margin: 0 12rpx;
}

.dynamicState {
    padding: 24rpx 24rpx;
}

.titles1 {
    display: flex;
    align-items: center;
}

.fgx {
    width: 12rpx;
    height: 36rpx;
    background: #ff4f61;
    border-radius: 28rpx;
}

.titles {
    font-size: 34rpx;
    color: #333333;
    font-weight: bold;
    margin-left: 12rpx;
}

.dynamic {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    margin-top: 20rpx;
    width: 100%;
}

.dynamicImg1 {
    width: 340rpx;
}

.dynamicImg1 video {
    width: 100%;
    height: 450rpx;
    border-radius: 20rpx 20rpx 0 0;
}

.dynamicImg {
    width: 100%;
    /* min-height: 254rpx; */
    height: 450rpx;
    border-top-left-radius: 20rpx;
    border-top-right-radius: 20rpx;
}

.dynamicImg2 {
    width: 340rpx;
    background: #fff;
    border-bottom-left-radius: 20rpx;
    border-bottom-right-radius: 20rpx;
    padding: 24rpx 0;
}

.dynamicImg2_1 {
    color: #333333;
    font-size: 28rpx;
    font-weight: bold;
    width: 90%;
    margin: 0 auto;
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
}

.dynamicImg3 {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 30rpx;
    width: 90%;
    margin: 0 auto;
    margin-top: 24rpx;
}

.dynamicImg3_1 {
    display: flex;
    align-items: center;
}

.dynamicImg3_2 {
    color: #777777;
    font-size: 22rpx;
    margin-left: 8rpx;
}

.dynamic4 {
    margin-bottom: 20rpx;
}

.label {
    margin-left: 12rpx;
    flex: 1;
    justify-content: space-between;
}

.label-left {
    text-align: center;
    padding: 4rpx 16rpx 4rpx 26rpx;
    background: #ffe4e5;
    border-radius: 80rpx;
    color: #ff4f61;
    font-size: 24rpx;
    font-weight: 600;
    position: relative;
    margin-left: 8rpx;
}

.label-left image {
    width: 46rpx;
    height: 46rpx;
    transform: rotate(-10deg);
    position: absolute;
    left: -16rpx;
    z-index: 1;
    bottom: 0rpx;
}

.introduce {
    margin-top: 20rpx;
}

.comment-list {
    width: 100%;
    overflow: hidden;
}
.note {
    &-image {
        width: 198rpx;
        height: 84rpx;
        image {
            width: 100%;
            height: 100%;
        }
    }
    &-title {
        display: flex;
        align-items: center;
        padding-top: 6rpx;
        padding-bottom: 28rpx;
        &-tag {
            width: 6rpx;
            height: 28rpx;
            background: #1FA2FF;
            border-radius: 28rpx;
            margin-right: 18rpx;
        }
        &-content {
            font-weight: 500;
            font-size: 32rpx;
            color: #333333;
        }
        &-right {
            flex: 1;
            text-align: right;
            font-weight: 400;
            font-size: 28rpx;
            color: #686868;
            image {
                display: inline-block;
                width: 16rpx; 
                height: 16rpx; 
            }
        }
    }
    &-content {
          height: 630rpx;
          padding: 24rpx 0 0 24rpx;
          background-color: #F8F9F9;
          ::-webkit-scrollbar {
              display: none;
          }
        .top-container::-webkit-scrollbar {
            display: none;
        }
        .top-container::after {
            content: '';
            display: block;
            width: 28rpx;
        }
         .top-container {
              height: 100%;
              white-space: nowrap;
              .top {
                  background-color: white;
                  border-radius: 20rpx;
                  // overflow: hidden;
                  padding: 24rpx;
                  border-radius: 20rpx;
                  margin-bottom: 24rpx;
                  position: relative;
              }
              .top1 {
                  width: 702rpx;
              }
              .top2 {
                  width: 494rpx;
                  display: inline-block;
                  margin-right: 12rpx;
              }
              .top3 {
                  width: 494rpx;
                  // overflow: hidden;
                  position: absolute;
                  left: -332rpx;
                  display: inline-block;
                  border-right: 12rpx #f8f9f9 solid;
                  z-index: 99;
              }
              .top4 {
                  display: inline-block;
                  width: 494rpx;
                  margin-left: 148rpx;
                  margin-right: 12rpx;
              }
              .right {
                  background-color: white;
                  width: 72rpx;
                  position: absolute;
                  right: -72rpx;
                  top: 0;
                  border-left: 12rpx #f8f9f9 solid;
                  height: 100%;
                  z-index: 100;
                  border-radius: 15rpx;
                  .text {
                      position: relative;
                      top: 42%;
                      font-weight: 400;
                      font-size: 23rpx;
                      color: #AAAAAA;
                      writing-mode: vertical-rl;
                      letter-spacing: .4em;
                      line-height: 60rpx;
                  }
              }
              // .top2:last-child{
              //     margin-right: 0;
              // }
              .flex {
                  display: flex;
              }
         }
         .top-container > .top2:last-child::after {
           display: none
         }
    }
}
/deep/.card-reset {
    .detail-right {
        margin-left: 8rpx;
    }
    .service-card {
        margin-bottom: 12rpx !important;
    }
    .detail {
        margin-bottom: 12rpx !important;
    }
    .detail-left {
        height: 60rpx !important;
        width: 60rpx !important;
    }
    .detail-right-date {
        font-size: 18rpx !important;
    }
    .detail-right-name {
        font-size: 22rpx !important;
        margin-right: 8rpx !important;
    }
    .bg-blue {
        font-size: 18rpx !important;
    }
    .detail-tag-enum {
        padding: 4rpx 10rpx !important;
    }
    .detail-right-content-staf {
        padding: 2rpx 10rpx !important;
        font-size: 18rpx !important;
        image {
            width: 20rpx !important;
            height: 20rpx !important;
        }
    }
    .videos {
        height: 228rpx !important
    }
    .many {
        height: 228rpx !important;
    }
    .bg-blue {
        font-size: 18rpx !important;
    }
    .img_box {
        margin-top: 12rpx;
    }
}
.mainss-top {
    padding: 10rpx 30rpx 20rpx 30rpx;
    background-color: white;
}
.title-devide {
    width: 702rpx;
    height: 1rpx;
    background-color:  #F0F0F0;
     margin: 32rpx 0;
}
.issue_2{
	display: flex;
	margin-left: 10rpx;
	.issue_2_1{
		font-size: 26rpx;
		color: #333333;
		margin-left: 10rpx;
	}
}
.issue_3{
	display: flex;
}