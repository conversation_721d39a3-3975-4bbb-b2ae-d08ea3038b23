const coverImageArray = [{
        id: '82',
        url: 'http://cdn.xiaodingdang1.com/2025/03/20/04c11eaa3e4a4bd3aa23f7f9ed249d82.png'
    },
    {
        id: '95',
        url: 'http://cdn.xiaodingdang1.com/2025/03/20/325053002d784109a28711387132373c.png'
    },
    {
        id: '121',
        url: 'http://cdn.xiaodingdang1.com/2025/03/20/a98218557f4a418fa3eb91fae9d3b880.png'
    },
    {
        id: '153',
        url: 'http://cdn.xiaodingdang1.com/2025/03/20/c1b88ff009e145619bd571f55e847693.png'
    },
    {
        id: '154',
        url: 'http://cdn.xiaodingdang1.com/2025/03/20/0fdabcab926b41ff90270e33c02256ce.png'
    },
    {
        id: '155',
        url: 'http://cdn.xiaodingdang1.com/2025/03/20/7083bd8e0fe8485da5c24ef0a174b891.png'
    },
    {
        id: '156',
        url: 'http://cdn.xiaodingdang1.com/2025/03/20/0483a59720574bb2a34d3fd75514e580.png'
    },
    {
        id: '165',
        url: 'http://cdn.xiaodingdang1.com/2025/03/20/db70219ab43f4f7d939f4e5d86891297.png'
    },
    {
        id: '166',
        url: 'http://cdn.xiaodingdang1.com/2025/03/20/86c68680b093485e92ab02a8dcb9f4ae.png'
    },
    {
        id: '167',
        url: 'http://cdn.xiaodingdang1.com/2025/03/20/c5ef01d76af44e2da5ef19e4d30f48e5.png'
    },
    {
        id: '169',
        url: 'http://cdn.xiaodingdang1.com/2025/03/27/e881ff13e7654fcda7d4d03897326557.png'
    },
    {
        id: '170',
        url: 'http://cdn.xiaodingdang1.com/2025/03/21/ad7fd288501e43c080b9d5720c107cb0.png'
    },
    {
        id: '171',
        url: 'http://cdn.xiaodingdang1.com/2025/03/20/e1497faf15bd4a71b8e4a5a1094677c4.png'
    },
    {
        id: '172',
        url: 'http://cdn.xiaodingdang1.com/2025/03/20/2fb0fe7cfc4140698190be60517b0f2d.png'
    },
    {
        id: '177',
        url: 'http://cdn.xiaodingdang1.com/2025/03/20/b50fcc0ac7de45a9bc110b498e34df43.png'
    },
    {
        id: '181',
        url: 'http://cdn.xiaodingdang1.com/2025/03/20/f2e61caaa829455d9f9ad82fea45128b.png'
    },
    {
        id: '183',
        url: 'http://cdn.xiaodingdang1.com/2025/03/20/ae0862d8e4fc452cbe3527ccb02275e9.png'
    },
    {
        id: '185',
        url: 'http://cdn.xiaodingdang1.com/2025/03/20/4ec2e0f1a2164576a0381ba15887d471.png'
    },
    {
        id: '186',
        url: 'http://cdn.xiaodingdang1.com/2025/03/20/7a922dbac65a4457935bb31978b1a156.png'
    },
    {
        id: '187',
        url: 'http://cdn.xiaodingdang1.com/2025/03/20/c8c730649df943349af278f2e71b3234.png'
    },
    {
        id: '188',
        url: 'http://cdn.xiaodingdang1.com/2025/03/20/c986b37488ce4025be4689fbcb9ad16c.png'
    },
    {
        id: '189',
        url: 'http://cdn.xiaodingdang1.com/2025/03/24/23c77df38234420f8cf8ea62cb4cbec8.png'
    },
    {
        id: '190',
        url: 'http://cdn.xiaodingdang1.com/2025/03/20/810996bb94934b1a96d5522b5a6d7bef.png'
    },
    {
        id: '191',
        url: 'http://cdn.xiaodingdang1.com/2025/03/20/563d90f9cc104cdd8374af587eff6e20.png'
    },
    {
        id: '192',
        url: 'http://cdn.xiaodingdang1.com/2025/03/20/9cc76befc58f4805a0e055931810c657.png'
    },
    {
        id: '193',
        url: 'http://cdn.xiaodingdang1.com/2025/03/20/15c9fbc2fa2b4639bf1b046ceadbcb8a.png'
    },
    {
        id: '194',
        url: 'http://cdn.xiaodingdang1.com/2025/03/20/eab2a3e686ec4f3abaf3424c6646290f.png'
    },
    {
        id: '195',
        url: 'http://cdn.xiaodingdang1.com/2025/03/20/9e07e1a9d5d94424a0f5e5b6a5d78fbd.png'
    },
    {
        id: '196',
        url: 'http://cdn.xiaodingdang1.com/2025/03/20/bc930015997b440b874a22f2dc6b5ba1.png'
    },
    {
        id: '197',
        url: ''
    },
    {
        id: '198',
        url: 'http://cdn.xiaodingdang1.com/2025/03/27/bd7f776e0b8e4805bbc5f1109db389dc.png'
    },
    {
        id: '200',
        url: 'http://cdn.xiaodingdang1.com/2025/03/27/bb940c8de432468d9562838ce99f4a2c.png'
    },
    {
        id: '201',
        url: 'http://cdn.xiaodingdang1.com/2025/03/27/dff488f85e37485d976e544b0cfde740.png'
    },
    {
        id: '203',
        url: 'http://cdn.xiaodingdang1.com/2025/03/27/52f628c16ab949bcbbb68f50991135d6.png'
    },
    {
        id: '221',
        url: 'http://cdn.xiaodingdang1.com/2025/03/27/4b0a42f5bffa4da19ce0fef24873902b.png'
    }
]

export const getBgUrl = (id) => {
    let url = ''
    coverImageArray.forEach((item) => {
        if (item.id == id) {
            url = item.url
        }
    })
    return url
}