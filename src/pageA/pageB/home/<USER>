<template>
  <view>
    <view class="mains">
      <view class="main">
        <view class="head">
          <view class="parent">
            <!-- 轮播图 -->
            <swiper
              @change="monitorCurrent"
              :indicator-dots="false"
              :circular="false"
              indicator-color="white"
              indicator-active-color="orange"
              :current="current"
              :autoplay="autoplay"
              style="height: 342rpx"
            >
              <block v-for="(item, index) in banner_list" :key="index">
                <swiper-item>
                  <video
                    class="detail-banner-img"
                    v-if="item.type == 'video'"
                    :src="item.url"
                    @click="preview"
                    :data-index="index"
                  ></video>
                  <u-lazy-load
                    v-else
                    :image="item.url"
                    effect="fade"
                    :duration="0"
                    @click="preview"
                    :data-index="index"
                  ></u-lazy-load>
                </swiper-item>
              </block>
            </swiper>
            <!-- 自定义轮播图进度点 -->
            <view class="dots">{{ current + 1 }}/{{ banner_list.length }}</view>
          </view>
          <view class="title">月子膳食</view>
          <view class="content">
            {{ infoMerchantsList.mealDescription }}
          </view>
        </view>
        <!-- v-if="menuMyList" -->
        <view class="todayMenu" v-if="menuMyList">
          <view class="todayMenuTitle">
            <view class="todayMenuTitle1">
              <view class="todayMenufgx"></view>
              <view class="todayMenuName"> 今日餐单 </view>
            </view>
            <view class="todayMenuTitle2" @click="menu()">
              <image src="/src/static/images/icons/poster.png" mode=""></image>
            </view>
          </view>
          <view class="todayMenuComent">
            <view class="todayMenuComent1_1">
              <view class="todayMenuComent1">
                <image
                  src="http://cdn.xiaodingdang1.com/2025/06/26/cbbefabf4a284b95b3f34de10bab6fd4.png"
                  mode=""
                  style="width: 38rpx; height: 40rpx"
                ></image>
              </view>
            </view>

            <view class="todayMenuComent2">
              <view class="todayMenuComent2_1">早餐</view>
              <view class="todayMenuComent2_2">{{ menuMyList.breakfast }}</view>
            </view>
          </view>
          <view class="cutoffrule"></view>
          <view class="todayMenuComent">
            <view class="todayMenuComent1_1">
              <view class="todayMenuComent1">
                <image
                  src="http://cdn.xiaodingdang1.com/2025/06/26/488df46629c04e6ba69a2a4c9d2f1c23.png"
                  mode=""
                  style="width: 38rpx; height: 40rpx"
                ></image>
              </view>
            </view>
            <view class="todayMenuComent2">
              <view class="todayMenuComent2_1">午餐</view>
              <view class="todayMenuComent2_2">{{ menuMyList.lunch }}</view>
            </view>
          </view>
          <view class="cutoffrule"></view>
          <view class="todayMenuComent">
            <view class="todayMenuComent1_1">
              <view class="todayMenuComent1">
                <image
                  src="http://cdn.xiaodingdang1.com/2025/06/26/316a27c10c8a4d4a83cfd3850b4a2a3d.png"
                  mode=""
                  style="width: 40rpx; height: 38rpx"
                ></image>
              </view>
            </view>
            <view class="todayMenuComent2">
              <view class="todayMenuComent2_1">下午茶</view>
              <view class="todayMenuComent2_2">{{
                menuMyList.afternoonTea
              }}</view>
            </view>
          </view>
          <view class="cutoffrule"></view>
          <view class="todayMenuComent">
            <view class="todayMenuComent1_1">
              <view class="todayMenuComent1">
                <image
                  src="http://cdn.xiaodingdang1.com/2025/06/26/3c514722f25d464a8495a90a2b2ea3a0.png"
                  mode=""
                  style="width: 40rpx; height: 40rpx"
                ></image>
              </view>
            </view>
            <view class="todayMenuComent2">
              <view class="todayMenuComent2_1">晚餐</view>
              <view class="todayMenuComent2_2">{{ menuMyList.dinner }}</view>
            </view>
          </view>
          <view class="cutoffrule"></view>
          <view class="todayMenuComent">
            <view class="todayMenuComent1_1">
              <view class="todayMenuComent1">
                <image
                  src="http://cdn.xiaodingdang1.com/2025/06/26/06c158875175493abbb80ec78b112582.png"
                  mode=""
                  style="width: 38rpx; height: 40rpx"
                ></image>
              </view>
            </view>
            <view class="todayMenuComent2">
              <view class="todayMenuComent2_1">夜宵</view>
              <view class="todayMenuComent2_2">{{
                menuMyList.nightSnack
              }}</view>
            </view>
          </view>
        </view>
        <view class="contentTabs">
          <view class="contentTab1">
            <view class="tabList">
              <view
                class="tabList1"
                v-for="(item, index) in itemList"
                :key="index"
                @click="foodDetails(item.itemId)"
              >
                <u-lazy-load
                  effect="fade"
                  :image="item.photos[0]"
                  border-radius="20"
                  :duration="0"
                ></u-lazy-load>
                <view class="tabList1_1">
                  {{ item.dishName }}
                </view>
                <view class="tabList1_2">
                  {{ item.effect }}
                </view>
              </view>
            </view>
          </view>
        </view>
        <view class="foods">
          <view class="heads" id="head">
            <view class="head1">
              <view id="head1_1"></view>
              <view class="head1_2">膳食套餐</view>
            </view>
            <view class="head2"></view>
          </view>
          <view
            class="foodss"
            v-for="(item, index) in productDeliveryPageList"
            :key="index"
          >
            <view
              @tap="priceBtn"
              :data-deliveryid="item.deliveryId"
              :data-producttype="item.productType"
              class="food"
            >
              <view>
                <image
                  :src="item.productPhotoUrl[0]"
                  mode="aspectFill"
                  style="width: 200rpx; height: 200rpx; border-radius: 10rpx"
                />
              </view>

              <view class="food2">
                <view class="foodTitle2">
                  {{ item.productName }}
                </view>
                <view class="foodTitle">
                  <view class="foodTitle1">{{ item.tag[0] }}</view>
                  <view class="fgx"></view>
                  <view class="foodTitle1">{{ item.tag[1] }}</view>
                </view>
              </view>
            </view>
            <button
              @click="handleContact"
              style="
                position: absolute;
                right: 0rpx;
                bottom: 10rpx;
                margin: 0 0;
                display: flex;
                padding: 3rpx 40rpx;
                justify-content: center;
                align-items: center;
                background: #ff4f61;
                border-radius: 100rpx;
              "
            >
              <image
                src="http://cdn.xiaodingdang1.com/2024/05/30/3d5410c725194862807ec3f572db6817png"
                mode=""
                style="width: 32rpx; height: 32rpx"
              />
              <view class="consultTitle">在线咨询</view>
            </button>
          </view>
        </view>
        <view class="main2">
          <question-vue category="meal_package"></question-vue>
        </view>
        <view
          class="contentTabs"
          v-if="infoMerchantsList.certificateUrl.length > 0"
        >
          <view class="heads" id="head">
            <view class="head1">
              <view id="head1_1"></view>
              <view class="head1_2">资历证书</view>
            </view>
            <view class="head2"></view>
          </view>
          <view class="contentTab1">
            <view class="headImg" id="headImg">
              <image
                :src="item"
                mode="aspectFill"
                v-for="(item, index) in infoMerchantsList.certificateUrl"
                :key="index"
              ></image>
            </view>
          </view>
        </view>
      </view>
      <bottom></bottom>
      <view style="height: 200rpx"></view>
    </view>
    <view class="blank"></view>
    <rightMenuBarVue :scrollTop="scrollTop"></rightMenuBarVue>
    <new-bottom :from="SessionFrom"></new-bottom>
    <mask-dialog></mask-dialog>
  </view>
</template>

<script>
import questionVue from './component/question.vue';
import rightMenuBarVue from '@/components/rightMenuBar.vue';
export default {
  components: {
    questionVue,
    rightMenuBarVue,
  },
  data() {
    return {
      scrollTop: '',
      banner_list: [],
      category: '早餐',
      //菜品类别
      infoMerchantsList: {
        mealDescription: '',
        certificateUrl: [],
      },
      //查询月子膳食信息
      itemList: [],
      //查询菜品列表
      productDeliveryPageList: [],
      //查询膳食套餐列表
      backgroundArr: [
        'http://*************:9000/tools/files/1746798282601467906/download',
        'http://*************:9000/tools/files/1746739786648940546/download',
        'blue',
      ],
      currentIndex: 0,
      current: 0,
      //是否自动播放轮播图
      autoplay: false,
      tabList: [
        {
          name: '早餐',
        },
        {
          name: '午餐',
        },
        {
          name: '晚餐',
        },
        {
          name: '点心',
        },
      ],
      SessionFrom: '',
      menuMyList: '',
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.setData({
      SessionFrom:
        uni.getStorageSync('tenantId') +
        ',' +
        uni.getStorageSync('userInfo').nickname +
        ',' +
        '月子膳食',
    });
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},
  onPageScroll(e) {
    this.scrollTop = e.scrollTop;
  },
  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.getMenuMyList(); //获取当前登录客户的餐单
    this.infoMerchants(); //查询月子膳食信息
    this.getItemList(); //查询菜品列表
    this.productDeliveryPage(); //查询膳食套餐列表
    var newDateTime = Date.parse(new Date());
    let data = {
      eventType: 'PAGE_VIEW',
      pageUrl: '/pageA/pageB/home/<USER>',
      module: 'meal',
      eventTime: newDateTime,
      pageTitle: '月子膳食',
    };
    let iftoken = uni.getStorageSync('token');
    if (iftoken) {
      this.$point.basePoint(data);
    }
  },
  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},
  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    var newDateTime = Date.parse(new Date());
    let data = {
      eventId: uni.getStorageSync('eventId'),
      leaveTime: newDateTime,
    };
    this.$point.reportEnd(data);
  },
  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},
  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {},

  onShareTimeline() {},
  methods: {
    menu() {
      uni.navigateTo({
        url: '/pageA/poster/poster?type=menu',
      });
    },
    foodDetails(itemId) {
      let url = '/pageA/pageB/community/foodDetails/foodDetails';
      let params = {
        itemId: itemId,
      };
      this.$jumpPage(url, params);
      // uni.navigateTo({
      // 	url:'/pageA/pageB/community/foodDetails/foodDetails?itemId:itemId='+itemId
      // })
      // uni.navigateTo({
      // 	url:'/pageA/pageB/community/newfoodDetails/foodDetails'
      // })
    },
    productDeliveryPage() {
      //查询膳食套餐列表
      let data = {
        pageSize: 100,
        pageNum: 1,
      };
      this.$axios.get(this.$api.productDeliveryPage, data).then((res) => {
        if (res.data.code == 200) {
          console.log(res.data.data.rows);
          this.setData({
            productDeliveryPageList: res.data.data.rows,
          });
        }
      });
    },
    getMenuMyList() {
      //获取当前登录客户的餐单
      const now = new Date();
      const formattedTime = `${now.getFullYear()}-${String(
        now.getMonth() + 1,
      ).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')} `;
      let data = {
        date: formattedTime,
      };
      this.$axios.get(this.$api.menuMyList, data).then((res) => {
        if (res.data.code == 200) {
          this.setData({
            menuMyList: res.data.data,
          });
        }
      });
    },

    getItemList() {
      //查询菜品列表
      let data = {
        pageSize: 4,
        pageNum: 1,
        category: this.category,
      };
      this.$axios.get(this.$api.getItemList, data).then((res) => {
        if (res.data.code == 200) {
          this.setData({
            itemList: res.data.data,
          });
        }
      });
    },

    infoMerchants() {
      //查询月子膳食信息
      this.$axios.get(this.$api.infoMerchants).then((res) => {
        if (res.data.code == 200) {
          let data = res.data.data;
          let list = [];
          data.videos.forEach((item) => {
            let data = {
              type: 'video',
              url: item,
            };
            list.push(data);
          });
          data.mealPhotos.forEach((item) => {
            let data = {
              type: 'image',
              url: item,
            };
            list.push(data);
          });
          this.setData({
            infoMerchantsList: res.data.data,
            banner_list: list,
          });
        }
      });
    },

    //用户点击tab时调用
    titleClick: function (e) {
      this.setData({
        //拿到当前索引并动态改变
        currentIndex: e.currentTarget.dataset.index,
        category: e.currentTarget.dataset.name,
      });
      this.getItemList();
    },

    //监听轮播图的下标
    monitorCurrent: function (e) {
      // console.log(e.detail.current)
      let current = e.detail.current;
      this.setData({
        current: current,
      });
    },

    priceBtn(e) {
      //咨询
      let deliveryId = e.currentTarget.dataset.deliveryid;
      let productType = e.currentTarget.dataset.producttype;
      let url = '/pageA/pageB/community/comboDetails/comboDetails';
      let params = {
        deliveryId: deliveryId,
        productType: productType,
      };
      this.$jumpPage(url, params);
      // uni.navigateTo({
      //     url: '/pageA/pageB/community/comboDetails/comboDetails?deliveryId=' + deliveryId +
      //         '&productType=' + productType
      // });
      // this.$jumpPage('/pageA/pageB/community/comboDetails/comboDetails', {
      //     deliveryId: deliveryId,
      //     productType: productType
      // })
    },

    // purchase(e) {
    //     //购买
    //     let deliveryId = e.currentTarget.dataset.deliveryid;
    //     let productType = e.currentTarget.dataset.producttype;
    //     uni.navigateTo({
    //         url: '/pages/irregularityDetails/irregularityDetails?deliveryId=' + deliveryId +
    //             '&productType=' + productType
    //     });
    // },

    preview(event) {
      console.log(event);
      let maparr = this.banner_list;
      let index = event.currentTarget.dataset.index;
      // 既有视频又有图片用这个
      console.log(maparr);
      uni.previewMedia({
        sources: maparr,
        // 官方文档 : 素材展示列表 是个数组对象 一个url链接 一个type 判断这是视频还是图片
        // 接收image 和 video 所以处理数据时 最好给个type 指定 image 或者 video
        current: index,
        // 当前显示的资源序号
      });
    },

    handleContact() {
      uni.navigateTo({
        url: '/subPackages/customerService/pages/userChat',
      });
    },
  },
};
</script>
<style scoped>
@import './food.css';
</style>
