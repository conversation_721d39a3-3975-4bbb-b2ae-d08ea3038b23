.poster {
        // height: 100vh;
        height: 100%;
        width: 100%;
        // overflow-y: scroll;
        /* position: fixed; */
        /* z-index: 0; */
        /* top: 0; */
        background-color: #4c4c4c;
        // padding: 30rpx;
        /* visibility: hidden; */
    }

    .box {
        height: 100%;
        width: 100%;
        background-image: url('http://cdn.xiaodingdang1.com/2025/01/20/0046e46896cb4f3bb334e003cedb4037.png');
        background-size: cover;
        padding-bottom: 40rpx;
    }

    .top {
        padding-left: 50rpx;
        height: 250rpx;
        display: flex;
        align-items: center;

        &-image {
            width: 100rpx;
            height: 100rpx;
            border-radius: 100rpx;
            margin-right: 10rpx;
        }

        &-right { 
            text {
                display: block;
            }
            &-title {
                font-size: 40rpx;
                color: black;
                font-weight: 600;
            }

            &-subtitle {
                font-size: 26rpx;
                color: #33333369;
            }
        }
    }

    .flex {
        display: flex;
        align-items: center;
    }

    .header {
        padding: 0 60rpx;
        display: flex;
        align-items: center;
        margin-bottom: 40rpx;

        &-image {
            width: 200rpx;
            height: 200rpx;
            background-size: cover;
            background-image: url('http://cdn.xiaodingdang1.com/2025/01/20/0b86ed83621045cea5a084d6f56de44d.png');
            margin-right: 30rpx;
            position: relative;

            image {
                position: absolute;
                left: 26rpx;
                top: 24rpx;
                width: 152rpx;
                height: 152rpx;
                border-radius: 152rpx;
            }
        }

        &-right {
            flex: 1;
            font-size: 26rpx;
            font-weight: 400;
            color: #333333;
            line-height: 40rpx;
        }
    }

    .main {
        // background-color: white;
        margin: 0 20rpx;
    }

    .content {
        padding: 40rpx 20rpx;
        width: 100%;
        // background-color: white;
        position: relative;
        // margin: 0 40rpx;
        // height: 800rpx;
        border-radius: 40rpx;

        .bg {
            // width: 100%;
            // position: absolute;
            // top: 0;
            // left: 0;
            // z-index: 1;
            padding: 20rpx 30rpx;
            border-radius: 40rpx 40rpx 0 0;
            background-size: cover;
            background-image: url('http://cdn.xiaodingdang1.com/2025/01/20/3aa0331c80f54166a71fb26dc9b07ac7.png');
            // image {
            //     height: 300rpx;
            //     width: 100%;   
            // }
        }

        .zhen {
            image {
                height: 80rpx;
                width: 80rpx;
            }

            position: absolute;
            top: -10rpx;
            right: 50rpx;
            z-index: 2;
            // padding: 0 40rpx;
        }
    }

    .detail {
        display: flex;
        position: relative;
        align-items: center;
        margin-bottom: 20rpx;

        &-left {
            width: 80rpx;
            height: 80rpx;
            margin-right: 12rpx;

            image {
                height: 100%;
                width: 100%;
                border-radius: 80rpx;
                display: inline-block;
            }
        }

        &-right {
            // flex: 1;
            margin-left: 16rpx;
            // display: flex;
            // align-items: center;
            &-name {
                font-weight: 500;
                font-size: 26rpx;
                color: #7A4E2B;
            }

            &-content {

                &-staf {
                    margin-left: 12rpx;
                    margin-bottom: 4rpx;
                    display: flex;
                    align-items: center;
                    // line-height: 44rpx;
                    height: 44rpx;
                    padding: 0 20rpx;
                    background: linear-gradient(90deg, #F6E0B7 0%, #FFE7C0 100%);
                    // box-shadow: inset 0rpx 0rpx 4rpx 0rpx rgba(255, 255, 255, 0.25);
                    border-radius: 40rpx;
                    font-weight: 500;
                    font-size: 26rpx;
                    image {
                        height: 26rpx;
                        width: 26rpx;
                        display: inline-block;
                    } 
                }
                &-staf::after {
                  content: '';
                  position: absolute;
                  top: 50%;
                  left: 50%;
                  transform: translate(-50%, -50%);
                }
            }

            &-tag {
                text-align: center;
                padding: 16rpx;
                border-radius: 80rpx;
                color: #30A9FF;
                font-size: 26rpx;
                font-weight: 600;
                position: relative;
                margin-left: 8rpx;
                background: #E9F2FF;
                position: absolute;
                right: 0;

                image {
                    width: 94rpx;
                    height: 48rpx;
                    transform: rotate(-10deg);
                    position: absolute;
                    left: 76rpx;
                    z-index: 1;
                    top: -5rpx;
                }
            }


            &-date {
                font-weight: 400;
                font-size: 26rpx;
                color: #AAAAAA;
            }
        }

    }

    .detail-tag {
        // display: flex;
        // align-items: center;
        // overflow-x: scroll;
        // overflow-y: hidden;
        // white-space: nowrap;
        margin-bottom: 12rpx;
        &-enum {
            position: relative;
            width: fit-content;
            display: inline-block;
            border-radius: 10rpx;
            height: 40rpx;
            padding: 0 16rpx;
            // line-height: 40rpx;
            font-weight: 400;
            font-size: 26rpx;
            background: #1FA2FF;
            color: #E9F2FF;
        }
        &-enum::after {
          content: '';
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
        }
    }


    .service-card {
        // position: absolute;
        // top: 0;
        // z-index: 2;
        // left: 0;
        // padding: 10rpx 20rpx;
        background-color: white;
        border-radius: 40rpx;
    }

    .content-note {
        font-weight: 400;
        font-size: 26rpx;
        color: #333333;
        line-height: 40rpx;
        flex-wrap: wrap;
        white-space: break-spaces;
        // padding: 0 20rpx;
        margin: 8rpx 0;
        // width: 600rpx;
    }
    .content-image {
        width: 100%;
		height: 616rpx;
        // padding: 0 30rpx;
        // margin-bottom: 10rpx;
        padding: 0 30rpx 10rpx 30rpx;
        image {
           width: 100%;
           height: 100%;
           border-radius: 10rpx;
        }
    }
    .note {
        padding: 20rpx 36rpx;
        border-radius: 110rpx;
        display: flex;
        align-items: center;
        color: #fff;
        font-size: 32rpx;
        position: fixed;
        bottom: 50rpx;
        left: 38%;
        z-index: 99;
        background: #1FA2FF;
    }
    .right-name {
        color: #FF6E00;
        font-size: 36rpx;
    }
    
    .bottom-left, .bottom-right{
        float: left;
        width: 50%;
        padding: 40rpx 0;
    }
    .bottom-comment {
        font-size: 28rpx;
        color: #333333;   
    }
    .bottom-line {
        width: 20rpx;
        height: 2rpx;
        background-color: #333333;
        margin: 20rpx 0;
    }
    .bottom-subtitle {
        font-size: 30rpx;
        color: #333333;
        font-weight: bold;
        margin-bottom: 10rpx;
    }
    .bottom-title {
        font-size: 24rpx;
        color: #33333369;
        line-height: 35rpx;
    }
    .bottom-clear {
        content: '';
        display: block;
        clear: both;
    }
    .bottom-image {
        width: 120rpx;
        height: 120rpx;
        margin-bottom: 10rpx;
    }
    .bottom-left {
        padding-left: 30rpx;
    }
    .bottom-right {
        // padding-right: 30rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }
	#fit-img {
	    width: 100%;
	    height: 100%;
	    border-radius: 10rpx;
	    background-size: cover;
	    background-position: center;
	}
    