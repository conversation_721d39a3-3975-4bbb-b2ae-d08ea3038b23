<template>
  <scroll-view
    type="custom"
    scroll-y
    :show-scrollbar="false"
    class="scroll-view"
    @scrolltolower="bindSrollToLower"
  >
    <sticky-section :push-pinned-header="false">
      <grid-view
        type="masonry"
        :cross-axis-count="crossAxisCount"
        :cross-axis-gap="crossAxisGap"
        :main-axis-gap="mainAxisGap"
      >
        <slot name="header"></slot>
        <view
          class="grid-box"
          @click="momDetails(item)"
          v-for="(item, index) in pageList"
          :key="index"
        >
          <view>
            <view class="loadImg">
              <template v-if="item.videos?.length > 0">
                <view @click.stop="play(index, item)" class="default-video">
                  <video
                    :controls="false"
                    :src="item.videos[0]"
                    :custom-cache="false"
                    muted="true"
                  />
                </view>
              </template>
              <template v-else>
                <view class="show-img" v-if="item.contentPhotos?.length > 0">
                  <image :src="item.contentPhotos[0]" mode="widthFix"></image>
                </view>
                <image
                  v-else
                  class="default-img"
                  :src="
                    item.type == 'USER'
                      ? 'http://cdn.xiaodingdang1.com/2024/12/06/12b306b19f21410f8f6d8ef61e838977.png'
                      : 'http://cdn.xiaodingdang1.com/2024/12/06/2638525a978d4d93905491269f790474.png'
                  "
                >
                </image>
              </template>
            </view>
            <view
              :class="['content-box', item.type == 'USER' ? 'redbg' : 'bluebg']"
            >
              <view class="momDetails">{{ item.content }}</view>
              <userdesc-vue :showTag="showTag" :item="item"></userdesc-vue>
            </view>
          </view>
        </view>
      </grid-view>
    </sticky-section>
  </scroll-view>
</template>

<script>
import userdescVue from '@/components/userdesc.vue';
export default {
  components: {
    userdescVue,
  },
  props: {
    list: {
      type: Array,
      default: () => [],
    },
    title: {
      type: String,
      default: '',
    },
    showTag: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      crossAxisCount: 2,
      crossAxisGap: 6,
      mainAxisGap: 6,
      observe: null,
      currentPlayingId: '',
    };
  },
  computed: {
    pageList() {
      return this.list;
    },
    current() {
      return this.title;
    },
  },
  mounted() {
    this.observe = uni.createIntersectionObserver(this, {
      thresholds: [0],
    });
    console.log(this.pageList);
  },
  beforeDestroy() {
    this.observe.disconnect();
  },
  methods: {
    play(index, item) {
      this.momDetails(item);
    },
    momDetails(item) {
      if (this.title == 2) {
        uni.navigateTo({
          url:
            '/pageA/pageB/community/topicdetail/commentdetail?postId=' +
            item.postId,
        });
      } else if (this.title == 0) {
        uni.navigateTo({
          url:
            '/pageA/pageB/community/staffdetail?userId=' +
            item.userId +
            '&postId=' +
            item.postId +
            '&taskNodeId=' +
            item.taskNodeId,
        });
      } else if (this.title == 3) {
        uni.navigateTo({
          url:
            '/pageA/pageB/community/sending?userId=' +
            item.userId +
            '&postId=' +
            item.postId +
            '&taskNodeId=' +
            item.taskNodeId,
        });
      } else {
        this.$emit('detail', item);
      }
    },
  },
};
</script>
<style>
video::-webkit-media-controls {
  display: none;
  /* Chrome, Safari, Opera */
}
</style>
<style lang="less" scoped>
.scroll-view {
  width: 100%;
  height: 100%;
}

.grid-box {
  position: sticky;
  border-radius: 10px;
  min-height: 510rpx;
}

.loadImg {
  width: 100%;
  overflow: hidden;
  border-radius: 18rpx 18rpx 0 0;

  image {
    display: block;
    // min-height: 450rpx;
    // max-height: 700rpx;
    width: 100%;
  }

  .default-img {
    width: 100%;
  }

  video {
    width: 100%;
    display: block;
    border-radius: 18rpx 18rpx 0 0;
  }

  video::-webkit-media-controls-fullscreen-button {
    display: none;
  }
}

.loadImg.active {
  transition: all 0.5s ease-in-out;
  opacity: 1;
}

.redbg {
  background-color: white;
  // background: linear-gradient(180deg, #FFEFEF 0%, #FFFFFF 100%);
}

.bluebg {
  background-color: white;
  // background: linear-gradient(180deg, #EDF2FF 0%, #FFFFFF 100%);
}

.flex {
  display: flex;
  align-items: center;
}

.over {
  white-space: nowrap;
}

.momDetails {
  color: #333333;
  font-size: 28rpx;
  display: -webkit-box;
  white-space: pre-wrap;
  -webkit-line-clamp: 2;
  /* 子元素垂直排列 */
  -webkit-box-orient: vertical;
  /* 文本溢出部分显示三个点 */
  text-overflow: ellipsis;
  /* 超出隐藏 */
  overflow: hidden;
}

.content-box {
  border-radius: 0 0 18rpx 18rpx;
  padding: 16rpx 16rpx 22rpx 16rpx;
}

.default-video {
  height: 450rpx;
  width: 100%;
  position: relative;
  overflow: hidden;

  .video {
    height: 100%;
    width: 100%;
    // background-image: url('http://cdn.xiaodingdang1.com/2025/03/03/cefeda4f0440487bbbae639710efaf4c.jpg');
    // background-size: cover;
    background-position: -206rpx;
  }
}

.show-img {
  // min-height: 450rpx;
  // max-height: 700rpx;
  height: 100%;
  width: 100%;

  // .img {
  //   width: 100%;
  //   height: 100%;
  //   background-size: cover;
  //   background-position: center;
  // }
}
</style>
