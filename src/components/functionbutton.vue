<template>
    <view class="fn" :style="stylefly">
        <button class="custom-button" open-type="share" @click="shareCallback()">
            <view class="flex">
                <image src="http://cdn.xiaodingdang1.com/1/120240125/118c00f3-3132-46c7-83b3-628a1a90e2db.png" mode=""
                    style="width: 24rpx; height: 24rpx; margin-right: 4rpx" />
                <view class="discuss5_2">{{ shareCount }}</view>
            </view>
        </button>
        <view class="custom-button" @tap="collect()">
            <view class="flex">
                <image src="http://cdn.xiaodingdang1.com/2024/04/11/fc942fa7cdcd4b6db6dc5dd6e96d43b9png" mode=""
                    style="width: 24rpx; height: 24rpx" v-if="!isFavorite" />
                <image src="http://cdn.xiaodingdang1.com/2024/07/03/a04df81b1b87430fac608f4d651dfda4.png" mode=""
                    style="width: 24rpx; height: 24rpx" v-else />
                <view :class="isFavorite ? 'active' : 'default'">
                    {{ favoriteCount}}
                </view>
            </view>
        </view>
        <view class="custom-button" @tap="like()">
            <view class="flex">
                <image src="http://cdn.xiaodingdang1.com/2024/07/03/e915e045f143407aa4d7b4142f167389.png" mode=""
                    style="width: 24rpx; height: 24rpx" v-if="isLike" />
                <image src="http://cdn.xiaodingdang1.com/1/120240125/058cf0f5-7185-4671-961c-ee263c166795.png" mode=""
                    style="width: 24rpx; height: 24rpx" v-else />
                <view :class="isLike ? 'active' : 'default'">{{ likesCount }}
                </view>
            </view>
        </view>
    </view>
</template>

<script>
    // 转发，收藏，点赞功能按钮
    export default {
        props: {
            item: {
                type: Object,
                default: () => {}
            },
            stylefly: {
                type: Object,
                default: () => {
                    return {
                        "alignItems": "center",
                        "justifyContent": "space-around"
                    }
                }
            },
            pageType: {
                type: String,
                default: ''
            }
        },
        data() {
            return {
                shareCount: this.item.shareCount || this.item.shareNum || 0,
                favoriteCount: this.item.favoriteCount || this.item.bookmarkNum || 0,
                likesCount: this.item.likesCount || this.item.likeNum || 0,
                isFavorite: this.item.isFavorite,
                isLike: this.item.isLike
            }
        },
        methods: {
            shareCallback(index) {
                this.shareCount++
            },
            collect() {
                this.favoriteCount = !this.isFavorite ? this.favoriteCount + 1 : this.favoriteCount - 1
                this.isFavorite = !this.isFavorite
                const api = this.pageType == 'topic' ? this.$api.communityBookmark : this.$api.feedPostFavorite
                const $options = this.pageType == 'topic' ? this.$axios.put : this.$axios.get
                $options(api, {
                    postId: this.item.postId
                })
            },
            like() {
                this.likesCount = !this.isLike ? this.likesCount + 1 : this.likesCount - 1
                this.isLike = !this.isLike
                const api = this.pageType == 'topic' ? this.$api.saveLike : this.$api.feedPostLikes
                const $options = this.pageType == 'topic' ? this.$axios.put : this.$axios.get
                $options(api, {
                    postId: this.item.postId
                })
            }
        }
    }
</script>

<style style="less" scoped>
    .custom-button {
        background: #fff;
        color: hsla(0, 0%, 47%, 1);
        font-size: 24rpx;
        margin: 0;
        padding: 6rpx;
    }

    .flex {
        display: flex;
        align-items: center;
        justify-content: center;
    }


    .fn {
        display: flex;
        width: 100%;
    }

    .active {
        color: #ff4f61;
        font-size: 24rpx;
        margin-left: 4rpx;
    }

    .default {
        margin-left: 4rpx;
    }
</style>