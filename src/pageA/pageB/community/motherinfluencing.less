.page-influencing {
    background: #F3F3F3;
    min-height: 100vh;
    .top-img {
        height: 200rpx;
        background-image: url('http://cdn.xiaodingdang1.com/2024/12/21/faf3ff8d194047a690086c271ac3faef.png');
        background-size: cover;
        // background: linear-gradient(to bottom, rgba(255,255,255,1), rgba(255,255,255,0)), url('https://cdn.xiaodingdang1.com/2024/11/09/c1e8a3924cef4dc38e2765ef8cd1f36e.png') no-repeat;
        overflow-x: hidden;
    }
    .content {
		background-image: url('http://cdn.xiaodingdang1.com/2025/07/21/14b41588e0c240a69aad0efe8619c6e2.png');
		background-size: cover;
		padding-top: 180rpx;
        .content-tab {
            background-color: #FFEFEF;
            /deep/.u-tabs {
                border-radius: 40rpx 40rpx 0 0 ;
                padding-bottom: 8rpx;
                background: linear-gradient( to bottom, #FFFFFF, 0%, #FFFFFC 100%) !important;
            }
            /deep/.u-tab-item {
                height: 60rpx !important;
                line-height: 60rpx !important;
            }
            /deep/.u-tab-bar {
               background: linear-gradient( 270deg, #FD997B 0%, #FF4F61 100%) !important;
            }
        }
        .content-main {
            padding: 0 12rpx 80rpx 12rpx;
        }
    }
}
.head-content{
	border-radius:20rpx ;
	background: #FFFFFF;
	padding: 36rpx 31rpx;
	margin: 12rpx 12rpx 22rpx 12rpx;
}
.head-content1{
	display: flex;
	align-items: center;
}
.head-content1_1{
	position: relative;
}
.head-content1_2{
	color: #000000;
	font-size: 32rpx;
	font-weight: bold;
	margin-left: 16rpx;
}
.head-content2{
	color: #7C7C7C;
	font-size: 28rpx;
}
.head-contentImg{
	position: absolute;
	width: 35rpx;
	height: 35rpx;
	right: -20rpx;
	top: -20rpx;
}