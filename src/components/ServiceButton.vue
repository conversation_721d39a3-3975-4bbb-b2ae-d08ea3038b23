<template>
    <view class="service-btn" @tap="navigateTo">
        <image :src="iconUrl" mode="" :style="`width: ${iconSize}rpx; height: ${iconSize}rpx`" />
        <view class="service-text" :style="`color: ${textColor}`">{{ text }}</view>
    </view>
</template>

<script>
export default {
    name: 'ServiceButton',
    props: {
        // 按钮文本
        text: {
            type: String,
            default: '在线咨询'
        },
        // 图标URL
        iconUrl: {
            type: String,
            default: 'http://cdn.xiaodingdang1.com/2024/05/30/3d5410c725194862807ec3f572db6817png'
        },
        // 图标尺寸
        iconSize: {
            type: Number,
            default: 32
        },
        // 文本颜色
        textColor: {
            type: String,
            default: '#ffffff'
        },
        // 目标页面 pageA/pageB/chat/userBot
        targetPage: {
            type: String,
            default: '/subPackages/customerService/pages/userChat'
        },
        // 传递的参数，对象格式
        params: {
            type: Object,
            default: () => ({})
        },
        // 跳转方式
        navigateType: {
            type: String,
            default: 'navigateTo' // navigateTo, redirectTo, reLaunch, switchTab
        }
    },
    methods: {
        navigateTo() {
            // 构建URL参数字符串
            const query = Object.entries(this.params)
                .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
                .join('&');
            
            const url = query ? `${this.targetPage}?${query}` : this.targetPage;
            
            // 根据不同的跳转类型调用不同的方法
            switch(this.navigateType) {
                case 'redirectTo':
                    uni.redirectTo({ url });
                    break;
                case 'reLaunch':
                    uni.reLaunch({ url });
                    break;
                case 'switchTab':
                    uni.switchTab({ url });
                    break;
                case 'navigateTo':
                default:
                    uni.navigateTo({ url });
                    break;
            }
            
            // 触发点击事件，便于父组件处理
            this.$emit('click');
        }
    }
};
</script>

<style scoped>
.service-btn {
    display: flex;
    padding: 20rpx 40rpx;
    justify-content: center;
    align-items: center;
    background: #ff4f61;
    border-radius: 100rpx;
    text-align: center;
}

.service-text {
    margin-left: 10rpx;
    font-size: 28rpx;
}

/* 支持按钮样式自定义 */
.service-btn.bordered {
    border: 1px solid #ff4f61;
    background: transparent;
}

.service-btn.light {
    background: #ffffff;
}
</style>
