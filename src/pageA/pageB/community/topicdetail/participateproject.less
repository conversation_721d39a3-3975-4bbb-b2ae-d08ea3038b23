.main {
    padding: 20rpx 24rpx;
}

.head {
    display: flex;
    justify-content: space-between;
}

.cancel {
    color: #333333;
    font-size: 28rpx;
}

.publish {
    width: 96rpx;
    text-align: center;
    height: 56rpx;
    line-height: 56rpx;
    background: #7e6dfc;
    color: #ffffff;
    border-radius: 10rpx;
    font-size: 28rpx;
}

.uploading {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 20rpx;
}

.uploadingImg {
    margin-right: 20rpx;
    position: relative;
}

.topicName {
    color: #5b6799;
    font-size: 28rpx;
    width: 100%;
}

.topic {
    display: flex;
    margin-top: 48rpx;
}

.imgCancel {
    position: absolute;
    top: 0;
    right: 0;
    background: #fff;
    border-radius: 50rpx;
}

.addImg1 {
    width: 200rpx;
    height: 200rpx;
    border: 2rpx dashed #aaaaaa;
    text-align: center;
	margin-right: 20rpx;
	z-index: 999;
}

.addImg {
    font-size: 24rpx;
    color: #aaaaaa;
}

.textarea {
    margin-top: 20rpx;
    width: 100%;
}
.moveWrap{
		width: 100%;
	}
.movarea {
		width: 100%;
		height: 320rpx;
		display: flex;
		flex-direction: row;
	}
	.moveV {
		opacity: 0.6;
		z-index: 999;
		box-sizing: border-box;
	}
	.select {
		opacity: 0;
	}
	
	.addImg {
	    font-size: 24rpx;
	    color: #aaaaaa;
	}
	
	.imgBox {
		width: 100%;
		height: 320rpx;
		display: flex;
		flex-direction: row;
		flex-wrap: wrap
	}
	.imgItem{
		position: relative;
		box-sizing: border-box;
		left: 0;
		top: 0;
		margin-right: 20rpx; 
		margin-bottom: 20rpx;
	}
	.imgCancel{
		position: absolute;
		   top: 0;
		   right: 0;
		   background: #fff;
		   border-radius: 50rpx;
	}
	.imgItem image{
		width: 100%;
		height: 100%;
		vertical-align: top;
	}


