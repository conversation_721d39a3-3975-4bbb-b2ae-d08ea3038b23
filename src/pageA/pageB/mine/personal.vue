<template>
    <view class="personal">
        <view class="card">
            <view class="lbox1" style="margin: 32rpx 0">
                <text>头像</text>
                <button class="avatar-wrapper" open-type="chooseAvatar" @chooseavatar="onChooseAvatar" slot="right">
                    <u-avatar :src="avatar"></u-avatar>
                </button>
            </view>
            <view class="devide"></view>
            <view class="lbox1">
                <text>姓名</text>
                <input type="nickname" :value="nickname" @blur="userNameInput" placeholder="请输入昵称" />
            </view>
            <view class="devide"></view>
            <view class="lbox1">
                <text>年龄</text>
                <input v-model="age" placeholder="请输入年龄" />
            </view>
            <view class="devide"></view>
            <view class="lbox1">
                <text>联系电话</text>
                <view class="tel">{{telephone}}</view>
            </view>

        </view>
        <view class="footer">
            <u-button class="save" @click="confirm">保存</u-button>
            <u-button class="loginout" @click="loginout" plain>退出登录</u-button>
        </view>
    </view>
</template>

<script setup>
    import {
        onShow,
        onHide,
        onLoad,
        onPageScroll
    } from "@dcloudio/uni-app";
    import {
        ref,
        reactive,
        onMounted,
        computed,
        getCurrentInstance
    } from "vue";
    import {
        useStore
    } from "vuex";
    const instance = getCurrentInstance();
    const App = instance.appContext.config.globalProperties
    const store = useStore();
    const avatar = ref('')
    const nickname = ref('')
    const telephone = ref('')
    const age = ref('')
    onLoad(() => {
        const userInfo = uni.getStorageSync('userInfo')
        if (userInfo) {
            avatar.value = userInfo.avatar
            nickname.value = userInfo.nickname
            age.value = userInfo.age
            if (userInfo.tel) {
                telephone.value = userInfo.tel.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
            }
        }
    })
    const onChooseAvatar = (e) => {
        try {
            uni.uploadFile({
                filePath: e.detail.avatarUrl,
                name: 'file',
                url: App.$api.uploadOSS,
                formData: {},
                header: {
                    'Content-Type': 'multipart/form-data',
                    Authorization: 'Bearer ' + uni.getStorageSync('token'),
                    'Accept-Encoding': 'gzip',
                    Clientid: '428a8310cd442757ae699df5d894f051'
                },
                success: function(rests) {
                    let data = JSON.parse(rests.data);
                    if (data.code == 200) {
                        avatar.value = data.data.url
                    }
                }
            })
        } catch (e) {
            uni.showToast({
                title: '请选择大于100px x 100px的图片',
                icon: 'none',
                duration: 2500
            });
        }
    }
    const confirm = () => {
        const data = {
            avatar: avatar.value,
            nickname: nickname.value,
            age: age.value,
            userId: uni.getStorageSync('uid')
        }
        App.$axios.post(App.$api.userUpdateInfo, data).then((res) => {
            App.$auth.getUserInfo().then(() => {
                uni.navigateBack({
                    del: 1,
                });
            })
        })
    }
    const loginout = () => {
        uni.setStorageSync('uid', '')
        uni.setStorageSync('userInfo', {});
        uni.setStorageSync('permissions', []);
        uni.setStorageSync('roles', []);
        uni.setStorageSync('hasBindPhone', false);
        uni.setStorageSync('clubName', '');
        uni.removeStorageSync('token');
        store.commit('m_user/setLoginStatus', false)
        uni.navigateBack({
            del: 1,
        });
    }
    //获取昵称输入内容
    const userNameInput = (e) => {
        nickname.value = e.detail.value
    }
</script>

<style lang="less" scoped>
    @import './personal.less';
</style>