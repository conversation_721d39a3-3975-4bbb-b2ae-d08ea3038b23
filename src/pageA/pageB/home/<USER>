<template>
	<view class="list">
		<!-- 条件渲染组件 -->
		<template v-if="tabIndex === 0">
			<AllInvitationsModule ref="allInvitationsRef" />
		</template>
		<template v-else>
			<MyWorksModule ref="myWorksRef" />
		</template>
		<view class="suggest" @click="suggest" v-if="suggestShow">
			<image src="http://cdn.xiaodingdang1.com/2025/07/18/a64b2550ee7f4348819e3a0ba84ff8d1.png" mode=""
				style="width: 36rpx;height: 32rpx;"></image>
			<view class="suggestTitle">
				建议
			</view>
		</view>
	</view>

	<!-- 底部导航 -->
	<view class="footer">
		<view class="tab" @click="tabClick(0)">
			<image :src="
          !!tabIndex
            ? 'http://cdn.xiaodingdang1.com/2025/03/21/9b9182de4f4b42e39876d3ca5f7c4281.png'
            : 'http://cdn.xiaodingdang1.com/2025/03/21/3e480b7200c14365b851d147b8c19c70.png'
        ">
			</image>
			全部请帖
		</view>
		<view class="tab" @click="tabClick(1)">
			<image :src="
          !!tabIndex
            ? 'http://cdn.xiaodingdang1.com/2025/03/21/ef78a3f5a40a4972a8faba5f69863969.png'
            : 'http://cdn.xiaodingdang1.com/2025/03/21/f8a7a9d7861941ceb418e7265846b907.png'
        ">
			</image>
			我的作品
		</view>
	</view>
</template>

<script setup>
	import {
		ref,
		computed
	} from 'vue';
	import {
		onLoad,
		onShareAppMessage
	} from '@dcloudio/uni-app';
	import AllInvitationsModule from './rehabilitation/components/AllInvitationsModule.vue';
	import MyWorksModule from './rehabilitation/components/MyWorksModule.vue';

	// 响应式数据
	const tabIndex = ref(0);

	// 计算属性
	const title = computed(() => {
		return tabIndex.value ? '我的作品' : '全部请帖';
	});

	// 组件引用
	const allInvitationsRef = ref(null);
	const myWorksRef = ref(null);
	const suggestShow= ref(true);

	// 生命周期
	onLoad((options) => {
		console.log('optionsoptions', options);
		tabIndex.value = options.type == 'mine' ? 1 : 0;
	});

	// 微信分享
	onShareAppMessage((res) => {
		console.log('resssss', res);
		if (res.from === 'button') {
			// 从全局存储中获取分享数据
			const currentShareData = uni.getStorageSync('currentShareData') || {};
			return {
				title: currentShareData.title,
				path: currentShareData.path,
				imageUrl: currentShareData.imageUrl,
			};
		}
	});

	// 事件处理函数
	const tabClick = (index) => {
		if(index==0){
			suggestShow.value=true
		}else{
			suggestShow.value=false
		}
		if (index != tabIndex.value) {
			tabIndex.value = index;
		}
	};
	// 建议
	const suggest = () => {
		uni.navigateTo({
			url:'/pageA/pageB/home/<USER>'
		})
	};
</script>
<style scoped lang="less">
	.list {
		background-color: #f3f3f3;
		height: 100vh;
		overflow: hidden;
	}

	.footer {
		position: fixed;
		bottom: 0;
		background-color: white;
		width: 100%;
		height: 110rpx;
		padding-bottom: 10rpx;
		padding-top: 17rpx;
		display: flex;
		font-weight: 500;
		font-size: 20rpx;
		color: #777777;

		.tab {
			width: 50%;
			display: flex;
			align-items: center;
			gap: 2rpx;
			flex-direction: column;
		}

		image {
			width: 40rpx;
			height: 40rpx;
		}
	}

	.suggest {
		position: fixed;
		padding: 10rpx 22rpx;
		border-radius: 100rpx;
		background: #fff;
		bottom: 150rpx;
		right: 20rpx;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		text-align: center;
		 box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
		.suggestTitle {
			color: #4A4A4A;
			font-size: 18rpx;
			font-weight: bold;
		}
	}
</style>