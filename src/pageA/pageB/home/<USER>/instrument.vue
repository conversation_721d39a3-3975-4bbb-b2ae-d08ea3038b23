<template>
	<view class="main">
		<view class="instrument">
			<scroll-view :show-scrollbar="false" :scroll-x="true" class="tab-h1" :scroll-this="scrollthis">
				<view class="superiorBM1" v-for="(item, index) in inforList.postpartumDeviceDetails" :key="index">
					<view @tap="tabClick(index)" :class="index==subscriptIndex?'activesuperiorBM':'superiorBM'">
						<image :src="item.devicePhotos[0]" mode="" />
						<view :class="index==subscriptIndex?'activesuperiorBMTitle':'superiorBMTitle'">
							{{item.deviceName}}
						</view>
					</view>
				</view>
			</scroll-view>
		</view>
		<view class="introduce">
			<view class="titleName">
				<view class="fgx"></view>
				<view class="titleName1">仪器介绍</view>
			</view>
			<view class="introduce1">
				{{inforList.postpartumDeviceDetails[subscriptIndex].deviceIntro}}
			</view>
			<view class="titleName">
				<view class="fgx"></view>
				<view class="titleName1">仪器功效</view>
			</view>
			<view class="effect">
				<view class="effect1"
					v-for="item,index in inforList.postpartumDeviceDetails[subscriptIndex].deviceEffect" :key="index">
					修复盆底肌
				</view>
			</view>
			<view class="titleName">
				<view class="fgx"></view>
				<view class="titleName1">适用人群</view>
			</view>
			<view class="crowd">
				{{inforList.postpartumDeviceDetails[subscriptIndex].applicablePeople}}
			</view>
			<!-- 	<view class="crowd" v-for="item,index in [1,2,3,4,5]" :key="index">
				{{index+1}}、主要适合于反复阴道炎，子宫手术后的康复，以及尿路感染者。
			</view> -->
		</view>

	</view>
</template>
<script setup>
	// 待开发： 产康页面 仪器展示
	import {
		onShow,
		onHide,
		onLoad,
		onPageScroll
	} from "@dcloudio/uni-app";
	import {
		ref,
		reactive,
		onMounted,
		computed,
		getCurrentInstance
	} from "vue";
	const subscriptIndex = ref(0);
	const instance = getCurrentInstance();
	const App = instance.appContext.config.globalProperties
	const props = defineProps({
		contentHeight: {
			type: Number,
			default: 0
		},
		inforList: {
			type: Object,
			default: () => {}
		},
	})
	onLoad((options) => {
		console.log('optionsssss', options)
		pageInit()
	})
	const pageInit = async () => {
		try {

		} catch {

		}
	}
	const tabClick = (index) => {
		subscriptIndex.value = index
	}
</script>
<style lang="less" scoped>
	.crowd {
		color: #333333;
		font-size: 24rpx;
		margin: 20rpx 0;
	}

	.effect {
		display: flex;
		flex-wrap: wrap;

		.effect1 {
			border-radius: 100rpx;
			color: #FF4F61;
			font-size: 24rpx;
			background: #FFEDED;
			padding: 6rpx 18rpx;
			margin-top: 20rpx;
			margin-right: 20rpx;
		}
	}

	.instrument {
		background: #ffffff;
		padding: 28rpx 0rpx 28rpx 24rpx;
	}

	.introduce1 {
		font-size: 24rpx;
		color: #333333;
		padding: 20rpx 0;
	}

	.tab-h1 {
		width: 100%;
		box-sizing: border-box;
		overflow: hidden;
		font-size: 16px;
		white-space: nowrap;
	}

	.superiorBM1 {
		display: inline-block;
		margin-right: 12rpx;
		text-align: center;
	}

	.superiorBM image:first-child {
		width: 264rpx;
		height: 264rpx;
		border-radius: 10rpx;
		border: 1rpx solid #EBEBEB;
	}

	.activesuperiorBM image:first-child {
		width: 264rpx;
		height: 264rpx;
		border-radius: 10rpx;
		border: 1rpx solid #FF4F61;
	}

	.activesuperiorBMTitle {
		color: #FF4F61;
		font-size: 28rpx;
		font-weight: bold;
	}

	.superiorBMTitle {
		color: #333333;
		font-size: 28rpx;
		font-weight: bold;
	}

	.introduce {
		background: #ffffff;
		padding: 32rpx 24rpx;
		margin-top: 20rpx;
	}

	.titleName {
		display: flex;
		align-items: center;

		.fgx {
			height: 26rpx;
			background: #333333;
		}

		.titleName1 {
			color: #333333;
			font-size: 28rpx;
			margin-left: 10rpx;
			font-weight: bold;
		}
	}
</style>