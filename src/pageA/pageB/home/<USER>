<template>
    <view class="main">
        <view class="next">
            <view class="headImg" @tap="next">
                <image src="http://cdn.xiaodingdang1.com/1/120240201/183e1847-20bc-461e-aecf-444fe898e91f.png" mode=""
                    style="width: 18rpx; height: 34rpx" />
            </view>
            <view class="headTitle"></view>
        </view>
        <view v-if="listData.clubFacilityPhotos" class="prosonal"
            :style="'background: url(' + listData.clubFacilityPhotos[0] + ') no-repeat; background-size: 100% 478rpx;'">
            <view class="content">
                <text class="title">{{ listData.clubName }}</text>
                <view class="title1">{{ listData.clubDescription }}</view>
                <view @tap="previewImage" :data-url="listData.clubFacilitiesPhotos" :data-src="item"
                        v-for="(item, index) in listData.clubFacilitiesPhotos" :key="index" style="margin-bottom: 20rpx"> 
                    <!-- <image :src="item" mode=""
                        style="width: 100%; height: 374rpx; border-radius: 20rpx; margin-bottom: 20rpx"
                        @tap="previewImage" :data-url="listData.clubFacilitiesPhotos" :data-src="item"
                        v-for="(item, index) in listData.clubFacilitiesPhotos" :key="index"></image> -->
						<u-lazy-load :duration="0" :image="item"  effect="fade" border-radius="20" loading-img="" error-img=""
                        style="width: 100%; height: 374rpx; border-radius: 20rpx; "
                        ></u-lazy-load>
                </view>
            </view>
        </view>
		<rightMenuBarVue :scrollTop="scrollTop"></rightMenuBarVue>
        <view class="blank"></view>
		<mask-dialog></mask-dialog>
    </view>
    <!-- <view class="tag">
		<view>
			<image src="http://cdn.xiaodingdang1.com/1/120240115/1896701e-aa88-4619-8521-8a498b5e22c5.png" mode="" style="width: 118rpx;height: 118rpx;" />
		</view>
		<view>
			<image src="http://cdn.xiaodingdang1.com/1/120240115/8a35145f-a9e0-402c-be52-deb8f1a40d3a.png" mode="" style="width: 118rpx;height: 118rpx;" />
		</view>
	</view> -->
    <!-- <bottom></bottom> -->
</template>

<script>
	import rightMenuBarVue from "@/components/rightMenuBar.vue";
    // import bottom from '../../../components/bottom/bottom';
    export default {
        components: {
            rightMenuBarVue
        },
        data() {
            return {
				scrollTop:'',
                listData: {
                    clubName: '',
                    clubDescription: '',
                    clubFacilitiesPhotos: []
                }
            };
        },
        /**
         * 生命周期函数--监听页面加载
         */
        onLoad(options) {},
        /**
         * 生命周期函数--监听页面初次渲染完成
         */
        onReady() {},
        /**
         * 生命周期函数--监听页面显示
         */
        onShow() {
            this.homeBrand();
        },
		onPageScroll(e) {
			this.scrollTop = e.scrollTop;
		},
        /**
         * 生命周期函数--监听页面隐藏
         */
        onHide() {},
        /**
         * 生命周期函数--监听页面卸载
         */
        onUnload() {},
        /**
         * 页面相关事件处理函数--监听用户下拉动作
         */
        onPullDownRefresh() {},
        /**
         * 页面上拉触底事件的处理函数
         */
        onReachBottom() {},
        /**
         * 用户点击右上角分享
         */
        onShareAppMessage() {},

        onShareTimeline() {},
        methods: {
            homeBrand() {
                this.$axios.get(this.$api.homeBrand).then((res) => {
                    if (res.data.code == 200) {
                        this.setData({
                            listData: res.data.data
                        });
                    }
                });
            },

            next() {
                uni.navigateBack({
                    delta: 1
                });
            },

            previewImage: function(e) {
                var current = e.currentTarget.dataset.src;
                var url = e.currentTarget.dataset.url;
                uni.previewImage({
                    current: current,
                    // 当前显示图片的http链接
                    urls: url // 需要预览的图片http链接列表
                });
            }
        }
    };
</script>
<style scoped>
    @import './introduce.css';
</style>