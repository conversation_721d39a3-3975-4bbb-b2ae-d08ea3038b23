<template>
  <navigation
    title="月子套餐"
    :background="bgColor"
    :isSowArrow="true"
  ></navigation>
  <view class="main">
    <view class="content">
      <scroll-view class="top-container" scroll-x="true">
        <view
          :class="['top', topStyle]"
          v-for="(item, index) in roomList"
          :key="index"
          @tap.self="postpartumNext(item)"
        >
          <card :list="item" cardType="房间"></card>
        </view>
      </scroll-view>
    </view>
    <view class="room-bottom">
      <room-info-vue
        :currentIndex="currentIndex"
        @callback="tabChange"
      ></room-info-vue>
    </view>
	<rightMenuBarVue :scrollTop="scrollTop"></rightMenuBarVue>
    <mask-dialog></mask-dialog>
  </view>
</template>
<script setup>
import card from "./component/card.vue";
import roomInfoVue from "./component/roomInfo.vue";
import rightMenuBarVue from "@/components/rightMenuBar.vue";
import {
  onShow,
  onHide,
  onLoad,
  onPageScroll,
  onShareAppMessage,
  onShareTimeline,
} from "@dcloudio/uni-app";
import { ref, reactive, onMounted, computed, getCurrentInstance } from "vue";
const instance = getCurrentInstance();
const App = instance.appContext.config.globalProperties;
onShareAppMessage(() => {});
onShareTimeline(() => {});
onShow(() => {
  let newDateTime = Date.parse(new Date());
  let pointStart = {
    eventType: "PAGE_VIEW",
    pageUrl: "/pageA/pageB/home/<USER>",
    module: "room",
    pageTitle: "月子套房列表",
    eventTime: newDateTime,
  };
  let iftoken = uni.getStorageSync("token");
  if (iftoken) {
    App.$point.basePoint(pointStart);
  }
});
onHide(() => {
  let newDateTime = Date.parse(new Date());
  let pointEnd = {
    eventId: uni.getStorageSync("eventId"),
    leaveTime: newDateTime,
  };
  App.$point.reportEnd(pointEnd);
});
const bgColor = ref("");
const scrollTop = ref("");
onPageScroll((e) => {
  if (e.scrollTop > 0) {
    bgColor.value = "#ffffff";
  } else if (e.scrollTop < 2) {
    bgColor.value = "";
  }
  scrollTop.value=e.scrollTop
});
onLoad((options) => {
  console.log("optionsssss", options);
  currentIndex.value = options.currentIndex;
  pageInit();
});
const pageInit = async () => {
  try {
    currentIndex.value == 1
      ? await suiteRrecommendation()
      : await packagerRecommendation();
  } catch (e) {}
};

const roomList = reactive([]);
const topStyle = ref("top2");
const currentIndex = ref("1");
const suiteRrecommendation = async () => {
  //查询套房热门推荐列表
  const res = await App.$axios.get(App.$api.suiteRrecommendation);
  if (res.data.code == 200) {
    let data = res.data.data;
    data.forEach((item) => {
      let price1 = item.price.slice(0, 1);
      let price2 = item.price.slice(2, 10);
      // 添加card组件展示需要的字段
      item.prices = price1 + "*" + price2;
      item.displayPhotos = item.suitePhotos || [];
      item.projectName = item.roomName || "";
      item.tag = item.bathroomFacilities || [];
    });
    topStyle.value = data.length > 1 ? "top2" : "top1";
    roomList.splice(0, roomList.length);
    Object.assign(roomList, data);
  }
};

const packagerRecommendation = async () => {
  //查询优惠套餐热门推荐列表
  const res = await App.$axios.get(App.$api.packagerRecommendation);
  if (res.data.code == 200) {
    let data = res.data.data;
    data.forEach((item) => {
      let packagePrice1 = item.packagePrice.slice(0, 1);
      let packagePrice2 = item.packagePrice.slice(2, 10);
      // 添加card组件展示需要的字段;
      item.prices = packagePrice1 + "*" + packagePrice2;
      item.displayPhotos = item.textPhotos || [];
      item.projectName = item.packageName || "";
      item.tag = item.facilityFeatures || [];
    });
    topStyle.value = data.length > 1 ? "top2" : "top1";
    roomList.splice(0, roomList.length);
    Object.assign(roomList, data);
  }
};
const postpartumNext = (query) => {
  let url = "";
  let params = {};
  if (currentIndex.value == 1) {
    url = "room/detail";
    params = {
      suiteId: query.suiteId,
    };
	App.$jumpPage(url, params);
  } else {
    // url = "room/pagedetail";
    params = {
      packageId: query.packageId,
    };
	uni.navigateTo({
		url:'/pageA/pageB/community/confinementDetails/confinementDetails?packageId='+query.packageId
	})
  }
  // App.$jumpPage(url, params);
};
const tabChange = (e) => {
  currentIndex.value = e;
  e == 1 ? suiteRrecommendation() : packagerRecommendation();
};
</script>
<style lang="less" scoped>
@import "./room.less";
</style>
