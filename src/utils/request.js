import api from './api.js'
import auth from './auth.js';
import store from '../store/index.js'

function get(url, data = {}, head = 'json') {
    return request(url, data, head, 'GET');
}

function post(url, data = {}, head = 'json') {
    return request(url, data, head, 'POST');
}

function put(url, data = {}, head = 'json') {
    return request(url, data, head, 'PUT');
}

function del(url, data = {}, head = 'json') {
    return request(url, data, head, 'DELETE');
}

// let isRefreshing = true;
// let subscribers = [];

// function onAccessTokenFetched() {
//     subscribers.forEach((callback) => {
//         callback();
//     })
//     subscribers = [];
// }

// function addSubscriber(callback) {
//     subscribers.push(callback)
// }

function getHeader(head) {
    let headerObj = {
        'content-type': head == 'json' ? 'application/json' : 'application/x-www-form-urlencoded',
        Clientid: '428a8310cd442757ae699df5d894f051',
        'X-Tenant-ID': '194338'
    };
    if (uni.getStorageSync('token')) {
        headerObj['Authorization'] = 'Bearer ' + uni.getStorageSync('token');
    }
    if (uni.getStorageSync('tenantId')) {
        headerObj['X-Tenant-ID'] = uni.getStorageSync('tenantId')
    }
    return headerObj;
}
let is_redirecting = false;

function request(url, data = {}, head, method = 'GET') {
    return new Promise(function (resolve, reject) {
        if (!url || url == undefined) {
            console.error('请求地址异常：', url);
            return;
        }
        uni.request({
            url: url,
            data: data,
            method: method,
            header: getHeader(head),
            success: function (res) {
                store.commit('m_user/changeLoading', false)
                // console.log('requesttttt', data, url, method, getHeader(head), res)
                if (res.statusCode == 200) {
                    // 登录校验失败
                    if (res.data.code === 401) {
                        // 判断是否登录
                        if (is_redirecting) {
                            return;
                        }
                        is_redirecting = true;
                        uni.showModal({
                            title: '提示',
                            content: '登录已过期，请立即登录，否则无法正常使用',
                            success: (res) => {
                                uni.hideLoading();
                                if (res.cancel) {
                                    console.log('用户点击取消');
                                } else if (res.confirm) {
                                    uni.redirectTo({
                                        url: '/pageA/login',
                                    })
                                }
                            },
                            complete() {
                                is_redirecting = false;
                            }
                        })
                        // 将需要重新执行的接口缓存到一个队列中
                        // addSubscriber(() => {
                        //     request(
                        //         url,
                        //         data,
                        //         head,
                        //         method
                        //     )
                        // })
                        // if (isRefreshing) {
                        //     auth.authorize().then(() => {
                        //         // 依次去执行缓存的接口
                        //         onAccessTokenFetched();
                        //         isRefreshing = true;
                        //     })
                        // }
                        // isRefreshing = false;
                    } else {
                        resolve(res);
                    }
                } else if (res.statusCode == 401) {
                    //未登录，跳转登录界面
                    reject('登录已过期', res);
                    uni.showModal({
                        title: '提示',
                        content: '登录已过期，请立即登录，否则无法正常使用',
                        success(res) {
                            if (res.confirm) {
                                uni.reLaunch({
                                    url: '/pageA/login'
                                });
                            } else if (res.cancel) { }
                        }
                    });
                } else {
                    reject(res);
                    res?.data?.msg && uni.showToast({
                        title: res?.data?.msg,
                        icon: 'none',
                        duration: 2000 //持续的时间
                    });
                }
            },
            fail: function (err) {
                //服务器连接异常
                store.commit('m_user/changeLoading', false)
                uni.hideToast();
                uni.hideLoading();
                uni.stopPullDownRefresh();
                reject('网络开小差了~ 稍后重试', err);
                uni.showToast({
                    title: '网络开小差了~ 稍后重试',
                    icon: 'none',
                    duration: 2000 //持续的时间
                });
            }
        });
    });
}
export default {
    request,
    get,
    post,
    put,
    del
};