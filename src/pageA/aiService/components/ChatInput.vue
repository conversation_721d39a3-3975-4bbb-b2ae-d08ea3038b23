<template>
  <view class="chat-input" :style="{ bottom: inputBottomDistance + 'px' }">
    <view class="input-container">
      <textarea
        class="message-input"
        :value="modelValue"
        :placeholder="placeholder"
        :auto-height="true"
        :maxlength="maxlength"
        :adjust-position="false"
        :show-confirm-bar="false"
        :disabled="disabled"
        @input="handleInput"
        @confirm="handleSend"
        @focus="handleFocus"
        @blur="handleBlur"
      ></textarea>
      <view class="send-btn" @click="handleSend" :class="{ active: canSend }">
        <!-- 发送图标 -->
        <view class="send-icon">
          <image
            :src="
              !canSend
                ? '/static/images/chat/send.svg'
                : '/static/images/chat/svg_active.svg'
            "
            mode="scaleToFill"
          />
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { checkHasLogined } from '@/utils/auth.js';

// Props
const props = defineProps({
  modelValue: {
    type: String,
    default: '',
  },
  placeholder: {
    type: String,
    default: '有什么问题尽管问我~',
  },
  maxlength: {
    type: Number,
    default: 500,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  loading: {
    type: Boolean,
    default: false,
  },
});

// Events
const emit = defineEmits(['update:modelValue', 'send']);

// 键盘高度管理
const keyboardHeight = ref(0);
const tabbarHeight = ref(0);

// 初始化tabbar高度
const initTabbarHeight = () => {
  // 优先从全局获取
  const globalHeight = getApp().globalData.customTabbarHeight;
  if (globalHeight && globalHeight > 0) {
    tabbarHeight.value = globalHeight;
    console.log('ChatInput从全局获取tabbar高度:', globalHeight);
  }
  uni.$on('customTabbarHeightUpdated', (height) => {
    tabbarHeight.value = height;
    console.log('ChatInput监听到tabbar高度更新:', height);
  });
};

// 计算属性
const canSend = computed(() => {
  return (
    props.modelValue.trim().length > 0 && !props.loading && !props.disabled
  );
});

// 计算输入框底部距离
const inputBottomDistance = computed(() => {
  if (keyboardHeight.value > 0) {
    // 有键盘时，使用键盘高度
    return keyboardHeight.value - tabbarHeight.value -1;
  } else {
    // 无键盘时，位于自定义tabbar上方
    return 0;
  }
});

// 事件处理
const handleInput = (e) => {
  emit('update:modelValue', e.detail.value);
};

const handleSend = async () => {
  if (!canSend.value) return;

  // 检查登录状态，未登录时跳转登录页面
  const isLoggedIn = await checkHasLogined();
  if (!isLoggedIn) {
    console.log('用户未登录，跳转到登录页面');
    uni.showModal({
      title: '提示',
      content: '请先登录后再使用AI助理功能',
      confirmText: '去登录',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          uni.navigateTo({
            url: '/pageA/login',
          });
        }
      },
    });
    return;
  }

  emit('send');
};

const handleFocus = (e) => {
  console.log('输入框获得焦点', e);
};

const handleBlur = (e) => {
  console.log('输入框失去焦点', e);
};

// 键盘监听
const setupKeyboardListener = () => {
  uni.onKeyboardHeightChange((res) => {
    if (res.height === 0) {
      keyboardHeight.value = 0;
    } else {
      // 键盘弹起时，输入框应该在键盘上方
      keyboardHeight.value = res.height;
    }
  });
};

const removeKeyboardListener = () => {
  uni.offKeyboardHeightChange();
};

// 生命周期
onMounted(() => {
  // 初始化tabbar高度
  initTabbarHeight();
  // 设置键盘监听
  setupKeyboardListener();
});

onUnmounted(() => {
  removeKeyboardListener();
  uni.$off('customTabbarHeightUpdated');
});

// 不需要暴露内部状态给父组件
</script>

<style lang="scss" scoped>
.chat-input {
  width: 100%;
  padding: 30rpx;
  position: absolute;
  left: 0;
  right: 0;
  transition: all 0.3s ease;
  /* 移除默认的安全区域底部距离，通过JS动态计算 */
  z-index: 999;
  background: rgba(239, 240, 246, 0.8);
  backdrop-filter: blur(25px) saturate(1.2);

  .input-container {
    display: flex;
    align-items: center;
    border-radius: 20rpx;
    background: #fff;
    box-shadow: 0px 0px 20rpx 0px rgba(0, 0, 0, 0.08);
    padding: 24rpx;
    transition: all 0.3s ease;
  }

  .message-input {
    flex: 1;
    font-size: 28rpx;
    line-height: 1;
    max-height: 200rpx;
    border: none;
    outline: none;
    resize: none;
  }

  .send-btn {
    width: 60rpx;
    height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: transform 0.2s;
    cursor: pointer;

    &.active:active {
      transform: scale(0.95);
    }
  }

  .send-icon {
    width: 60rpx;
    height: 60rpx;

    image {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
