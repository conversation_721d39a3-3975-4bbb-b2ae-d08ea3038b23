import { getTemplatesByTypes, TEMPLATE_TYPES } from '@/api/wechatTemplate.js';

/**
 * 微信订阅消息管理工具类
 */
class SubscriptionManager {
  constructor() {
    this.templateCache = new Map(); // 模板缓存
    this.cacheExpiry = 30 * 60 * 1000; // 缓存30分钟
  }

  /**
   * 获取指定类型的模板ID列表
   * @param {Array<string>} templateTypes - 模板类型数组
   * @returns {Promise<Array<string>>} - 模板ID数组
   */
  async getTemplateIds(templateTypes) {
    try {
      const cacheKey = templateTypes.sort().join(',');
      const cached = this.templateCache.get(cacheKey);

      // 检查缓存是否有效
      if (cached && Date.now() - cached.timestamp < this.cacheExpiry) {
        console.log('使用缓存的模板数据:', cached.templateIds);
        return cached.templateIds;
      }

      // 调用API获取模板
      const response = await getTemplatesByTypes(templateTypes);
      console.log('API响应:', response);

      if (response.code !== 200) {
        throw new Error(response.msg || '获取模板失败');
      }

      const templateIds = response.data || [];

      // 缓存结果
      this.templateCache.set(cacheKey, {
        templateIds,
        timestamp: Date.now(),
      });

      console.log('获取到的模板ID:', templateIds);
      return templateIds;
    } catch (error) {
      console.error('获取模板ID失败:', error);
      // 不使用降级方案，直接返回空数组
      return [];
    }
  }

  /**
   * 请求订阅消息权限
   * @param {Array<string>} templateTypes - 模板类型数组
   * @param {Object} options - 配置选项
   * @returns {Promise<Object>} - 订阅结果
   */
  async requestSubscription(templateTypes, options = {}) {
    try {
      const templateIds = await this.getTemplateIds(templateTypes);

      if (templateIds.length === 0) {
        console.log('没有可用的订阅模板，直接执行失败回调');
        // 没有模板ID时直接执行失败回调，让业务逻辑继续
        if (options.onFail) {
          options.onFail({ errMsg: '没有可用的订阅模板' });
        }
        return Promise.resolve({ errMsg: '没有可用的订阅模板' });
      }

      return new Promise((resolve, reject) => {
        wx.requestSubscribeMessage({
          tmplIds: templateIds,
          success: (res) => {
            console.log('订阅消息成功:', res);
            resolve(res);

            // 执行成功回调
            if (options.onSuccess) {
              options.onSuccess(res);
            }
          },
          fail: (err) => {
            console.error('订阅消息失败:', err);
            reject(err);

            // 执行失败回调
            if (options.onFail) {
              options.onFail(err);
            }
          },
        });
      });
    } catch (error) {
      console.error('请求订阅失败:', error);

      if (options.onError) {
        options.onError(error);
      }

      throw error;
    }
  }

  /**
   * 清除模板缓存
   */
  clearCache() {
    this.templateCache.clear();
  }

  /**
   * 预加载常用模板
   */
  async preloadCommonTemplates() {
    const commonTypes = [
      TEMPLATE_TYPES.CUSTOMER_SERVICE_MESSAGE_UNREAD,
      TEMPLATE_TYPES.CLIENT_CONSULTATION_REMINDER,
    ];

    try {
      await this.getTemplateIds(commonTypes);
      console.log('常用模板预加载完成');
    } catch (error) {
      console.error('预加载模板失败:', error);
    }
  }
}

// 创建单例实例
const subscriptionManager = new SubscriptionManager();

export default subscriptionManager;
export { TEMPLATE_TYPES };
