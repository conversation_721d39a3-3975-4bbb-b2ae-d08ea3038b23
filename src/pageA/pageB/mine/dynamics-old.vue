<template>
    <view class="main">
        <template v-if="communityHistory.length > 0">
            <view class="content" v-for="(item, index) in communityHistory" :key="index">
                <view class="headContent">
                    <view class="headContent1">
                        <image :src="item.avatar || $defaultAvatar" mode=""
                            style="width: 80rpx; height: 80rpx; border-radius: 100rpx" />
                        <view class="headTitle">
                            <view class="headTitle1">{{ item.nickname }}</view>
                            <view class="headTitle2">{{ item.hisTimeStr }}</view>
                        </view>
                    </view>
                    <view class="headContent2">
                        <view class="dot" v-for="(item, index1) in [1, 2, 3]" :key="index1"></view>
                    </view>
                </view>

                <view class="content1">
                    {{ item.content }}
                </view>

                <!-- <view class="content2">
            	<image src="{{res}}" mode="" style="width: 200rpx;height: 200rpx;border-radius: 4rpx;" wx:for="{{item.imageUrl}}" wx:for-item="res" wx:for-index="i" wx:key="i"/>
            </view> -->

                <!-- 视频 -->

                <view class="img_box" v-if="item.videos && item.videos.length > 0">
                    <view class="videos" v-for="(res, index1) in item.videos" :key="index1">
                        <video class="video" :src="res" @tap="preview" :data-src="item.videos[0]" data-index="0"
                            :ontrols="false"></video>
                    </view>
                </view>

                <!-- 图片 -->

                <view class="img_box" v-else>
                    <template v-if="item.imageUrl">
                        <view :class="item.imageUrl.length > 1 ? 'many_img' : ''">
                            <view :class="
                                    'img_item ' + (item.imageUrl.length == 1 || item.imageUrl.length == 2 || item.imageUrl.length == 4 ? 'many' : item.imageUrl.length >= 3 ? 'four' : '')
                                " v-for="(res, index1) in item.imageUrl" :key="index1">
                                <image class="img" :src="res" @tap="previewImage" :data-url="item.imageUrl"
                                    :data-src="res" :data-sources="item.imageUrl" :data-index="index" mode="aspectFill">
                                </image>
                            </view>
                        </view>
                    </template>
                </view>

                <view class="content3">
                    <view class="content3_1">
                        <view class="content3_2">
                            <image src="http://cdn.xiaodingdang1.com/2024/07/03/f37d41b628534dd7bf9263a68fe7f488.png"
                                mode="" style="width: 48rpx; height: 48rpx" />
                            <view class="content3_4">
                                {{ item.bookmarkNum }}
                            </view>
                        </view>
                        <view class="content3_3">
                            <image src="http://cdn.xiaodingdang1.com/2024/07/03/4f4255e5d09e4bc6a9b6f96676d38d9a.png"
                                mode="" style="width: 48rpx; height: 48rpx" />
                            <view class="content3_4">
                                {{ item.likeNum }}
                            </view>
                        </view>
                    </view>
                    <!-- <view class="content3_5">
            		<image wx:for="{{item.commentList}}" wx:for-item="e" wx:for-index="j" wx:key="j" src="{{e.avatar}}" class="topic1Text3topic1Text3" style='transform:translateX({{-index*20}}rpx)'></image>
            	</view> -->
                    <view class="avatar-list-stacked">
                        <text class="avatar"
                            :style="'background: url(' + e.avatar || $defaultAvatar + ') center center;background-repeat:no-repeat;background-size: cover; background-position:0px 0px;  '"
                            v-for="(e, j) in item.commentList" :key="j"></text>
                    </view>
                </view>
            </view>
        </template>
        <empty-vue v-if="isFinshed && communityHistory.length == 0"></empty-vue>
        <new-request-loading></new-request-loading>
    </view>
</template>

<script>
    import emptyVue from '@/components/empty.vue';
    export default {
        components: {
            emptyVue
        },
        data() {
            return {
                communityHistory: [],

                imgList: [{
                        img: 'http://*************:9000/tools/files/1750417822270951425/download'
                    },
                    {
                        img: 'http://*************:9000/tools/files/1750417822270951425/download'
                    },
                    {
                        img: 'http://*************:9000/tools/files/1750417822270951425/download'
                    }
                ],

                res: [],

                e: {
                    avatar: ''
                },

                j: '',
                isFinshed: false
            };
        }
        /**
         * 生命周期函数--监听页面加载
         */
        ,
        onLoad(options) {},
        /**
         * 生命周期函数--监听页面初次渲染完成
         */
        onReady() {
            this.communityHistoryFun(); //查询宝妈社区我的动态列表
        },
        /**
         * 生命周期函数--监听页面显示
         */
        onShow() {},
        /**
         * 生命周期函数--监听页面隐藏
         */
        onHide() {},
        /**
         * 生命周期函数--监听页面卸载
         */
        onUnload() {},
        /**
         * 页面相关事件处理函数--监听用户下拉动作
         */
        onPullDownRefresh() {},
        /**
         * 页面上拉触底事件的处理函数
         */
        onReachBottom() {},
        /**
         * 用户点击右上角分享
         */
        onShareAppMessage() {},

        onShareTimeline() {},
        methods: {
            communityHistoryFun() {
                //查询宝妈社区我的动态列表
                let left = this;
                let data = {
                    pageSize: 1000,
                    pageNum: 1
                };
                this.changeLoading(true)
                this.isFinshed = false
                this.$axios.get(this.$api.communityHistory, data).then((res) => {
                    if (res.data.code == 200) {
                        left.setData({
                            communityHistory: res.data.data.rows
                        });
                    }
                }).finally(() => {
                    this.isFinshed = true
                    this.changeLoading(false)
                });
            },

            preview(event) {
                let src = event.currentTarget.dataset.src;
                let maparr = [];
                maparr.push({
                    type: 'video',
                    url: src
                });
                let index = event.currentTarget.dataset.index;
                // 既有视频又有图片用这个
                console.log(maparr);
                uni.previewMedia({
                    sources: maparr,
                    // 官方文档 : 素材展示列表 是个数组对象 一个url链接 一个type 判断这是视频还是图片
                    // 接收image 和 video 所以处理数据时 最好给个type 指定 image 或者 video
                    current: index
                    // 当前显示的资源序号
                });
            },

            previewImage: function(e) {
                console.log(e);
                var current = e.currentTarget.dataset.src;
                var url = e.currentTarget.dataset.url;
                uni.previewImage({
                    current: current,
                    // 当前显示图片的http链接
                    urls: url // 需要预览的图片http链接列表
                });
            }
        }
    };
</script>
<style>
    @import './dynamics.css';
</style>