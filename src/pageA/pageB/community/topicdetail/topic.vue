<template>
    <view>
        <view class="main">
            <view class="topic" @tap="topic" :data-topicid="item.topicId" v-for="(item, index) in hotList" :key="index">
                <view class="topic1">
                    <image :src="item.topicPhotos[0]" mode="" class="topicImg" />
                    <view class="topic2">
                        <view class="topic1Text1">
                            <text class="topic1Text1_1">#</text>
                            <text class="topic1Text1_2">{{ item.topicName }}</text>
                        </view>
                        <view class="topic1Text2">
                            <text class="topic1Text2_1">简介：</text>
                            <text class="topic1Text2_2">{{ item.topicDescription }}</text>
                        </view>
                        <view class="topic1Text3">
                            <view class="topic1Text3_2">
                                <view style="margin-right: 10rpx">
                                    <image :src="res.avatar || $defaultAvatar" class="topic1Text3topic1Text3"
                                        style="margin-right: -0.8em !important" v-for="(res, i) in item.userList"
                                        :key="i"></image>
                                </view>
                                <!-- <view class="avatar-list-stacked">
								<view wx:for="{{item.userList}}" wx:for-item="res" wx:for-index="i" wx:key="i" class="avatar" style="background: url({{res.avatar}}) center center;background-repeat:no-repeat;background-size: cover; background-position:0px 0px;"></view>
							</view> -->
                                <text class="topic1Text3_1">
                                    {{ item.postNum < 99 ? item.postNum : '99' }}
                                    <text v-if="item.postNum > 100">+</text>
                                    人参与
                                </text>
                            </view>
                            <view class="participation">参与</view>
                        </view>
                    </view>
                    <view class="title">热门</view>
                </view>
            </view>
            <!-- <view class="topic">
		<view class="topic1">
			<image src="http://*************:9000/tools/files/1750419034328338434/download" mode="" class="topicImg" />
			<view class="topic2">
				<view class="topic1Text1">
					<text class="topic1Text1_1">#</text><text class="topic1Text1_2">月子中心的美好时刻</text>
				</view>
				<view class="topic1Text2">
					<text class="topic1Text2_1">简介：</text><text class="topic1Text2_2">每一位宝妈都经历了一段珍贵而难忘的时光。每一位宝妈都经历了一段珍贵而难忘的时光。每一位宝妈都经历了一段珍贵而难忘的时光。每一位宝妈都经历了一段珍贵而难忘的时光。每一位宝妈都经历了一段珍贵而难忘的时光。每一位宝妈都经历了一段珍贵而难忘的时光。每一位宝妈都经历了一段珍贵而难忘的时光。每一位宝妈都经历了一段珍贵而难忘的时光。每一位宝妈都经历了一段珍贵而难忘的时光。每一位宝妈都经历了一段珍贵而难忘的时光。每一位宝妈都经历了一段珍贵而难忘的时光。</text>
				</view>
				<view class="topic1Text3">
					<view class="topic1Text3_2">
						<image wx:for="{{imgList}}" wx:key="index" src="{{item.img}}" class="topic1Text3topic1Text3" style='transform:translateX({{-index*20}}rpx)'></image>
						<text class="topic1Text3_1 ">99+人参与</text>
					</view>
					<view class="participation">
						参与
					</view>
				</view>
			</view>
			<view class="title">
				热门
			</view>
		</view>
	</view> -->
        </view>
        <view class="blank"></view>
        <!-- <bottom></bottom> -->
    </view>
</template>

<script>
    // import bottom from '../../components/bottom/bottom';
    export default {
        // components: {
        //     bottom
        // },
        data() {
            return {
                hotList: [],

                imgList: [{
                        img: 'http://*************:9000/tools/files/1750417822270951425/download'
                    },
                    {
                        img: 'http://*************:9000/tools/files/1750417822270951425/download'
                    },
                    {
                        img: 'http://*************:9000/tools/files/1750417822270951425/download'
                    },
                    {
                        img: 'http://*************:9000/tools/files/1750417822270951425/download'
                    }
                ],

                res: {
                    avatar: ''
                },

                i: ''
            };
        },
        /**
         * 生命周期函数--监听页面加载
         */
        onLoad(options) {},
        /**
         * 生命周期函数--监听页面初次渲染完成
         */
        onReady() {},
        /**
         * 生命周期函数--监听页面显示
         */
        onShow() {
            this.hotListFun();
        },
        /**
         * 生命周期函数--监听页面隐藏
         */
        onHide() {},
        /**
         * 生命周期函数--监听页面卸载
         */
        onUnload() {},
        /**
         * 页面相关事件处理函数--监听用户下拉动作
         */
        onPullDownRefresh() {},
        /**
         * 页面上拉触底事件的处理函数
         */
        onReachBottom() {},
        /**
         * 用户点击右上角分享
         */
        onShareAppMessage() {},

        onShareTimeline() {},
        methods: {
            topic(e) {
                let topicId = e.currentTarget.dataset.topicid;
                uni.navigateTo({
                    url: '/pageA/pageB/community/topicdetail?topicId=' + topicId
                });
            },

            hotListFun() {
                //热门话题列表

                this.$axios.get(this.$api.hotList).then((res) => {
                    if (res.data.code == 200) {
                        this.setData({
                            hotList: res.data.data
                        });
                    }
                });
            }
        }
    };
</script>
<style scoped>
    @import './topic.css';
</style>