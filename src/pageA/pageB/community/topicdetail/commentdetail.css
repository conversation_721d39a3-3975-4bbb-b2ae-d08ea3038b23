.discus {
    padding: 0rpx 24rpx;
}



.discuss1 {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20rpx;
}

.discuss1_1 {
    display: flex;
}

.discuss1_2 {
    margin-left: 16rpx;
}

.discuss1_3 {
    color: hsla(26, 100%, 50%, 1);
    font-size: 30rpx;
}

.discuss1_4 {
    font-size: 24rpx;
    color: hsla(0, 0%, 67%, 1);
    margin-top: 4rpx;
}

.discussName {
    margin-left: 24rpx;
}

.discussTitle {
    color: hsla(0, 0%, 47%, 1);
    font-size: 28rpx;
    margin-left: 8rpx;
}

.discuss2 {
    margin-top: 16rpx;
    font-size: 28rpx;
}

.discuss2_1 {
    color: hsla(228, 25%, 48%, 1);
}

.discuss2_2 {
    color: hsla(0, 0%, 20%, 1);
}

.discuss3 {
    margin: 20rpx 0 24rpx 0;
}

.discuss3_1 {
    width: 220rpx;
    height: 220rpx;
    margin-right: 5rpx;
}

.discuss1_5 {
    display: flex;
    /* align-items: center; */
}

.comment {
    display: flex;
    align-items: flex-end;
}

.commentTitle {
    width: 10%;
    color: hsla(0, 0%, 20%, 1);
    font-size: 32rpx;
}

.fgx {
    width: 88%;
    height: 1rpx;
    background: hsla(0, 0%, 92%, 1);
    margin-left: 24rpx;
}

.tab {
    display: flex;
    font-size: 24rpx;
    margin-top: 20rpx;
}

.tabList {
    padding: 12rpx 12rpx;
    color: hsla(0, 0%, 47%, 1);
    margin-right: 28rpx;
}

.tabActive {
    background: hsla(44, 100%, 83%, 1);
    border-radius: 4rpx;
    color: hsla(7, 100%, 50%, 1);
    padding: 12rpx 12rpx;
    margin-right: 28rpx;
}

.contentList {
    margin-top: 24rpx;
}

.discuss1_6 {
    display: flex;
    align-items: center;
    margin-left: 10rpx;
}

.reply {
    display: flex;
    margin-left: 20rpx;
}

.discuss1_7 {
    margin-right: 4rpx;
}

#discus1_6 {
    margin-right: 24rpx;
}

.contentList1 {
    display: flex;
    margin-top: 12rpx;
}

.contentList1_1 {
    width: 13%;
}

.contentList1_2 {
    width: 87%;
    color: hsla(0, 0%, 20%, 1);
    font-size: 28rpx;
}

.contentList1_3 {
    background: hsla(0, 0%, 96%, 1);
    border-radius: 20rpx;
    padding: 24rpx 24rpx 26rpx 24rpx;
}

.contentList1_4 {
    color: #ff4f61;
}

.contentList1_5 {
    color: hsla(0, 0%, 20%, 1);
}

.contentList1_6 {
    font-size: 24rpx;
    margin-bottom: 8rpx;
}

#contentList1_6 {
    display: flex;
    align-items: center;
}

.comments {
    margin: 0;
    padding: 38rpx 24rpx 60rpx 24rpx;
    width: 100%;
    /* height: 92rpx; */
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 1rpx solid #cccccc;
    border-bottom: 1rpx solid #cccccc;
    /* padding和border被包含在定义的width和height之内。盒子的实际宽度=设置的width（padding和border不会影响实际宽度） */
    box-sizing: border-box;
    font-size: 32rpx;
    transition: all 2s inherit;
    overflow: hidden;
    /* 设置为固定定位 */
    position: fixed;
    left: 0;
    background: hsla(0, 0%, 100%, 1);
}

/* textarea输入框的样式 */
.textarea {
    margin: 0;
    padding: 5rpx 18rpx;
    /* 宽度为 父容器的宽度 减去 发送按钮的宽度 减去 (左右内边距和左右边框宽度) 减去 右边外边距*/
    width: calc(100% - 100rpx - 50rpx - 24px);
    /* textarea 的高度随着文本的内容来改变的 设置一个最小高度60rpx*/
    height: 60rpx;
    /* 取消默样式 */
    outline: none;
    border-radius: 84rpx;
    background-color: hsla(0, 0%, 92%, 1);
    /* padding和border不被包含在定义的width和height之内。盒子的实际宽度=设置的width+padding+border */
    box-sizing: content-box;
    overflow: hidden;
}

/* 发送按钮样式 */
.send_out {
    margin: 0;
    padding: 0;
    width: 100rpx;
    height: 70rpx;
    text-align: center;
    line-height: 70rpx;
    border: 1rpx solid #cccccc;
    border-radius: 10rpx;
    /* 将发送按钮固定在底部 */
    position: absolute;
    right: 24rpx;
    bottom: 60rpx;
}

.img_box {
    margin-top: 20rpx;
    padding-left: 4rpx;
}

.videos {
    width: 340rpx;
    height: 340rpx;
}

.videos video {
    width: 100%;
    height: 100%;
}

.img_box .many_img {
    display: flex;
    justify-self: start;
    flex-wrap: wrap;
    justify-content: space-between;
}

.img_item.four {
    width: 220rpx;
    height: 220rpx;
    margin-bottom: 10rpx;
    border-radius: 16rpx;
    overflow: hidden;
}

.img_item.four image {
    width: 100%;
    height: 100%;
}

.img_item.four:nth-child(2n) {}

.img_item.many {
    width: 340rpx;
    height: 340rpx;
    margin-bottom: 10rpx;
    border-radius: 10px;
    overflow: hidden;
}

.img_item.many image {
    width: 100%;
    height: 100%;
}

.img_item.many:nth-child(3n) {}