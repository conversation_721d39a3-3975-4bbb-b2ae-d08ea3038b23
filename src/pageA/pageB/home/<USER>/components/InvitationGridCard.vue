<template>
  <view class="content" @tap="handlePreview">
    <view class="img_content">
      <image :src="item.backGroundImage" mode="widthFix" />
    </view>
    <view class="operate">
      <view class="title">{{ item.title }}</view>
      <view class="sub-title">
        <view>长图</view>
        <view class="devide"></view>
        <view>{{ item.total }}图</view>
        <view class="devide"></view>
        <view>{{ item.viewNumber }}人浏览</view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { getCurrentInstance } from 'vue';
import utils from '@/utils/util';

// 获取当前实例
const instance = getCurrentInstance();

// 接收属性
const props = defineProps({
  item: {
    type: Object,
    required: true,
  },
});

// 处理预览点击
const handlePreview = () => {
  const { id, templateType } = props.item;
  const queryPath = utils.queryParams({
    id,
    type: templateType,
  });
  const url = `/pageA/pageB/home/<USER>/detail?${queryPath}`;

  uni.navigateTo({
    url,
  });
};
</script>

<style scoped lang="less">
.content {
  height: fit-content;
  display: flex;
  justify-content: center;
  position: relative;
  flex-wrap: wrap;

  .img_content {
    width: 100%;
    height: 500rpx;
    text-align: center;
    position: relative;
    overflow: hidden;
  }

  .img_content > image {
    width: 100%;
    height: 100%;
  }

  .operate {
    width: 100%;
    padding: 20rpx;
    background-color: white;

    .title {
      font-weight: 500;
      font-size: 24rpx;
      color: #333333;
      margin-bottom: 4rpx;
    }

    .sub-title {
      font-weight: 400;
      font-size: 20rpx;
      color: #333333;
      opacity: 0.5;
      display: flex;

      .devide {
        height: 18rpx;
        width: 1rpx;
        margin: 0 16rpx;
        position: relative;
        top: 6rpx;
        background-color: #d9d9d9;
      }
    }
  }
}
</style>
