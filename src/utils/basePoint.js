import api from './api.js'
import axios from './request.js'

function basePoint(data) {
	let token=uni.getStorageSync('token')
	if(token){
		axios.post(api.reportStart, data).then((res) => {
		    if (res.data.code == 200) {
		        uni.setStorageSync('eventId', res.data.data);
		    }
		});
	}
  
}

function reportEnd(data) {
	let token=uni.getStorageSync('token')
	if(token){
    axios.post(api.reportEnd, data).then((res) => {
        if (res.data.code == 200) {} 
    });
	}
}

// 宝妈社区点击埋点上报
function reportComStart(data) {
	let token=uni.getStorageSync('token')
	if(token){
    axios.post(api.reportComStart, data).then((res) => {
        if (res.data.code == 200) {
            uni.setStorageSync('eventId', res.data.data);
        } 
    });
	}
}
function reportComEnd(data) {
	let token=uni.getStorageSync('token')
		if(token){
    axios.post(api.reportComEnd, data).then((res) => {
        if (res.data.code == 200) {} 
    });
	}
}
export default {
    basePoint,
    reportEnd,
    reportComStart,
    reportComEnd
};