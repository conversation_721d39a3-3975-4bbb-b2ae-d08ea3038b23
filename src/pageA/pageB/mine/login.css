.main {
    background: #ff4f61;
    width: 100%;
    height: 100vh;
}

.mains {
    padding: 190rpx 24rpx 150rpx 24rpx;
}

.text1 {
    font-size: 64rpx;
    color: #ffffff;
    font-weight: bold;
}

.content {
    background: #ffffff;
    border-radius: 40rpx;
    margin-top: 65rpx;
    padding: 32rpx 48rpx;
}

.text2 {
    font-size: 28rpx;
    color: #333333;
}

.img {
    position: relative;
    width: 140rpx;
    margin: 40rpx auto 0 auto;
}

.photo {
    position: absolute;
    bottom: 0;
    left: 100rpx;
}

.list {
    display: flex;
    font-size: 28rpx;
    align-items: center;
    margin: 36rpx 0;
}

.text3 {
    font-weight: bold;
    width: 26%;
}

.text4 {
    color: #aaaaaa;
    width: 100%;
    position: relative;
}

.fgx {
    height: 2rpx;
    width: 100%;
    background: #ebebeb;
}

#text4 {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

picker {
    width: 100%;
}

.btn {
    width: 240rpx;
    height: 96rpx;
    text-align: center;
    line-height: 96rpx;
    background: #ffffff;
    color: #ff4f61;
    border-radius: 130rpx;
    margin: 20rpx auto;
    font-size: 40rpx;
    font-weight: bold;
}

.next {
    display: flex;
    position: fixed;
    top: 120rpx;
    width: 100%;
}

.headImg {
    width: 41%;
}

.headTitle {
    font-size: 34rpx;
    color: #ffffff;
    font-weight: bold;
}