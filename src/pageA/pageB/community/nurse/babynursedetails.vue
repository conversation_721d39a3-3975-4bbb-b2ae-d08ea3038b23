<template>
	<view class="main">
		<view class="coment">
			<view class="titles">
				基础信息
			</view>
			<view class="fgx"></view>
			<view class="list1">
				<view class="title">
					宝妈姓名
				</view>
				<view class="name">
					{{babyInfoList.momName?babyInfoList.momName:'--'}}
				</view>
			</view>
			<view class="fgx"></view>
			<view class="list1">
				<view class="title">
					宝宝姓名
				</view>
				<view class="name">
					{{babyInfoList.babyName?babyInfoList.babyName:'--'}}
				</view>
			</view>
			<view class="fgx"></view>
			<view class="list1">
				<view class="title">
					宝宝性别
				</view>
				<view class="name">
					{{babyInfoList.gender?babyInfoList.gender:'--'}}
				</view>
			</view>
			<view class="fgx"></view>
			<view class="list1">
				<view class="title">
					入住房间
				</view>
				<view class="name">
					{{babyInfoList.roomNumber?babyInfoList.roomNumber:'--'}}
				</view>
			</view>
			<view class="fgx"></view>
			<view class="list1">
				<view class="title">
					出生体重
				</view>
				<view class="name">
					{{babyInfoList.birthWeight?babyInfoList.birthWeight:'--'}}
				</view>
			</view>
			<view class="fgx"></view>
			<view class="list1">
				<view class="title">
					出生身高
				</view>
				<view class="name">
						{{babyInfoList.birthHeight?babyInfoList.birthHeight:'--'}}
				</view>
			</view>
			<view class="fgx"></view>
			<view class="list1">
				<view class="title">
					出生日期
				</view>
				<view class="name">
					{{babyInfoList.birthDate?babyInfoList.birthDate:'--'}}
				</view>
			</view>
			<view class="fgx"></view>
			<view class="list1">
				<view class="title">
					入住日期
				</view>
				<view class="name">
					--
				</view>
			</view>
		</view>
		<view class="coment">
			<view class="titles">
				评估表
			</view>
			<view class="fgx"></view>
			<view v-if="fieldList[1].isEnabled">
				<view class="list1">
					<view class="title">
						入住体温
					</view>
					<view class="name">
						{{detailsList.temperature?detailsList.temperature:'--'}}
					</view>
				</view>
				<view class="fgx"></view>
			</view>
			<view v-if="fieldList[2].isEnabled">
				<view class="list1">
					<view class="title">
						入住体重
					</view>
					<view class="name">
						{{detailsList.weight?detailsList.weight:'--'}}
					</view>
				</view>
				<view class="fgx"></view>
			</view>
			<view v-if="fieldList[3].isEnabled">
				<view class="list1">
					<view class="title">
						入住身高
					</view>
					<view class="name">
							{{detailsList.height?detailsList.height:'--'}}
					</view>
				</view>
				<view class="fgx"></view>
			</view>
			<view v-if="fieldList[4].isEnabled">
				<view class="list1">
					<view class="title">
						头部
					</view>
					<view class="name">
						{{detailsList.head?detailsList.head:'--'}}
					</view>
				</view>
				<view class="fgx"></view>
			</view>
			<view v-if="fieldList[5].isEnabled">
				<view class="list1">
					<view class="title">
						前囟门
					</view>
					<view class="name">
						{{detailsList.fontanelle?detailsList.fontanelle:'--'}}
					</view>
				</view>
				<view class="fgx"></view>
			</view>
			<view v-if="fieldList[6].isEnabled">
				<view class="list1">
					<view class="title">
						眼部
					</view>
					<view class="name">
							{{detailsList.eyes?detailsList.eyes:'--'}}
					</view>
				</view>
				<view class="fgx"></view>
			</view>
			<view v-if="fieldList[7].isEnabled">
				<view class="list1">
					<view class="title">
						鼻部
					</view>
					<view class="name">
						{{detailsList.nose?detailsList.nose:'--'}}
					</view>
				</view>
				<view class="fgx"></view>
			</view>
			<view v-if="fieldList[8].isEnabled">
				<view class="list1">
					<view class="title">
						口腔
					</view>
					<view class="name">
						{{detailsList.mouth?detailsList.mouth:'--'}}
					</view>
				</view>
				<view class="fgx"></view>
			</view>
			<view v-if="fieldList[9].isEnabled">
				<view class="list1">
					<view class="title">
						面部
					</view>
					<view class="name">
						{{detailsList.face?detailsList.face:'--'}}
					</view>
				</view>
				<view class="fgx"></view>
			</view>
			<view v-if="fieldList[10].isEnabled">
				<view class="list1">
					<view class="title">
						颈部
					</view>
					<view class="name">
						{{detailsList.neck?detailsList.neck:'--'}}
					</view>
				</view>
				<view class="fgx"></view>
			</view>
			<view v-if="fieldList[11].isEnabled">
				<view class="list1">
					<view class="title">
						腹部
					</view>
					<view class="name">
						{{detailsList.abdomen?detailsList.abdomen:'--'}}
					</view>
				</view>
				<view class="fgx"></view>
			</view>
			<view v-if="fieldList[12].isEnabled">
				<view class="list1">
					<view class="title">
						脐部
					</view>
					<view class="name">
						{{detailsList.umbilicus?detailsList.umbilicus:'--'}}
					</view>
				</view>
				<view class="fgx"></view>
			</view>
			<view v-if="fieldList[13].isEnabled">
				<view class="list1">
					<view class="title">
						生殖器
					</view>
					<view class="name">
						{{detailsList.genitals?detailsList.genitals:'--'}}
					</view>
				</view>
				<view class="fgx"></view>
			</view>
			<view v-if="fieldList[14].isEnabled">
				<view class="list1">
					<view class="title">
						四肢
					</view>
					<view class="name">
						{{detailsList.limbs?detailsList.limbs:'--'}}
					</view>
				</view>
				<view class="fgx"></view>
			</view>
			<view v-if="fieldList[15].isEnabled">
				<view class="list1">
					<view class="title">
						肌张力
					</view>
					<view class="name">
						{{detailsList.muscleTone?detailsList.muscleTone:'--'}}
					</view>
				</view>
				<view class="fgx"></view>
			</view>
			<view v-if="fieldList[16].isEnabled">
				<view class="list1">
					<view class="title">
						黄疸值mg/dl
					</view>
					<view class="name">
							{{detailsList.jaundiceValue?detailsList.jaundiceValue:'--'}}
					</view>
				</view>
				<view class="fgx"></view>
			</view>
			<view v-if="fieldList[17].isEnabled">
				<view class="list1">
					<view class="title">
						红疹部位
					</view>
					<view class="name">
						{{detailsList.rashLocation?detailsList.rashLocation:'--'}}
					</view>
				</view>
				<view class="fgx"></view>
			</view>
			<view v-if="fieldList[18].isEnabled">
				<view class="list1">
					<view class="title">
						血管瘤部位
					</view>
					<view class="name">
						{{detailsList.hemangiomaLocation?detailsList.hemangiomaLocation:'--'}}
					</view>
				</view>
				<view class="fgx"></view>
			</view>
			<view v-if="fieldList[19].isEnabled">
				<view class="list1">
					<view class="title">
						蒙古斑部位
					</view>
					<view class="name">
						{{detailsList.mongolianSpotLocation?detailsList.mongolianSpotLocation:'--'}}
					</view>
				</view>
				<view class="fgx"></view>
			</view>
			<view v-if="fieldList[20].isEnabled">
				<view class="list2">
					<view class="title2">
						备注信息
					</view>
					<view class="name2">
						{{detailsList.notes?detailsList.notes:''}}
					</view>
				</view>
				<view class="fgx"></view>
			</view>
			<view v-if="fieldList[21].isEnabled">
				<view class="list2">
					<view class="title2">
						照片
					</view>
					<view class="chooseImg2">
						<view class="chooseImg" v-for="item,index in detailsList.imageInfo" :key="index">
							<view class="chooseImg1">
								<image :src="item" mode="" style="width: 100%;height: 100%;border-radius: 8rpx;"></image>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				bgColor: '#ffffff',
				title: "",
				listImg: [1, 2, 3, 4, 5, 6],
				detailsList: '',
				babyInfoList:'',
				type: 2,
				fieldList: []
			};
		},
		methods: {
			configGetField() {
				this.changeLoading(true)
				let data = {
					tableName: 'biz_baby_care'
				}
				this.$axios.get(this.$api.configGetField, data).then((res) => {
					if (res.data.code == 200) {
						this.setData({
							fieldList: res.data.data
						});
					}
				}).finally(() => {
					this.changeLoading(false)
				})
			},
			getGetDetail(id) {
				this.changeLoading(true)
				let data = {
					id: id
				}
				this.$axios.get(this.$api.careGetDetail, data).then((res) => {
					if (res.data.code == 200) {
						this.setData({
							detailsList: res.data.data
						});
					}
				}).finally(() => {
					this.changeLoading(false)
				})
			},
			infoGetDetail(id) {
				this.changeLoading(true)
				let data = {
					babyId: id
				}
				this.$axios.get(this.$api.infoGetDetail, data).then((res) => {
					if (res.data.code == 200) {
						this.setData({
							babyInfoList: res.data.data
						});
					}
				}).finally(() => {
					this.changeLoading(false)
				})
			}
		},
		/**
		 * 生命周期函数--监听页面加载
		 */
		onLoad: function(options) {
			this.title = options.title
			this.getGetDetail(options.id)
			this.infoGetDetail(options.babyId)
			this.configGetField()
		},
		/**
		 * 生命周期函数--监听页面初次渲染完成
		 */
		onReady: function() {},
		/**
		 * 生命周期函数--监听页面显示
		 */
		onShow: function() {},
		/**
		 * 生命周期函数--监听页面隐藏
		 */
		onHide: function() {},
		/**
		 * 生命周期函数--监听页面卸载
		 */
		onUnload: function() {},
		/**
		 * 页面相关事件处理函数--监听用户下拉动作
		 */
		onPullDownRefresh: function() {},
		/**
		 * 页面上拉触底事件的处理函数
		 */
		onReachBottom: function() {},
		/**
		 * 用户点击右上角分享
		 */
		onShareAppMessage() {},

		onShareTimeline() {},
	}
</script>

<style lang="less" scoped>
	@import './nursedetails.less';
</style>