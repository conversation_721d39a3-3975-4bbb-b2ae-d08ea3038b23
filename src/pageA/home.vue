<template>
  <view class="home-page">
    <!-- 会所信息 -->
    <TenantInfo />

    <TenantMenu v-if="tenantId != '148757' && tenantId != '705382'" />
    <newTenantMenu v-if="tenantId == '148757' || tenantId == '705382'" />

    <view class="modules-container">
      <!-- 活动轮播图组件 -->
      <ActivityModule />
      <!-- 宝妈社区组件 -->
      <CommunityModule />
      <!-- 商家动态模块 -->
      <!-- <DynamicsModule /> -->
      <!-- 宝妈服务笔记模块 -->
      <ServiceNoteModule />
      <!-- 优惠套餐组件 -->
      <DiscountsModule />
      <!-- 护理团队组件 -->
      <TeamModule />
      <!-- 月子膳食组件 -->
      <FoodModule />
      <!-- 品牌介绍组件 -->
      <BrandModule />
    </view>
    <!-- <mask-dialog></mask-dialog> -->

    <!-- 自定义 tabbar -->
    <CustomTabBar activeKey="home" />
  </view>
</template>

<script setup>
import TenantInfo from './components/TenantInfo.vue';
import TenantMenu from './components/TenantMenu.vue';
import newTenantMenu from './components/newTenantMenu.vue';
import ActivityModule from './components/ActivityModule.vue';
import CommunityModule from './components/CommunityModule.vue';
// import DynamicsModule from './components/DynamicsModule.vue';
import ServiceNoteModule from './components/ServiceNoteModule.vue';
import DiscountsModule from './components/DiscountsModule.vue';
import TeamModule from './components/TeamModule.vue';
import FoodModule from './components/FoodModule.vue';
import BrandModule from './components/BrandModule.vue';
import CustomTabBar from '@/components/CustomTabBar.vue';
import { onMounted, ref } from 'vue';

const tenantId = ref('');

onMounted(() => {
  tenantId.value = uni.getStorageSync('tenantId');
});

// 分享功能
const onShareAppMessage = () => {};
const onShareTimeline = () => {};
</script>
<style scoped>
.home-page {
  background: #f8f9f9;
  padding-bottom: calc(150rpx + env(safe-area-inset-bottom));
}

.modules-container {
  padding: 0 24rpx 24rpx 24rpx;
}
</style>
