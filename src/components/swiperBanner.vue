<template>
    <swiper class="detail-banner" :indicator-dots="attrsObj.indicatorDots" :autoplay="attrsObj.autoplay"
        :interval="attrsObj.interval" :duration="attrsObj.duration" :circular="attrsObj.circular"
        :indicator-color="attrsObj.indicatorColor" :indicator-active-color="attrsObj.indicatorAactiveColor"
        :navigation="attrsObj.navigation" circular style="background: white">
        <!-- @transition="onTransition" @animationfinish="onAnimationFinish" -->
        <swiper-item v-for="(item, index) in list" :key="index">
            <template v-if="item.type == 1 || item.type == 'video'">
                <view class="banner" :data-id="index" @tap="playbtn">
                    <view class="videocoverbg"></view>
                    <image src="http://cdn.xiaodingdang1.com/2024/12/05/37a53a82945247f7b5db7f788d8d5603.png"
                        class="playbtn" @tap="videoPlay" v-if="!controls"></image>
                </view>
                <video class="banner-video videocoverbg" id='video' :src="item.url" :show-center-play-btn="false"
                    :autoplay="false" objectFit="cover" @ended="jieshu" :controls="controls"></video>
            </template>
            <view class="banner-img" v-else>
                <image mode="aspectFill" :src="item.url" @tap="previewImage(item.url)">
                </image>
            </view>
        </swiper-item>
        <!-- <view class="swiper-button-prev" @tap="prevSwiper"></view>
        <view class="swiper-button-next" @tap="nextSwiper"></view> -->
    </swiper>
</template>

<script setup>
    // 轮播图片或者视频
    import {
        ref,
        reactive,
        getCurrentInstance
    } from "vue";
    const instance = getCurrentInstance();
    const props = defineProps({
        attrsObj: {
            type: Object,
            default: () => {
                return {
                    indicatorDots: true,
                    autoplay: false,
                    interval: 3000,
                    duration: 300,
                    circular: false,
                    indicatorColor: 'lightgray',
                    indicatorAactiveColor: '#999',
                    navigation: 'default'
                }
            }
        },
        list: {
            type: Array,
            default: () => []
        },
        imgList: {
            type: Array,
            default: () => []
        },
        // 指示点上移
        indicatorBottom: {

        }
    })
    const controls = ref(false)
    const isSwiping = ref(false)
    const current = 0
    const playbtn = () => {
        console.log('占位：函数 playbtn 未声明');
    }
    const videoPlay = () => {
        var videoplay = uni.createVideoContext('video', instance);
        console.log('开始播放', videoplay);
        videoplay.play();
        controls.value = true
    }
    const jieshu = () => {
        controls.value = false
        console.log('占位：函数 jieshu 未声明');
    }
    const previewImage = (url) => {
        uni.previewImage({
            current: url,
            // 当前显示图片的http链接
            urls: props.imgList
            // urls: this.data.imgUrls // 需要预览的图片http链接列表
        });
    }
    const onTransition = (e) => {
        // 滑动开始时设置isSwiping为true
        isSwiping.value = true;
    }
    const onAnimationFinish = (e) => {
        // 滑动结束时设置isSwiping为false
        isSwiping.value = false;
        // 更新current值
        updateCurrent(e.detail.current);
    }
    const updateCurrent = (newCurrent) => {
        const count = props.list.length;
        // 处理循环逻辑
        if (isSwiping.value) {
            if (newCurrent === 0) {
                current.value = count - 1;
            } else if (newCurrent === count - 1) {
                current.value = 0;
            }
        }
    }
    const prevSwiper = (e) => {
        current.value = (current.value - 1 + props.list.length) % props.list.length
    }
    const nextSwiper = (e) => {
        current.value = (current.value + 1) % props.list.length
    }
</script>

<style lang="less" scoped>
    /* 轮播图 */

    .detail-banner {
        width: 100%;
        height: 100%;
        position: relative;
    }

    .banner-img {
        width: 100%;
        height: 100%;

        image {
            height: 100%;
            width: 100%;
            // height: 100%;
            // object-fit: cover;
            // object-position: center;
        }
    }



    /* video */

    .videocover {
        width: 100%;
        overflow: hidden;
    }

    .banner-video {
        display: block;
        width: 100%;
        height: 100%;
    }

    .videocoverbg {
        position: absolute;
        left: 0;
        top: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.3);
    }

    .playbtn {
        position: absolute;
        top: 50%;
        z-index: 2;
        left: 50%;
        width: 150rpx;
        height: 150rpx;
        transform: translate(-50%, -50%);
    }

    .videocover .cover {
        width: 100%;
    }

    .swiper-button-prev {
        position: absolute;
        top: 50%;
        left: 10%;
        z-index: 2;
    }

    .swiper-button-next {
        position: absolute;
        top: 50%;
        right: 10%;
        z-index: 2;
    }
</style>