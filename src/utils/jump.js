/*
跳转
file: 子页面文件名称
params: 携带的参数 {}
way: 默认navigateTo 
*/
export const jumpPage = (file, params, type) => {
    if (typeof file === 'string') {
        let query = null
        if (params && Object.keys(params).length > 0) {
            query = Object.keys(params)
                .map(key => encodeURIComponent(key) + '=' + encodeURIComponent(params[key]))
                .join('&');
        }
        const url = query ? file + '?' + query : file
        // if (this.hasLeftWin) {
        //     uni.reLaunch({
        //         url: url
        //     })
        // } else {
        //     uni.navigateTo({
        //         url: url
        //     })
        // }
        if (type === 'switchTab') {
            // tabbar跳转
            uni.switchTab({
                url
            })
        } else if (type === 'reLaunch') {
            // 关闭前面所有页面
            uni.reLaunch({
                url
            })
        } else if (type === 'redirectTo') {
            // 关闭当前跳转
            uni.redirectTo({
                url
            })
        } else {
            // 保留上一个页面跳转
            uni.navigateTo({
                url
            })
        }
    } else {

    }
}