<template>
	<view class="main">
		<view class="head" v-if="rolesShow=='ADMIN'||rolesShow=='BOSS'||rolesShow=='APP_SUPER_ADMIN'||rolesShow=='SALES_MANAGER'">
			<view class="head-tab" >
				<view v-for="(item,index) in listTab" :key="index" :class="index==tabIndex?'activeTab':'head-tab1'"
					@click="listTabs(index)">{{item}}</view>
			</view>
			<view class="head-time">
				<view class="head-time1" @click="prevMonth">
					<image src="http://cdn.xiaodingdang1.com/2025/06/25/75e64c7243c247c9b1edcb23a495f381.png" mode=""
						style="width: 20rpx;height: 28rpx;" />
				</view>
				<view class="head-time2">
					<text>{{currentYear}}年{{currentMonth}}月</text>
				</view>
				<view class="head-time1" @click="nextMonth">
					<image src="http://cdn.xiaodingdang1.com/2025/06/25/edaefae8de0e47faadb5b369ec3c7599.png" mode=""
						style="width: 20rpx;height: 28rpx;" />
				</view>
			</view>
		</view>

		<view class="calendar-container">
			<!-- 月份切换区 -->
			   <view class="header" v-if="rolesShow=='CUSTOMER'">
	      <text @click="prevMonth">◀</text>
	      <text>{{currentYear}}年{{currentMonth}}月</text>
	      <text @click="nextMonth">▶</text>
	    </view>

			<!-- 星期标题 -->
			<view class="week-header">
				<text v-for="day in weekDays" :key="day">{{day}}</text>
			</view>

			<!-- 日期网格 -->
			<view class="days-grid">
				<view v-for="(day,index) in daysArray" :key="index" :class="[
	          'day-cell',
	          day.isCurrentMonth?'':'other-month',
	          day.isToday?'today':'',
	          day.isSelected?'selected':''
	        ]" @click="selectDay(day,index)">
					{{day.date}}
					<view v-if="day.hasEvent" class="event-mark"></view>
				</view>
			</view>
		</view>

		<view v-if="tabIndex==0">
			<view class="mealType">
				<view class="mealType1">
					<view style="width: 15%;">
						<view class="mealType-1">
							<image src="http://cdn.xiaodingdang1.com/2025/06/25/e0ade65201284f299c70b4a84123f32a.png"
								mode="" style="width: 50rpx;height: 50rpx;" />
						</view>
					</view>
					<view class="fgx"></view>
					<view class="mealType-5">
						<view class="mealType-2">早餐</view>
						<view class="mealType-6">
							{{infoList?infoList.breakfast:'--'}}
						</view>
					</view>
				</view>
			</view>
			<view class="mealType">
				<view class="mealType1">
					<view style="width: 15%;">
					<view class="mealType-1">
						<image src="http://cdn.xiaodingdang1.com/2025/06/25/f4e91d464b27475eba5b6edc472d8ba0.png"
							mode="" style="width: 50rpx;height: 50rpx;" />
					</view>
					</view>
					<view class="fgx"></view>
					<view class="mealType-5">
						<view class="mealType-2">午餐</view>
						<view class="mealType-6">
							{{infoList?infoList.lunch:'--'}}
						</view>
					</view>
				</view>
			</view>
			<view class="mealType">
				<view class="mealType1">
					<view style="width: 15%;">
					<view class="mealType-1">
						<image src="http://cdn.xiaodingdang1.com/2025/06/25/4834040060d445108bfd5bc0698846c7.png"
							mode="" style="width: 50rpx;height: 50rpx;" />
					</view>
					</view>
					<view class="fgx"></view>
					<view class="mealType-5">
						<view class="mealType-2">下午茶</view>
						<view class="mealType-6">
							{{infoList?infoList.afternoonTea:'--'}}
						</view>
					</view>
				</view>
			</view>
			<view class="mealType">
				<view class="mealType1">
					<view style="width: 15%;">
					<view class="mealType-1">
						<image src="http://cdn.xiaodingdang1.com/2025/06/25/5fb06e8f8b2f47259324439440d689e2.png"
							mode="" style="width: 50rpx;height: 50rpx;" />
					</view>
					</view>
					<view class="fgx"></view>
					<view class="mealType-5">
						<view class="mealType-2">晚餐</view>
						<view class="mealType-6">
							{{infoList?infoList.dinner:'--'}}
						</view>
					</view>
				</view>
			</view>
			<view class="mealType">
				<view class="mealType1">
					<view style="width: 15%;">
					<view class="mealType-1">
						<image src="http://cdn.xiaodingdang1.com/2025/06/25/3b2d575060cd4b9987a51653558db53a.png"
							mode="" style="width: 50rpx;height: 50rpx;" />
					</view>
					</view>
					<view class="fgx"></view>
					<view class="mealType-5">
						<view class="mealType-2">夜宵</view>
						<view class="mealType-6">
							{{infoList?infoList.nightSnack:'--'}}
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="customer-list" v-if="tabIndex==1">
			<view class="customer-list1">
				<view class="hue2">
					<view class="hue"></view>
					<view class="huetitle">定制餐单</view>
				</view>
				<view class="hue2">
					<view class="hue1"></view>
					<view class="huetitle">会所餐单</view>
				</view>
			</view>
			<view class="customer-list3">
				<view :class="item.status==1?'customer-list2':'customer-list2-1'" v-for="item,index in roomList" :key="index"
					@click="nextDetails(item)">
					<view :class="item.status==1?'num':'num-1'">{{item.roomNumber}}</view>
					<view :class="item.status==1?'customer-list4':'customer-list5'">
						<view :class="item.status==1?'customer-list4-1':'customer-list4-1-1'">{{item.customerName}}</view>
						<view :class="item.status==1?'customer-list4-2':'customer-list4-2-2'">{{item.startDate}}</view>
					</view>
				</view>

			</view>

		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				roles:'',
				listTab: ['全部', '客户餐单'],
				tabIndex: 0,
				currentYear: new Date().getFullYear(),
				currentMonth: new Date().getMonth() + 1,
				weekDays: ['日', '一', '二', '三', '四', '五', '六'],
				daysArray: [],
				infoList: '',
				date: '',
				roomList:[]
			}
		},
		mounted() {
			this.generateCalendar()
			if(this.rolesShow=='ADMIN'||this.rolesShow=='BOSS'||this.rolesShow=='APP_SUPER_ADMIN'||this.rolesShow=='SALES_MANAGER'){
			 this.getStandardMenu(this.date)
			}
			if(this.rolesShow=='CUSTOMER'){
				this.getmenuMyList(this.date)
			}
		},
		onLoad(options) {},
		onShow(){
			let roles = uni.getStorageSync("roles");
			this.rolesShow=roles[0]
		},
		methods: {
			getStandardMenu(date) {
				//获取指定日期的标准餐单
				let data = {
					date: date
				};
				this.$axios.get(this.$api.getStandardMenu, data).then((res) => {
					if (res.data.code == 200) {
						let data = res.data.data;
						this.setData({
							infoList: data,
						});
					}
				});
			},
			getmenuMyList(date) {
				//获取当前登录客户的餐单
				let data = {
					date: date
				};
				this.$axios.get(this.$api.menuMyList, data).then((res) => {
					if (res.data.code == 200) {
						let data = res.data.data;
						this.setData({
							infoList: data,
						});
					}
				});
			},
			getRoomCustomers(date) {
				//获取入住宝妈列表
				let data = {
					date: date
				};
				this.$axios.get(this.$api.getRoomCustomer, data).then((res) => {
					if (res.data.code == 200) {
						let data = res.data.data;
						this.setData({
							roomList: data,
						});
					}
				});
			},
			nextDetails(item) {
				//跳转详情
				uni.navigateTo({
					url: '/pageA/pageB/community/customermenu/details?customerId='+item.customerId+'&date='+this.date+'&roomNumber='+item.roomNumber
				})
			},
			listTabs(index) {
				this.generateCalendar()
				this.setData({
					tabIndex: index
				})
				if(index==0){
					this.getStandardMenu(this.date)
				}
				if(index==1){
					this.getRoomCustomers(this.date)
				}
			},
			generateCalendar() {
				const firstDay = new Date(this.currentYear, this.currentMonth - 1, 1)
				const lastDay = new Date(this.currentYear, this.currentMonth, 0)
				const startWeekDay = firstDay.getDay()
				const totalDays = lastDay.getDate()
				const today = new Date()
				const dateStr =
					`${today.getFullYear()}-${(today.getMonth()+1).toString().padStart(2,'0')}-${today.getDate().toString().padStart(2,'0')}`
				this.date = dateStr
				let days = []
				// 添加上月残余
				const prevMonthDays = new Date(this.currentYear, this.currentMonth - 1, 0).getDate()
				for (let i = 0; i < startWeekDay; i++) {
					days.push(this.createDayData(
						prevMonthDays - startWeekDay + i + 1,
						false
					))
				}

				// 添加本月日期
				for (let i = 1; i <= totalDays; i++) {
					days.push(this.createDayData(
						i,
						true,
						this.currentYear === today.getFullYear() &&
						this.currentMonth === today.getMonth() + 1 &&
						i === today.getDate()
					))
				}

				// 添加下月开头
				const remaining = 42 - days.length
				for (let i = 1; i <= remaining; i++) {
					days.push(this.createDayData(i, false))
				}

				this.daysArray = days
			},
			createDayData(date, isCurrentMonth, isToday = false) {
				return {
					date,
					isCurrentMonth,
					isToday,
					fullDate: `${this.currentYear}-${this.currentMonth}-${date}`,
					hasEvent: Math.random() > 0.7,
					isSelected: false
				}
			},
			prevMonth() {
				if (this.currentMonth === 1) {
					this.currentYear--
					this.currentMonth = 12
				} else {
					this.currentMonth--
				}
				this.generateCalendar()
			},
			nextMonth() {
				if (this.currentMonth === 12) {
					this.currentYear++
					this.currentMonth = 1
				} else {
					this.currentMonth++
				}
				this.generateCalendar()
			},
			selectDay(day, index) {
				// this.daysArray.forEach(d => d.isSelected = false)
				// day.isSelected = true
				const daysArray = this.daysArray.map((day, i) => ({
					...day,
					isToday: i === index && day.isCurrentMonth
				}))
				this.daysArray = daysArray
				this.date=day.fullDate
				if (this.tabIndex == 0) {
					if(this.rolesShow=='ADMIN'||this.rolesShow=='BOSS'||this.rolesShow=='APP_SUPER_ADMIN'||this.rolesShow=='SALES_MANAGER'){
					 this.getStandardMenu(day.fullDate)
					}
					if(this.rolesShow=='CUSTOMER'){
						this.getmenuMyList(day.fullDate)
					}
				}
				if (this.tabIndex ==1) {
					this.getRoomCustomers(day.fullDate)
				}
			}
		}
	}
</script>

<style lang="less" scoped>
	@import './customermenu.less';
</style>