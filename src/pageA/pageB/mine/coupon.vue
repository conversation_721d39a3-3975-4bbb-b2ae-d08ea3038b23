<template>
    <view class="main">
        <!-- <view class="content" wx:for="{{listData}}" bindtap="couponDetails"> -->
        <view class="content" v-for="(item, index) in listData" :key="index">
            <view class="content2">
                <image :src="item.image" mode="" style="width: 160rpx; height: 160rpx; border-radius: 10rpx" />
                <view class="content1">
                    <view class="content_1">
                        <view>
                            <view class="content_2">
                                {{ item.couponName }}
                            </view>
                            <!-- <view class="content_3">
						有效期至2024年12月21日
					</view> -->
                        </view>
                        <view>
                            <text class="content_4">￥</text>
                            <text class="content_5">{{ item.price }}</text>
                        </view>
                    </view>
                    <view class="content_6">
                        <view class="content_7" @tap="pullDownShow">
                            <!-- <view class="content_8">
						使用规则
					</view>
					<image src="{{pullDown?'http://cdn.xiaodingdang1.com/ic_home_%E6%9B%B4%E5%A4%9A_default%403x%20%283%29.png':'http://cdn.xiaodingdang1.com/ic_home_%E6%9B%B4%E5%A4%9A_default%403x%20%282%29.png'}}" mode="" style="width: 20rpx;height: 10rpx;" /> -->
                        </view>
                        <view class="content_9">去使用</view>
                    </view>
                </view>
            </view>

            <!-- <view class="content3" wx:if="{{pullDown}}">
			<view>
				(1)仅限在线支付使用，且下单时收货人联系电话需要与账号绑定的手机号一致；
			</view>
			<view>
				(2)红包有品类及金额限制，需要在对应品类下且满足限制金额后才可使用;
			</view>
			<view>
				(3)每个红包只能使用一次，不能叠加或拆分使用；
			</view>
			<view>
				(4)到店自取订单不可使用；
			</view>
		</view> -->
        </view>
    </view>
</template>

<script>
    export default {
        data() {
            return {
                pullDown: false,
                listData: []
            };
        }
        /**
         * 生命周期函数--监听页面加载
         */
        ,
        onLoad(options) {},
        /**
         * 生命周期函数--监听页面初次渲染完成
         */
        onReady() {},
        /**
         * 生命周期函数--监听页面显示
         */
        onShow() {
            this.getList();
        },
        /**
         * 生命周期函数--监听页面隐藏
         */
        onHide() {},
        /**
         * 生命周期函数--监听页面卸载
         */
        onUnload() {},
        /**
         * 页面相关事件处理函数--监听用户下拉动作
         */
        onPullDownRefresh() {},
        /**
         * 页面上拉触底事件的处理函数
         */
        onReachBottom() {},
        /**
         * 用户点击右上角分享
         */
        onShareAppMessage() {},

        onShareTimeline() {},
        methods: {
            pullDownShow() {
                //使用规则
                this.setData({
                    pullDown: !this.pullDown
                });
            },

            couponDetails() {
                //优惠劵详情
                uni.navigateTo({
                    url: '/pageA/pageB/mine/coupon/detail'
                });
            },

            getList() {
                let left = this;
                let data = {
                    pageSize: 1000,
                    pageNum: 1
                };
                this.$axios.get(this.$api.couponPage, data).then((res) => {
                    if (res.data.code == 200) {
                        left.setData({
                            listData: res.data.rows
                        });
                    }
                });
            }
        }
    };
</script>
<style>
    @import './coupon.css';
</style>