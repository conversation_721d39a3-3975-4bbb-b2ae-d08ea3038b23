<template>
  <view
    class="sidebar-overlay"
    v-if="visible"
    @click="closeSidebar"
    @touchmove.stop.prevent
  >
    <view class="sidebar-container" @click.stop>
      <!-- 侧边栏头部 -->
      <view class="sidebar-header">
        <text class="sidebar-title">历史会话</text>
      </view>

      <!-- 会话列表 -->
      <scroll-view class="sidebar-content" scroll-y>
        <!-- 今天 -->
        <view class="session-group" v-if="todaySessions.length > 0">
          <view class="group-title">今天</view>
          <view
            class="session-item"
            :class="{
              'current-session': isCurrentSession(session.conversationId),
            }"
            v-for="session in todaySessions"
            :key="session.conversationId"
            @click="selectSession(session)"
          >
            <view class="session-content">
              <text class="session-title">{{
                formatTitle(session.title)
              }}</text>
              <text
                v-if="isCurrentSession(session.conversationId)"
                class="current-label"
                >当前会话</text
              >
            </view>
            <view class="session-actions">
              <view
                v-if="!isCurrentSession(session.conversationId)"
                class="delete-btn"
                @click.stop="showDeleteConfirm(session)"
              >
                <uni-icons type="trash" size="16" color="#999"></uni-icons>
              </view>
            </view>
          </view>
        </view>

        <!-- 本周 -->
        <view class="session-group" v-if="thisWeekSessions.length > 0">
          <view class="group-title">本周</view>
          <view
            class="session-item"
            :class="{
              'current-session': isCurrentSession(session.conversationId),
            }"
            v-for="session in thisWeekSessions"
            :key="session.conversationId"
            @click="selectSession(session)"
          >
            <view class="session-content">
              <text class="session-title">{{
                formatTitle(session.title)
              }}</text>
              <text
                v-if="isCurrentSession(session.conversationId)"
                class="current-label"
                >当前会话</text
              >
            </view>
            <view class="session-actions">
              <view
                v-if="!isCurrentSession(session.conversationId)"
                class="delete-btn"
                @click.stop="showDeleteConfirm(session)"
              >
                <uni-icons type="trash" size="16" color="#999"></uni-icons>
              </view>
            </view>
          </view>
        </view>

        <!-- 30天内 -->
        <view class="session-group" v-if="thisMonthSessions.length > 0">
          <view class="group-title">30天内</view>
          <view
            class="session-item"
            :class="{
              'current-session': isCurrentSession(session.conversationId),
            }"
            v-for="session in thisMonthSessions"
            :key="session.conversationId"
            @click="selectSession(session)"
          >
            <view class="session-content">
              <text class="session-title">{{
                formatTitle(session.title)
              }}</text>
              <text
                v-if="isCurrentSession(session.conversationId)"
                class="current-label"
                >当前会话</text
              >
            </view>
            <view class="session-actions">
              <view
                v-if="!isCurrentSession(session.conversationId)"
                class="delete-btn"
                @click.stop="showDeleteConfirm(session)"
              >
                <uni-icons type="trash" size="16" color="#999"></uni-icons>
              </view>
            </view>
          </view>
        </view>

        <!-- 更早的会话 -->
        <view class="session-group" v-if="olderSessions.length > 0">
          <view class="group-title">2025年4月</view>
          <view
            class="session-item"
            :class="{
              'current-session': isCurrentSession(session.conversationId),
            }"
            v-for="session in olderSessions"
            :key="session.conversationId"
            @click="selectSession(session)"
          >
            <view class="session-content">
              <text class="session-title">{{
                formatTitle(session.title)
              }}</text>
              <text
                v-if="isCurrentSession(session.conversationId)"
                class="current-label"
                >当前会话</text
              >
            </view>
            <view class="session-actions">
              <view
                v-if="!isCurrentSession(session.conversationId)"
                class="delete-btn"
                @click.stop="showDeleteConfirm(session)"
              >
                <uni-icons type="trash" size="16" color="#999"></uni-icons>
              </view>
            </view>
          </view>
        </view>

        <!-- 空状态 -->
        <view class="empty-state" v-if="allSessions.length === 0">
          <text class="empty-text">暂无历史会话</text>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import dayjs from 'dayjs';
import {
  getConversationListPageApi,
  deleteConversationApi,
} from '@/api/aiService.js';
import { checkHasLogined } from '@/utils/auth.js';

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  currentConversationId: {
    type: [String, Number],
    default: null,
  },
});

// Emits
const emit = defineEmits(['close', 'select-session']);

// 数据
const sessions = ref([]);
const isLoading = ref(false);

// 计算属性 - 按时间分组会话
const allSessions = computed(() => sessions.value);

const todaySessions = computed(() => {
  const today = dayjs().startOf('day');
  return allSessions.value.filter((session) =>
    dayjs(session.createTime).isAfter(today),
  );
});

const thisWeekSessions = computed(() => {
  const weekStart = dayjs().startOf('week');
  const today = dayjs().startOf('day');
  return allSessions.value.filter((session) => {
    const sessionTime = dayjs(session.createTime);
    return sessionTime.isAfter(weekStart) && sessionTime.isBefore(today);
  });
});

const thisMonthSessions = computed(() => {
  const monthStart = dayjs().subtract(30, 'day');
  const weekStart = dayjs().startOf('week');
  return allSessions.value.filter((session) => {
    const sessionTime = dayjs(session.createTime);
    return sessionTime.isAfter(monthStart) && sessionTime.isBefore(weekStart);
  });
});

const olderSessions = computed(() => {
  const monthStart = dayjs().subtract(30, 'day');
  return allSessions.value.filter((session) =>
    dayjs(session.createTime).isBefore(monthStart),
  );
});

// 判断是否为当前会话
const isCurrentSession = (sessionId) => {
  return (
    props.currentConversationId && sessionId === props.currentConversationId
  );
};

// 格式化标题，移除换行符以确保省略号正常显示
const formatTitle = (title) => {
  if (!title) return '';
  // 将所有换行符（\n, \r\n, \r）替换为空格，并移除多余的空格
  return title
    .replace(/[\r\n]+/g, ' ')
    .replace(/\s+/g, ' ')
    .trim();
};

// 加载会话列表
const loadSessions = async () => {
  try {
    isLoading.value = true;

    // 检查登录状态，未登录时不加载会话列表
    const isLoggedIn = await checkHasLogined();
    if (!isLoggedIn) {
      console.log('用户未登录，不加载会话列表');
      sessions.value = [];
      return;
    }

    const res = await getConversationListPageApi({
      pageNum: 1,
      pageSize: 50,
    });
    if (res.code === 200) {
      sessions.value = res.data.records || [];
    }
  } catch (error) {
    console.error('加载历史会话失败:', error);
    uni.showToast({
      title: '加载会话失败',
      icon: 'none',
    });
  } finally {
    isLoading.value = false;
  }
};

// 刷新会话列表
const refreshSessions = () => {
  loadSessions();
};

// 删除会话逻辑
const deleteSession = async (session) => {
  try {
    const res = await deleteConversationApi(session.conversationId);
    if (res.code === 200) {
      // 从本地列表中移除已删除的会话
      const index = sessions.value.findIndex(
        (s) => s.conversationId === session.conversationId,
      );
      if (index > -1) {
        sessions.value.splice(index, 1);
      }
      uni.showToast({
        title: '会话已删除',
        icon: 'success',
      });
    } else {
      uni.showToast({
        title: res.message || '删除失败',
        icon: 'none',
      });
    }
  } catch (error) {
    console.error('删除会话失败:', error);
    uni.showToast({
      title: '删除失败',
      icon: 'none',
    });
  }
};

// 方法
const closeSidebar = () => {
  console.log('关闭侧边栏');
  emit('close');
};

const selectSession = (session) => {
  emit('select-session', session);
  closeSidebar();
};

const showDeleteConfirm = (session) => {
  // 检查是否为当前会话，当前会话不能被删除
  if (
    props.currentConversationId &&
    session.conversationId === props.currentConversationId
  ) {
    uni.showToast({
      title: '当前会话不能删除',
      icon: 'none',
    });
    return;
  }

  // 使用原生 API 显示确认弹窗
  uni.showModal({
    title: '删除会话',
    content: '确定要删除这个会话吗？删除后无法恢复。',
    confirmText: '删除',
    confirmColor: '#FF4D4F',
    cancelText: '取消',
    success: (res) => {
      if (res.confirm) {
        // 用户点击确定
        deleteSession(session);
      }
      // 用户点击取消，不需要做任何处理
    },
  });
};

// 监听侧边栏显示状态，显示时加载会话列表
watch(
  () => props.visible,
  (newVisible) => {
    if (newVisible) {
      loadSessions();
    }
  },
);

// 组件挂载时加载会话列表
onMounted(() => {
  loadSessions();
});

// 暴露方法供父组件调用
defineExpose({
  refreshSessions,
  loadSessions,
});
</script>

<style lang="less" scoped>
.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  justify-content: flex-start;
}

.sidebar-container {
  padding-top: calc(var(--status-bar-height) + 10rpx);
  width: 280px;
  height: 100vh;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

.sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.sidebar-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.close-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: #f5f5f5;
}

.sidebar-content {
  flex: 1;
  height: 0;
  padding: 16px 0;
  padding-bottom: 180rpx;

  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.session-group {
  margin-bottom: 16rpx;
}

.group-title {
  font-size: 14px;
  color: #999;
  padding: 0 16px 8px;
  font-weight: 500;
}

.session-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f8f8f8;
  transition: background-color 0.2s;
}

.session-item:active {
  background-color: #f5f5f5;
}

.session-item.current-session {
  background-color: #e3f2fd;
  border-left: 3px solid #4285f4;
}

.session-item.current-session:active {
  background-color: #bbdefb;
}

.session-content {
  flex: 1;
  min-width: 0;
}

.session-title {
  font-size: 15px;
  color: #333;
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.current-label {
  font-size: 12px;
  color: #4285f4;
  margin-top: 2px;
  display: block;
}

.session-actions {
  margin-left: 12px;
}

.delete-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.delete-btn:active {
  background-color: #f0f0f0;
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60px 16px;
}

.empty-text {
  font-size: 14px;
  color: #999;
}
</style>
