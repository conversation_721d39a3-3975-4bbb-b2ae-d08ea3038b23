<template>
    <view class="navbar"
        :style="'background:' + background + ';height:' + (heightCustom == 0 ? navbarHeight : heightCustom) + 'px;position:' + (isFixed ? 'fixed' : '')">
        <image :src="backgroundImg" alt="" class="img_nav" v-if="backgroundImg"></image>
        <view @tap="handleGoToBack" :style="'height:' + capsuleHeight + 'px;top:' + capsuleTop + 'px;'"
            class="arrow-content">
            <view class="arrow"
                :style="'border: 5rpx solid ' + color + ';  width: 20rpx;height: 20rpx;border-right-color:transparent;border-bottom-color:transparent;transform: rotate(-45deg);'"
                v-if="isSowArrow"></view>
        </view>
        <view class="text" :style="'height:' + capsuleHeight + 'px;top:' + capsuleTop + 'px;color:' + color">{{ title }}
        </view>
    </view>
</template>

<script>
    // 自定义设置顶部栏
    const app = getApp();
    export default {
        data() {
            return {
                capsuleTop: '',
                //胶囊距离屏幕顶部的距离
                capsuleHeight: '',
                //胶囊高度
                navbarHeight: '' //导航栏高度
            };
        },
        props: {
            title: {
                type: String,
                default: '新视界'
            },
            background: {
                type: String,
                default: ''
            },
            isSowArrow: {
                type: Boolean,
                default: true
            },
            color: {
                type: String,
                default: ''
            },
            heightCustom: {
                type: Number,
                default: 0
            },
            backgroundImg: {
                type: String,
                default: ''
            },
            isFixed: {
                type: Boolean,
                default: true
            }
        },
        mounted() {
            // 处理小程序 attached 生命周期
            this.attached();
        },
        methods: {
            attached: function() {
                this.setData({
                    capsuleTop: app.globalData.capsule.top,
                    capsuleHeight: app.globalData.capsule.height,
                    navbarHeight: (app.globalData.capsule.top - app.globalData.system.statusBarHeight) * 2 +
                        app.globalData.capsule.height + app.globalData.system.statusBarHeight
                });
            },

            handleGoToBack() {
                try {
                    uni.navigateBack({
                        delta: 1,
                        fail: () => {
                            uni.switchTab({
                                url: '/pageA/home'
                            })
                        }
                    });
                } catch (e) {
                    uni.switchTab({
                        url: '/pageA/home'
                    })
                }
            }
        },
        created: function() {}
    };
</script>
<style scoped>
    .navbar {
        width: 100%;
        /* position: fixed; */
        top: 0;
        z-index: 9999;
    }

    .arrow-content {
        position: absolute;
        left: 20rpx;
        z-index: 1000;
        display: flex;
        align-items: center;
        padding: 0 40rpx 0 20rpx;
    }

    .text {
        position: absolute;
        left: 0;
        right: 0;
        font-size: 33rpx;
        color: #333333;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .img_nav {
        height: 100%;
        width: 100%;
        object-fit: cover;
    }

    .click-area {
        width: 60rpx;
        height: 60rpx;
        position: relative;
        bottom: 10rpx;
        right: 10rpx;
    }

    .click-area view {
        position: relative;
        top: 20rpx;
        left: 20rpx;
    }
</style>