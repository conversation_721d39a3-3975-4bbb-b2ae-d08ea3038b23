<template>
  <view>
    <navigation
      :title="title"
      :background="bgColor"
      :isSowArrow="true"
    ></navigation>
    <view style="height: 600rpx; width: 100%">
      <swiper-banner :imgList="imgList" :list="banner_list"></swiper-banner>
    </view>
    <view class="main">
      <view class="card">
        <view class="card-top flex">
          <view class="card-top-price">{{
            inforList.packagePrice ? inforList.packagePrice : "----"
          }}</view>
          <view class="card-top-desc"
            >已有{{ inforList.consultNumber}}人咨询</view
          >
        </view>
        <view class="card-title">{{ inforList.packageName }}</view>
        <view
          class="card-config flex"
          v-if="
            inforList.bathroomFacilities &&
            inforList.bathroomFacilities.length > 0
          "
        >
          <view class="card-config-label">配置</view>
          <scroll-view :scroll-x="true" class="card-config-tag flex">
            <view
              class="card-config-tag-enum"
              v-for="(res, i) in inforList.bathroomFacilities"
              :key="i"
            >
              {{ res }}
            </view>
          </scroll-view>
        </view>
        <view class="card-service flex">
          <view class="card-service-label">服务</view>
          <scroll-view :scroll-x="true" class="card-service-tag flex">
            <view
              class="card-service-tag-enum"
              v-for="(item, index) in tag"
              :key="index"
            >
              {{ item }}
            </view>
          </scroll-view>
        </view>
      </view>
      <view class="devices">
        <view class="page-title">房间设施</view>
        <view class="devices-content">
          <view class="roomFacility2">
            <view class="roomFacility1">
              <image
                src="http://cdn.xiaodingdang1.com/2024/11/23/7b2fae05a71141069fe8c71677dbbcaa.png"
                mode=""
                style="width: 50rpx; height: 50rpx"
              />
              <view class="roomFacilityTitle">便利设施</view>
            </view>
            <view class="roomFacilityFGX"></view>
            <view class="roomFacilityConment">
              <view
                class="roomFacilityConment1"
                v-for="(item, index) in inforList.facilityFeatures"
                :key="index"
              >
                {{ item }}
              </view>
            </view>
          </view>
          <view class="roomFacility2">
            <view class="roomFacility1">
              <image
                src="http://cdn.xiaodingdang1.com/2024/11/23/939024bbd99c4bf0923591b3363a7d57.png"
                mode=""
                style="width: 50rpx; height: 50rpx"
              />
              <view class="roomFacilityTitle">卫浴配套</view>
            </view>
            <view class="roomFacilityFGX"></view>
            <view class="roomFacilityConment">
              <view
                class="roomFacilityConment1"
                v-for="(item, index) in inforList.bathroomFacilities"
                :key="index"
              >
                {{ item }}
              </view>
            </view>
          </view>
          <view class="roomFacility2">
            <view class="roomFacility1">
              <image
                src="http://cdn.xiaodingdang1.com/2024/11/23/5da398bbdfbb4bdbb087bd5db3e1399e.png"
                mode=""
                style="width: 50rpx; height: 50rpx"
              />
              <view class="roomFacilityTitle">媒体娱乐</view>
            </view>
            <view class="roomFacilityFGX"></view>
            <view class="roomFacilityConment">
              <view
                class="roomFacilityConment1"
                v-for="(item, index) in inforList.mediaFeatures"
                :key="index"
              >
                {{ item }}
              </view>
            </view>
          </view>
          <view class="roomFacility2">
            <view class="roomFacility1">
              <image
                src="http://cdn.xiaodingdang1.com/2024/11/23/a28feb4eedef435e980b66ad3b39c8e7.png"
                mode=""
                style="width: 50rpx; height: 50rpx"
              />
              <view class="roomFacilityTitle">室外景观</view>
            </view>
            <view class="roomFacilityFGX"></view>
            <view class="roomFacilityConment">
              <view
                class="roomFacilityConment1"
                v-for="(item, index) in inforList.outdoorFeatures"
                :key="index"
              >
                {{ item }}
              </view>
            </view>
          </view>
        </view>
      </view>
      <view class="package">
        <view class="page-title">套餐详情</view>
        <view class="package-content">
          <template v-for="(model, index) in modalData" :key="index">
            <view class="title">{{ model.title }}</view>
            <view
              class="subtitle"
              v-for="(item, i) in model.itemArray"
              :key="i"
            >
              <view class="left">{{ item.key }}</view>
              <view class="right">{{ item.value }}</view>
            </view>
          </template>
        </view>
      </view>
      <!-- <view class="sign">
                <view class="page-title">签约礼物</view>
                <image style="width: 365px; margin: 0 auto; display: block"
                    src="http://cdn.xiaodingdang1.com/2024/11/23/df8b6ab891cc4f0cabff357d3ae0b612.png"></image>
            </view> -->
      <view class="question">
        <view class="page-title">大家都在问</view>
        <view class="question-content">
          <question-vue category="room"></question-vue>
        </view>
      </view>
      <view
        class="detail"
        v-if="inforList.textPhotos && inforList.textPhotos.length > 0"
      >
        <view class="page-title">图文详情</view>
        <view class="detail-content">
          <image
            class="third-img"
            :src="item"
            mode=""
            v-for="(item, index) in inforList.textPhotos"
            :key="index"
          >
          </image>
        </view>
      </view>
      <view class="recomment">
        <view class="page-title">为您推荐</view>
        <view class="recomment-content">
          <recomment-vue category="room"></recomment-vue>
        </view>
      </view>
    </view>
    <new-bottom :from="SessionFrom" :type="4" :id="inforList.packageId"></new-bottom>
    <mask-dialog></mask-dialog>
  </view>
</template>
<script setup>
const modalData = ref([
  {
    title: "护理模式",
    itemArray: [
      {
        key: "陪护人员类型",
        value: "月嫂",
      },
      {
        key: "陪护人员配置",
        value: "1对1护理",
      },
      {
        key: "母婴照护模式",
        value: "母婴同室护理",
      },
    ],
  },
  {
    title: "月子餐",
    itemArray: [
      {
        key: "餐饮模式",
        value: "3餐3点",
      },
    ],
  },
  {
    title: "妈妈护理",
    itemArray: [
      {
        key: "专业妈妈护理",
        value: "按需",
      },
    ],
  },
  {
    title: "宝宝护理",
    itemArray: [
      {
        key: "专业宝宝护理",
        value: "按需",
      },
    ],
  },
  // {
  //     title: '妈妈用品',
  //     itemArray: [{
  //         key: '高级母婴品牌套餐内赠送物',
  //         value: ''
  //     }, ]
  // },
  // {
  //     title: '宝宝用品',
  //     itemArray: [{
  //         key: '高级母婴品牌套餐内赠送物',
  //         value: ''
  //     }, ]
  // }
]);
const tag = ["住宿服务", "餐饮服务", "妈妈专业服务", "宝宝专业护理"];
const attendantTypeList = new Map([
  ["1", "护士"],
  ["2", "月嫂"],
]);
const attendantConfigurationList = new Map([
  ["1", "1对1护理"],
  ["2", "2对1护理"],
  ["3", "3对1护理"],
  ["4", "集中护理"],
]);
const maternityCareModelList = new Map([
  ["1", "母婴同室护理"],
  ["2", "婴儿集中托管"],
  ["3", "母婴同室（可托管）"],
]);
import questionVue from "../component/question.vue";
import recommentVue from "../component/recomment.vue";
import swiperBanner from "@/components/swiperBanner.vue";
import {
  onShow,
  onHide,
  onLoad,
  onPageScroll,
  onShareAppMessage,
  onShareTimeline,
} from "@dcloudio/uni-app";
import { ref, reactive, onMounted, computed, getCurrentInstance } from "vue";
const instance = getCurrentInstance();
const App = instance.appContext.config.globalProperties;
onShareAppMessage(() => {});
onShareTimeline(() => {});
const title = ref("");
onShow( async () => {
  let newDateTime = Date.parse(new Date());
  let pointStart = {
    eventType: "PAGE_VIEW",
    pageUrl: "/pageA/pageB/home/<USER>/pagedetail",
    module: "room",
    eventTime: newDateTime,
  };
  let iftoken = uni.getStorageSync("token");
  if (iftoken) {
    App.$point.basePoint(pointStart);
  }
  await pageInit();
});
onHide(() => {
  let newDateTime = Date.parse(new Date());
  let pointEnd = {
    eventId: uni.getStorageSync("eventId"),
    leaveTime: newDateTime,
  };
  App.$point.reportEnd(pointEnd);
});
const bgColor = ref("");
onPageScroll((e) => {
  if (e.scrollTop > 0) {
    bgColor.value = "#ffffff";
    title.value = "详情";
  } else if (e.scrollTop < 2) {
    bgColor.value = "";
    title.value = "";
  }
});
// const bgColor = ref('rgba(0,0,0,0)')
const packageId = ref("");
const SessionFrom =
  uni.getStorageSync("tenantId") +
  "," +
  uni.getStorageSync("userInfo").nickname +
  "," +
  "月子套房";
onLoad(async (options) => {
  console.log("optionsssss", options);
  packageId.value = options.packageId;
});
const pageInit = async () => {
  try {
    await suiteInfo();
  } catch {}
};
const inforList = reactive({});
const banner_list = reactive([]);
const imgList = reactive([]);
const suiteInfo = async () => {
  //查询房间信息
  const res = await App.$axios.get(App.$api.packageInfo, {
    packageId: packageId.value,
  });
  if (res.data.code == 200) {
    let data = res.data.data;
    let slideshow = [];
    let imgList = [];
    if (data.photos) {
      // data.suitePhotos.forEach(item => {
      let list = {
        type: 0,
        url: data.photos,
      };
      slideshow.push(list);
      imgList.push(data.photos);
      // });
    }
    Object.assign(inforList, data);
    Object.assign(banner_list, slideshow);
    Object.assign(imgList, imgList);
    // 套餐详情字段填充
    // 陪护人员类型
    if (data.attendantType) {
      let key = data.attendantType.toString();
      modalData.value[0].itemArray[0].value = attendantTypeList.get(key);
    }
    //陪护人员配置
    if (data.attendantConfiguration) {
      let key = data.attendantConfiguration.toString();
      modalData.value[0].itemArray[1].value =
        attendantConfigurationList.get(key);
    }
    // 母婴照护模式
    if (data.maternityCareModel) {
      let key = data.maternityCareModel.toString();
      modalData.value[0].itemArray[2].value = maternityCareModelList.get(key);
    }
    // 月子餐
    if (data.dailyMeals && data.dailySnacks) {
      modalData.value[1].itemArray[0].value =
        data.dailyMeals + "餐" + data.dailySnacks + "点";
    }
  }
};
</script>

<style lang="less" scoped>
@import "./pagedetail.less";
</style>
