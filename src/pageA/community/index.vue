<template>
  <view>
    <navigation
      :background="navigationBgColor"
      title="会所服务"
      :isSowArrow="false"
    ></navigation>
    <view class="main">
      <view class="mains">
        <view class="top">
          <ClubTabs
            :activeTab="activeServiceTab"
            @tab-change="handleServiceTabChange"
          />
        </view>
        <view class="cut-off"></view>
        <ServiceTabs
          :tagList="serviceTagList"
          :selectedTaskNodeIds="selectedTaskNodeIds"
          :isAllSelected="isAllSelected"
          :scrollPosition="horizontalScrollPosition"
          @tab-click="onSecondClick"
          @select-all="selectAll"
        />
        <view class="content">
          <MomRecommendation
            :recommendedMomList="recommendedMomList"
            :scrollPosition="horizontalScrollPosition"
          />
          <ContentList ref="contentListRef" :contentList="contentList">
            <template #header>
              <MomSays :momSaysData="momSaysData" />
            </template>
          </ContentList>
        </view>
      </view>
    </view>
    <!-- <rightMenuBarVue :scrollTop="scrollTop"></rightMenuBarVue> -->
    <!-- <u-back-top :scroll-top="scrollTop" top="1200"></u-back-top> -->
    <view class="btn">
      <button open-type="share" class="image-button">
        <image
          src="http://cdn.xiaodingdang1.com/2025/05/23/e4c88700d42747ba826408937b7177c7.png"
          mode="aspectFill"
          style="width: 100%; height: 100%"
        />
      </button>
      <u-back-top
        style="border-radius: 100rpx"
        :scrollTop="scrollTop"
        :mode="backToTopMode"
        :icon-style="backToTopIconStyle"
        :custom-style="backToTopCustomStyle"
      ></u-back-top>
    </view>
    <publishbutton-vue
      ref="publishRef"
      roleIf="2"
      permissions="feed:post:create"
    ></publishbutton-vue>

    <!-- 自定义 tabbar -->
    <CustomTabBar activeKey="community" />
  </view>
</template>

<script setup>
import { ref, reactive, getCurrentInstance, nextTick } from 'vue';
import {
  onLoad,
  onReady,
  onShow,
  onHide,
  onUnload,
  onPullDownRefresh,
  onReachBottom,
  onShareAppMessage,
  onShareTimeline,
  onPageScroll,
} from '@dcloudio/uni-app';
import publishbuttonVue from '@/components/publishbutton.vue';
import CustomTabBar from '@/components/CustomTabBar.vue';
import ClubTabs from './components/ClubTabs.vue';
import ServiceTabs from './components/ServiceTabs.vue';
import MomRecommendation from './components/MomRecommendation.vue';
import MomSays from './components/MomSays.vue';
import ContentList from './components/ContentList.vue';
import {
  SERVICE_TYPES,
  PAGINATION_CONFIG,
  RESPONSE_CODES,
  LOAD_STATUS,
} from './constants.js';
import {
  getServiceListByType,
  getServiceListByTitle,
  mergeAllServiceLists,
  processStaffEmploymentInfo,
  checkHasMoreData,
} from './utils.js';

// 获取当前实例
const { proxy } = getCurrentInstance();

// 响应式数据
const backToTopMode = ref('square');
const backToTopIconStyle = reactive({
  fontSize: '35rpx',
  color: '#4A4A4A',
});
const backToTopCustomStyle = reactive({
  backgroundColor: '#ffffff',
  borderRadius: '100rpx',
  width: '70rpx',
  height: '70rpx',
  bottom: '180rpx',
  right: '24rpx',
  boxShadow: '2px 2px 5px rgba(0,0,0,0.1)',
});

const navigationBgColor = ref('');
const intersectionObserver = ref(null);
const scrollTop = ref(0);
const momSaysData = ref([]);
const selectedTaskNodeIds = ref([]);
const contentList = ref([]);
const hasMoreData = ref('');
const pageSize = ref(PAGINATION_CONFIG.DEFAULT_PAGE_SIZE);
const currentPage = ref(PAGINATION_CONFIG.INITIAL_PAGE);
const recommendedMomList = ref([]);
const postpartumRecoveryList = ref([]); // 产后康复
const confinementMealList = ref([]); // 月子膳食
const nursingServiceList = ref([]); // 护理服务
const otherServiceList = ref([]); // 其他服务

const horizontalScrollPosition = ref(0);

const serviceTagList = ref([]);
const isAllSelected = ref(false);
const activeServiceTab = ref('');

// refs
const contentListRef = ref(null);
const publishRef = ref(null);
// 生命周期函数
/**
 * 生命周期函数--监听页面加载
 */
onLoad(() => {
  loadMomSaysData();
  loadRecommendedMomList();
  initializeServiceTags();
});

/**
 * 生命周期函数--监听页面初次渲染完成
 */
onReady(() => {
  intersectionObserver.value = uni.createIntersectionObserver(proxy, {
    thresholds: [0],
  });
});

/**
 * 生命周期函数--监听页面显示
 */
onShow(() => {
  uni.$on('publishsuccesss', () => {
    // 刷新页面列表接口
    reloadContentList();
  });
  // reload()
  nextTick(() => {
    publishRef.value?.publishInit();
  });
});

/**
 * 生命周期函数--监听页面隐藏
 */
onHide(() => {
  intersectionObserver.value?.disconnect();
  let newDateTime = Date.parse(new Date());
  let endData = {
    eventId: uni.getStorageSync('eventId'),
    leaveTime: newDateTime,
  };
  proxy.$point.reportComEnd(endData);
});

/**
 * 生命周期函数--监听页面卸载
 */
onUnload(() => {
  uni.$off('publishsuccesss');
  intersectionObserver.value?.disconnect();
});

/**
 * 页面相关事件处理函数--监听用户下拉动作
 */
onPullDownRefresh(() => {});

/**
 * 页面上拉触底事件的处理函数
 */
onReachBottom(() => {
  // 触发页面上拉刷新
  reachBottom();
});

/**
 * 用户点击右上角分享
 */
onShareAppMessage(() => {});

onShareTimeline(() => {});

onPageScroll((e) => {
  if (e.scrollTop > 0) {
    navigationBgColor.value = '#ffffff';
  } else if (e.scrollTop < 2) {
    navigationBgColor.value = '';
  }
  scrollTop.value = e.scrollTop;
});
// 方法定义
const handleServiceTabChange = (item) => {
  // 判断重复点击取消
  selectedTaskNodeIds.value = [];
  isAllSelected.value = false;
  if (activeServiceTab.value == item.title) {
    activeServiceTab.value = '';
    serviceTagList.value = mergeAllServiceLists({
      postpartumRecoveryList,
      confinementMealList,
      nursingServiceList,
      otherServiceList,
    });
  } else {
    activeServiceTab.value = item.title;
    const selectedList = getServiceListByTitle(item.title, {
      postpartumRecoveryList,
      confinementMealList,
      nursingServiceList,
      otherServiceList,
    });
    if (selectedList) {
      serviceTagList.value = selectedList.value;
    }
  }
  reloadContentList();
};

const onSecondClick = (taskNodeId) => {
  if (selectedTaskNodeIds.value.includes(taskNodeId)) {
    selectedTaskNodeIds.value.splice(
      selectedTaskNodeIds.value.indexOf(taskNodeId),
      1,
    );
  } else {
    selectedTaskNodeIds.value.push(taskNodeId);
  }
  if (selectedTaskNodeIds.value.length == serviceTagList.value.length) {
    isAllSelected.value = true;
  }
  reloadContentList();
};

const selectAll = () => {
  isAllSelected.value = !isAllSelected.value;
  if (isAllSelected.value) {
    selectedTaskNodeIds.value = [];
    serviceTagList.value.forEach((item) => {
      selectedTaskNodeIds.value.push(item.taskNodeId);
    });
  } else {
    selectedTaskNodeIds.value = [];
  }
  reloadContentList();
};
const reloadContentList = async () => {
  // 重置分页参数
  currentPage.value = PAGINATION_CONFIG.INITIAL_PAGE;
  pageSize.value = PAGINATION_CONFIG.DEFAULT_PAGE_SIZE;
  contentList.value = [];
  loadContentData();
};

const reachBottom = () => {
  console.log('触底加载更多');
  if (hasMoreData.value == LOAD_STATUS.HAS_MORE) {
    currentPage.value = currentPage.value + 1;
    proxy.changeLoading(true);
    loadContentData();
  }
};

const initializeServiceTags = async () => {
  reloadContentList();
  await loadServiceTagsByType(SERVICE_TYPES.POSTPARTUM_RECOVERY); // 产后康复
  await loadServiceTagsByType(SERVICE_TYPES.CONFINEMENT_MEAL); // 月子膳食
  await loadServiceTagsByType(SERVICE_TYPES.NURSING_SERVICE); // 护理服务
  await loadServiceTagsByType(SERVICE_TYPES.OTHER_SERVICE); // 其他服务
  serviceTagList.value = mergeAllServiceLists({
    postpartumRecoveryList,
    confinementMealList,
    nursingServiceList,
    otherServiceList,
  });
};

const loadServiceTagsByType = async (nurseType) => {
  // 通过类型与角色查询标签列表
  const res = await proxy.$axios.post(proxy.$api.listByType, {
    nurseType: nurseType,
  });
  if (res.data.code == RESPONSE_CODES.SUCCESS) {
    const targetList = getServiceListByType(nurseType, {
      postpartumRecoveryList,
      confinementMealList,
      nursingServiceList,
      otherServiceList,
    });
    if (targetList) {
      targetList.value = res.data.data;
    }
  }
};

const loadRecommendedMomList = () => {
  // 查询优质动态宝妈
  proxy.$axios
    .get(proxy.$api.getMomPage, {
      pageSize: PAGINATION_CONFIG.RECOMMENDED_MOM_PAGE_SIZE,
      pageNum: PAGINATION_CONFIG.INITIAL_PAGE,
      isShow: true,
    })
    .then((res) => {
      if (res.data.code == RESPONSE_CODES.SUCCESS) {
        recommendedMomList.value = res.data.rows;
      }
    });
};
const loadContentData = () => {
  let data = {
    pageSize: pageSize.value,
    pageNum: currentPage.value,
  };
  if (activeServiceTab.value && selectedTaskNodeIds.value.length == 0) {
    let list = '';
    serviceTagList.value.forEach((item) => {
      list += item.taskNodeId + ',';
    });
    data.taskNodeIdList = list.substring(0, list.length - 1);
  } else {
    if (selectedTaskNodeIds.value.length > 0) {
      data.taskNodeIdList = selectedTaskNodeIds.value.join(', ');
    }
  }
  proxy.$axios
    .get(proxy.$api.getFeedPostPage, data)
    .then((res) => {
      if (res.data.code == RESPONSE_CODES.SUCCESS) {
        let responseData = res.data.rows;
        if (responseData != null) {
          responseData.forEach(processStaffEmploymentInfo);
          let hasMore = checkHasMoreData(
            responseData.length,
            PAGINATION_CONFIG.DEFAULT_PAGE_SIZE,
          );
          let currentData;
          if (currentPage.value == PAGINATION_CONFIG.INITIAL_PAGE) {
            currentData = [];
          } else {
            currentData = contentList.value;
          }
          let newData = currentData.concat(responseData);
          contentList.value = newData;
          hasMoreData.value = hasMore;
        }
      }
    })
    .finally(() => {
      proxy.changeLoading(false);
    });
};

const loadMomSaysData = () => {
  // 宝妈说
  let data = {
    pageSize: PAGINATION_CONFIG.MOM_SAYS_PAGE_SIZE,
    pageNum: PAGINATION_CONFIG.INITIAL_PAGE,
    orderByColumn: 'fp.create_time',
    isAsc: 'desc',
  };
  proxy.$axios.get(proxy.$api.customerPage, data).then((res) => {
    if (res.data.code == RESPONSE_CODES.SUCCESS) {
      momSaysData.value = res.data.rows;
    }
  });
};
</script>
<style scoped lang="less">
.main {
  // background: url('http://cdn.xiaodingdang1.com/2024/11/19/c0c35d0060e94a348dc109b520c5f305.png') no-repeat;
  background-size: 100%;
  position: relative;
  z-index: 1;
  background: #ffffff;
}

.mains {
  background-size: 100% 988rpx;
  padding: 190rpx 0rpx 0rpx;
  position: relative;
  z-index: 2;
}

.content {
  padding: 5rpx 12rpx calc(48rpx + 150rpx + env(safe-area-inset-bottom)) 12rpx;
  background-color: #f3f3f3;
  min-height: calc(100vh - 545rpx);
}

.top-img {
  border-top-left-radius: 40rpx;
  border-top-right-radius: 40rpx;
  width: 750rpx;
  height: 764rpx;
  position: absolute;
  top: 190rpx;
  left: 0;
  z-index: 1;
  background: url('http://cdn.xiaodingdang1.com/2024/12/03/1c9609a316c34c3a8d4aa592604c0a63.png')
    no-repeat;
}

.top {
  padding: 10rpx 0 0 12rpx;
  // background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #ffffff),color-stop(40%, #fffffd), color-stop(100%, #f3f3f3));
  // background: -webkit-linear-gradient(to bottom, #ffffff 0%, #fffffd 40%, #f3f3f3 100%);
  // background: -o-linear-gradient(to bottom, #ffffff 0%, #fffffd 40%, #f3f3f3 100%);
  // background: -ms-linear-gradient(to bottom, #ffffff 0%, #fffffd 40%, #f3f3f3 100%);
  // background: linear-gradient(to bottom, #ffffff 0%, #fffffd 40%, #f3f3f3 100%);
  border-top-left-radius: 40rpx;
  border-top-right-radius: 40rpx;
  margin-bottom: 20rpx;
}

.title {
  width: 145rpx;
  font-weight: 400;
  font-size: 24rpx;
  color: #5d1e0a;
}

.content1 {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  position: relative;
  z-index: 9;
}

/* pages/tab/index.wxss */
.tab-h {
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
  font-size: 16px;
  white-space: nowrap;
}

.tab-h::-webkit-scrollbar {
  display: none;
  /* 对于Webkit浏览器，隐藏滚动条 */
}

.tab-h {
  scrollbar-width: none;
  /* 对于IE和Edge，隐藏滚动条 */
}

.tab-item {
  display: inline-block;
  padding: 8rpx 20rpx;
  background: #fff4f2;
  border-radius: 66rpx;
  color: #5d1e0a;
  font-size: 24rpx;
  margin-right: 20rpx;
}

.tab-item.active {
  background: linear-gradient(270deg, #fd997b 0%, #ff4f61 100%);
  color: #ffffff;
  position: relative;
  padding: 8rpx 20rpx;
  border-radius: 66rpx;
}

.tab-content {
  margin-top: 80rpx;
}

.scoll-h {
  height: 100%;
}

.scroll-view {
  width: 100%;
  height: 100%;
}

.grid-box {
  position: sticky;
  border-radius: 10px;
}

.content-box {
  border-radius: 0 0 18rpx 18rpx;
  padding: 16rpx 16rpx 22rpx 16rpx;
}

.text {
  display: inline-block;
  color: #ff4f61;
  font-size: 16rpx;
  padding: 4rpx 8rpx;
  border-radius: 80rpx;
  background: #fff3f5;
}

.dynamic {
  padding: 20rpx 36rpx;
  background: #ff4f61;
  border-radius: 110rpx;
  display: flex;
  align-items: center;
  color: #fff;
  font-size: 32rpx;
  position: fixed;
  bottom: 50rpx;
  left: 38%;
}

.momDetails {
  color: #333333;
  font-size: 28rpx;
  display: -webkit-box;
  white-space: pre-wrap;
  -webkit-line-clamp: 2;
  /* 子元素垂直排列 */
  -webkit-box-orient: vertical;
  /* 文本溢出部分显示三个点 */
  text-overflow: ellipsis;
  /* 超出隐藏 */
  overflow: hidden;
}

.loadImg {
  width: 100%;
  overflow: hidden;
  border-radius: 18rpx 18rpx 0 0;

  image {
    display: block;
    min-height: 450rpx;
    max-height: 700rpx;
    width: 100%;
  }

  .default-img {
    width: 100%;
  }

  video {
    width: 100%;
    // height: 450rpx;
    border-radius: 18rpx 18rpx 0 0;
  }

  video::-webkit-media-controls-fullscreen-button {
    display: none;
  }
}

.loadImg.active {
  transition: all 0.5s ease-in-out;
  opacity: 1;
}

.redbg {
  background: linear-gradient(180deg, #ffefef 0%, #ffffff 100%);
}

.bluebg {
  background: linear-gradient(180deg, #edf2ff 0%, #ffffff 100%);
}

.flex {
  display: flex;
  align-items: center;
}

.over {
  white-space: nowrap;
}

.cut-off {
  background: #f6f6f6;
  height: 2rpx;
  width: 100%;
}

.btn {
  position: fixed;
  bottom: 260rpx;
  right: 0rpx;
  z-index: 999;
}

.topRoof {
  background: #fff;
  border-radius: 100rpx;
  width: 80rpx;
  height: 80rpx;
  text-align: center;
  line-height: 130rpx;
}

.image-button {
  /* 1. 去除按钮默认样式 */
  padding: 0;
  border: none;
  background: none;
  /* 2. 设置按钮尺寸 */
  width: 120rpx;
  /* 自定义宽度 */
  height: 120rpx;
  /* 自定义高度 */
  /* 3. 隐藏溢出内容 */
  overflow: hidden;
  /* 4. 可选：添加点击效果 */
  cursor: pointer;
}

.image-button image {
  /* 1. 确保图片填充容器 */
  width: 100%;
  height: 100%;
  /* 2. 保持图片比例并裁剪填充 */
  object-fit: cover;
  /* 3. 消除img默认间距 */
  display: block;
  /* 4. 可选：添加过渡效果 */
  transition: transform 0.3s ease;
}
</style>
