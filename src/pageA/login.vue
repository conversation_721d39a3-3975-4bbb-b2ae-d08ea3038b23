<template>
  <navigation title="" background="" :isSowArrow="true"></navigation>
  <view class="container-login">
    <view class="content">
      <view class="logo-container">
        <view class="circle">
          <image
            src="http://cdn.xiaodingdang1.com/2025/01/23/3c0c17411133461db0cd0b450e22671e.png"
            class="logo-image"
            mode="aspectFit"
          />
        </view>
      </view>
      <view class="app-title">宝妈小叮当</view>
      <view class="agreement">
        <checkbox-group @change="changeCheckbox">
          <checkbox :value="1" checked="{{false}}" class="mycheck" />
        </checkbox-group>
        <text class="agreement-text">我已阅读并同意</text>
        <text class="link" @tap="viewTerms">《用户服务协议》</text>
        <text class="agreement-text">及</text>
        <text class="link" @tap="viewPrivacy">《隐私条款》</text>
      </view>
      <button
        v-if="agree"
        class="login-button"
        open-type="getPhoneNumber"
        @getphonenumber="getPhoneNumber"
      >
        <text class="login-text">授权手机号登录</text>
      </button>
      <button class="login-button" v-else @tap="getAgreeMsg">
        <text class="login-text">授权手机号登录</text>
      </button>
      <button class="back-button" @tap="onBack">
        <text class="back-text">取消</text>
      </button>
    </view>
  </view>
  <u-popup
    v-model="show"
    :closeable="true"
    mode="bottom"
    :safe-area-inset-bottom="true"
    border-radius="20"
  >
    <view v-if="popContent == 'viewTerms'" class="pop-html">
      <view class="pop-title"> 用户服务协议 </view>
      <view class="pop-content">
        微信小程序是一种基于微信平台的轻应用，为用户提供便捷、高效、安全的服务。
        微信小程序用户服务协议和隐私政策是用户使用微信小程序所必须遵守的规定。<br />
        微信小程序用户服务协议主要包括以下内容：<br />
        1.服务内容：微信小程序提供各种服务，包括但不限于线上购物、社交、娱乐、教育等。同时，微信平台也尊重和保护用户的个人信息和隐私权。<br />
        2.使用规范：用户在使用微信小程序时，需要遵守相关规定，包括但不限于不得进行恶意攻击、发布不良信息、侵犯他人权益等行为。同时，用户也需要按照平台要求填写真实有效的个人信息。<br />
        3.用户权利：作为微信小程序的用户，您享有以下权利：获取优质服务、自由表达观点和意见、保护自身隐私权和个人信息等。<br />
        4.用户义务：在享受权利的同时，您需要承担以下义务：保证填写的个人信息真实有效、遵守平台规定、不进行任何违规行为等。
      </view>
    </view>
    <view v-else class="pop-html">
      <view class="pop-title"> 隐私条款 </view>
      <view class="pop-content">
        1.微信小程序平台将采取各种措施保护用户的个人信息和隐私权，包括但不限于数据加密、权限管理、内部监管等。<br />
        2.微信小程序平台将严格控制个人信息的使用范围，只有在必要的情况下才会将个人信息提供给第三方。同时，平台将确保第三方遵守同样的隐私保护标准。
      </view>
    </view>
    <view class="pop-botton">
      <u-button type="primary" shape="circle" @click="confirm">确定</u-button>
    </view>
  </u-popup>
</template>

<script setup>
import { onShow, onHide, onLoad, onPageScroll } from '@dcloudio/uni-app';
import { ref, reactive, onMounted, computed, getCurrentInstance } from 'vue';
import { useStore } from 'vuex';
const store = useStore();
const instance = getCurrentInstance();
const App = instance.appContext.config.globalProperties;
console.log('instance', instance, App);
const popContent = ref(null);
const show = ref(false);
const agree = ref(false);
const getPhoneNumber = async (e) => {
  const phone_code = e.detail.code;
  store.commit('m_user/changeLoading', true);
  uni.login({
    success: async function (res) {
      const code = res.code;
      const data = {
        code: code,
        phoneCode: phone_code,
        appid: uni.getAccountInfoSync().miniProgram.appId,
        clientId: '428a8310cd442757ae699df5d894f051',
        grantType: 'phone',
        tenantId: uni.getStorageSync('tenantId') || '194338',
      };
      const resInfo = await App.$axios.post(App.$api.login_phone_wx, data);
      const result = resInfo?.data.data;
      uni.setStorageSync('token', result.access_token);
      await App.$auth.getUserInfo();
      store.commit('m_user/setLoginStatus', true, {
        root: true,
      });
      setLogin();
      // 获取头像和昵称 必须全部设置
      // const userInfo = uni.getStorageSync("userInfo")
      // if (userInfo.avatar && userInfo.nickname) {
      //     setLogin()
      // } else {
      //     store.commit("m_user/setAvatorName", true);
      // }
      store.commit('m_user/changeLoading', false);
    },
    fail: (err) => {
      store.commit('m_user/changeLoading', false);
      console.log(err);
    },
  });
};
const setLogin = () => {
  const currentPage = getCurrentPages();
  let url = currentPage[currentPage.length - 2]?.$page?.fullPath;
  if (url) {
    // tabbar页面区分
    const tabRoute = [
      '/pageA/home',
      '/pageA/community/index',
      '/pageA/mine/index',
      '/pageA/dynamics/index',
    ];
    const tabRoutes = [
      '/pageA/home?',
      '/pageA/community/index?',
      '/pageA/mine/index?',
      '/pageA/dynamics/index?',
    ];

    let isTab = false;
    tabRoutes.forEach((item) => {
      if (url.indexOf(item) > -1) {
        isTab = true;
      }
    });
    if (url && (isTab || tabRoute.includes(url))) {
      uni.switchTab({
        url,
      });
    } else {
      uni.navigateBack({
        delta: 1, // 返回的页面数，如果 delta 大于现有页面数，则返回到首页。
      });
    }
  } else {
    url = '/pageA/home';
    uni.switchTab({
      url,
    });
  }
};
const viewTerms = () => {
  popContent.value = 'viewTerms';
  show.value = true;
};
const viewPrivacy = () => {
  popContent.value = 'viewPrivacy';
  show.value = true;
};
const onBack = () => {
  uni.navigateBack({
    del: 1,
  });
};
const getAgreeMsg = () => {
  uni.showToast({
    title: '请阅读协议并确认勾选',
    icon: 'none',
    duration: 3000,
  });
};
const confirm = () => {
  show.value = false;
};
const changeCheckbox = (e) => {
  agree.value = e.detail.value[0] == '1' ? true : false;
};
</script>

<style lang="less" scoped>
page {
  height: 100%;
  width: 750rpx;
}

.container-login {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  height: 100vh;
  background-color: white;
  // background: linear-gradient(180deg, #FDE6E5 5%, #FFFFFF 67%);
}

.content {
  margin-top: 215rpx;
}

.logo-container {
  margin-top: 100rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 24rpx;
}

.circle {
  // width: 200rpx;
  // height: 200rpx;
  border-radius: 50%;
  // background: linear-gradient(to bottom, #ff4d4d, #ff8080);
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.logo-image {
  width: 160rpx;
  height: 160rpx;
  object-fit: cover;
}

.app-title {
  font-size: 38rpx;
  font-weight: bold;
  color: #e9342b;
  text-align: center;
}
.pop-img {
  width: 184rpx;
  height: 184rpx;
  margin-bottom: 24rpx;
}
.login-button {
  margin-top: 32rpx;
  background-color: white;
  border-radius: 106rpx;
  width: 649rpx;
  font-weight: normal;
  border: 2rpx solid #f53b30;
}

.login-text {
  color: #f1392e;
  font-weight: 500;
  font-size: 32rpx;
  text-align: center;
}

.back-button {
  margin-top: 20rpx;
  background: #ffffff;
  border-radius: 106rpx 106rpx 106rpx 106rpx;
  border: 2rpx solid #e7e7e7;
  width: 649rpx;
  font-weight: normal;
}

.back-text {
  color: #3f3f3f;
  font-size: 28rpx;
  text-align: center;
}

.agreement {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  // margin-bottom: 160rpx;
  margin-top: 166rpx;
  font-size: 22rpx;
  color: #666666;
}

.agreement-text {
  color: #666666;
  font-size: 24rpx;
}

// .checkbox {
// 	width: 36rpx;
// 	height: 36rpx;
// 	margin-right: 12rpx;
// }

.agreement-text {
  color: #666666;
  font-size: 24rpx;
}

.link {
  color: #3f3f3f;
  font-size: 24rpx;
  text-decoration: underline;
  margin-left: 5rpx;
}

.next {
  display: flex;
  position: fixed;
  top: 120rpx;
  width: 100%;
}
.headImg {
  width: 41%;
}
.headTitle {
  font-size: 34rpx;
  color: #ffffff;
  font-weight: bold;
}

.pop-html {
  padding: 24rpx 20rpx 24rpx 20rpx;
}
.pop-title {
  font-size: 32rpx;
  margin-bottom: 20rpx;
  text-align: center;
}
.pop-content {
  font-size: 24rpx;
  text-align: left;
}

.pop-botton {
  margin: 0 40rpx !important;
}
</style>
