.main{
	min-height: 100vh;
     background: linear-gradient(to bottom, #FFE9E7, #F3F3F4);
	 .content{
		 padding: 184rpx 24rpx 28rpx 24rpx;
		 .list{
			 background: #ffffff;
			 border-radius: 20rpx;
			 padding: 20rpx 20rpx;
			 .coment1{
			 	display: flex;
			 	color: #333333;
			 	font-size: 24rpx;
			 	width: 100%;
			 	border-bottom: 1rpx solid #F2F7FF;
			 	padding: 26rpx 0;
				.coment1_1{
					width:40%;
				}
				.coment1_2{
					width: 60%;
				}
			 }
		 }
		 }
		 }