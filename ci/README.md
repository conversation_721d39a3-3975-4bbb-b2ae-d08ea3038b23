# 小程序 CI 管理系统

## 🎯 概述

这是一个完整的小程序 CI/CD 管理系统，支持多项目配置、构建、上传和预览，提供命令行工具和图形化管理界面。

### 🆕 新功能：租户ID和AppID自动注入

- **租户ID配置**：支持为不同项目和环境配置不同的租户ID
- **构建时注入**：在构建过程中自动注入配置到源代码
- **多租户支持**：同一套代码支持多个租户的小程序

## 📁 目录结构

```
ci/
├── config/                 # 配置文件
│   ├── ci.config.js        # 主配置文件
│   ├── manager.js          # 配置管理器
│   └── backups/            # 配置备份目录
├── scripts/                # 脚本工具
│   ├── upload.js           # 上传脚本
│   ├── preview.js          # 预览脚本
│   ├── manage.js           # 管理脚本
│   ├── build-config-injector.js  # 构建配置注入器
│   └── start-gui.js        # 图形化界面启动脚本
├── server/                 # Web 服务器
│   ├── index.js            # 服务器主文件
│   └── public/             # 静态文件
│       └── index.html      # 图形化界面
├── private/                # 私钥文件目录
│   └── README.md           # 私钥使用说明
└── docs/                   # 文档
    ├── miniprogram-ci.md   # 使用文档
    └── gui-usage.md        # 图形化界面文档
```

## 🚀 快速开始

### 方式一：图形化界面（推荐）

```bash
# 启动图形化管理界面
pnpm run ci:gui --open

# 或者直接运行
node ci/scripts/start-gui.js --open
```

访问 http://localhost:3001 使用图形化界面。

### 方式二：命令行工具

```bash
# 查看项目列表
node ci/scripts/manage.js list

# 检查项目配置
node ci/scripts/manage.js check

# 验证私钥路径格式
node ci/scripts/manage.js validate

# 生成项目配置模板
node ci/scripts/manage.js template

# 重新生成ci.config.js文件
pnpm run ci:regenerate

# 运行完整测试套件
pnpm run ci:test-full

# 上传项目
node ci/scripts/upload.js main develop

# 生成预览
node ci/scripts/preview.js main develop
```

## ✨ 功能特性

### 🖥️ 图形化管理界面

- **项目管理**：增删改查项目配置
- **实时操作**：构建、上传、预览一键操作
- **状态监控**：实时显示项目状态和操作进度
- **私钥管理**：拖拽上传私钥文件
- **配置备份**：自动备份配置文件，支持恢复
- **日志查看**：实时显示操作日志

### 📱 项目配置管理

- **多项目支持**：同时管理多个小程序项目
- **环境配置**：支持开发、体验、正式环境
- **配置验证**：自动验证配置格式和必要字段
- **配置导出**：支持导出配置为 JSON 文件
- **配置克隆**：快速复制项目配置

### 🔧 构建和部署

- **自动构建**：根据环境自动执行构建命令
- **版本管理**：自动生成版本号或手动指定
- **上传管理**：支持上传到不同环境
- **预览生成**：生成预览二维码
- **进度监控**：实时显示构建和上传进度

### 🔒 安全管理

- **私钥保护**：私钥文件本地存储，不上传到版本控制
- **权限控制**：自动设置私钥文件权限
- **配置备份**：自动备份配置文件，防止误操作
- **操作日志**：记录所有操作日志

## 📋 使用指南

### 1. 项目配置

#### 新建项目

1. 点击"新建项目"按钮
2. 填写项目信息：
   - **项目ID**：唯一标识符（如：main, test）
   - **项目名称**：显示名称
   - **AppID**：微信小程序 AppID
   - **默认租户ID**：项目默认租户ID（如：194338）
   - **构建路径**：构建产物路径
   - **私钥路径**：私钥文件路径
3. 配置环境信息：
   - **环境名称**：develop/trial/release
   - **构建命令**：对应环境的构建命令
   - **租户ID**：环境特定的租户ID（可选，不填则使用默认租户ID）
4. 保存项目

#### 编辑项目

1. 点击项目卡片右上角的"更多"按钮
2. 选择"编辑项目"
3. 修改配置信息
4. 保存更改

#### 删除项目

1. 点击项目卡片右上角的"更多"按钮
2. 选择"删除项目"
3. 确认删除操作

### 2. 私钥管理

#### 上传私钥

- **拖拽上传**：将 .key 文件拖拽到上传区域
- **点击上传**：点击上传区域选择文件

#### 私钥获取

1. 登录微信公众平台
2. 进入小程序管理后台
3. 开发 -> 开发管理 -> 开发设置
4. 小程序代码上传 -> 生成私钥
5. 下载 .key 文件

#### 私钥文件命名规范

为了更好地管理多个项目的私钥文件，建议使用以下命名格式：

```
private.{appid}.key
```

**示例：**
- `private.wx1df89e340decbc7a.key` - 艾尔母婴商家管理平台
- `private.wxc05ce1faaa835bb2.key` - 馨喜月月子会所平台

**优势：**
- 通过文件名即可识别对应的小程序
- 避免私钥文件混淆
- 支持自动路径生成和验证

### 3. 项目操作

#### 构建项目

1. 选择环境（开发/体验/正式）
2. 点击"构建项目"按钮
3. 查看实时构建日志

#### 上传小程序

1. 确保已上传私钥文件
2. 选择环境
3. 点击"上传小程序"按钮
4. 配置版本信息
5. 确认上传

#### 生成预览

1. 确保已上传私钥文件
2. 选择环境
3. 点击"生成预览"按钮
4. 配置预览页面（可选）
5. 扫描生成的二维码

### 4. 配置管理

#### 备份管理

- **自动备份**：每次保存配置时自动创建备份
- **手动恢复**：从备份列表选择备份进行恢复
- **备份清理**：自动保留最近 10 个备份

#### 配置导出

- 点击"导出配置"按钮
- 下载 JSON 格式的配置文件
- 可用于配置迁移或备份

## 🛠️ 开发指南

### 配置文件格式

```javascript
{
  "projects": {
    "main": {
      "name": "项目名称",
      "appid": "wx1234567890abcdef",
      "projectPath": "构建路径",
      "privateKeyPath": "私钥路径",
      "ignores": ["node_modules/**/*"],
      "environments": {
        "develop": {
          "name": "开发版",
          "desc": "开发环境版本",
          "buildCommand": "pnpm run build:mp-weixin --mode test"
        }
      }
    }
  }
}
```

### API 接口

- `GET /api/projects` - 获取项目列表
- `POST /api/projects` - 创建项目
- `PUT /api/projects/:id` - 更新项目
- `DELETE /api/projects/:id` - 删除项目
- `GET /api/config/backups` - 获取备份列表
- `POST /api/config/restore` - 恢复备份

### WebSocket 事件

- `buildStarted` - 构建开始
- `buildCompleted` - 构建完成
- `uploadStarted` - 上传开始
- `uploadCompleted` - 上传完成
- `previewCompleted` - 预览生成完成

## 🔧 故障排除

### 常见问题

1. **服务器启动失败**
   - 检查端口 3001 是否被占用
   - 确保依赖已正确安装

2. **私钥上传失败**
   - 确保文件格式为 .key
   - 检查文件权限

3. **构建失败**
   - 检查构建命令是否正确
   - 确保项目依赖已安装

4. **上传失败**
   - 检查 AppID 是否正确
   - 确保私钥文件对应正确的小程序

### 日志查看

- **服务器日志**：在启动终端中查看
- **操作日志**：在图形化界面中实时显示
- **详细日志**：通过浏览器开发者工具查看

## 🏢 租户ID配置说明

### 配置层级

租户ID配置支持两个层级：

1. **项目级别**：`defaultTenantId` - 项目的默认租户ID
2. **环境级别**：`environments[env].tenantId` - 特定环境的租户ID

### 配置优先级

构建时租户ID的选择优先级：
1. 环境特定的租户ID（`environments[env].tenantId`）
2. 项目默认租户ID（`defaultTenantId`）
3. 系统默认租户ID（`194338`）

### 配置示例

```json
{
  "projects": {
    "main": {
      "name": "艾尔母婴商家管理平台",
      "appid": "wx1df89e340decbc7a",
      "defaultTenantId": "194338",
      "environments": {
        "develop": {
          "name": "开发版",
          "tenantId": "194338"
        },
        "trial": {
          "name": "体验版",
          "tenantId": "194338"
        }
      }
    },
    "xinxiyue": {
      "name": "馨喜月月子会所平台",
      "appid": "wxc05ce1faaa835bb2",
      "defaultTenantId": "123456",
      "environments": {
        "develop": {
          "name": "开发版",
          "tenantId": "123456"
        }
      }
    }
  }
}
```

### 构建时注入

构建过程中，系统会自动：

1. **备份原始文件**：创建 `.backup` 备份文件
2. **注入配置**：将租户ID和AppID注入到以下文件：
   - `src/App.vue` - 更新默认租户ID
   - `src/utils/config.js` - 更新CLIENT_CONFIG中的TENANT_ID
3. **执行构建**：运行对应环境的构建命令
4. **清理备份**：构建成功后删除备份文件
5. **恢复备份**：构建失败时自动恢复原始文件

### 命令行使用

```bash
# 使用默认配置构建
node ci/scripts/upload.js main develop

# 系统会自动注入对应的租户ID和AppID

# 测试租户ID注入功能
pnpm run ci:test-tenant

# 演示租户ID构建过程
node ci/scripts/demo-tenant-build.js --demo

# 验证私钥文件路径格式
pnpm run ci:manage validate
```

## 📈 更新日志

- **v2.2.0**：完善CI工作流程和配置管理
  - 修复所有脚本使用统一的配置加载器
  - 添加完整的测试套件验证所有功能
  - 支持配置文件自动重新生成
  - 优化私钥文件路径验证和管理
  - 确保所有CI流程完全通畅
- **v2.1.0**：新增租户ID和AppID自动注入功能
  - 支持项目级别和环境级别的租户ID配置
  - 构建时自动注入配置到源代码
  - 自动备份和恢复机制
  - 多租户支持
- **v2.0.0**：重构目录结构，添加完整的 CRUD 功能
- **v1.1.0**：添加图形化管理界面
- **v1.0.0**：初始版本，支持命令行工具

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

MIT License
