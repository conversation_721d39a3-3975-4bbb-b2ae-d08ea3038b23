/* pages/invitationMake/share/index.wxss */
page {
    background: #f8f9f9;
}

.main {
    padding-top: 20rpx;
}

.content {
    margin: 0rpx 24rpx;
    background-color: #fff;
    padding: 24rpx;
    border-radius: 20rpx;
    /* margin-top: 100rpx; */
}

.header {
    display: flex;
    flex-wrap: wrap;
}

.title_top {
    font-weight: 500;
    font-size: 24rpx;
    color: #777777;
}

.input_content {
    border-radius: 6rpx;
    display: flex;
    margin: 20rpx 0;
    width: 100%;
    background: #f6f6f6;
    padding: 20rpx;
}

.input_icon {
    font-weight: 500;
    font-size: 28rpx;
    color: #aaaaaa;
}

input {
    width: 85%;
    background: #f6f6f6;
    font-weight: 500;
    font-size: 28rpx;
    color: #333333;
    border-radius: 6rpx;
}

.center {
    display: flex;
    position: relative;
}

.image_content {
    position: relative;
    width: 200rpx;
    height: 200rpx;
}

.image_content image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 10rpx;
}

.img_btn {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    display: block;
    text-align: center;
    background: rgba(0, 0, 0, 0.4);
    font-weight: 500;
    font-size: 28rpx;
    color: #ffffff;
    padding: 12rpx 0;
    border-bottom-left-radius: 10rpx;
    border-bottom-right-radius: 10rpx;
}

textarea {
    background: #f6f6f6;
    height: 200rpx;
    margin-left: 20rpx;
    padding: 20rpx 24rpx;
    font-weight: 500;
    font-size: 28rpx;
    color: #333333;
    box-sizing: border-box;
    border-radius: 6rpx;
}

.text_icon {
    position: absolute;
    bottom: 30rpx;
    right: 40rpx;
    font-weight: 500;
    font-size: 28rpx;
    color: #aaaaaa;
}

.footer_top {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 24rpx 0rpx 40rpx 0rpx;
}

.footer_top p {
    font-weight: 500;
    font-size: 32rpx;
    color: #333333;
    margin: 0 20rpx;
}

.footer_top span {
    height: 6rpx;
    width: 36rpx;
    display: block;
    border: 3rpx solid #333333;
    box-sizing: border-box;
    background-color: #333333;
}

.footer_list,
.footer_btn {
    display: flex;
}

/* .footer_btn view */
.footer_list image {
    width: 100rpx;
    height: 100rpx;
}

.footer_list p {
    font-weight: 500;
    font-size: 28rpx;
    color: #333333;
    width: 100%;
    margin-top: 12rpx;
    text-align: center;
}

.footer_btn {
    flex-wrap: wrap;
    width: 112rpx;
    justify-content: center;
}

.footer_list .footer_btn:nth-child(1) {
    margin-right: 80rpx;
}