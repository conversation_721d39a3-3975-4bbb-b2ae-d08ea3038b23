<template>
    <view class="submain" :style="{'minHeight': minHeight + 'px'}">
        <view class="care" v-for="(item, index) in staffPageList" :key="index" @tap="nurseryTeamDetails(item)">
            <view class="flex">
                <view class="care-left">
                    <image :src="item.staffPhotos[0]" alt="" />
                </view>
                <view class="right">
                    <view class="right-title flex">
                        <view class="right-title-name">{{ item.staffName }}</view>
                        <view class="right-title-log">{{ item.staffPost }}</view>
                    </view>
                    <view class="right-dot">{{ clubName }}{{ item.staffPost }}</view>
                    <view class="right-experience flex">
                        <view class="right-experience-label">从业年限：</view>
                        <view class="right-experience-value">{{ item.yearsEmployment }}</view>
                        <view class="right-experience-label">服务人员数量：</view>
                        <view class="right-experience-value">{{ item.serviceNum }}</view>
                    </view>
                    <view class="right-desc flex">
                        <view class="right-desc-title">简介：</view>
                        <view class="right-desc-des">{{ item.staffDesc }}</view>
                    </view>
                    <scroll-view scroll-x="true" class="right-tag flex">
                        <view class="right-tag-enum" v-for="(res, i) in item.tag" :key="i">{{ res }}</view>
                    </scroll-view>
                </view>
            </view>
            <view class="care-button">详细咨询</view>
        </view>
    </view>
</template>
<script setup>
    // 护理标签描述
    import {
        onShow,
        onHide,
        onLoad,
        onPageScroll
    } from "@dcloudio/uni-app";
    import {
        ref,
        reactive,
        onMounted,
        computed,
        getCurrentInstance
    } from "vue";
    const instance = getCurrentInstance();
    const App = instance.appContext.config.globalProperties
    const props = defineProps({
        contentHeight: {
            type: Number,
            default: 0
        }
    })
    const minHeight = computed(() => {
        return props.contentHeight > 320 ? props.contentHeight : 320
    })
    onMounted(() => {
        clubName.value = uni.getStorageSync('clubName')
        pageInit()
    })
    const staffPageList = reactive([])
    const clubName = ref('')
    const staffPage = async () => {
        //查询护理团队列表
        let data = {
            pageSize: 100,
            pageNum: 1,
            inPost: '孕产产后康复师,孕产健康管理师'
        };
        const res = await App.$axios.get(App.$api.staffPage, data)
        if (res.data.code == 200) {
            Object.assign(staffPageList, res.data.rows)
        }
    }
    const nurseryTeamDetails = (item) => {
        let staffId = item.staffId;
        App.$jumpPage('/pageA/pageB/home/<USER>/detail', {
            staffId,
        })
    }
    const pageInit = async () => {
        try {
            await staffPage()
        } catch {

        }
    }
</script>
<style lang="less" scoped>
    .submain {
        padding: 24rpx;
        background: white;
        overflow: hidden;

        .flex {
            display: flex;
        }
    }

    .care {
        height: 226rpx;
        border-bottom: 1rpx solid #EBEBEB;
        position: relative;
        margin-bottom: 24rpx;

        &-left {
            margin-right: 24rpx;
        }

        image {
            width: 120rpx;
            height: 120rpx;
            border-radius: 10rpx;
        }

        .care-button {
            background: #FFE2E5;
            border-radius: 74rpx 74rpx 74rpx 74rpx;
            position: absolute;
            right: 24rpx;
            top: 0;
            padding: 8rpx 20rpx;
            font-weight: 500;
            font-size: 24rpx;
            color: #FF4F61;
        }
    }


    .right {
        &-title {
            align-items: center;

            &-name {
                font-weight: 500;
                font-size: 34rpx;
                color: #333333;
                text-align: center;
                font-style: normal;
                text-transform: none;
                margin-right: 12rpx;
            }

            &-log {
                padding: 4rpx 20rpx;
                background: linear-gradient(90deg, #7AB6FC 0%, #5E5BFE 100%);
                border-radius: 118rpx;
                font-weight: 500;
                font-size: 24rpx;
                color: #FFFFFF;
            }
        }

        &-dot {
            font-weight: 400;
            font-size: 24rpx;
            color: #333333;
            line-height: 28rpx;
            text-align: left;
            font-style: normal;
            text-transform: none;
            margin-top: 8rpx;
            margin-bottom: 12rpx;
        }

        &-experience {
            font-weight: 400;
            font-size: 24rpx;
            line-height: 28rpx;
            text-align: left;
            font-style: normal;
            text-transform: none;

            &-label {
                color: #333333;
                // margin-right: 24rpx;
            }

            &-value {
                color: #777777;
                margin-right: 12rpx;
            }
        }

        &-desc {
            margin: 4rpx 0;
            font-weight: 500;
            font-size: 24rpx;
            line-height: 40rpx;
            font-style: normal;
            text-transform: none;
            color: #777777;

            &-title {
                color: #333333;
                margin-right: 12rpx;
            }

            &-des {
                overflow: hidden;
                width: 400rpx;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }

        &-tag {
            margin: 4rpx 0;
            width: 540rpx;
            white-space: nowrap;
            overflow-x: scroll;

            &-enum {
                display: inline-block;
                padding: 4rpx;
                background: #F2F2F2;
                border-radius: 6rpx 6rpx 6rpx 6rpx;
                font-weight: 400;
                font-size: 24rpx;
                color: #999;
                line-height: 28rpx;
                text-align: left;
                font-style: normal;
                text-transform: none;
                margin-right: 32rpx;
            }
        }
    }
</style>