#!/usr/bin/env node

/**
 * 租户ID注入功能完整测试脚本
 * 测试不同项目和环境的租户ID注入
 */

const BuildConfigInjector = require('./build-config-injector.js');
const { projects } = require('../config/config-loader.js');
const fs = require('fs');
const path = require('path');

// 颜色输出
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

/**
 * 测试特定项目和环境的租户ID注入
 */
async function testProjectEnvironment(projectName, environment) {
  log(`\n🧪 测试项目: ${projectName}, 环境: ${environment}`, 'blue');
  log('-'.repeat(50));

  const project = projects[projectName];
  if (!project) {
    logError(`项目 ${projectName} 不存在`);
    return false;
  }

  const envConfig = project.environments[environment];
  if (!envConfig) {
    logError(`环境 ${environment} 不存在`);
    return false;
  }

  // 确定租户ID
  const tenantId = envConfig.tenantId || project.defaultTenantId || '194338';
  logInfo(`使用租户ID: ${tenantId}`);
  logInfo(`AppID: ${project.appid}`);

  const injector = new BuildConfigInjector();
  
  try {
    // 执行注入
    injector.inject({
      tenantId,
      appid: project.appid,
      projectName,
      environment
    });

    // 验证注入结果
    const success = await verifyInjection(tenantId);
    
    // 恢复备份
    injector.restoreBackups();
    
    return success;
  } catch (error) {
    logError(`注入失败: ${error.message}`);
    injector.restoreBackups();
    return false;
  }
}

/**
 * 验证注入结果
 */
async function verifyInjection(expectedTenantId) {
  const rootPath = path.resolve(__dirname, '../..');
  const configPath = path.join(rootPath, 'src/utils/config.js');
  const appVuePath = path.join(rootPath, 'src/App.vue');

  let success = true;

  // 验证config.js
  if (fs.existsSync(configPath)) {
    const configContent = fs.readFileSync(configPath, 'utf8');
    if (configContent.includes(`TENANT_ID: '${expectedTenantId}'`)) {
      logSuccess(`config.js 租户ID验证通过: ${expectedTenantId}`);
    } else {
      logError(`config.js 租户ID验证失败`);
      success = false;
    }
  }

  // 验证App.vue
  if (fs.existsSync(appVuePath)) {
    const appVueContent = fs.readFileSync(appVuePath, 'utf8');
    if (appVueContent.includes(`"${expectedTenantId}"`)) {
      logSuccess(`App.vue 租户ID验证通过: ${expectedTenantId}`);
    } else {
      logError(`App.vue 租户ID验证失败`);
      success = false;
    }
  }

  return success;
}

/**
 * 测试所有项目和环境
 */
async function testAllProjects() {
  log('🚀 开始测试所有项目的租户ID注入功能', 'blue');
  log('='.repeat(60));

  let totalTests = 0;
  let passedTests = 0;

  for (const [projectName, project] of Object.entries(projects)) {
    for (const [envName, envConfig] of Object.entries(project.environments)) {
      totalTests++;
      const success = await testProjectEnvironment(projectName, envName);
      if (success) {
        passedTests++;
      }
    }
  }

  log('\n📊 测试结果汇总', 'blue');
  log('='.repeat(60));
  logInfo(`总测试数: ${totalTests}`);
  logSuccess(`通过测试: ${passedTests}`);
  
  if (passedTests < totalTests) {
    logError(`失败测试: ${totalTests - passedTests}`);
  }

  if (passedTests === totalTests) {
    logSuccess('🎉 所有测试通过！租户ID注入功能工作正常');
  } else {
    logWarning('⚠️ 部分测试失败，请检查配置');
  }

  return passedTests === totalTests;
}

/**
 * 显示帮助信息
 */
function showHelp() {
  log('租户ID注入功能测试脚本', 'blue');
  log('');
  log('使用方法:', 'yellow');
  log('  node ci/scripts/test-tenant-injection.js [项目名] [环境名]', 'white');
  log('');
  log('参数说明:', 'yellow');
  log('  项目名   - 可选，指定要测试的项目', 'white');
  log('  环境名   - 可选，指定要测试的环境', 'white');
  log('');
  log('示例:', 'yellow');
  log('  node ci/scripts/test-tenant-injection.js', 'white');
  log('  node ci/scripts/test-tenant-injection.js main develop', 'white');
  log('');
}

// 主函数
async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--help') || args.includes('-h')) {
    showHelp();
    return;
  }

  const projectName = args[0];
  const environment = args[1];

  if (projectName && environment) {
    // 测试特定项目和环境
    const success = await testProjectEnvironment(projectName, environment);
    process.exit(success ? 0 : 1);
  } else {
    // 测试所有项目和环境
    const success = await testAllProjects();
    process.exit(success ? 0 : 1);
  }
}

// 运行主函数
if (require.main === module) {
  main().catch(error => {
    logError(`脚本执行失败: ${error.message}`);
    process.exit(1);
  });
}

module.exports = {
  testProjectEnvironment,
  testAllProjects
};
