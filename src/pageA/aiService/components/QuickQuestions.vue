<template>
  <view class="quick-questions-container" v-if="showQuestions">
    <view class="quick-questions-title">您可以尝试这样问我:</view>
    <view class="quick-questions-list">
      <view
        v-for="(question, index) in questions"
        :key="index"
        class="quick-question-item"
        @click="selectQuestion(question)"
      >
        <text class="question-text">{{ question.questionText }}</text>
        <view class="question-arrow">
          <image src="/static/images/chat/arrow-right.svg" mode="aspectFit" />
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue';
import { getStartingQuestionsApi } from '@/api/aiService.js';
import { checkHasLogined } from '@/utils/auth.js';
import { useStore } from 'vuex';

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: true,
  },
});

// Events
const emit = defineEmits(['select-question']);

// 数据
const questions = ref([]);
const loading = ref(false);

// 获取 store
const store = useStore();

// 计算属性
const showQuestions = computed(() => {
  return props.visible && questions.value.length > 0;
});

// 获取推荐问题
const fetchQuestions = async () => {
  try {
    loading.value = true;
    console.log('开始获取推荐问题...');

    // 检查登录状态，未登录时不请求
    const isLoggedIn = await checkHasLogined();
    if (!isLoggedIn) {
      console.log('用户未登录，不获取推荐问题');
      setDefaultQuestions();
      return;
    }

    // 构建请求参数
    const requestData = {
      questionCount: 4,
      businessCategory: 'maternal_care', // 母婴护理
      includeAiGenerated: true,
    };

    const res = await getStartingQuestionsApi(requestData);
    console.log('推荐问题API响应:', res);

    if (res.code === 200 && res.data) {
      // 处理API返回的数据格式
      if (Array.isArray(res.data.recommendedQuestions)) {
        questions.value = res.data.recommendedQuestions;
      } else {
        console.warn('推荐问题数据格式异常:', res.data);
      }
    } else {
      console.error('获取推荐问题失败:', res.msg || '未知错误');
    }
  } catch (error) {
    console.error('获取推荐问题异常:', error);
  } finally {
    loading.value = false;
  }
};

// 设置默认问题
const setDefaultQuestions = () => {
  questions.value = [];
};

// 选择问题
const selectQuestion = (question) => {
  const questionText = question.questionText;
  console.log('选择推荐问题:', questionText);
  emit('select-question', questionText);
};

// 监听登录状态变化，登录成功后重新获取推荐问题
watch(
  () => store.state.m_user?.user_isLogin,
  (newStatus, oldStatus) => {
    console.log('QuickQuestions: 登录状态变化', { newStatus, oldStatus });

    // 从未登录变为已登录时重新获取推荐问题
    if (newStatus && !oldStatus && props.visible) {
      console.log('检测到登录成功，重新获取推荐问题');
      fetchQuestions();
    }
  },
  { immediate: false }
);

// 生命周期
onMounted(() => {
  if (props.visible) {
    fetchQuestions();
  }
});

// 暴露方法给父组件
defineExpose({
  fetchQuestions,
  questions,
});
</script>

<style lang="scss" scoped>
.quick-questions-container {
  padding: 28rpx 28rpx 20rpx 28rpx;
  background-color: #fff;
  border-radius: 14rpx;
  margin-bottom: 24rpx;
}

.quick-questions-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #000;
  margin-bottom: 14rpx;
}

.quick-questions-list {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.quick-question-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #f4f7fd;
  border-radius: 14rpx;
  padding: 24rpx 22rpx;
  transition: all 0.2s ease;
  position: relative;
}

.quick-question-item:active {
  background-color: #e8f0fc;
  transform: scale(0.98);
}

.question-text {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  margin-right: 12rpx;
  font-weight: 400;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.question-arrow {
  width: 28rpx;
  height: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;

  image {
    width: 28rpx;
    height: 28rpx;
  }
}
</style>
