<template>
    <view>
        <view class="contentTab">
            <view :class="index == currentIndex ? 'title-sel-selected' : 'title-sel'" :data-index="index"
                @tap="titleClick" v-for="(item, index) in tabList" :key="index">
                {{ item.name }}

                <hr class="line-style" />
            </view>
        </view>
        <view class="main">
            <view class="main-content" v-if="currentIndex == 0">
                <view class="content" v-for="(item, index) in pageViewList" :key="index">
                    <view class="content_2">
                        <!-- <image src="http://47.106.72.143:9000/tools/files/1750419034328338434/download" mode="" style="width: 140rpx;height: 140rpx;" /> -->
                        <view class="content_1">
                            <view class="contentTitle1">{{ item.pageTitle }}</view>
                            <!-- <view class="contentTitle2">套房</view> -->
                            <view class="contentTitle3">收藏时间：{{ item.createTime }}</view>
                        </view>
                    </view>

                    <image src="http://cdn.xiaodingdang1.com/1/120240115/2ee8ae21-7a8f-45d9-a490-fc0879a0d56b.png"
                        mode="" style="width: 8rpx; height: 18rpx" />
                </view>
                <view v-if="pageViewList.length === 0 && emptyStatus">
                    <empty-vue></empty-vue>
                </view>
                <!-- <view class="facility1" bindtap="logFacility">
		<view class="text3">{{showFacility?'收起':'展开'}}</view>
		<image src="{{showFacility?'http://cdn.xiaodingdang1.com/1/120240127/80aa2ca5-069f-477b-91a0-de6f9da90750.png':'http://47.106.72.143:9000/tools/files/1746799119402864642/download'}}" mode="" style="width: 24rpx; height: 12rpx;" class="facilityImg" />
	</view> -->
            </view>
            <view class="main-content" v-if="currentIndex == 1">
                <view class="content" v-for="(item, index) in favoritePageList" :key="index">
                    <view class="content2">
                        <image v-if="item.contentPhotos" :src="item.contentPhotos[0]" mode=""
                            style="width: 190rpx; height: 190rpx" />
                        <view class="content2_1">
                            <view class="contentTitle2_1">
                                <image :src="item.avatar || $defaultAvatar" mode=""
                                    style="width: 36rpx; height: 36rpx; border-radius: 100rpx" />
                                <view class="contentTitle2_4">
                                    {{ item.nickname || $defaultName }}
                                </view>
                                <view class="tagName" v-if="item.type == 'USER'">宝妈</view>
                                <view class="tagName" v-if="item.type == 'STAFF'">会所</view>
                            </view>
                            <view class="contentTitle2_2">
                                <view class="contentTitle2_6">宝妈社区</view>
                                <view class="contentTitle2_6">高级育婴师</view>
                            </view>
                            <view class="contentTitle2_3">{{ item.content }}</view>
                        </view>
                    </view>

                    <image src="http://cdn.xiaodingdang1.com/1/120240115/2ee8ae21-7a8f-45d9-a490-fc0879a0d56b.png"
                        mode="" style="width: 8rpx; height: 18rpx" />
                </view>
                <view v-if="favoritePageList.length === 0 && emptyStatus">
                    <empty-vue></empty-vue>
                </view>
            </view>
            <view class="main-content" v-if="currentIndex == 2">
                <view class="titles" v-if="pageTopicList.length > 0">话题</view>
                <view class="topic" v-for="(item, index) in pageTopicList" :key="index">
                    <view class="topic1">
                        <image v-if="item.topicPhotos" :src="item.topicPhotos[0]" mode="" class="topicImg" />
                        <view class="topic2">
                            <view class="topic1Text1">
                                <text class="topic1Text1_1">#</text>
                                <text class="topic1Text1_2">{{ item.topicName }}</text>
                            </view>
                            <view class="topic1Text2">
                                <text class="topic1Text2_1">简介：</text>
                                <text class="topic1Text2_2">{{ item.topicDescription }}</text>
                            </view>
                            <!-- <view class="topic1Text3">
					<view class="topic1Text3_2">
							<image wx:for="{{item.userList}}" wx:for-item="res" wx:for-index="i" wx:key="i" src="{{res.avatar}}" class="topic1Text3topic1Text3" style='transform:translateX({{-index*20}}rpx)'>
							</image>
						<text class="topic1Text3_1">{{item.postNum}}+人参与</text>
					</view>
			
				</view> -->
                            <view class="topic1Text3">
                                <!-- body -->
                                <view class="avatar-list-stacked">
                                    <text class="avatar" :style="
                                            'background: url(' + res.avatar || $defaultAvatar + ') center center;background-repeat:no-repeat;background-size: cover; background-position:0px 0px;  '
                                        " v-for="(res, i) in item.userList" :key="i"></text>
                                </view>
                                <view class="topic1Text3_2">
                                    <image
                                        src="http://cdn.xiaodingdang1.com/1/120240125/328f8e35-a207-4845-b987-71ca8fd2c79c.png"
                                        mode="" style="width: 30rpx; height: 30rpx; margin-right: 4rpx" />
                                    <text class="topic1Text3_1">{{ item.postNum }}人参与</text>
                                </view>
                            </view>
                        </view>
                        <!-- <view class="title">
				热门
			</view> -->
                    </view>
                </view>
                <view class="titles" v-if="pagePostList.length > 0">话题评论</view>
                <view class="content" v-for="(item, index) in pagePostList" :key="index">
                    <view class="content2">
                        <image v-if="item.imageUrl" :src="item.imageUrl[0]" mode=""
                            style="width: 190rpx; height: 190rpx" />
                        <view class="content2_1">
                            <view class="contentTitle2_1">
                                <image :src="item.avatar || $defaultAvatar" mode=""
                                    style="width: 36rpx; height: 36rpx; border-radius: 100rpx" />
                                <view class="contentTitle2_4">
                                    {{ item.topicName || $defaultName }}
                                </view>
                            </view>
                            <view class="contentTitle2_3">{{ item.content }}</view>
                        </view>
                    </view>

                    <image src="http://cdn.xiaodingdang1.com/1/120240115/2ee8ae21-7a8f-45d9-a490-fc0879a0d56b.png"
                        mode="" style="width: 8rpx; height: 18rpx" />
                </view>
                <view v-if="pageTopicList.length === 0 && emptyStatus">
                    <empty-vue></empty-vue>
                </view>
            </view>
        </view>
        <new-request-loading></new-request-loading>
    </view>
</template>

<script>
    import emptyVue from '@/components/empty.vue';
    export default {
        components: {
            emptyVue
        },
        data() {
            return {
                pageViewList: [],

                //分页查询我收藏页面&界面列表
                favoritePageList: [],

                //查询当前用户收藏的朋友圈动态
                pageTopicList: [],

                //分页查询我收藏的话题列表
                pagePostList: [],

                //分页查询我收藏宝妈社区动态列表
                showFacility: false,

                Interface: [1, 2, 3],
                currentIndex: 0,

                tabList: [{
                        name: '界面'
                    },
                    {
                        name: '动态'
                    },
                    {
                        name: '话题'
                    }
                ],

                imgList: [{
                        img: 'http://47.106.72.143:9000/tools/files/1750417822270951425/download'
                    },
                    {
                        img: 'http://47.106.72.143:9000/tools/files/1750417822270951425/download'
                    },
                    {
                        img: 'http://47.106.72.143:9000/tools/files/1750417822270951425/download'
                    },
                    {
                        img: 'http://47.106.72.143:9000/tools/files/1750417822270951425/download'
                    }
                ],

                res: {
                    avatar: ''
                },
                i: '',
                emptyStatus: false
            };
        }
        /**
         * 生命周期函数--监听页面加载
         */
        ,
        onLoad(options) {
            if (this.currentIndex == 0) {
                this.pageView(); //分页查询我收藏页面&界面列表
            }
        },
        /**
         * 生命周期函数--监听页面初次渲染完成
         */
        onReady() {},
        /**
         * 生命周期函数--监听页面显示
         */
        onShow() {},
        /**
         * 生命周期函数--监听页面隐藏
         */
        onHide() {},
        /**
         * 生命周期函数--监听页面卸载
         */
        onUnload() {},
        /**
         * 页面相关事件处理函数--监听用户下拉动作
         */
        onPullDownRefresh() {},
        /**
         * 页面上拉触底事件的处理函数
         */
        onReachBottom() {},
        /**
         * 用户点击右上角分享
         */
        onShareAppMessage() {},

        onShareTimeline() {},
        methods: {
            logFacility() {
                this.setData({
                    showFacility: !this.showFacility
                });
            },

            //用户点击tab时调用
            titleClick: function(e) {
                this.emptyStatus = false
                console.log(e.currentTarget.dataset.index);
                this.setData({
                    //拿到当前索引并动态改变
                    currentIndex: e.currentTarget.dataset.index
                });
                if (e.currentTarget.dataset.index == 0) {
                    this.pageView(); //分页查询我收藏页面&界面列表
                }
                if (e.currentTarget.dataset.index == 1) {
                    this.favoritePage(); //查询当前用户收藏的朋友圈动态
                }
                if (e.currentTarget.dataset.index == 2) {
                    this.pageTopic(); //分页查询我收藏的话题列表
                    this.pagePost(); //分页查询我收藏宝妈社区动态列表
                }
            },

            pageView() {
                //分页查询我收藏页面&界面列表
                this.changeLoading(true)
                let data = {
                    pageSize: 1000,
                    pageNum: 1
                };
                this.$axios.get(this.$api.pageView, data).then((res) => {
                    if (res.data.code == 200) {
                        this.setData({
                            pageViewList: res.data.rows
                        });
                    }
                }).finally(() => {
                    this.emptyStatus = true
                    this.changeLoading(false)
                });
            },

            favoritePage() {
                //查询当前用户收藏的朋友圈动态
                this.changeLoading(true)
                let data = {
                    pageSize: 1000,
                    pageNum: 1
                };
                this.changeLoading(true)
                this.$axios.get(this.$api.favoritePage, data).then((res) => {
                    if (res.data.code == 200) {
                        this.setData({
                            favoritePageList: res.data.rows
                        });
                    }
                }).finally(() => {
                    this.emptyStatus = true
                    this.changeLoading(false)
                });;
            },

            pageTopic() {
                //分页查询我收藏的话题列表
                this.changeLoading(true)
                let data = {
                    pageSize: 1000,
                    pageNum: 1
                };
                this.$axios.get(this.$api.pageTopic, data).then((res) => {
                    if (res.data.code == 200) {
                        this.setData({
                            pageTopicList: res.data.rows
                        });
                    }
                }).finally(() => {
                    this.emptyStatus = true
                    this.changeLoading(false)
                });;
            },

            pagePost() {
                //分页查询我收藏宝妈社区动态列表
                let data = {
                    pageSize: 1000,
                    pageNum: 1
                };
                this.changeLoading(true)
                this.$axios.get(this.$api.pagePost, data).then((res) => {
                    if (res.data.code == 200) {
                        this.setData({
                            pagePostList: res.data.rows
                        });
                    }
                }).finally(() => {
                    this.emptyStatus = true
                    this.changeLoading(false)
                });;
            }
        }
    };
</script>
<style>
    @import './favority.css';
</style>