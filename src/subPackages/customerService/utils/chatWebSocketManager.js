/**
 * 聊天WebSocket管理器
 * 基于 cim.example.js 的模式重构，使用 JSON 格式
 */

import { getEnvConfig } from '@/utils/config.js';

// 消息类型常量
const MESSAGE_TYPES = {
  PONG: 0,
  PING: 1,
  MESSAGE: 2,
  SENT_BODY: 3,
  REPLY_BODY: 4,
  CONNECT: 'CONNECT', // 保留字符串类型用于兼容
};

class ChatWebSocketManager {
  constructor() {
    this.websocketTask = null;
    this.websocketConnected = false;
    this.websocketConnectedTimes = 0;
    this.messageHandlers = new Map();
    this.currentConversationId = null;
    this.userInfo = null;
    this.config = getEnvConfig();
    console.log(this.config);
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
  }

  /**
   * 初始化WebSocket连接
   * @param {Object} options 配置选项
   * @param {string} options.userId - 用户ID
   * @param {string} options.userType - 用户类型 (USER/AGENT)
   * @param {string} options.tenantId - 租户ID
   */
  async init(options = {}) {
    try {
      // 获取认证token
      const token = uni.getStorageSync('token');
      if (!token) {
        throw new Error('未找到认证token，请先登录');
      }

      // 获取用户信息
      this.userInfo = uni.getStorageSync('userInfo');
      if (!this.userInfo || !this.userInfo.userId) {
        throw new Error('未找到用户信息，请先登录');
      }

      // 获取租户信息
      const tenantId = uni.getStorageSync('tenantId') || options.tenantId;
      if (!tenantId) {
        throw new Error('未找到租户信息');
      }

      // 保存用户类型
      this.userType = options.userType || 'USER';

      // 构建WebSocket URL
      const wsUrl =
        this.config.WSS_URL ||
        `${this.config.WSS_URL}?userId=${this.userInfo.userId}&token=${token}&userType=${this.userType}&tenantId=${tenantId}`;

      console.log('开始创建WebSocket连接:', wsUrl);
      this.createWebsocketConnection(wsUrl);

      return true;
    } catch (error) {
      console.error('ChatWebSocketManager初始化失败:', error);
      uni.showToast({
        title: error.message || '连接失败',
        icon: 'none',
      });
      return false;
    }
  }

  /**
   * 创建WebSocket连接
   */
  createWebsocketConnection(wsUrl) {
    const websocketTask = uni.connectSocket({
      url: wsUrl,
      success: () => {
        console.log('WebSocket连接创建成功');
      },
      fail: (error) => {
        console.error('WebSocket连接创建失败:', error);
      },
    });

    websocketTask.onOpen((res) => {
      console.log('WebSocket连接已打开');
      this.websocketTask = websocketTask;
      this.websocketConnected = true;
      this.websocketConnectedTimes++;
      this.reconnectAttempts = 0;

      // 连接成功后发送bind请求
      this.sendBindRequest();

      // 触发连接成功事件
      this.emit('connected', res);
    });

    websocketTask.onMessage((res) => {
      try {
        // 处理JSON格式消息
        const message = JSON.parse(res.data);
        console.log('收到WebSocket消息:', message);

        if (message.type === MESSAGE_TYPES.PING) {
          // 收到心跳，回复PONG
          this.sendWebsocketPong();
          return;
        }

        // 检查是否是client_bind的响应
        if (message.key === 'client_bind') {
          // client_bind成功响应
          console.log('收到client_bind响应:', message);
          this.onWebsocketConnected();
          return;
        }

        if (message.type === MESSAGE_TYPES.CONNECT) {
          // 连接成功响应
          console.log('收到连接响应:', message);
          this.onWebsocketConnected();
          return;
        }

        if (message.type === MESSAGE_TYPES.MESSAGE) {
          // 聊天消息
          console.log('收到聊天消息:', message);
          this.handleChatMessage(message);
          return;
        }

        if (message.type === MESSAGE_TYPES.REPLY_BODY) {
          // REPLY_BODY类型消息，可能是聊天消息或其他响应
          console.log('收到REPLY_BODY消息:', message);

          // 检查是否包含聊天内容
          if (message.content) {
            try {
              const parsedContent = typeof message.content === 'string'
                ? JSON.parse(message.content)
                : message.content;

              // 如果包含聊天相关字段，作为聊天消息处理
              if (parsedContent.action || parsedContent.sender || parsedContent.content) {
                this.handleChatMessage(message);
                return;
              }
            } catch (error) {
              console.log('REPLY_BODY内容解析失败，可能不是聊天消息:', error);
            }
          }

          // 其他REPLY_BODY消息
          this.emit('replyMessage', message);
          return;
        }

        // 其他类型消息
        this.emit('message', message);
      } catch (error) {
        console.error('WebSocket消息解析失败:', error);
      }
    });

    websocketTask.onClose((res) => {
      console.log('WebSocket连接已关闭:', res);
      this.websocketConnected = false;

      if (
        res.reason !== 'FINISH' &&
        this.reconnectAttempts < this.maxReconnectAttempts
      ) {
        // 5秒后重连
        this.reconnectAttempts++;
        setTimeout(() => {
          console.log(`开始第${this.reconnectAttempts}次重连WebSocket`);
          this.createWebsocketConnection(wsUrl);
        }, 5000);
      }

      this.emit('disconnected', res);
    });

    websocketTask.onError((error) => {
      console.error('WebSocket连接错误:', error);
      this.websocketConnected = false;
      this.emit('error', error);
    });
  }

  /**
   * 发送PONG响应
   */
  sendWebsocketPong() {
    if (!this.websocketTask) {
      return;
    }

    const pongMessage = {
      type: MESSAGE_TYPES.PONG,
      content: 'PONG',
      timestamp: Date.now(), // 保持使用 Date.now() 用于心跳时间戳
    };

    this.websocketTask.send({
      data: JSON.stringify(pongMessage),
      success: () => {
        console.log('PONG发送成功');
      },
      fail: (error) => {
        console.error('PONG发送失败:', error);
      },
    });
  }

  /**
   * 发送client_bind请求
   */
  sendBindRequest() {
    if (!this.websocketTask || !this.userInfo) {
      return;
    }

    // 使用JSON格式构建client_bind请求，保持原有格式
    const bindMessage = {
      key: 'client_bind',
      timestamp: Date.now(), // 保持使用 Date.now() 用于协议时间戳
      data: {
        uid: String(this.userInfo.userId),
        channel: 'wechat',
        appVersion: '1.0.0',
        osVersion: uni.getSystemInfoSync().version,
        deviceId: this.getDeviceId(),
        deviceName: uni.getSystemInfoSync().platform,
        language: 'zh-CN',
      },
    };

    this.websocketTask.send({
      data: JSON.stringify({
        type: MESSAGE_TYPES.SENT_BODY,
        content: JSON.stringify(bindMessage),
      }),
      success: () => {
        console.log('client_bind请求发送成功');
      },
      fail: (error) => {
        console.error('client_bind请求发送失败:', error);
      },
    });
  }

  /**
   * WebSocket连接成功回调
   */
  onWebsocketConnected() {
    console.log('WebSocket绑定成功');
    this.emit('bindSuccess');
  }

  /**
   * 处理聊天消息
   */
  handleChatMessage(message) {
    console.log('收到聊天消息:', message);

    try {
      // 解析 content 字段中的 JSON 数据
      let parsedContent = message.content;
      if (typeof message.content === 'string') {
        parsedContent = JSON.parse(message.content);
      }

      // 构建标准化的消息格式
      const formattedMessage = {
        id: parsedContent.id || Date.now().toString(), // 保持使用 Date.now() 用于消息ID
        messageId: parsedContent.id,
        action: parsedContent.action,
        content: parsedContent.content,
        sender: parsedContent.sender,
        receiver: parsedContent.receiver,
        format: parsedContent.format || 'TEXT',
        timestamp: parsedContent.timestamp || Date.now(), // 保持使用 Date.now() 用于协议时间戳
        createdTime: parsedContent.timestamp || Date.now(), // 保持使用 Date.now() 用于协议时间戳
        // 根据 action 字段判断消息类型
        senderType: parsedContent.action === 'AGENT' ? 'AGENT' : 'USER',
        messageType: parsedContent.format || 'TEXT'
      };

      console.log('解析后的聊天消息:', formattedMessage);
      this.emit('chatMessage', formattedMessage);
    } catch (error) {
      console.error('解析聊天消息失败:', error);
      // 如果解析失败，使用原始消息格式
      this.emit('chatMessage', message);
    }
  }

  /**
   * 发送聊天消息（用户消息）
   */
  sendMessage(content, conversationId, action = 'send_user_message', messageType = 'TEXT') {
    if (!this.websocketConnected || !this.websocketTask) {
      console.error('WebSocket未连接，无法发送消息');
      return false;
    }

    // 使用JSON格式构建消息，保持原有的SentBody结构
    const messageData = {
      key: 'cs_message',
      timestamp: new Date().getTime(),
      data: {
        action: action,
        content: content,
        format: messageType.toUpperCase(),
        conversationId: conversationId || this.currentConversationId,
        tenantId: uni.getStorageSync('tenantId'),
        timestamp: new Date().getTime(),
      },
    };

    this.websocketTask.send({
      data: JSON.stringify({
        type: MESSAGE_TYPES.SENT_BODY,
        content: JSON.stringify(messageData),
      }),
      success: () => {
        console.log('消息发送成功');
      },
      fail: (error) => {
        console.error('消息发送失败:', error);
      },
    });

    return true;
  }

  /**
   * 发送客服消息
   */
  sendAgentMessage(content, conversationId, messageType = 'TEXT') {
    return this.sendMessage(content, conversationId, 'send_agent_message', messageType);
  }

  /**
   * 发送图片消息
   */
  sendImageMessage(imageUrl, conversationId, isAgent = false) {
    const action = isAgent ? 'send_agent_message' : 'send_user_message';
    return this.sendMessage(imageUrl, conversationId, action, 'IMAGE');
  }

  /**
   * 获取设备ID
   */
  getDeviceId() {
    let deviceId = uni.getStorageSync('x-device-id');
    if (deviceId) {
      return deviceId;
    }

    let d = new Date().getTime();
    let uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(
      /[xy]/g,
      function (c) {
        let r = (d + Math.random() * 16) % 16 | 0;
        d = Math.floor(d / 16);
        return (c === 'x' ? r : (r & 0x3) | 0x8).toString(16);
      },
    );

    let newDeviceId = uuid.replace(/-/g, '');
    uni.setStorageSync('x-device-id', newDeviceId);
    return newDeviceId;
  }

  /**
   * 注册事件监听器
   */
  on(event, handler) {
    if (!this.messageHandlers.has(event)) {
      this.messageHandlers.set(event, []);
    }
    this.messageHandlers.get(event).push(handler);
  }

  /**
   * 移除事件监听器
   */
  off(event, handler) {
    if (this.messageHandlers.has(event)) {
      const handlers = this.messageHandlers.get(event);
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    }
  }

  /**
   * 触发事件
   */
  emit(event, data) {
    if (this.messageHandlers.has(event)) {
      this.messageHandlers.get(event).forEach((handler) => {
        try {
          handler(data);
        } catch (error) {
          console.error(`事件处理器执行失败 [${event}]:`, error);
        }
      });
    }
  }

  /**
   * 设置当前会话ID
   */
  setCurrentConversationId(conversationId) {
    this.currentConversationId = conversationId;
  }

  /**
   * 获取连接状态
   */
  isConnected() {
    return this.websocketConnected;
  }

  /**
   * 关闭连接
   */
  close() {
    if (this.websocketTask) {
      this.websocketTask.close({ code: 1000, reason: 'FINISH' });
      this.websocketTask = null;
    }
    this.websocketConnected = false;
    this.messageHandlers.clear();
  }
}

// 创建全局实例
const chatWebSocketManager = new ChatWebSocketManager();

export default chatWebSocketManager;
