<template>
    <navigation :background="bgColor" :title="titleName" :isSowArrow="true"></navigation>
    <view class="preciousMother" v-if="Object.keys(userPageList).length > 0">
        <view class="top">
            <view class="pd-24" style="padding-top: 24rpx;">
                <userdesc-vue :item="userPageList" pageType="detail"></userdesc-vue>
                <view style="margin-top: 16rpx">
                    <img-content-area-vue :listData="userPageList" :index="9999"></img-content-area-vue>
                </view>
            </view>
            <view class="devide"></view>
            <view class="discusss5 pd-24">
                <functionbutton-vue :item="userPageList"></functionbutton-vue>
            </view>
        </view>
        <view class="devide-content"></view>
        <view class="more">
            <view class="more-title pd-24" v-if="pageList.length > 0">更多动态</view>
            <view class="more-card " v-for="(item, index) in pageList" :key="index">
                <view class="pd-24">
                    <userdesc-vue :item="item" pageType="detail"></userdesc-vue>
                    <view style="margin-top: 16rpx">
                        <img-content-area-vue :listData="item" :index="index"></img-content-area-vue>
                    </view>
                </view>
                <view class="devide"></view>
                <view class="discusss5 pd-24">
                    <functionbutton-vue :item="item"></functionbutton-vue>
                </view>
                <view class="devide-content"></view>
            </view>
        </view>
        <new-request-loading></new-request-loading>
    </view>
</template>

<script>
    import imgContentAreaVue from "@/components/imgContentArea.vue";
    import userdescVue from '@/components/userdesc.vue';
    import functionbuttonVue from '@/components/functionbutton.vue';
    export default {
        components: {
            functionbuttonVue,
            userdescVue,
            imgContentAreaVue
        },
        data() {
            return {
                userPageList: {

                },
                res: [],
                j: '',

                e: {
                    nickname: '',
                    comment: ''
                },
                userId: '',
                postId: '',
                pageList: [],
                pageNum: 1,
                pageSize: 4,
                types: '',
                bgColor: '',
                titleName: '',
            };
        }
        /**
         * 生命周期函数--监听页面加载
         */
        ,
        onLoad(options) {
            this.userId = options.userId;
            this.postId = options.postId
            this.feedPostUserPage();
            this.getPageList()
        },
        /**
         * 生命周期函数--监听页面初次渲染完成
         */
        onReady() {},
        /**
         * 生命周期函数--监听页面显示
         */
        onShow() {},
        /**
         * 生命周期函数--监听页面隐藏
         */
        onHide() {},
        /**
         * 生命周期函数--监听页面卸载
         */
        onUnload() {},
        /**
         * 页面相关事件处理函数--监听用户下拉动作
         */
        onPullDownRefresh() {},
        /**
         * 页面上拉触底事件的处理函数
         */
        onReachBottom() {
            if (this.types == 1) {
                this.setData({
                    pageNum: this.pageNum + 1
                });
                this.getPageList();
            }
        },
        /**
         * 用户点击右上角分享
         */
        onShareAppMessage() {},
        onPageScroll: function(e) {
            if (e.scrollTop > 0) {
                this.setData({
                    bgColor: '#ffffff'
                });
            } else if (e.scrollTop < 2) {
                this.setData({
                    bgColor: ''
                });
            }
        },
        methods: {
            feedPostUserPage() {
                //查询指定用户朋友圈列表
                let data = {
                    postId: this.postId
                };
                this.$axios.get(this.$api.feedPostInfo, data).then((res) => {
                    if (res.data.code == 200) {
                        let data = res.data.data;
                        if (data.content.length > 115) {
                            data.packUpShow = false;
                            data.moreBtns = true;
                            data.moreBtn = true;
                        } else {
                            data.packUpShow = false;
                            data.moreBtns = false;
                            data.moreBtn = false;
                        }
                        this.setData({
                            userPageList: data,
                            titleName: data.nickname + '动态'
                        });
                    }
                });
            },

            previewImage: function(e) {
                console.log(e);
                var current = e.currentTarget.dataset.src;
                var url = e.currentTarget.dataset.url;
                uni.previewImage({
                    current: current,
                    // 当前显示图片的http链接
                    urls: url // 需要预览的图片http链接列表
                });
            },

            preview(event) {
                let src = event.currentTarget.dataset.src;
                let maparr = [];
                maparr.push({
                    type: 'video',
                    url: src
                });
                let index = event.currentTarget.dataset.index;
                // 既有视频又有图片用这个
                console.log(maparr);
                uni.previewMedia({
                    sources: maparr,
                    // 官方文档 : 素材展示列表 是个数组对象 一个url链接 一个type 判断这是视频还是图片
                    // 接收image 和 video 所以处理数据时 最好给个type 指定 image 或者 video
                    current: index
                    // 当前显示的资源序号
                });
            },

            packUp() {
                //收起全文
                this.setData({
                    'userPageList.packUpShow': !this.userPageList.packUpShow,
                    'userPageList.moreBtns': !this.userPageList.moreBtns
                });
            },
            getPageList() {
                let data = {
                    pageSize: this.pageSize,
                    pageNum: this.pageNum,
                    userId: this.userId,
                    notPostIds: this.postId
                };
                this.changeLoading(true)
                this.$axios.get(this.$api.getFeedPostPage, data).then((res) => {
                    if (res.data.code == 200) {
                        let data = res.data.rows;
                        if (data != null) {
                            let types = data != null ? (data.length == 4 ? 1 : 2) : '';
                            let dtnow;
                            if (this.pageNum == 1) {
                                dtnow = [];
                            } else {
                                dtnow = this.pageList;
                            }
                            data.forEach((item) => {
                                if (item.content.length > 65) {
                                    item.packUpShow = false;
                                    item.moreBtns = true;
                                    item.moreBtn = true;
                                } else {
                                    item.packUpShow = false;
                                    item.moreBtns = false;
                                    item.moreBtn = false;
                                }
                                var timearr = item.createTime.replace(' ', ':').replace(/\:/g, '-')
                                    .split('-');
                                item.createTimes = timearr[1] + '/' + timearr[2] + ' ' + timearr[3] +
                                    ':' + timearr[4];
                            });
                            let dtnows = dtnow.concat(data);
                            this.setData({
                                pageList: dtnows,
                                types: types
                            });
                        }
                    }
                }).finally(() => {
                    this.changeLoading(false)
                });
            },

            packUp2(index) {
                //收起全文
                this.setData({
                    [`pageList[${index}].packUpShow`]: !this.pageList[index].packUpShow,
                    [`pageList[${index}].moreBtns`]: !this.pageList[index].moreBtns
                });
            },
        }
    };
</script>
<style scoped>
    @import './staffdetail.css';
</style>