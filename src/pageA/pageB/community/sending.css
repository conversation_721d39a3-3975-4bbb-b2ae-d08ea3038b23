.main {
    background: #f8f9f9;
}

.comment-list {
    width: 100%;
    overflow: hidden;
}

.custom-button {
    background: #fff;
    color: hsla(0, 0%, 47%, 1);
    font-size: 24rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0;
    padding: 0;
}

.mainss {
    /* background: url('http://cdn.xiaodingdang1.com/2024/09/09/ef7454c13aca4a3da4fcad1dc69b1319.png') no-repeat;
    background-size: 100% 988rpx; */
    padding: 173rpx 0rpx 0rpx;
}

.swipper1 {
    height: 900rpx;
}
.swiper-item-box{
	 display: flex;
	  justify-content: center; /* 水平居中 */
	  align-items: center;     /* 垂直居中 */
	  height: 100%;            /* 继承swiper高度 */
}
.u-wrp-bnr {
    width: 100%;
    height: 100%;
    display: block;
    position: relative;
    background: #f0f0f0;
}

.lengthStay {
    background: url('http://cdn.xiaodingdang1.com/2024/08/20/c707e0287940456787f1cef4f1dec273.png') no-repeat;
    width: 190rpx;
    height: 88rpx;
    background-size: 100% 100%;
    color: #ff4f61;
    font-size: 26rpx;
    font-weight: bold;
    text-align: center;
    line-height: 100rpx;
}

.content {
    padding: 30rpx 24rpx;
    background: #fff;
}

.listHesds1 {
    background-color: white;
    display: flex;
    justify-content: space-between;
    padding: 10rpx 30rpx 20rpx 30rpx;
}

.listHesds {
    display: flex;
    align-items: center;
}

.listHesds_1 {
    margin-left: 20rpx;
}

.listHesds_2 {
    display: flex;
    align-items: center;
}

.listHesds_3 {
    color: #ff6e00;
    font-size: 30rpx;
}

.listHesds_4 {
    color: #aaaaaa;
    font-size: 24rpx;
}

/* .title {
    display: flex;
    margin-top: 20rpx;
    align-items: center;
} */

.title_1 {
    font-weight: bold;
    font-size: 38rpx;
    color: #ff6955;
}

.title_2 {
    /* margin-bottom: 32rpx; */
    font-weight: 500;
    font-size: 40rpx;
    color: #333333;
    margin-left: 10rpx;
    overflow: hidden;
    /* 确保超出容器的文本会被裁剪 */
    white-space: nowrap;
    /* 确保文本在一行内显示，避免换行 */
    text-overflow: ellipsis;
    /* 使用省略号表示被裁剪的文本 */
    max-width: 24em;
    /* 设置最大宽度，确保不会超过十个字符的宽度 */
}

.title_3 {
    color: #333333;
    font-size: 30rpx;
    margin-top: 10rpx;
    white-space: pre-wrap;
}

.issue {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 30rpx;
}

.issue_1 {
    color: #777777;
    font-size: 24rpx;
}

.issue1 {
    display: flex;
    align-items: center;
}

.issue1_1 {
    display: flex;
    align-items: center;
}

.issue1_2 {
    font-size: 28rpx;
    color: #777777;
    margin-left: 8rpx;
}

.dot {
    width: 8rpx;
    height: 8rpx;
    background: #777777;
    border-radius: 100rpx;
    margin: 0 12rpx;
}

.dynamicState {
    padding: 24rpx 24rpx;
}

.titles1 {
    display: flex;
    align-items: center;
}

.fgx {
    width: 12rpx;
    height: 36rpx;
    background: #ff4f61;
    border-radius: 28rpx;
}

.titles {
    font-size: 34rpx;
    color: #333333;
    font-weight: bold;
    margin-left: 12rpx;
}

.dynamic {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    margin-top: 20rpx;
    width: 100%;
}

.dynamicImg1 {
    width: 340rpx;
}

.dynamicImg1 video {
    width: 100%;
    height: 450rpx;
    border-radius: 20rpx 20rpx 0 0;
}

.dynamicImg {
    width: 100%;
    /* min-height: 254rpx; */
    height: 450rpx;
    border-top-left-radius: 20rpx;
    border-top-right-radius: 20rpx;
}

.dynamicImg2 {
    width: 340rpx;
    background: #fff;
    border-bottom-left-radius: 20rpx;
    border-bottom-right-radius: 20rpx;
    padding: 24rpx 0;
}

.dynamicImg2_1 {
    color: #333333;
    font-size: 28rpx;
    font-weight: bold;
    width: 90%;
    margin: 0 auto;
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
}

.dynamicImg3 {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 30rpx;
    width: 90%;
    margin: 0 auto;
    margin-top: 24rpx;
}

.dynamicImg3_1 {
    display: flex;
    align-items: center;
}

.dynamicImg3_2 {
    color: #777777;
    font-size: 22rpx;
    margin-left: 8rpx;
}

.dynamic4 {
    margin-top: 12rpx;
}

.label {
    margin-left: 12rpx;
    flex: 1;
    justify-content: space-between;
}

.label-left {
    text-align: center;
    padding: 4rpx 16rpx 4rpx 26rpx;
    background: #ffe4e5;
    border-radius: 80rpx;
    color: #ff4f61;
    font-size: 24rpx;
    font-weight: 600;
    position: relative;
    margin-left: 8rpx;
    width: 110rpx;
}

.label-left image {
    width: 46rpx;
    height: 46rpx;
    transform: rotate(-10deg);
    position: absolute;
    left: -16rpx;
    z-index: 1;
    bottom: 0rpx;
}

.mainss-top {
    padding: 10rpx 30rpx 20rpx 30rpx;
    background-color: white;
}

.title-devide {
    width: 702rpx;
    height: 1rpx;
    background-color: #F0F0F0;
    margin: 32rpx 0;
}
.issue_2{
	display: flex;
	justify-content: flex-end;
	margin-right: 20rpx;
}
.issue_2_1{
		font-size: 26rpx;
		color: #333333;
		margin-left: 10rpx;
	}
	.issue2_2{
		display: flex;
		justify-content: flex-end;
	}