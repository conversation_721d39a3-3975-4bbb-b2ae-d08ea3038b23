<template>
    <view class="content">
        <!--  <view class="content-tabbar">
            <view @tap="tabbarClick(2)" :class="['content-tabbar-room', current == 2? 'selected': '' ]">月子套餐</view>
            <view @tap="tabbarClick(1)" :class="['content-tabbar-room', current == 1? 'selected': '' ]">月子套房</view>
        </view> -->
        <view>
            <view class="content-card" @tap="nextClick(item)" v-for="(item, index) in pageList" :key="index">
                <view class="content-card-img">
                    <image :src="item.img"></image>
                </view>
                <view class="content-card-title">
                    {{ item.title }}
                </view>
                <scroll-view scroll-x="true" class="content-card-tag">
                    <view class="content-card-tag-enum" v-if="item.tag.length == 0">空气净化器</view>
                    <template v-else>
                        <view class="content-card-tag-enum" v-for="(res, i) in item.tag" :key="i"> {{ res }}</view>
                    </template>
                </scroll-view>
            </view>
        </view>
    </view>
</template>
<script setup>
    // 房间信息列表展示
    import {
        onShow,
        onHide,
        onLoad,
        onPageScroll
    } from "@dcloudio/uni-app";
    import {
        ref,
        reactive,
        onMounted,
        computed,
        getCurrentInstance
    } from "vue";
    const instance = getCurrentInstance();
    const App = instance.appContext.config.globalProperties
    console.log('instance', instance, App)
    onMounted(() => {
        pageInit()
    })
    const pageInit = async () => {
        try {
            current.value == 1 ? await suitePage() : await packagePage()
        } catch {

        }
    }
    const emits = defineEmits(['callback'])
    const props = defineProps({
        currentIndex: {
            type: [Number, String],
            default: 2
        }
    })
    const current = computed(() => props.currentIndex)
    const pageList = reactive([])
    const suitePage = async () => {
        //查询月子套房列表
        let data = {
            pageSize: 100,
            pageNum: 1,
            onlineStatus: true
        };
        const res = await App.$axios.get(App.$api.suitePage, data)
        if (res.data.code == 200) {
            let data = res.data.data.rows
            data.forEach((item) => {
                item.img = item?.suitePhotos[0] || ''
                item.title = item.roomName || ''
            })
            pageList.splice(0, pageList.length - 1)
            Object.assign(pageList, data)
        }
    }
    const packagePage = async () => {
        //查询优惠套餐列表
        let data = {
            pageSize: 1000,
            pageNum: 1
        };
        const res = await App.$axios.get(App.$api.packagePage, data)
        if (res.data.code == 200) {
            let data = res.data.rows
            data.forEach((item) => {
                item.img = item?.photos || ''
                item.title = item.packageName || ''
                item.tag = item.facilityFeatures || []
            })
            pageList.splice(0, pageList.length - 1)
            Object.assign(pageList, data)
        }
    }
    const tabbarClick = (index) => {
        if (index !== current.value) {
            // current.value = index
            index == 1 ? suitePage() : packagePage()
            emits('callback', index)
            console.log('currentcurrent', current)
        }
    }
    const nextClick = (query) => {
        let url = ''
        let params = {}
        if (current.value == 1) {
            url = 'room/detail'
            params = {
                suiteId: query.suiteId
            }
			App.$jumpPage(url, params)
        } else {
            // url = 'room/pagedetail'
            // params = {
            //     packageId: query.packageId
            // }
			uni.navigateTo({
				url:'/pageA/pageB/community/confinementDetails/confinementDetails?packageId='+ query.packageId
			})
        }
		// App.$jumpPage(url, params)
    }
</script>
<style lang="less" scoped>
    .content {
        background-color: #F8F9F9;
        padding: 0 24rpx 40rpx 24rpx;

        .flex {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
        }

        &-tabbar {
            height: 92rpx;
            font-weight: 400;
            font-size: 32rpx;
            font-style: normal;
            text-transform: none;
            color: #999;
            margin-bottom: 18rpx;

            &-room {
                width: 50%;
                border-radius: 20rpx 20rpx 0 0;
                background-color: #F2F2F2;
                display: inline-block;
                text-align: center;
                padding: 24rpx 0;
            }

            &-menu {
                width: 50%;
                border-radius: 20rpx 20rpx 0 0;
                background-color: #F2F2F2;
                display: inline-block;
                text-align: center;
                padding: 24rpx 0;
            }

            .selected {
                color: #333;
                background-color: white;
                font-weight: 500;
            }
        }

        &-card {
            margin: 0 auto;
            background-color: white;
            border-radius: 20rpx;
            margin-bottom: 24rpx;
            padding-bottom: 16rpx;

            &-img {
                image {
                    border-radius: 20rpx 20rpx 0 0;
                    width: 100%;
                    height: 360rpx;
                }
            }

            &-title {
                font-weight: 500;
                font-size: 30rpx;
                color: #333333;
                line-height: 35rpx;
                text-align: left;
                font-style: normal;
                text-transform: none;
                margin: 16rpx 20rpx 12rpx 20rpx;
            }

            &-tag {
                width: calc(100% - 48rpx);
                display: flex;
                align-items: center;
                overflow-x: scroll;
                overflow-y: hidden;
                white-space: nowrap;
                margin-bottom: 16rpx;
                padding: 0 20rpx;

                &-enum {
                    display: inline-block;
                    margin-right: 8rpx;
                    height: 40rpx;
                    background-color: #F2F2F2;
                    border-radius: 6rpx;
                    padding: 6rpx 12rpx;
                    font-weight: 400;
                    font-size: 24rpx;
                    color: #999;
                    line-height: 28rpx;
                    text-align: left;
                    font-style: normal;
                    text-transform: none;
                }
            }
        }
    }
</style>