.main {
    background: #f6f6f6;
    padding: 42rpx 32rpx;
}

.headTitle {
    display: flex;
    justify-content: space-between;
    color: #333333;
    font-weight: bold;
}

.headTitle1 {
    font-size: 32rpx;
}

.headTitle2 {
    font-size: 28rpx;
}

.coment {
    border-radius: 20rpx;
    background: #ffffff;
    padding: 28rpx 28rpx;
    margin-top: 20rpx;
}

.container-comment {
    padding: 10px;
}

textarea {
    width: 100%;
    height: 150px;
    box-sizing: border-box;
}

.wordNumber {
    width: 100%;
    margin-top: 10px;
    color: #888;
    display: flex;
    justify-content: flex-end;
}

.uploading {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 20rpx;
}

.uploadingImg {
    margin-right: 20rpx;
    position: relative;
}

.imgCancel {
    position: absolute;
    top: 0;
    right: 0;
    background: #fff;
    border-radius: 50rpx;
}

.addImg1 {
    width: 200rpx;
    height: 200rpx;
    border: 2rpx dashed #aaaaaa;
    text-align: center;
}

.addImg {
    font-size: 24rpx;
    color: #aaaaaa;
}

.btn {
    position: fixed;
    bottom: 0;
    padding: 20rpx 24rpx 60rpx 24rpx;
    background: #fff;
    width: 100%;
}

.btn1 {
    background: #3f6fff;
    border-radius: 100rpx;
    color: #ffffff;
    font-size: 30rpx;
    width:100%;
    height: 90rpx;
    text-align: center;
    line-height: 90rpx;
}