<template>
	<view class="main">
		<view class="list" v-for="item,index in userHistory" :key="index" @click="next(item)">
			<view class="clubName" @click="switchover(item.tenantId)">
				<view class="name">
					{{item.clubName}}
				</view>
				<view class="time">
					{{item.address}}
				</view>
			</view>
			<view class="arrival1" @click="handleLocationClick(index)">
				<image src="http://cdn.xiaodingdang1.com/2025/06/23/7fc497b5b2984a92935934afacb3d098.png" mode=""
					style="width: 35rpx;height: 35rpx;"></image>
				<view class="arrival">
					到店
				</view>
			</view>
			<!-- 			<image src="http://cdn.xiaodingdang1.com/2025/06/07/eccdd66ff949420f87dd8c8afc1c6f3a.png" mode=""
				style="width: 35rpx;height: 35rpx;"></image> -->
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				userHistory: [],
				centerLatitude: '',
				centerLongitude: '',
				markers: [{
					id: 1,
					latitude: '',
					longitude: '',
					title: '茶山馨苑',
					width: 30,
					height: 30,
				}, ],
			};
		},
		/**
		 * 生命周期函数--监听页面加载
		 */
		onLoad(options) {
			this.getuserHistory()
		},
		methods: {
			switchover(tenantId) { //切换商家
				uni.showModal({
					title: '提示',
					content: '是否切换到澜悦湾月子会所?切换后需重新授权手机号',
					success: (res) => {
						uni.hideLoading();
						if (res.confirm) {
							uni.removeStorageSync('token');
							uni.removeStorageSync('tenantId');
							uni.removeStorageSync('clubInfo');
							uni.removeStorageSync('bgBackground');
							uni.removeStorageSync('servicePhone');
							uni.removeStorageSync('Report_Data_Time');
							uni.removeStorageSync('Report_Status');
							uni.removeStorageSync('uid');
							uni.removeStorageSync('userInfo');
							uni.removeStorageSync('permissions');
							uni.removeStorageSync('roles');
							uni.removeStorageSync('hasBindPhone');
							uni.removeStorageSync('clubName');
							uni.setStorageSync('tenantId', tenantId)
							uni.reLaunch({
								url: '/pageA/login',
							});
						}
					},
				});
			
			},
			handleLocationClick(index) {
				this.getgeocodes(this.userHistory[index].address)
			},
			getmap() {
				let lng = this.centerLongitude;
				let lat = this.centerLatitude;
				let locationAddress = this.address
				uni.getLocation({
					type: 'gcj02', //定位类型 wgs84, gcj02
					success: function(res) {
						uni.openLocation({
							//当前经纬度
							latitude: Number(lat),
							longitude: Number(lng),
							//缩放级别默认28
							scale: 18,
							//位置名
							address: locationAddress,
							success(res) {
								console.log(res);
							},
							fail(err) {
								console.log(err);
							},
						});
					},
					fail(err) {
						console.log(err);
					},
				});
			},
			next(item) {
				let items = JSON.stringify(item)
				// uni.navigateTo({
				// 	url: '/pageA/pageB/community/browsingHistory/location?item='+items
				// })
			},
			getuserHistory() {
				//获取用户浏览记录-会所记录
				this.$axios.get(this.$api.userHistory).then((res) => {
					if (res.data.code == 200) {
						this.setData({
							userHistory: res.data.data
						});
					}
				});
			},
			getgeocodes(address) {
				//获取用户浏览记录-会所记录
				let data = {
					address: address,
				}
				this.$axios.get(this.$api.geocodes, data).then((res) => {
					if (res.data.code == 200) {
						let location = res.data.data.location.split(',');
						this.markers[0].latitude = location[1]
						this.markers[0].longitude = location[0]
						this.centerLatitude = location[1]
						this.centerLongitude = location[0]
						this.getmap()
					}
				});
			},
		}

	}
</script>

<style lang="less" scoped>
	@import './browsingHistory.less';
</style>