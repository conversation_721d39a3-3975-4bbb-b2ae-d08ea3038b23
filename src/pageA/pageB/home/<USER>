<template>
    <view class="activity-content">
        <rich-text class="activity" :nodes="richText" type="text"></rich-text>
        <new-bottom :from="SessionFrom" :isActivity="true" :activityId="activityId"></new-bottom>
		<mask-dialog></mask-dialog>
    </view>
</template>

<script>
    export default {
        data() {
            return {
                richText: '',
                SessionFrom: '',
                activityId: ''
            };
        }
        /**
         * 生命周期函数--监听页面加载
         */
        ,
        onLoad(options) {
            this.activityId = options.activityId
            const SessionFrom = uni.getStorageSync('tenantId') + ',' + uni.getStorageSync('userInfo').nickname +
                ',' + '活动详情'
            this.$axios.get(this.$api.getActivityDetail, {
                activityId: this.activityId
            }).then((res) => {
                const data = res?.data?.data
                if (data.content) {
                    let content = data.content;
                    this.richText = content.replace(/<img[^>]*>/gi, function(match, capture) {
                        return match.replace(/style=".*"/gi, '').replace(/style='.*'/gi, '')
                    }).replace(/\<img/gi, '<img style="width:100%;height:auto;display:block;"')
                }
            })

        },
        /**
         * 生命周期函数--监听页面初次渲染完成
         */
        onReady() {},
        /**
         * 生命周期函数--监听页面显示
         */
        onShow() {},
        /**
         * 生命周期函数--监听页面隐藏
         */
        onHide() {},
        /**
         * 生命周期函数--监听页面卸载
         */
        onUnload() {},
        /**
         * 页面相关事件处理函数--监听用户下拉动作
         */
        onPullDownRefresh() {},
        /**
         * 页面上拉触底事件的处理函数
         */
        onReachBottom() {},
        /**
         * 用户点击右上角分享
         */
        onShareAppMessage() {},

        onShareTimeline() {},
        methods: {}
    };
</script>
<style scoped lang="less">
    @import './activity.less';
</style>