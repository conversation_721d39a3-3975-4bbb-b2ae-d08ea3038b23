<template>
	<view class="main">
		<view class="list" @tap="next" :data-id="item.complaintId" v-for="(item, index) in userList" :key="index">
			<view class="listTitle">
				{{ item.userName }}
			</view>

			<view class="fgx"></view>
			<view class="listComent">
				<text class="listComent1">入住房间：</text>
				<text class="listComent2">{{ item.roomNumber?item.roomNumber:'--' }}</text>
			</view>
			<view class="listComent">
				<text class="listComent1">投诉理由：</text>
				<text class="listComent2">{{ item.content }}</text>
			</view>

			<view class="listComent">
				<text class="listComent1">投诉时间：</text>
				<text class="listComent2">{{ item.createTime }}</text>
			</view>

			<view class="fgx" v-if="item.status == 2"></view>

			<view class="reason" v-if="item.status == 2">
				<view class="listComent">
					<text class="listComent1">处理人：</text>
					<text class="listComent2">{{ item.feedbackTransactorName }}</text>
				</view>
				<view class="listComent">
					<text class="listComent1">处理时间：</text>
					<text class="listComent2">{{ item.feedbackTime }}</text>
				</view>
				<view class="listComent3">
					<text class="listComent1">处理结果：</text>
					<text class="listComent2">{{ item.feedbackContent }}</text>
				</view>
			</view>

			<view class="label" v-if="item.status == 1">处理中</view>

			<view class="label1" v-if="item.status == 2">已处理</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				type: '',
				userList: []
			};
		}
		/**
		 * 生命周期函数--监听页面加载
		 */
		,
		onLoad(options) {
			this.setData({
				type: options.type
			});
			this.getUserList();
		},
		/**
		 * 生命周期函数--监听页面初次渲染完成
		 */
		onReady() {},
		/**
		 * 生命周期函数--监听页面显示
		 */
		onShow() {},
		/**
		 * 生命周期函数--监听页面隐藏
		 */
		onHide() {},
		/**
		 * 生命周期函数--监听页面卸载
		 */
		onUnload() {},
		/**
		 * 页面相关事件处理函数--监听用户下拉动作
		 */
		onPullDownRefresh() {},
		/**
		 * 页面上拉触底事件的处理函数
		 */
		onReachBottom() {},
		/**
		 * 用户点击右上角分享
		 */
		onShareAppMessage() {},

		onShareTimeline() {},
		methods: {
			next(e) {
				let complaintId = e.currentTarget.dataset.id;
				uni.navigateTo({
					url: '/pageA/pageB/mine/complaint/detail?complaintId=' + complaintId
				});
			},

			getUserList() {
				//获取用户投诉列表
				let userId = uni.getStorageSync('uid');
				let data = {
					userId: userId
				};
				this.$axios.get(this.$api.userList, data).then((res) => {
					if (res.data.code == 200) {
						let data = res.data.data;
						this.setData({
							userList: data
						});
					}
				});
			}
		}
	};
</script>
<style scoped>
	@import './history.css';
</style>