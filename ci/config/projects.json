{"projects": {"main": {"name": "艾尔母婴商家管理平台", "appid": "wx1df89e340decbc7a", "projectPath": "../../dist/build/mp-weixin", "privateKeyPath": "../private/private.wx1df89e340decbc7a.key", "defaultTenantId": "194338", "ignores": ["node_modules/**/*"], "environments": {"develop": {"name": "开发版", "desc": "开发环境版本", "buildCommand": "npx uni build -p mp-weixin --mode test", "tenantId": "194338"}, "trial": {"name": "体验版", "desc": "体验环境版本", "buildCommand": "npx uni build -p mp-weixin --mode trial", "tenantId": "194338"}, "release": {"name": "正式版", "desc": "生产环境版本", "buildCommand": "npx uni build -p mp-weixin --mode production", "tenantId": "194338"}}}, "xinxiyue": {"name": "馨喜月月子会所平台", "appid": "wxc05ce1faaa835bb2", "projectPath": "../../dist/build/mp-weixin", "privateKeyPath": "../private/private.wxc05ce1faaa835bb2.key", "defaultTenantId": "123456", "environments": {"develop": {"name": "开发版", "desc": "开发环境版本", "buildCommand": "npx uni build -p mp-weixin --mode test", "tenantId": "123456"}, "trial": {"name": "体验版", "desc": "体验环境版本", "buildCommand": "npx uni build -p mp-weixin --mode trial", "tenantId": "123456"}}, "ignores": ["node_modules/**/*"]}}, "defaultConfig": {"version": {"auto": true, "format": "YYYY.MM.DD.HHmm"}, "upload": {"desc": "通过 miniprogram-ci 自动上传", "robot": 1, "timeout": 300000}, "preview": {"pagePath": "pages/index/index", "searchQuery": "", "scene": 1001, "qrcodeFormat": "image", "qrcodeOutputDest": "../../preview-qrcode.jpg"}}}