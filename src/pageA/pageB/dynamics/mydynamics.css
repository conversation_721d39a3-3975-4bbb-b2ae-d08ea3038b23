.prosonal {
    background: url('http://cdn.xiaodingdang1.com/Rectangle%206595%403x.png') no-repeat;
    background-size: 100% 678rpx;
}

.prosonal {
    padding: 560rpx 24rpx 24rpx;
}

.head {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    position: relative;
    top: 30rpx;
}

.title {
    color: #ffffff;
    font-size: 34rpx;
    margin-right: 12rpx;
}

.img-avatar {
    width: 84rpx;
    height: 84rpx;
    border-radius: 84px;
    margin-right: 8rpx;
    margin-bottom: 4rpx;
}

.mains {
    margin: 24rpx;
}

.dynamicTitle {
    display: flex;
}

.dynamicTitle1 {
    width: 100%;
}

.dynamicTitle1_1 {
    font-size: 30rpx;
    color: #5b6799;
    font-weight: bold;
}



.dynamicTitle1_2 {
    margin-top: 4rpx;
    font-size: 28rpx;
    color: #333333;
}

.beyond {
    margin-top: 4rpx;
    font-size: 28rpx;
    color: #333333;
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 5;
}

.packUp {
    color: #4d669b;
    font-size: 30rpx;
}

.dynamicTitle1_3 {
    display: flex;
    justify-content: space-between;
    position: relative;
    margin: 6rpx 0;
}

.dynamicTitle1_3_1 {
    font-size: 24rpx;
    color: #777777;
}

.fgx {
    width: 100%;
    height: 1rpx;
    background: #ebebeb;
}

.img_box {
    margin-top: 20rpx;
    padding-left: 4rpx;
}

.videos {
    width: 240rpx;
    height: 240rpx;
}

.videos video {
    width: 100%;
    height: 100%;
    border-radius: 10rpx;
}

.img_box .many_img {
    display: flex;
    justify-self: start;
    flex-wrap: wrap;
}

.img_item.four {
    width: 32%;
    height: 195rpx;
    margin-bottom: 10rpx;
    border-radius: 16rpx;
    overflow: hidden;
}

.img_item.four image {
    width: 100%;
    height: 100%;
}

.img_item.four:nth-child(2) {
    margin: 0 1%;
}

.img_item.four:nth-child(5) {
    margin: 0 1%;
}

.img_item.four:nth-child(8) {
    margin: 0 1%;
}

.img_item.many {
    width: 48%;
    height: 280rpx;
    margin-bottom: 10rpx;
    border-radius: 10px;
    overflow: hidden;
}

.img_item.many image {
    width: 100%;
    height: 100%;
}

.img_item.many:nth-child(2n) {
    margin-left: 1%;
}

.message {
    background: #ebebeb;
    border-radius: 4rpx;
    font-size: 24rpx;
    padding: 4rpx 8rpx;
    margin: 15rpx 0 24rpx 0;
}

.messageName {
    color: #5b6799;
    margin-right: 8rpx;
    flex-shrink: 0;
}

.message1 {
    display: flex;
    align-items: center;
}

.messageTitle {
    color: #777777;
    flex: 1;
    word-wrap: break-word;
    word-break: break-all;
}

.discuss1_3 {
    display: flex;
    align-items: center;
    color: #5b6799;
    font-size: 30rpx;
}

.head1_2 {
    display: flex;
    position: relative;
    margin-left: 16rpx;

}

.head1_3 {
    background: #ffeecc;
    padding: 0 8rpx 0 30rpx;
    border-radius: 10rpx;
    color: #fb4105;
    font-size: 24rpx;
    height: 34rpx;
}

.head1Img {
    position: absolute;
    left: -10rpx;
}

.dynamic {
    background: #ff4f61;
    width: 198rpx;
    height: 84rpx;
    text-align: center;
    line-height: 84rpx;
    border-radius: 110rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    position: fixed;
    bottom: 150rpx;
    left: 40%;
}

.dynamic {
    color: #ffffff;
    font-size: 32rpx;
}

.like {
    display: flex;
    background: #4c4c4c;
    width: 326rpx;
    height: 72rpx;
    align-items: center;
    color: #ffffff;
    font-size: 26rpx;
    border-radius: 10rpx;
    position: absolute;
    top: -10rpx;
    right: 50rpx;
    z-index: 999;
}

.like1 {
    display: flex;
    align-items: center;
    width: 50%;
    justify-content: center;
}

.like2 {
    display: flex;
    align-items: center;
    width: 50%;
    justify-content: center;
}

.like1_1 {
    margin-left: 4rpx;
}

.fgxs {
    width: 1rpx;
    height: 40rpx;
    background: #333333;
}

.tabbar image {
    display: block;
    margin: 0 auto 0rpx;
}

.tabTitle {
    font-size: 20rpx;
}

.cu-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    color: #000 !important;
    background-color: #fff;
    display: flex;
    justify-content: space-around;
    padding: 20rpx 0 40rpx 0;
}

.tabImg {
    width: 50rpx;
    height: 50rpx;
}

.text-active {
    color: #3b86eb;
    text-align: center;
}

/* 点赞 评论触发键盘输入 的input样式 */
.input-section {
    position: fixed;
    display: flex;
    align-items: center;
    height: 110rpx;
    bottom: 0rpx;
    left: 0rpx;
    right: 0rpx;
    z-index: 500;
    background: #f2f2f2;
}

.input_input {
    background: #fff;
    margin-left: 12rpx;
    z-index: 500;
    width: 580rpx;
    height: 94rpx;
    padding-left: 35rpx;
    font-size: 30rpx;
    border-radius: 6rpx;
}

.send_btn {
    width: 140rpx;
    height: 79rpx;
    position: absolute;
    top: 13rpx;
    right: 14rpx;
    background: var(--green);
    z-index: 550;
    border-radius: 10rpx;
    display: flex;
    align-items: center;
    justify-content: center;
}

.send_btn_text {
    display: flex;
    font-size: 32rpx;
    color: #fff;
    z-index: 560;
}

.comments {
    margin: 0;
    padding: 38rpx 24rpx 60rpx 24rpx;
    width: 100%;
    /* height: 92rpx; */
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 1rpx solid #cccccc;
    border-bottom: 1rpx solid #cccccc;
    /* padding和border被包含在定义的width和height之内。盒子的实际宽度=设置的width（padding和border不会影响实际宽度） */
    box-sizing: border-box;
    font-size: 32rpx;
    transition: all 2s inherit;
    overflow: hidden;
    /* 设置为固定定位 */
    position: fixed;
    left: 0;
    background: hsla(0, 0%, 100%, 1);
}

/* textarea输入框的样式 */
.textarea {
    margin: 0;
    padding: 5rpx 18rpx;
    /* 宽度为 父容器的宽度 减去 发送按钮的宽度 减去 (左右内边距和左右边框宽度) 减去 右边外边距*/
    width: calc(100% - 100rpx - 50rpx - 24px);
    /* textarea 的高度随着文本的内容来改变的 设置一个最小高度60rpx*/
    height: 60rpx;
    /* 取消默样式 */
    outline: none;
    border-radius: 84rpx;
    background-color: hsla(0, 0%, 92%, 1);
    /* padding和border不被包含在定义的width和height之内。盒子的实际宽度=设置的width+padding+border */
    box-sizing: content-box;
    overflow: hidden;
}

/* 发送按钮样式 */
.send_out {
    margin: 0;
    padding: 0;
    width: 100rpx;
    height: 70rpx;
    text-align: center;
    line-height: 70rpx;
    border: 1rpx solid #cccccc;
    border-radius: 10rpx;
    /* 将发送按钮固定在底部 */
    position: absolute;
    right: 24rpx;
}

/* 点赞 评论触发键盘输入 的input样式 */
.input-section {
    position: fixed;
    display: flex;
    align-items: center;
    height: 110rpx;
    bottom: 0rpx;
    left: 0rpx;
    right: 0rpx;
    z-index: 500;
    background: #f2f2f2;
}

.input_input {
    background: #fff;
    margin-left: 12rpx;
    z-index: 500;
    width: 70%;
    height: 94rpx;
    padding-left: 35rpx;
    font-size: 30rpx;
    border-radius: 6rpx;
}

.send_btn {
    width: 140rpx;
    height: 79rpx;
    position: absolute;
    top: 13rpx;
    right: 14rpx;
    background: var(--green);
    z-index: 550;
    border-radius: 10rpx;
    display: flex;
    align-items: center;
    justify-content: center;
}

.send_btn_text {
    display: flex;
    font-size: 32rpx;
    color: #fff;
    z-index: 560;
}

.shadow {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 99;
}

.liker {
    display: flex;
    flex-wrap: wrap;
    background: #EBEBEB;
    border-radius: 4rpx;
}

.like-user {
    display: flex;
    align-items: center;
    padding: 8rpx 8rpx 0 8rpx;
}

.like-user:not(:last-child):after {
    content: ',';
    display: block;
    color: #5B6799;
}

.hidetext {
    flex-wrap: nowrap;
    overflow: hidden;
}

.like-name {
    margin-left: 2rpx;
    font-weight: 500;
    font-size: 24rpx;
    color: #5B6799;
}

.likeContent {
    background: #f7f7f7;
    border-radius: 20rpx;
    padding: 20rpx 20rpx;
}

.likeContent1 {
    display: flex;
}

.likeAvatar {
    padding-left: 8rpx;
    position: relative;
}

.likeIcon {
    line-height: 68rpx;
}

.likeCount {
    /* flex-shrink: 0; */
    position: absolute;
    left: 450rpx;
    bottom: 0;
}

.likeBotton {
    display: -webkit-box;
    font-size: 24rpx;
    color: #777777;
    position: absolute;
    bottom: 15rpx;
}

.likeCount image {
    position: relative;
    top: 10rpx;
}