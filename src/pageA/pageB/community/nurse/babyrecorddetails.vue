<template>
	<view class="main">
		<view class="coment">
			<view class="fgx"></view>
			<view v-if="fieldList[0].isEnabled">
				<view class="list1">
					<view class="title">
						宝宝姓名
					</view>
					<view class="name">
							{{detailsList.babyName?detailsList.babyName:'--'}}
					</view>
				</view>
				<view class="fgx"></view>
			</view>
			<view v-if="fieldList[1].isEnabled">
				<view class="list1">
					<view class="title">
						记录时间
					</view>
					<view class="name">
						{{detailsList.recordTime?detailsList.recordTime:'--'}}
					</view>
				</view>
				<view class="fgx"></view>
			</view>
			<view v-if="fieldList[2].isEnabled">
				<view class="list1">
					<view class="title">
						母婴同房送去时间
					</view>
					<view class="name">
						{{detailsList.motherBabySendTime?detailsList.motherBabySendTime:'--'}}
					</view>
				</view>
				<view class="fgx"></view>
			</view>
			<view v-if="fieldList[3].isEnabled">
				<view class="list1">
					<view class="title">
						母婴同房接回时间
					</view>
					<view class="name">
						{{detailsList.motherBabyReceiveTime?detailsList.motherBabyReceiveTime:'--'}}
					</view>
				</view>
				<view class="fgx"></view>
			</view>
			<view v-if="fieldList[4].isEnabled">
				<view class="list1">
					<view class="title">
						宝宝体温(°C)
					</view>
					<view class="name">
						{{detailsList.temperature?detailsList.temperature:'--'}}
					</view>
				</view>
				<view class="fgx"></view>
			</view>
			<view v-if="fieldList[5].isEnabled">
				<view class="list1">
					<view class="title">
						宝宝身高(cm)
					</view>
					<view class="name">
						{{detailsList.height?detailsList.height:'--'}}
					</view>
				</view>
				<view class="fgx"></view>
			</view>
			<view v-if="fieldList[6].isEnabled">
				<view class="list1">
					<view class="title">
						宝宝体重(kg)
					</view>
					<view class="name">
						{{detailsList.weight?detailsList.weight:'--'}}
					</view>
				</view>
				<view class="fgx"></view>
			</view>
			<view v-if="fieldList[7].isEnabled">
				<view class="list1">
					<view class="title">
						宝宝头围(cm)
					</view>
					<view class="name">
						{{detailsList.headCircumference?detailsList.headCircumference:'--'}}
					</view>
				</view>
				<view class="fgx"></view>
			</view>
			<view v-if="fieldList[8].isEnabled">
				<view class="list1">
					<view class="title">
						宝宝胸围(cm)
					</view>
					<view class="name">
						{{detailsList.chestCircumference?detailsList.chestCircumference:'--'}}
					</view>
				</view>
				<view class="fgx"></view>
			</view>
			<view v-if="fieldList[9].isEnabled">
				<view class="list1">
					<view class="title">
						宝宝黄疸(mg/dl)
					</view>
					<view class="name">
						{{detailsList.jaundiceValue?detailsList.jaundiceValue:'--'}}
					</view>
				</view>
				<view class="fgx"></view>
			</view>
			<view v-if="fieldList[10].isEnabled">
				<view class="list1">
					<view class="title">
						宝宝心跳(次/分)
					</view>
					<view class="name">
						{{detailsList.heartRate?detailsList.heartRate:'--'}}
					</view>
				</view>
				<view class="fgx"></view>
			</view>
			<view v-if="fieldList[11].isEnabled">
				<view class="list1">
					<view class="title">
						呼吸频率(次/分)
					</view>
					<view class="name">
					{{detailsList.respiratoryRate?detailsList.respiratoryRate:'--'}}
					</view>
				</view>
				<view class="fgx"></view>
			</view>
			<view v-if="fieldList[12].isEnabled">
				<view class="list1">
					<view class="title">
						宝宝声音
					</view>
					<view class="name">
							{{detailsList.sound?detailsList.sound:'--'}}
					</view>
				</view>
				<view class="fgx"></view>
			</view>
			<view v-if="fieldList[13].isEnabled">
				<view class="list1">
					<view class="title">
						宝宝肤色
					</view>
					<view class="name">
						{{detailsList.skinColor?detailsList.skinColor:'--'}}
					</view>
				</view>
				<view class="fgx"></view>
			</view>
			<view v-if="fieldList[14].isEnabled">
				<view class="list1">
					<view class="title">
						过敏史
					</view>
					<view class="name">
						{{detailsList.allergyHistory?detailsList.allergyHistory:'--'}}
					</view>
				</view>
				<view class="fgx"></view>
			</view>
			<view v-if="fieldList[15].isEnabled">
				<view class="list1">
					<view class="title">
						睡眠时长(小时)
					</view>
					<view class="name">
						{{detailsList.sleepDuration?detailsList.sleepDuration:'--'}}
					</view>
				</view>
				<view class="fgx"></view>
			</view>
			<view v-if="fieldList[16].isEnabled">
				<view class="list1">
					<view class="title">
						班次
					</view>
					<view class="name">
						{{detailsList.shift?detailsList.shift:'--'}}
					</view>
				</view>
				<view class="fgx"></view>
			</view>
			<view v-if="fieldList[17].isEnabled">
				<view class="list1">
					<view class="title">
						异常情况
					</view>
					<view class="name">
						{{detailsList.abnormalCondition?detailsList.abnormalCondition:'--'}}
					</view>
				</view>
				<view class="fgx"></view>
			</view>
			<view v-if="fieldList[18].isEnabled">
				<view class="list2">
					<view class="title2">
						备注信息
					</view>
					<view class="name2">
						{{detailsList.notes?detailsList.notes:''}}
					</view>
				</view>
				<view class="fgx"></view>
			</view>
			<view v-if="fieldList[19].isEnabled">
				<view class="list2">
					<view class="title2">
						照片
					</view>
					<view class="chooseImg2">
						<view class="chooseImg" v-for="item,index in detailsList.photoList" :key="index">
							<view class="chooseImg1">
								<image :src="item" mode="" style="width: 100%;height: 100%;border-radius: 8rpx;"></image>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				bgColor: '#ffffff',
				title: "",
				listImg: [1, 2, 3, 4, 5, 6],
				detailsList: '',
				babyInfoList:'',
				type: 2,
				fieldList: []
			};
		},
		methods: {
			configGetField() {
				this.changeLoading(true)
				let data = {
					tableName: 'biz_baby_routine'
				}
				this.$axios.get(this.$api.configGetField, data).then((res) => {
					if (res.data.code == 200) {
						this.setData({
							fieldList: res.data.data
						});
					}
				}).finally(() => {
					this.changeLoading(false)
				})
			},
			getGetDetail(id) {
				this.changeLoading(true)
				let data = {
					id: id
				}
				this.$axios.get(this.$api.routineGetDetail, data).then((res) => {
					if (res.data.code == 200) {
						this.setData({
							detailsList: res.data.data
						});
					}
				}).finally(() => {
					this.changeLoading(false)
				})
			},
		},
		/**
		 * 生命周期函数--监听页面加载
		 */
		onLoad: function(options) {
			this.title = options.title
			this.getGetDetail(options.id)
			this.configGetField()
		},
		/**
		 * 生命周期函数--监听页面初次渲染完成
		 */
		onReady: function() {},
		/**
		 * 生命周期函数--监听页面显示
		 */
		onShow: function() {},
		/**
		 * 生命周期函数--监听页面隐藏
		 */
		onHide: function() {},
		/**
		 * 生命周期函数--监听页面卸载
		 */
		onUnload: function() {},
		/**
		 * 页面相关事件处理函数--监听用户下拉动作
		 */
		onPullDownRefresh: function() {},
		/**
		 * 页面上拉触底事件的处理函数
		 */
		onReachBottom: function() {},
		/**
		 * 用户点击右上角分享
		 */
		onShareAppMessage() {},

		onShareTimeline() {},
	}
</script>

<style lang="less" scoped>
	@import './nursedetails.less';
</style>