/* 全局样式覆盖 */
body {
    overflow: hidden;
    -webkit-overflow-scrolling: touch;
}

scroll-view ::-webkit-scrollbar {
    display: none;
    width: 0;
    height: 0;
    color: transparent;
}

button::after {
    border: none;
    padding: 0;
    margin: 0;
}

textarea {
    padding: 4rpx 12rpx !important;
}

checkbox.mycheck .wx-checkbox-input,
checkbox.mycheck .uni-checkbox-input {
    width: 30rpx;
    height: 30rpx;
    border-radius: 50%
}

checkbox.mycheck .uni-checkbox-input-checked,
checkbox.mycheck .wx-checkbox-input-checked {
    background: #FF4F61;
    border-color: #FF4F61;
}

checkbox.mycheck .uni-checkbox-input-checked::before,
checkbox.mycheck .wx-checkbox-input-checked::before {
    width: 30rpx;
    height: 30rpx;
    line-height: 30rpx;
    text-align: center;
    font-size: 22rpx;
    color: #fff;
    background: transparent;
    transform: translate(-50%, -50%) scale(1);
    -webkit-transform: translate(-50%, -50%) scale(1);
}

.flex {
    display: flex;
}

image {
    height: 0;
}

.waterfall-card {
    width: 100%;
    height: 100%;
}

view {
    font-family: "PingFang SC, 'Heiti SC', 'Microsoft YaHei', sans-serif"
}