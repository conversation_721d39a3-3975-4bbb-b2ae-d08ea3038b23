<template>
  <view class="tenant-info">
    <image
      class="tenant-info__image"
      :src="clubHomeList.clubBackgroundPhotos[0] || ''"
      mode="aspectFill"
    ></image>

    <view class="tenant-info__content">
      <view class="tenant-info__title" @click="next" v-if="tenantId!='705382'&&tenantId!='148757'">
        <view :style="{color:clubHomeList.nameColor}">{{ clubHomeList.clubName }}</view>
		<u-icon name="arrow-right" color="#ffffff" size="30" style="margin-left: 10rpx;"></u-icon>
      </view>
	  <view class="tenant-info__title"  v-else>
	    <view :style="{color:clubHomeList.nameColor}">{{ clubHomeList.clubName }}</view>
	  </view>
      <view class="tenant-info__tags">
        <view
          v-for="(item, index) in clubHomeList.clubTagNames"
          :key="index"
          class="tenant-info__tag"
        >
          {{ item }}
        </view>
      </view>
      <view class="tenant-info__location" @tap="handleLocationClick">
        <u-icon name="map" color="#ffffff" size="24"></u-icon>
		<view>
			定位:
		</view>
        <view class="tenant-info__location-text" :style="{color:clubHomeList.addressColor}">
          {{ locationText }}
        </view>
        <u-icon name="arrow-right" color="#ffffff" size="24"></u-icon>
      </view>
    </view>
  </view>
</template>

<script setup>
import { computed, ref, reactive, onMounted, getCurrentInstance } from 'vue';

const instance = getCurrentInstance();

// 内部管理的响应式数据
const clubHomeList = reactive({
  clubName: '',
  clubTagNames: [],
  locationCity: '',
  locationAddress: '',
  clubActivities: '',
  clubBackgroundPhotos: [],
});

const latitude = ref('');
const longitude = ref('');
const tenantId=ref('')

// 计算属性
const locationText = computed(() => {
  const city = clubHomeList.locationCity || '';
  const address = clubHomeList.locationAddress || '';
  return city + address;
});

// 事件处理
const handleLocationClick = async () => {
  let address = '';

  const { locationCity, locationAddress } = clubHomeList;

  if (locationCity) {
    address = locationCity;
  } else if (locationAddress) {
    address = locationAddress;
  }

  await geocodes(address);

  let lng = longitude.value;
  let lat = latitude.value;

  uni.getLocation({
    type: 'gcj02', //定位类型 wgs84, gcj02
    success: function (res) {
      uni.openLocation({
        //当前经纬度
        latitude: Number(lat),
        longitude: Number(lng),
        //缩放级别默认28
        scale: 18,
        //位置名
        address: locationAddress,
        success(res) {
          console.log(res);
        },
        fail(err) {
          console.log(err);
        },
      });
    },
    fail(err) {
      console.log(err);
    },
  });
};

const geocodes = (address) => {
  // 获取地理/逆地理编码
  let data = {
    address: address,
  };
  instance.proxy.$axios.get(instance.proxy.$api.geocodes, data).then((res) => {
    if (res.data.code == 200) {
      let location = res.data.data.location.split(',');

      latitude.value = location[1];
      longitude.value = location[0];
    }
  });
};
const next=()=>{
	uni.navigateTo({
		url:'/pageA/pageB/community/browsingHistory/browsingHistory'
	})
}
// 查询会所主页信息
const clubHome = () => {
	tenantId.value=wx.getStorageSync('tenantId')
  instance.proxy.$axios.get(instance.proxy.$api.clubInfo).then((res) => {
    if (res.data.code == 200) {
      Object.assign(clubHomeList, res.data.data);
      if (
        clubHomeList.clubBackgroundPhotos &&
        clubHomeList.clubBackgroundPhotos.length > 0
      ) {
        let imgBackground = clubHomeList.clubBackgroundPhotos[0];
        uni.setStorageSync('bgBackground', imgBackground);
      }
      uni.setStorageSync('servicePhone', res.data.data.servicePhone);
    }
  });
};

// 生命周期
onMounted(() => {
  clubHome();
});
</script>

<style lang="scss" scoped>
.tenant-info {
  width: 100vw;
  position: relative;
  margin-bottom: -120rpx;
  .tenant-info__image {
    width: 100vw;
    height: 478rpx;
  }
}

.tenant-info__content {
  position: absolute;
  padding: 174rpx var(--spacing-2xl) var(--spacing-2xl);
  top: 0;
  left: 0;
  width: 100%;
}

.tenant-info__title {
  font-size: 44rpx;
  color: #ffffff;
  font-weight: bold;
  display: flex;
  align-items: center;
}

.tenant-info__tags {
  display: flex;
  flex-wrap: wrap;
}

.tenant-info__tag {
  padding: 8rpx 12rpx;
  background: #ffffff;
  border-radius: 10rpx;
  color: #fe5b86;
  font-size: 24rpx;
  font-weight: 600;
  margin-right: 15rpx;
  margin-top: 15rpx;
}

.tenant-info__location {
  font-size: 24rpx;
  color: #ffffff;
  display: flex;
  align-items: center;
  margin-top: 12rpx;
}

.tenant-info__location-icon {
  width: 22rpx;
  height: 24rpx;
}
</style>
