<template>
  <view class="activity-module" v-if="hasBackgroundImages">
    <swiper
      :current="current"
      :interval="4000"
      :duration="1000"
      indicator-dots
      autoplay
      indicator-color="#cccccc"
      indicator-active-color="#fff"
      class="activity-swiper"
    >
      <block v-for="(item, index) in backgroundArr" :key="index">
        <swiper-item>
          <video
            v-if="item.type == '2'"
            class="activity-item"
            :src="item.video"
            @tap="handleVideoClick"
            :data-index="index"
            :data-src="item.video"
          />
          <image
            v-else
            mode="aspectFill"
            class="activity-item"
            :src="item.mainImage"
            @tap="handleImageClick"
            :data-index="index"
            :data-activityid="item.activityId"
          />
        </swiper-item>
      </block>
    </swiper>
  </view>
</template>

<script setup>
import { ref, computed, onMounted, getCurrentInstance } from 'vue';
const current = ref(0);

// 获取当前实例（用于访问全局API）
const instance = getCurrentInstance();

// 内部状态管理
const backgroundArr = ref([]);
const loading = ref(false);

// 计算属性
const hasBackgroundImages = computed(() => {
  return backgroundArr.value && backgroundArr.value.length > 0;
});

// 数据获取逻辑
const loadActivityData = async () => {
  try {
    loading.value = true;

    // 使用全局API实例
    const api = instance?.proxy?.$axios;
    const apiEndpoint = instance?.proxy?.$api?.getActivityList;

    if (!api || !apiEndpoint) {
      throw new Error('API实例或端点未找到');
    }

    const res = await api.get(apiEndpoint);
    const data = res?.data?.data;

    if (data) {
      backgroundArr.value = data;
    }
  } catch (err) {
    console.error('ActivityModule加载数据失败:', err);
  } finally {
    loading.value = false;
  }
};

// 视频预览处理
const handleVideoPreview = (src, index) => {
  const maparr = [
    {
      type: 'video',
      url: src,
    },
  ];

  uni.previewMedia({
    sources: maparr,
    current: index,
    success: () => {
      console.log('视频预览成功');
    },
    fail: (err) => {
      console.error('视频预览失败:', err);
    },
  });
};

// 活动点击处理
const handleActivityClick = (activityId) => {
  // 直接在组件内部处理跳转
  uni.navigateTo({
    url: `/pageA/pageB/home/<USER>
    success: () => {
      console.log('跳转活动详情成功');
    },
    fail: (err) => {
      console.error('跳转活动详情失败:', err);
    },
  });
};

// 事件处理
const handleVideoClick = (e) => {
  const { index, src } = e.currentTarget.dataset;
  const idx = parseInt(index);
  handleVideoPreview(src, idx);
};

const handleImageClick = (e) => {
  const { activityid } = e.currentTarget.dataset;
  handleActivityClick(activityid);
};

// 生命周期
onMounted(() => {
  loadActivityData();
});
</script>

<style scoped>
.activity-module {
  border-radius: 20rpx;
  overflow: hidden;
}

.activity-swiper {
  height: 400rpx;
}

.activity-item {
  width: 100%;
  height: 400rpx;
  display: block;
  position: relative;
  background: #f0f0f0;
  border-radius: 20rpx;
}
</style>
