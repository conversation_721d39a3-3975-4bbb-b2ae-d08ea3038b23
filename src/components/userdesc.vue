<template>
    <view v-if="Object.keys(item).length > 0">
        <view class="content-list" v-if="pageType == 'list'">
            <view class="label flex" v-if="isMother && showTag" style="margin-top:16rpx">
                <template v-if="item.customerInfo">
                    <view class="label-left">
                        <image src="http://cdn.xiaodingdang1.com/2024/11/28/f0c7b989f9874fa8bb57e85ed07c0ca5.png">
                        </image>
                        宝妈
                    </view>
                    <view class="label-right">
                        <image src="http://cdn.xiaodingdang1.com/2024/11/28/1320b46846484420901d13c5f93bddbc.png">
                        </image>
                        入住第{{ item.customerInfo.checkInDays }}天
                    </view>
                </template>
            </view>
            <view class="profile-text">
                <view class="left">
                    <image @tap="avatarClick" :src="item.avatar || $defaultAvatar"></image>
                    <view class="over">
                        {{ item.nickname || $defaultName }}
                    </view>
                </view>
                <view class="right">
                    <image src="http://cdn.xiaodingdang1.com/2024/09/10/a53f080cdde34195b0c74178dbba2ec1.png"
                        @tap.stop.prevent="like" :data-id="item.postId" v-if="!isLike"></image>
                    <image src="http://cdn.xiaodingdang1.com/2024/09/10/510837fda4fc49a8a6105d29e307c3cb.png"
                        @tap.stop.prevent="like" :data-id="item.postId" v-if="isLike"></image>
                    <view>{{ likesCount }}</view>
                </view>
            </view>
            <view v-if="!isMother && item.staffInfo" class="content-blue">
                <view class="tag-left">{{ item.staffInfo.staffPost || '护理' }}</view>
                <view class="tag-right">{{ item.staffInfo.yearsEmployment }}</view>
                <scroll-view scroll-x="true" class="content-tag">
                    <template v-if="item.staffInfo.tags && item.staffInfo.tags.length > 0">
                        <view class="tag-enum" v-for="(res, i) in item.staffInfo.tags" :key='i'>
                            {{ res }}
                        </view>
                    </template>
                </scroll-view>
            </view>
        </view>
        <view v-else>
            <view class="detail">
                <view class="detail-left">
                    <image @tap="avatarClick" :src="item.avatar || $defaultAvatar"></image>
                </view>
                <view class="detail-right">
                    <view class="flex">
                        <view :class="['detail-right-name', isMother? '': 'detail-ye']">
                            {{ item.nickname || $defaultName }}
                        </view>
                        <view class="detail-right-content flex" v-if="!isMother && item && item.staffInfo">
                            <view class="flex">
                                <image
                                    src="http://cdn.xiaodingdang1.com/2024/12/31/88d52e447c6e4a87baf9c93514de8073.png">
                                </image>
                                <view class="detail-right-content-staf">
                                    {{ item.staffPost || '护理' }}
                                </view>
                            </view>
                            <view class="detail-right-tag">
                                <image
                                    src="http://cdn.xiaodingdang1.com/2024/11/28/1320b46846484420901d13c5f93bddbc.png">
                                </image>
                                服务年限：{{ item.staffInfo.yearsEmployment }}
                            </view>
                        </view>
                        <view class="label flex" v-if="isMother">
                            <template v-if="item.customerInfo && showTag">
                                <view class="label-left">
                                    <image
                                        src="http://cdn.xiaodingdang1.com/2024/11/28/f0c7b989f9874fa8bb57e85ed07c0ca5.png">
                                    </image>
                                    宝妈
                                </view>
                                <view class="label-right">
                                    <image
                                        src="http://cdn.xiaodingdang1.com/2024/11/28/1320b46846484420901d13c5f93bddbc.png">
                                    </image>
                                    入住第{{ item.customerInfo.checkInDays }}天
                                </view>
                            </template>
                        </view>
                    </view>
                    <view class="detail-right-date">
                        <view v-if="pageType == 'secondDetail'">
                            <scroll-view scroll-x="true" class="detail-tag">
                                <template
                                    v-if="item.staffInfo && item.staffInfo.tags && item.staffInfo.tags.length > 0">
                                    <view :class="['detail-tag-enum', isMother? 'bg-red': 'bg-blue'] "
                                        v-for="(res, i) in item.staffInfo.tags" :key='i'>
                                        {{ res }}
                                    </view>
                                </template>
                            </scroll-view>
                        </view>
                        <view v-else>
                            {{ userDate }}
                        </view>
                    </view>
                </view>
            </view>
            <scroll-view scroll-x="true" class="detail-tag" v-if="pageType !== 'secondDetail'">
                <template v-if="item.staffInfo && item.staffInfo.tags && item.staffInfo.tags.length > 0">
                    <view :class="['detail-tag-enum', isMother? 'bg-red': 'bg-blue'] "
                        v-for="(res, i) in item.staffInfo.tags" :key='i'>
                        {{ res }}
                    </view>
                </template>
            </scroll-view>
        </view>
    </view>
</template>

<script>
    // 角色标签描述（宝妈/非宝妈）
    import util from '@/utils/util.js'
    export default {
        data() {
            return {
                isLike: false,
                likesCount: 0
            }
        },
        props: {
            item: {
                type: Object,
                default: () => {}
            },
            // 展示类型
            pageType: {
                type: String,
                default: 'list'
            },
            // 是否展示宝妈标签
            showTag: {
                type: Boolean,
                default: true
            }
        },
        computed: {
            userDate() {
                if (this.item.type == 'USER') {
                    if (this.item.customerInfo) {
                        return this.item.customerInfo.checkIn || util.formatTime(new Date())
                    } else {
                        return util.formatTime(new Date())
                    }
                } else {
                    if (this.item.staffInfo) {
                        return this.item.staffInfo.practiceTime || util.formatTime(new Date())
                    } else {
                        return util.formatTime(new Date())
                    }
                }
            },
            isMother() {
                return this.item && this.item.type == 'CUSTOMER'
            }
        },
        mounted() {
            this.likesCount = 0;
            this.likesCount = this.item.likesCount
            this.isLike = this.item.isLike
        },
        methods: {
            like() {
                this.likesCount = !this.isLike ? this.likesCount + 1 : this.likesCount - 1
                this.isLike = !this.isLike
                this.$axios.get(this.$api.feedPostLikes, {
                    postId: this.item.postId
                })
            },
            avatarClick() {
                // this.$emit('avatarClick', this.item)
                let params, url
                if (this.item.type == "USER") {
                    params = {
                        userId: this.item.userId,
                        momId: this.item.momId,
                        postId: this.item.postId,
						// uuid:this.item.uuid
                    }
                    url = this.item.momId ? "/pageA/pageB/community/note/notedetail" :
                        "/pageA/pageB/community/motherhome";
                } else {
                    params = {
                        staffId: this.item.staffInfo.staffId,
                    }
                    url = '/pageA/pageB/home/<USER>/detail';
                }
                this.$jumpPage(url, params);
            }

        }
    }
</script>

<style scoped lang="less">
    .content-list {
        .content-red {
            display: flex;
            justify-content: center;
            align-items: center;
            overflow-x: scroll;
            overflow-y: hidden;
            white-space: nowrap;

            .tag-enum {
                display: inline-block;
                margin-right: 8rpx;
                background: #FFF3F5;
                border-radius: 80rpx;
                color: #FF4F61;
                font-size: 18rpx;
                padding: 4rpx 12rpx;
            }
        }

        .content-blue {
            position: relative;
            padding-top: 20rpx;

            .tag-left {
                text-align: center;
                font-size: 18rpx;
                padding: 4rpx 0;
                width: 48%;
                background: #1FA2FF;
                color: #ffffff;
                border-radius: 20rpx;
                position: absolute;
                z-index: 9;
                left: 0;
                border-right-width: 0;
            }

            .tag-right {
                font-size: 18rpx;
                padding: 4rpx 10rpx 4rpx 20rpx;
                text-align: center;
                width: 49%;
                background: #DDECFF;
                color: #3994FF;
                border-radius: 20rpx;
                position: absolute;
                z-index: 1;
                right: 0;
            }

            .content-tag {
                padding-top: 36rpx;
                padding-left: 2rpx;
                display: flex;
                justify-content: center;
                align-items: center;
                overflow-x: scroll;
                overflow-y: hidden;
                white-space: nowrap;

                .tag-enum {
                    display: inline-block;
                    margin-right: 8rpx;
                    background: #E9F3FF;
                    border-radius: 80rpx;
                    color: #30A9FF;
                    font-size: 18rpx;
                    padding: 4rpx 12rpx;
                }
            }
        }
    }

    .profile-text {
        display: flex;
        flex-direction: row;
        padding-top: 12px;
        align-items: center;
    }

    .profile-text view {
        display: flex;
        color: #888;
        font-size: 14px;
        flex-direction: row;
        align-items: center;
    }

    .profile-text .left {
        width: 75%;
    }

    .left image {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        margin-right: 4px;
    }

    .profile-text .right {
        text-align: right;
        color: #888;
        font-size: 14px;
    }

    .right image {
        width: 16px;
        height: 16px;
        background-size: cover;
        opacity: 0.5;
        color: rgba(0, 0, 0, 0.5);
        margin: 0 3px;
    }

    .detail {
        display: flex;
        position: relative;

        &-left {
            width: 80rpx;
            margin-right: 4rpx;

            image {
                height: 80rpx;
                width: 80rpx;
                border-radius: 80rpx;
            }
        }

        &-right {
            flex: 1;
            margin-left: 16rpx;
            padding-top: 4rpx;

            &-name {
                font-weight: 500;
                font-size: 30rpx;
                color: #777777;
            }

            &-content {
                flex: 1;
                justify-content: space-between;
                align-items: center;

                image {
                    height: 38rpx;
                    width: 38rpx;
                    position: relative;
                    left: 10rpx;
                    z-index: 2;
                    top: 2rpx;
                }

                &-staf {
                    font-weight: 400;
                    font-size: 24rpx;
                    color: #FFFFFF;
                    line-height: 18px;
                    height: 36rpx;
                    padding: 0 8rpx 0 16rpx;
                    background: linear-gradient(90deg, #1FA2FF 0%, #1FA2FF 100%);
                    border-radius: 0rpx 118rpx 118rpx 0rpx;
                    position: relative;
                    top: 4rpx;
                    right: 4rpx;
                    z-index: 1;
                }
            }

            &-tag {
                text-align: center;
                padding: 16rpx;
                border-radius: 80rpx;
                color: #30A9FF;
                font-size: 24rpx;
                font-weight: 600;
                position: relative;
                margin-left: 8rpx;
                background: #E9F2FF;
                position: absolute;
                right: 0;
                top: 10rpx;

                image {
                    width: 94rpx;
                    height: 48rpx;
                    transform: rotate(-10deg);
                    position: absolute;
                    left: 76rpx;
                    z-index: 1;
                    top: -5rpx;
                }
            }


            &-date {
                font-weight: 400;
                font-size: 24rpx;
                color: #AAAAAA;
            }
        }

    }

    .detail-tag {
        display: flex;
        justify-content: center;
        align-items: center;
        overflow-x: scroll;
        overflow-y: hidden;
        white-space: nowrap;

        &-enum {
            display: inline-block;
            margin-right: 28rpx;
            border-radius: 80rpx;
            padding: 4rpx 16rpx;
            font-weight: 400;
            font-size: 22rpx;
        }
    }

    .bg-red {
        background: #FFF3F4;
        color: #FF5365;
    }

    .bg-blue {
        font-size: 18rpx;
        background: #E9F2FF;
        color: #30A9FF;
        margin-right: 8rpx;
    }

    .titleImg1 {
        background: url('http://cdn.xiaodingdang1.com/2024/09/10/ed81923ccf964c99be6675c79f6c4497.png') no-repeat;
        width: 190rpx;
        height: 88rpx;
        background-size: 100% 100%;
        color: #ff4f61;
        font-size: 26rpx;
        font-weight: bold;
        text-align: center;
    }

    .label {
        flex: 1;
        justify-content: space-between;

        &-left {
            text-align: center;
            padding: 4rpx 16rpx 4rpx 26rpx;
            background: #ffe4e5;
            border-radius: 80rpx;
            color: #ff4f61;
            font-size: 24rpx;
            font-weight: 600;
            position: relative;
            margin-left: 8rpx;


            image {
                width: 46rpx;
                height: 46rpx;
                transform: rotate(-10deg);
                position: absolute;
                left: -16rpx;
                z-index: 1;
                bottom: 0rpx;
            }
        }

        &-right {
            padding: 4rpx 16rpx;
            background: #FFE4E5;
            border-radius: 80rpx;
            color: #FF4F61;
            font-size: 24rpx;
            font-weight: 600;
            position: relative;

            image {
                width: 82rpx;
                height: 64rpx;
                position: absolute;
                left: 25%;
                top: -15rpx;
            }
        }
    }

    .detail-ye {
        color: #FF6E00;
    }
</style>