import request from './request.js';

/**
 * 客服聊天相关API - 基于真实的艾尔母婴API接口
 * 会话控制器相关接口
 */

// ==================== 会话管理相关接口 ====================

/**
 * 获取或创建会话
 * GET mp/conversation/get-or-create
 */
export function getOrCreateConversation(data = {}) {
  return request({
    url: 'mp/conversation/get-or-create',
    method: 'GET',
    data,
  });
}

/**
 * 获取用户会话列表
 * GET mp/conversation/user-list
 * @param {Object} data - 请求参数
 * @param {number} data.userId - 用户ID
 */
export function getUserConversationList(data = {}) {
  return request({
    url: 'mp/conversation/user-list',
    method: 'GET',
    data,
  });
}

/**
 * 获取客服会话列表
 * GET mp/conversation/agent-list
 * @param {Object} data - 请求参数
 * @param {number} data.agentId - 客服ID
 */
export function getAgentConversationList(data = {}) {
  return request({
    url: 'mp/conversation/agent-list',
    method: 'GET',
    data,
  });
}

/**
 * 获取待接入会话列表
 * GET mp/conversation/pending-list
 */
export function getPendingConversationList(data = {}) {
  return request({
    url: 'mp/conversation/pending-list',
    method: 'GET',
    data,
  });
}

/**
 * 分配客服
 * POST mp/conversation/assign
 * @param {Object} data - 请求参数
 * @param {number} data.conversationId - 会话ID
 * @param {number} data.agentId - 客服ID
 */
export function assignAgent(data = {}) {
  return request({
    url: `mp/conversation/accept?conversationId=${data.conversationId}&agentId=${data.agentId}`,
    method: 'POST',
  });
}

/**
 * 转接会话
 * POST mp/conversation/transfer
 * @param {number} conversationId - 会话ID
 * @param {number} targetAgentId - 目标客服ID
 */
export function transferConversation(conversationId, targetAgentId) {
  return request({
    url: `mp/conversation/transfer?conversationId=${conversationId}&targetAgentId=${targetAgentId}`,
    method: 'POST',
  });
}

/**
 * 通过会话ID获取客服会话状态
 * GET mp/conversation/oneByAgent
 * @param {number} conversationId - 会话ID
 */
export function getConversationStatus(conversationId) {
  return request({
    url: 'mp/conversation/oneByAgent',
    method: 'GET',
    data: {
      conversationId,
    },
  });
}

/**
 * 获取在线客服列表
 * GET mp/agent/online
 */
export function getOnlineAgentList() {
  return request({
    url: 'mp/agent/online',
    method: 'GET',
  });
}

/**
 * 结束会话
 * POST mp/conversation/close
 * @param {Object} data - 请求参数
 * @param {number} data.conversationId - 会话ID
 * @param {string} data.closerType - 结束者类型 (USER, AGENT, SYSTEM)
 */
export function closeConversation(data = {}) {
  return request({
    url: 'mp/conversation/close',
    method: 'POST',
    data,
  });
}

// ==================== 消息相关接口 ====================

/**
 * 获取历史消息
 * GET mp/conversation/messages
 * @param {number} conversationId - 会话ID
 * @param {number} page - 页码，默认1
 * @param {number} size - 每页大小，默认20
 */
export function getConversationMessages(data = {}) {
  return request({
    url: 'mp/conversation/messages',
    method: 'GET',
    data,
  });
}

/**
 * 标记消息为已读
 * POST mp/conversation/read
 * @param {Object} data - 请求参数
 * @param {number} data.conversationId - 会话ID
 * @param {string} data.receiverType - 接收者类型 (USER, AGENT)
 */
export function markConversationAsRead(data = {}) {
  return request({
    url: `mp/conversation/read?conversationId=${data.conversationId}&receiverType=${data.receiverType}`,
    method: 'POST',
  });
}

/**
 * 提交会话评价
 * POST mp/conversation/evaluate
 * @param {Object} data - 请求参数
 * @param {number} data.conversationId - 会话ID
 * @param {number} data.score - 评分
 * @param {string} data.content - 评价内容（可选）
 */
export function evaluateConversation(data = {}) {
  return request({
    url: 'mp/conversation/evaluate',
    method: 'POST',
    data,
  });
}

// ==================== 会话详情和历史相关接口 ====================

/**
 * 获取会话详情
 * GET mp/session/detail
 * @param {Object} data - 请求参数
 * @param {string} data.sessionId - 会话ID
 */
export function getSessionDetail(data = {}) {
  return request({
    url: 'mp/session/detail',
    method: 'GET',
    data,
  });
}

/**
 * 获取会话历史消息
 * GET mp/session/messages
 * @param {Object} data - 请求参数
 * @param {string} data.sessionId - 会话ID
 * @param {number} data.page - 页码，默认1
 * @param {number} data.size - 每页大小，默认50
 */
export function getSessionMessages(data = {}) {
  return request({
    url: 'mp/session/messages',
    method: 'GET',
    data,
  });
}

/**
 * 获取历史会话列表
 * GET mp/session/history
 * @param {Object} data - 请求参数
 * @param {number} data.customerId - 客户ID
 * @param {number} data.page - 页码，默认1
 * @param {number} data.size - 每页大小，默认20
 */
export function getSessionHistory(data = {}) {
  return request({
    url: 'mp/session/history',
    method: 'GET',
    data,
  });
}

/**
 * 获取客服处理过的历史会话列表
 * GET mp/session/service-history
 * @param {Object} data - 请求参数
 * @param {number} data.page - 页码，默认1
 * @param {number} data.size - 每页大小，默认20
 */
export function getServiceHistorySessions(data = {}) {
  return request({
    url: 'mp/session/service-history',
    method: 'GET',
    data,
  });
}

/**
 * 标记消息为已读（会话级别）
 * POST mp/session/mark-read
 * @param {Object} data - 请求参数
 * @param {string} data.sessionId - 会话ID
 * @param {number} data.userType - 用户类型
 */
export function markSessionAsRead(data = {}) {
  return request({
    url: 'mp/session/mark-read',
    method: 'POST',
    data,
  });
}

// ==================== 客服管理相关接口 ====================

/**
 * 分页查询客服列表
 * GET /platform/customerService/page
 */
export function getCustomerServicePage(data = {}) {
  return request({
    url: 'platform/customerService/page',
    method: 'GET',
    data,
  });
}

/**
 * 新增修改客服
 * POST /platform/customerService/save
 */
export function saveCustomerService(data) {
  return request({
    url: 'platform/customerService/save',
    method: 'POST',
    data,
  });
}

/**
 * 查询微信用户-用于客服
 * GET /platform/customerService/queryUserCustomer
 * @param {Object} data - 请求参数
 * @param {string} data.tenantId - 租户ID
 */
export function queryUserCustomer(data = {}) {
  return request({
    url: 'platform/customerService/queryUserCustomer',
    method: 'GET',
    data,
  });
}

// ==================== 便捷封装函数 ====================

/**
 * 用户发起聊天（获取或创建会话）
 */
export function startUserChat(data = {}) {
  return getOrCreateConversation(data);
}

/**
 * 用户获取聊天历史
 * @param {Object} data - 请求参数
 * @param {number} data.userId - 用户ID
 */
export function getUserChatHistory(data = {}) {
  return getUserConversationList(data);
}

/**
 * 客服获取待处理会话
 */
export function getServicePendingChats(data = {}) {
  return getPendingConversationList(data);
}

/**
 * 客服获取自己的会话列表
 * @param {Object} data - 请求参数
 * @param {number} data.agentId - 客服ID
 */
export function getServiceChatList(data = {}) {
  return getAgentConversationList(data);
}

/**
 * 发送消息（通过WebSocket发送）
 * 注意：实际发送消息应该通过WebSocket连接进行
 * @param {Object} data - 消息数据
 */
export function sendMessage(data = {}) {
  // 消息发送通过WebSocket进行，这里只是一个占位符
  // 实际使用时应该调用WebSocket管理器的发送方法
  console.warn('消息发送应该通过WebSocket连接进行', data);
  return Promise.resolve({
    success: false,
    message: '请使用WebSocket发送消息',
  });
}

/**
 * 用户结束聊天
 * @param {Object} data - 请求参数
 * @param {number} data.conversationId - 会话ID
 */
export function endUserChat(data = {}) {
  return closeConversation({ ...data, closerType: 'USER' });
}

/**
 * 客服结束聊天
 * @param {Object} data - 请求参数
 * @param {number} data.conversationId - 会话ID
 */
export function endServiceChat(data = {}) {
  return closeConversation({ ...data, closerType: 'AGENT' });
}

/**
 * 系统结束聊天
 * @param {Object} data - 请求参数
 * @param {number} data.conversationId - 会话ID
 */
export function endSystemChat(data = {}) {
  return closeConversation({ ...data, closerType: 'SYSTEM' });
}

/**
 * 用户标记消息已读
 * @param {Object} data - 请求参数
 * @param {number} data.conversationId - 会话ID
 */
export function markUserMessageRead(data = {}) {
  return markConversationAsRead({ ...data, receiverType: 'USER' });
}

/**
 * 客服标记消息已读
 * @param {Object} data - 请求参数
 * @param {number} data.conversationId - 会话ID
 */
export function markAgentMessageRead(data = {}) {
  return markConversationAsRead({ ...data, receiverType: 'AGENT' });
}

/**
 * 用户评价客服服务
 * @param {Object} data - 请求参数
 * @param {number} data.conversationId - 会话ID
 * @param {number} data.score - 评分 (1-5)
 * @param {string} data.content - 评价内容
 */
export function rateCustomerService(data = {}) {
  return evaluateConversation(data);
}
