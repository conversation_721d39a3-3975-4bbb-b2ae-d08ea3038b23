/**
 * 路径辅助工具
 * 提供项目路径相关的工具函数
 */

const path = require('path');

/**
 * 根据AppID生成私钥文件路径
 * @param {string} appid - 微信小程序AppID
 * @param {string} baseDir - 私钥文件基础目录，默认为 '../private'
 * @returns {string} 私钥文件路径
 */
function generatePrivateKeyPath(appid, baseDir = '../private') {
  if (!appid || typeof appid !== 'string') {
    throw new Error('AppID 不能为空且必须是字符串');
  }

  // 验证AppID格式
  if (!/^wx[a-f0-9]{16}$/.test(appid)) {
    throw new Error('AppID 格式不正确，应为 wx 开头的 18 位字符串');
  }

  return path.join(baseDir, `private.${appid}.key`);
}

/**
 * 根据AppID生成项目路径
 * @param {string} baseDir - 项目基础目录，默认为 '../../dist/build/mp-weixin'
 * @returns {string} 项目路径
 */
function generateProjectPath(baseDir = '../../dist/build/mp-weixin') {
  return baseDir;
}

/**
 * 验证私钥文件路径格式
 * @param {string} privateKeyPath - 私钥文件路径
 * @returns {Object} 验证结果
 */
function validatePrivateKeyPath(privateKeyPath) {
  if (!privateKeyPath || typeof privateKeyPath !== 'string') {
    return {
      valid: false,
      error: '私钥文件路径不能为空'
    };
  }

  // 检查是否符合命名规范
  const filename = path.basename(privateKeyPath);
  const privateKeyPattern = /^private\.wx[a-f0-9]{16}\.key$/;
  
  if (!privateKeyPattern.test(filename)) {
    return {
      valid: false,
      error: '私钥文件名格式不正确，应为 private.wx{appid}.key 格式'
    };
  }

  return { valid: true };
}

/**
 * 从私钥文件路径提取AppID
 * @param {string} privateKeyPath - 私钥文件路径
 * @returns {string|null} AppID，如果提取失败返回null
 */
function extractAppIdFromPrivateKeyPath(privateKeyPath) {
  if (!privateKeyPath || typeof privateKeyPath !== 'string') {
    return null;
  }

  const filename = path.basename(privateKeyPath);
  const match = filename.match(/^private\.(wx[a-f0-9]{16})\.key$/);
  
  return match ? match[1] : null;
}

/**
 * 生成项目配置模板
 * @param {Object} options - 配置选项
 * @param {string} options.projectId - 项目ID
 * @param {string} options.name - 项目名称
 * @param {string} options.appid - AppID
 * @param {string} options.defaultTenantId - 默认租户ID
 * @returns {Object} 项目配置模板
 */
function generateProjectTemplate(options) {
  const { projectId, name, appid, defaultTenantId = '194338' } = options;

  if (!projectId || !name || !appid) {
    throw new Error('项目ID、名称和AppID都是必需的');
  }

  return {
    [projectId]: {
      name,
      appid,
      projectPath: generateProjectPath(),
      privateKeyPath: generatePrivateKeyPath(appid),
      defaultTenantId,
      ignores: ['node_modules/**/*'],
      environments: {
        develop: {
          name: '开发版',
          desc: '开发环境版本',
          buildCommand: 'pnpm run build:mp-weixin --mode test',
          tenantId: defaultTenantId
        },
        trial: {
          name: '体验版',
          desc: '体验环境版本',
          buildCommand: 'pnpm run build:mp-weixin --mode trial',
          tenantId: defaultTenantId
        },
        release: {
          name: '正式版',
          desc: '生产环境版本',
          buildCommand: 'pnpm run build:mp-weixin --mode production',
          tenantId: defaultTenantId
        }
      }
    }
  };
}

/**
 * 批量更新项目配置的私钥路径
 * @param {Object} projects - 项目配置对象
 * @returns {Object} 更新后的项目配置
 */
function updatePrivateKeyPaths(projects) {
  const updatedProjects = { ...projects };

  Object.keys(updatedProjects).forEach(projectId => {
    const project = updatedProjects[projectId];
    if (project.appid) {
      // 根据AppID生成新的私钥路径
      project.privateKeyPath = generatePrivateKeyPath(project.appid);
    }
  });

  return updatedProjects;
}

module.exports = {
  generatePrivateKeyPath,
  generateProjectPath,
  validatePrivateKeyPath,
  extractAppIdFromPrivateKeyPath,
  generateProjectTemplate,
  updatePrivateKeyPaths
};
