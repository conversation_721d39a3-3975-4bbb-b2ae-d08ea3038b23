import request from './request';

/**
 * 微信订阅消息模板相关API
 */

/**
 * 根据模板类型获取微信订阅消息模板
 * @param {Array<string>} templateTypes - 模板类型数组
 * @returns {Promise<Object>} - API响应
 */
export const getTemplatesByTypes = (templateTypes) => {
  return request({
    url: 'mp/wechat_template_config/getTemplatesByTypes',
    method: 'GET',
    data: {
      types: templateTypes.join(','),
    },
  });
};

// 模板类型常量
export const TEMPLATE_TYPES = {
  CUSTOMER_SERVICE_MESSAGE_UNREAD: 'CUSTOMER_SERVICE_MESSAGE_UNREAD', // 消息未读
  CLIENT_CONSULTATION_REMINDER: 'CLIENT_CONSULTATION_REMINDER', // 咨询提醒
};

export default {
  getTemplatesByTypes,
  TEMPLATE_TYPES,
};
