.prosonal {
    /* background: url("http://cdn.xiaodingdang1.com/Rectangle%206595%403x%20%281%29.png") no-repeat; */
    background-size: 100% 478rpx;
}

.prosonal {
    padding: 394rpx 0rpx 0rpx;
}

.next {
    display: flex;
    position: fixed;
    top: 120rpx;
    width: 100%;
    margin-left: 24rpx;
}

.headImg {
    width: 41%;
}

.headTitle {
    font-size: 34rpx;
    color: #ffffff;
    font-weight: bold;
}

.content {
    background: #ffffff;
    border-top-left-radius: 80rpx;
    border-top-right-radius: 80rpx;
    padding: 52rpx 24rpx;
}

.title {
    color: #333333;
    font-size: 36rpx;
    font-weight: bold;
}

.title1 {
    color: #777777;
    font-size: 28rpx;
    margin: 20rpx 0;
}

.blank {
    height: 150rpx;
}

.tag {
    position: fixed;
    left: 10rpx;
    bottom: 400rpx;
}

.tag view {
    margin-bottom: 10rpx;
}