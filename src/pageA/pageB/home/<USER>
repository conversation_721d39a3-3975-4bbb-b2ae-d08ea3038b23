<template>
    <view>
        <navigation color="#ddd" title='' :background="bgColor" :isSowArrow='true'></navigation>
        <view class="main">
            <view class="top"></view>
            <view class="img"
                :style="{'backgroundImage': 'url('+ facilities[0].backgroundPhotos[0] +')'}"></view>
            <view class="head">
                <view class="head-title">
                    <view class="head-title-left">{{ clubInfo.clubName }}</view>
                    <!-- <vie class="head-title-right">已有162人咨询</vie> -->
                </view>
                <view class="head-tag">
                    <view class="head-tag-enum" v-for="(res, i) in clubInfo.clubTagNames" :key="i"> {{ res }}</view>
                </view>
                <view class="head-line"></view>
                <view class="head-address">
                    <image src="http://cdn.xiaodingdang1.com/2024/11/22/d476d079d6c14b07978ce845c6944827.png"></image>
                    {{ clubInfo.locationCity ? clubInfo.locationCity : '' }}{{ clubInfo.locationAddress }}
                </view>
            </view>
            <view class="introduce">
                <view class="flex">
                    <view class="head1_1"></view>
                    <view class="head1_2">简介</view>
                </view>
                <view class="briefContent">
                    {{ facilities[currentIndex]?.facilityDescription }}
                </view>
            </view>
            <view class="container">
                <view class="container-tabbar">
                    <scroll-view :scroll-x="true" class="tab-h">
                        <view :class="index == currentIndex ? 'title-sel-selected' : 'title-sel'"
                            @tap="titleClick(index)" :data-name="item.facilityName" v-for="(item, index) in facilities"
                            :key="index">
                            <view>
                                {{ item.facilityName }}
                            </view>
                            <view class="line-style"></view>
                        </view>
                    </scroll-view>
                </view>
                <view class="container-content">
                    <view v-for="(e, i) in facilities[currentIndex]?.banner_list" :key="i">
                        <video class="detail-banner-img" v-if="e.type == 'video'"
                            :data-src="facilities[currentIndex]?.banner_list" :src="e.url" @tap="preview"
                            :data-index="i"></video>
<!--                        <image class="detail-banner-img" v-else :src="e.url"
                            :data-src="facilities[currentIndex]?.banner_list" @tap="preview" :data-index="i"></image> -->
							<view class="detail-banner-img" v-else>
								<u-lazy-load :duration="0" :image="e.url" border-radius="20"  effect="fade" loading-img="" error-img=""
								:data-src="facilities[currentIndex]?.banner_list" @tap="preview" :data-index="i"></u-lazy-load>
							</view>
                    </view>
                </view>
            </view>
        </view>
		<mask-dialog></mask-dialog>
		<rightMenuBarVue :scrollTop="scrollTop"></rightMenuBarVue>
    </view>
</template>
<script setup>
	import rightMenuBarVue from "@/components/rightMenuBar.vue";
    import {
        onShow,
        onHide,
        onLoad,
        onPageScroll,
        onShareAppMessage,
        onShareTimeline
    } from "@dcloudio/uni-app";
    import {
        ref,
        reactive,
        onMounted,
        computed,
        getCurrentInstance
    } from "vue";
    const instance = getCurrentInstance();
    const App = instance.appContext.config.globalProperties
    onShareAppMessage(() => {

    })
    onShareTimeline(() => {

    })
    const bgColor = ref('rgba(0,0,0,0)')
	const scrollTop = ref("");
	onPageScroll((e) => {
	  scrollTop.value=e.scrollTop
	});
    onLoad((options) => {
        console.log('optionsssss', options)
        pageInit()
    })
    const pageInit = async () => {
        try {
            await clubInfoFun()
            await facilitiesListFun()
        } catch {

        }
    }
    const facilitiesList = reactive({})
    const facilities = reactive([])
    const clubInfo = reactive({})
    const currentIndex = ref(0)
    const facilitiesListFun = async () => {
        //查询会所设施列表
        const res = await App.$axios.get(App.$api.facilitiesList)
        if (res.data.code == 200) {
            let data = res.data.data;
            for (let i = 0; i < data.length; i++) {
                data[i].banner_list = [];
                for (let j = 0; j < data[i].videos.length; j++) {
                    console.log(data);
                    let datas = {
                        type: 'video',
                        url: data[i].videos[j]
                    };
                    data[i].banner_list.push(datas);
                }
                for (let f = 0; f < data[i].facilityPhotos.length; f++) {
                    let datas = {
                        type: 'image',
                        url: data[i].facilityPhotos[f]
                    };
                    data[i].banner_list.push(datas);
                }
            }
            Object.assign(facilitiesList, data[0])
            Object.assign(facilities, data)
        }
    }
    const clubInfoFun = async () => {
        const res = await App.$axios.get(App.$api.clubInfo)
        if (res.data.code == 200) {
            Object.assign(clubInfo, res.data.data)
        }
    }
    //用户点击tab时调用
    const titleClick = async (index) => {
        currentIndex.value = index
        // Object.assign(facilitiesList, facilities[index])
        // console.log('facilitiesList', facilitiesList)
    }
    const preview = (event) => {
        console.log(event);
        let maparr = event.currentTarget.dataset.src;
        let index = event.currentTarget.dataset.index;
        // 既有视频又有图片用这个
        console.log(maparr);
        uni.previewMedia({
            sources: maparr,
            // 官方文档 : 素材展示列表 是个数组对象 一个url链接 一个type 判断这是视频还是图片
            // 接收image 和 video 所以处理数据时 最好给个type 指定 image 或者 video
            current: index
            // 当前显示的资源序号
        });
    }
</script>
<style lang="less" scoped>
    @import './device.less';
</style>