#!/usr/bin/env node

/**
 * 构建配置注入器
 * 在构建过程中注入项目特定的配置（租户ID、AppID等）
 */

const fs = require('fs');
const path = require('path');

class BuildConfigInjector {
  constructor() {
    this.rootPath = path.resolve(__dirname, '../..');
    this.configFiles = [
      'src/App.vue',
      'src/utils/config.js'
    ];
  }

  /**
   * 注入构建配置
   * @param {Object} options - 配置选项
   * @param {string} options.tenantId - 租户ID
   * @param {string} options.appid - 小程序AppID
   * @param {string} options.projectName - 项目名称
   * @param {string} options.environment - 环境名称
   */
  inject(options) {
    const { tenantId, appid, projectName, environment } = options;
    
    console.log(`🔧 开始注入构建配置...`);
    console.log(`   项目: ${projectName}`);
    console.log(`   环境: ${environment}`);
    console.log(`   租户ID: ${tenantId}`);
    console.log(`   AppID: ${appid}`);

    // 创建配置备份
    this.createBackups();

    try {
      // 注入App.vue中的默认租户ID
      this.injectAppVueConfig(tenantId);
      
      // 注入config.js中的配置
      this.injectConfigJs(tenantId, appid);
      
      console.log(`✅ 构建配置注入完成`);
    } catch (error) {
      console.error(`❌ 构建配置注入失败:`, error.message);
      // 恢复备份
      this.restoreBackups();
      throw error;
    }
  }

  /**
   * 创建配置文件备份
   */
  createBackups() {
    console.log(`📦 创建配置文件备份...`);
    
    this.configFiles.forEach(file => {
      const filePath = path.join(this.rootPath, file);
      const backupPath = `${filePath}.backup`;
      
      if (fs.existsSync(filePath)) {
        fs.copyFileSync(filePath, backupPath);
        console.log(`   备份: ${file} -> ${file}.backup`);
      }
    });
  }

  /**
   * 恢复配置文件备份
   */
  restoreBackups() {
    console.log(`🔄 恢复配置文件备份...`);
    
    this.configFiles.forEach(file => {
      const filePath = path.join(this.rootPath, file);
      const backupPath = `${filePath}.backup`;
      
      if (fs.existsSync(backupPath)) {
        fs.copyFileSync(backupPath, filePath);
        fs.unlinkSync(backupPath);
        console.log(`   恢复: ${file}.backup -> ${file}`);
      }
    });
  }

  /**
   * 清理备份文件
   */
  cleanupBackups() {
    console.log(`🧹 清理备份文件...`);
    
    this.configFiles.forEach(file => {
      const backupPath = path.join(this.rootPath, `${file}.backup`);
      
      if (fs.existsSync(backupPath)) {
        fs.unlinkSync(backupPath);
        console.log(`   删除: ${file}.backup`);
      }
    });
  }

  /**
   * 注入App.vue中的默认租户ID
   * @param {string} tenantId - 租户ID
   */
  injectAppVueConfig(tenantId) {
    const appVuePath = path.join(this.rootPath, 'src/App.vue');

    if (!fs.existsSync(appVuePath)) {
      console.warn(`⚠️  App.vue 文件不存在，跳过注入`);
      return;
    }

    let content = fs.readFileSync(appVuePath, 'utf8');

    // 替换默认租户ID，支持多种格式
    const tenantIdRegex = /const DEFAULT_TENANT_ID = uni\.getStorageSync\(["']tenantId["']\)\s*\?\s*uni\.getStorageSync\(["']tenantId["']\)\s*:\s*["'][^"']*["'];/;
    const newTenantIdLine = `const DEFAULT_TENANT_ID = uni.getStorageSync("tenantId")
  ? uni.getStorageSync("tenantId")
  : "${tenantId}";`;

    if (tenantIdRegex.test(content)) {
      content = content.replace(tenantIdRegex, newTenantIdLine);
      console.log(`   ✅ 已更新 App.vue 中的默认租户ID: ${tenantId}`);
    } else {
      console.warn(`   ⚠️  未找到 App.vue 中的默认租户ID配置`);
      // 尝试查找更简单的模式
      const simpleRegex = /:\s*["'][^"']*["'];/;
      const matches = content.match(/const DEFAULT_TENANT_ID.*?["'][^"']*["'];/);
      if (matches) {
        console.warn(`   找到的配置: ${matches[0]}`);
      }
    }

    fs.writeFileSync(appVuePath, content, 'utf8');
  }

  /**
   * 注入config.js中的配置
   * @param {string} tenantId - 租户ID
   * @param {string} appid - AppID
   */
  injectConfigJs(tenantId, appid) {
    const configPath = path.join(this.rootPath, 'src/utils/config.js');

    if (!fs.existsSync(configPath)) {
      console.warn(`⚠️  config.js 文件不存在，跳过注入`);
      return;
    }

    let content = fs.readFileSync(configPath, 'utf8');

    // 替换CLIENT_CONFIG中的TENANT_ID，支持单引号和双引号
    const tenantIdRegex = /TENANT_ID:\s*['"][^'"]*['"]/;
    if (tenantIdRegex.test(content)) {
      content = content.replace(tenantIdRegex, `TENANT_ID: '${tenantId}'`);
      console.log(`   ✅ 已更新 config.js 中的租户ID: ${tenantId}`);
    } else {
      console.warn(`   ⚠️  未找到 config.js 中的租户ID配置`);
      console.warn(`   当前文件内容预览:`);
      console.warn(content.substring(0, 200) + '...');
    }

    fs.writeFileSync(configPath, content, 'utf8');
  }
}

// 命令行调用支持
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.length < 4) {
    console.error('使用方法: node build-config-injector.js <tenantId> <appid> <projectName> <environment>');
    console.error('示例: node build-config-injector.js 194338 wx1df89e340decbc7a main develop');
    process.exit(1);
  }

  const [tenantId, appid, projectName, environment] = args;
  const injector = new BuildConfigInjector();
  
  try {
    injector.inject({ tenantId, appid, projectName, environment });
  } catch (error) {
    console.error('配置注入失败:', error.message);
    process.exit(1);
  }
}

module.exports = BuildConfigInjector;
