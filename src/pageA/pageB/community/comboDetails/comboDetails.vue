<template>
	<view class="headimg">
		<image :src="infoImg" mode=""></image>
	</view>
	<view class="main">
		<view class="foodIntroduce5">
		<view class="foodIntroduce">
			<view class="foodIntroduce1">
				<view class="foodIntroduce1_1">{{infoList.productName}}</view>
				<view class="foodIntroduce1_2">已有{{infoList.consultNumber}}人咨询</view>
			</view>
			<view class="foodIntroduce2">
				<view class="foodIntroduce2_3">
					<view class="foodIntroduce2_1">
						{{infoList.productDescription}}
						<!-- <view >
							{{item}}
						</view>
						<view class="foodIntroduce2_4" v-if="index != infoList.tag.length - 1">|</view> -->
					</view>
				</view>
				<view class="foodIntroduce2_2">
					<button class="image-text-button" open-type="share">
						<img src="http://cdn.xiaodingdang1.com/2025/04/23/862e660bce0d4565bba27ff8c6f8ab17.png"
							alt="Icon" class="button-icon"> 分享
					</button>
				</view>
			</view>
		</view>
		<view class="foodMessage">
			<view class="foodMessage1">
				<image :src="bgBackground" mode="aspectFill"
					style="width: 100rpx;height: 100rpx;border-radius: 8rpx;"></image>
				<view class="foodMessage1_1">
					<view class="foodMessage1_2">
						{{clubName}}
					</view>
					<view class="foodMessage1_3">
						企业认证
					</view>
				</view>
			</view>
			<view class="foodMessage2" @click="nextStore">
				进店逛逛
			</view>
		</view>
		<view class="foodImg">
			<view class="foodImg1">
				<image :src="item" mode="widthFix" v-for="item,index in infoList.productDetails" :key="index" @tap="preview"
                    :data-index="index" :data-img="infoList.productDetails"></image>
			</view>
		</view>
		<view class="tabList5">
			<view class="heads" id="head">
				<view class="head1">
					<view id="head1_1"></view>
					<view class="head1_2">膳食套餐</view>
				</view>
				<view class="head2"></view>
			</view>
			<view class="tabList">
				<view class="tabList1" v-for="(item, index) in productDeliveryPageList" :key="index" @tap="priceBtn" :data-deliveryid="item.deliveryId"
                                    :data-producttype="item.productType">
					<image :src="item.productPhotoUrl[imgIndex]" mode="" style="width: 340rpx; height: 340rpx; border-radius: 20rpx" />

					<view class="tabList1_1">
						{{ item.productName }}
					</view>

					<view class="tabList1_2">
						{{ item.tag[imgIndex] }}  {{ item.tag[1] }}
					</view>
				</view>
			</view>
		</view>
		<view class="blank"></view>
		<new-bottom :from="SessionFrom" :type="2" :id="infoList.deliveryId"></new-bottom>
		<mask-dialog></mask-dialog>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				imgIndex:0,
				infoList: '',
				clubName:'',
				bgBackground:'',
				productDeliveryPageList:[],
				infoImg:'',
				deliveryId:''
			};
		},
		/**
		 * 生命周期函数--监听页面加载
		 */
		onLoad(options) {
			this.deliveryId=options.deliveryId
			this.clubName=uni.getStorageSync('clubName')
			this.bgBackground=uni.getStorageSync('bgBackground')
			this.productDeliveryPage()
		},
		/**
		 * 生命周期函数--监听页面初次渲染完成
		 */
		onReady() {},
		/**
		 * 生命周期函数--监听页面显示
		 */
		onShow() {
			this.suiteInfo(this.deliveryId)
		},
		/**
		 * 生命周期函数--监听页面隐藏
		 */
		onHide() {},
		/**
		 * 生命周期函数--监听页面卸载
		 */
		onUnload() {},
		/**
		 * 页面相关事件处理函数--监听用户下拉动作
		 */
		onPullDownRefresh() {},
		/**
		 * 页面上拉触底事件的处理函数
		 */
		onReachBottom() {},
		/**
		 * 用户点击右上角分享
		 */
		onShareAppMessage() {},

		onShareTimeline() {},
		methods: {
			suiteInfo(deliveryId) {
			    //查询优惠套餐列表
			    let data = {
			        deliveryId: deliveryId
			    };
			    this.$axios.get(this.$api.productDelivery, data).then((res) => {
			        if (res.data.code == 200) {
			            let data = res.data.data;
			            let list = [];
			            data.videos.forEach((item) => {
			                let data = {
			                    type: 'video',
			                    url: item
			                };
			                list.push(data);
			            });
			            data.productPhotoUrl.forEach((item) => {
			                let data = {
			                    type: 'image',
			                    url: item
			                };
			                list.push(data);
			            });
			            this.setData({
							infoImg:data.productPhotoUrl[this.imgIndex],
			                 infoList: data,
			                banner_list: list,
			                imgList: data.productPhotoUrl
			            });
			        }
			    });
			},
			priceBtn(e) {
			    //咨询
			    let deliveryId = e.currentTarget.dataset.deliveryid;
			    let productType = e.currentTarget.dataset.producttype;
			    uni.navigateTo({
			        url: '/pageA/pageB/community/comboDetails/comboDetails?deliveryId=' + deliveryId +
			            '&productType=' + productType
			    });
			    // this.$jumpPage('food/detail', {
			    //     deliveryId: deliveryId,
			    //     productType: productType
			    // })
			},
			  productDeliveryPage() {
			                //查询膳食套餐列表
			                let data = {
			                    pageSize: 4,
			                    pageNum: 1
			                };
			                this.$axios.get(this.$api.productDeliveryPage, data).then((res) => {
			                    if (res.data.code == 200) {
			                        this.setData({
			                            productDeliveryPageList: res.data.data.rows
			                        });
			                    }
			                });
			            },
			nextStore(){
			 uni.switchTab({
			 	url:'/pageA/home'
			 })
			},
		preview(event) {
						let maparr = event.currentTarget.dataset.img;
						let index = event.currentTarget.dataset.index;
						// 既有视频又有图片用这个
						uni.previewImage({
							urls: maparr,
							// 官方文档 : 素材展示列表 是个数组对象 一个url链接 一个type 判断这是视频还是图片
							// 接收image 和 video 所以处理数据时 最好给个type 指定 image 或者 video
							current: index
							// 当前显示的资源序号
						});
					},

		}
	};
</script>

<style lang="less" scoped>
	@import './comboDetails.less';
</style>