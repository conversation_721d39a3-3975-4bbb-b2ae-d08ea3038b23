/**
 * 海报类型定义
 * 定义不同海报类型的接口和配置
 */

/**
 * 海报类型枚举
 */
export const POSTER_TYPES = {
  DYNAMICS: 'dynamics',      // 动态海报
  STAFF: 'staff',           // 员工海报
  CREATE: 'create',         // 创建海报
  SENDING: 'sending',       // 发送海报
   MENU: 'menu',       // 膳食海报
};

/**
 * 截图方法枚举
 */
export const CAPTURE_METHODS = {
  SNAPSHOT: 'snapshot',     // 使用 snapshot 组件
  CANVAS: 'canvas',         // 使用 canvas
};

/**
 * 分享方法枚举
 */
export const SHARE_METHODS = {
  MENU: 'menu',            // 微信分享菜单
  ALBUM: 'album',          // 保存到相册
  MOMENTS: 'moments',      // 分享到朋友圈
};

/**
 * 动态海报数据结构
 * @typedef {Object} DynamicsPosterData
 * @property {string} logo - 俱乐部Logo
 * @property {string} posterSlogan - 海报标语
 * @property {string} avatar - 用户头像
 * @property {string} nickname - 用户昵称
 * @property {string} nodeName - 节点名称
 * @property {string} staffPost - 员工职位
 * @property {string} content - 内容文本
 * @property {string[]} contentPhotos - 内容图片数组
 * @property {string[]} videos - 视频数组
 */

/**
 * 员工海报数据结构
 * @typedef {Object} StaffPosterData
 * @property {string} logo - 俱乐部Logo
 * @property {string} posterSlogan - 海报标语
 * @property {string} avatar - 员工头像
 * @property {string} nickname - 员工昵称
 * @property {string} staffPost - 员工职位
 * @property {string} content - 内容文本
 * @property {string[]} contentPhotos - 内容图片数组
 * @property {string} nodeName - 节点名称
 */

/**
 * 创建海报数据结构
 * @typedef {Object} CreatePosterData
 * @property {string} logo - 俱乐部Logo
 * @property {string} posterSlogan - 海报标语
 * @property {string} staffPhotos - 员工照片
 * @property {string} staffName - 员工姓名
 * @property {string} staffPost - 员工职位
 * @property {string} content - 内容文本
 * @property {string[]} contentPhotos - 内容图片数组
 * @property {string} serviceStartDate - 服务开始日期
 * @property {string} count - 评论数量描述
 */

/**
 * 发送海报数据结构
 * @typedef {Object} SendingPosterData
 * @property {string} logo - 俱乐部Logo
 * @property {string} posterSlogan - 海报标语
 * @property {string} avatar - 用户头像
 * @property {string} nickname - 用户昵称
 * @property {string} staffPost - 员工职位
 * @property {string} content - 内容文本
 * @property {string[]} contentPhotos - 内容图片数组
 * @property {string[]} videos - 视频数组
 * @property {string} nodeName - 节点名称
 */

/**
 * 海报配置
 * @typedef {Object} PosterConfig
 * @property {string} type - 海报类型
 * @property {string} title - 页面标题
 * @property {string} buttonText - 按钮文本
 * @property {string} snapshotId - snapshot 元素ID
 * @property {string} captureMethod - 截图方法
 * @property {string} shareMethod - 分享方法
 * @property {Object} captureOptions - 截图选项
 * @property {Object} shareOptions - 分享选项
 */

/**
 * 海报类型配置映射
 */
export const POSTER_CONFIGS = {
  [POSTER_TYPES.DYNAMICS]: {
    type: POSTER_TYPES.DYNAMICS,
    title: '动态海报',
    buttonText: '生成海报',
    snapshotId: 'dynamics-poster-snapshot',
    captureMethod: CAPTURE_METHODS.SNAPSHOT,
    shareMethod: SHARE_METHODS.MENU,
    captureOptions: {
      type: 'arraybuffer',
      format: 'png',
      delay: 500,
    },
    shareOptions: {
      withShareTicket: true,
      navigateBack: true,
    },
    qrCodePath: 'pageA/pageB/dynamics/dynamicsdetail',
  },
  
  [POSTER_TYPES.STAFF]: {
    type: POSTER_TYPES.STAFF,
    title: '员工海报',
    buttonText: '生成海报',
    snapshotId: 'staff-poster-snapshot',
    captureMethod: CAPTURE_METHODS.CANVAS,
    shareMethod: SHARE_METHODS.MENU,
    captureOptions: {
      delay: 500,
    },
    shareOptions: {
      withShareTicket: true,
      navigateBack: true,
    },
    qrCodePath: 'pageA/pageB/community/staffdetail',
  },
  
  [POSTER_TYPES.CREATE]: {
    type: POSTER_TYPES.CREATE,
    title: '创建海报',
    buttonText: '生成海报',
    snapshotId: 'create-poster-snapshot',
    captureMethod: CAPTURE_METHODS.CANVAS,
    shareMethod: SHARE_METHODS.MENU,
    captureOptions: {
      delay: 500,
    },
    shareOptions: {
      withShareTicket: true,
      navigateBack: true,
    },
    qrCodePath: 'pageA/pageB/community/note/notedetail',
  },
  [POSTER_TYPES.MENU]: {
      type: POSTER_TYPES.MENU,
      title: '膳食海报',
      buttonText: '生成海报',
      snapshotId: 'staff-poster-snapshot',
      captureMethod: CAPTURE_METHODS.CANVAS,
      shareMethod: SHARE_METHODS.MENU,
      captureOptions: {
        delay: 500,
      },
      shareOptions: {
        withShareTicket: true,
        navigateBack: true,
      },
      qrCodePath: 'pageA/pageB/home/<USER>',
    },
  
  [POSTER_TYPES.SENDING]: {
    type: POSTER_TYPES.SENDING,
    title: '发送海报',
    buttonText: '生成海报',
    snapshotId: 'sending-poster-snapshot',
    captureMethod: CAPTURE_METHODS.CANVAS,
    shareMethod: SHARE_METHODS.MENU,
    captureOptions: {
      delay: 500,
    },
    shareOptions: {
      withShareTicket: true,
      navigateBack: true,
    },
    qrCodePath: 'pageA/pageB/community/sending',
  },
};

/**
 * 获取海报配置
 * @param {string} type - 海报类型
 * @returns {PosterConfig} 海报配置
 */
export function getPosterConfig(type) {
  const config = POSTER_CONFIGS[type];
  if (!config) {
    throw new Error(`不支持的海报类型: ${type}`);
  }
  return { ...config };
}

/**
 * 验证海报数据
 * @param {string} type - 海报类型
 * @param {Object} data - 海报数据
 * @returns {boolean} 验证结果
 */
export function validatePosterData(type, data) {
  if (!data || typeof data !== 'object') {
    return false;
  }

  switch (type) {
    case POSTER_TYPES.DYNAMICS:
      return validateDynamicsPosterData(data);
    case POSTER_TYPES.STAFF:
      return validateStaffPosterData(data);
    default:
      return true; // 其他类型暂时不验证
  }
}

/**
 * 验证动态海报数据
 * @param {DynamicsPosterData} data - 动态海报数据
 * @returns {boolean} 验证结果
 */
function validateDynamicsPosterData(data) {
  // 基本字段验证
  const requiredFields = ['nickname'];
  return requiredFields.every(field => data[field]);
}

/**
 * 验证员工海报数据
 * @param {StaffPosterData} data - 员工海报数据
 * @returns {boolean} 验证结果
 */
function validateStaffPosterData(data) {
  // 基本字段验证
  const requiredFields = ['staffName'];
  return requiredFields.every(field => data[field]);
}

/**
 * 创建默认海报数据
 * @param {string} type - 海报类型
 * @returns {Object} 默认海报数据
 */
/**
 * 海报模板映射
 * 定义每种海报类型对应的模板组件名称
 */
export const POSTER_TEMPLATES = {
  [POSTER_TYPES.DYNAMICS]: 'DynamicsPosterTemplate',
  [POSTER_TYPES.STAFF]: 'StaffPosterTemplate',
  [POSTER_TYPES.CREATE]: 'CreatePosterTemplate',
  [POSTER_TYPES.SENDING]: 'SendingPosterTemplate',
   [POSTER_TYPES.MENU]: 'menuPosterTemplate',
};

/**
 * 获取海报模板名称
 * @param {string} type - 海报类型
 * @returns {string} 模板组件名称
 */
export function getPosterTemplate(type) {
  const template = POSTER_TEMPLATES[type];
  if (!template) {
    throw new Error(`不支持的海报类型: ${type}`);
  }
  return template;
}

export function createDefaultPosterData(type) {
  switch (type) {
    case POSTER_TYPES.DYNAMICS:
      return {
        logo: '',
        posterSlogan: '',
        avatar: '',
        nickname: '',
        nodeName: '',
        staffPost: '',
        content: '',
        contentPhotos: [],
        videos: [],
      };
    case POSTER_TYPES.STAFF:
      return {
        logo: '',
        posterSlogan: '',
        avatar: '',
        nickname: '',
        staffPost: '',
        content: '',
        contentPhotos: [],
        nodeName: '',
      };
    case POSTER_TYPES.CREATE:
      return {
        logo: '',
        posterSlogan: '',
        staffPhotos: '',
        staffName: '',
        staffPost: '',
        content: '',
        contentPhotos: [],
        serviceStartDate: '',
        count: '',
      };
    case POSTER_TYPES.SENDING:
      return {
        logo: '',
        posterSlogan: '',
        avatar: '',
        nickname: '',
        staffPost: '',
        content: '',
        contentPhotos: [],
        videos: [],
        nodeName: '',
      };
    default:
      return {};
  }
}
