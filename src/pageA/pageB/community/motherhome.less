.page-motherhome {
    background: #F3F3F3;
    min-height: 100vh;
}
.top{
    height: 376rpx;
    background-image: url('http://cdn.xiaodingdang1.com/2024/12/21/faf3ff8d194047a690086c271ac3faef.png');
    background-size: cover;
    // background: linear-gradient(to bottom, rgba(255,255,255,1), rgba(255,255,255,0)), url('https://cdn.xiaodingdang1.com/2024/11/09/c1e8a3924cef4dc38e2765ef8cd1f36e.png') no-repeat;
    overflow-x: hidden;
    padding: 196rpx 32rpx 0 32rpx;
    display: flex;
    &-flex {
        display: flex;
        align-items: center;
        &:first-child{
            
        }
        &:last-child{
            flex: 1
        }
    }
    &-img {
        margin-right: 32rpx;
        image {
            width: 144rpx;
            height: 144rpx;
            border: 4rpx solid #FFFFFF;
            border-radius: 144rpx;
        }
    }
    &-content {
        flex: 1;
        &-name {
            font-weight: 500;
            font-size: 40rpx;
            color: #2E2E2E;
        }
        &-mother {
            padding-top: 4rpx;
            image {
                width: 128rpx;
                height: 60rpx;
            }
        }
        &-label {
            font-weight: 500;
            font-size: 26rpx;
            color: #2E2E2E;
            width: 120rpx;
        }
        &-date {
            font-weight: 500;
            font-size: 22rpx;
            color: #2E2E2E;
            padding-top: 4rpx;
        }
        &-tag {
            display: flex;
            width: 400rpx;
            overflow-x: scroll;
            white-space: nowrap;
            &-enum {
                margin-right: 6rpx;
                display: inline-block;
                background: linear-gradient( 270deg, #FFE2B9 0%, #FFE4B7 100%);
                border-radius: 47rpx;
                font-weight: 500;
                font-size: 18rpx;
                color: #A0601F;
                padding: 8rpx 13rpx;
            }
        }
    }
}
.tag {
    width: 100%;
    background-color: #FFEFEF;
    &-main {
        width: 100%;
        padding: 32rpx 16rpx 27rpx 16rpx;
        background-color: white;
        border-radius: 20rpx;
    }
    &-content {
        width: 100%;
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        &-enum {
            font-size: 28rpx;
            color: #777777;
            margin-right: 32rpx;
            font-weight: 400;
            margin-bottom: 12rpx;
        }
        &-active {
            font-weight: 500;
            color: #FF5664;
        }
        &-title {
            font-weight: 500;
            font-size: 28rpx;
            color: #333333;
            margin-bottom: 12rpx;
        }
    }
    &-week {
        display: flex;
        margin-top: 20rpx;
        white-space: nowrap;
        width: 100%;
        &-enum {
            display: inline-block;
            margin-right: 12rpx;
            width: 170rpx;
            background: #F9F9F9;
            border-radius: 66rpx;
            height: 58rpx;
            line-height: 58rpx;
            font-size: 32rpx;
            text-align: center;
            font-weight: 400;
            font-size: 32rpx;
            color: #777777;
        }
        &-active {
            font-weight: 500;
            color: #FF6268;
            background: #FFF3F2;
        }
    }
}
.content {
    background-color: #F8F9F9;
    padding: 10rpx 12rpx 40rpx 12rpx;
    .title {
       width: 100%;
       image {
           width: 165rpx;
           height: 61rpx;
           display: block;
       }
   }
}