<template>
	<navigation title="数据分析" :background="bgColor"></navigation>
	<view class="main">
		<view class="content">
			<view class="title">
				{{clubName}}
			</view>
			<view class="accountNumber">
				<image src="http://cdn.xiaodingdang1.com/2025/05/21/f5f7e06c2d8347f19bf3dc16c1745739.png" mode=""
					style="width: 18rpx;height: 22rpx;"></image>
				<view class="accountNumber_1">
					账号正常
				</view>
			</view>
			<view class="menuBar">
				<view class="menuBarTop">
					<view class="menuBarTop_1" @click="menuBar('room')">
						<image src="http://cdn.xiaodingdang1.com/2024/11/23/787eacdc44604034bd2edf88da39369c.png"
							mode="" style="width:100%;height:248rpx" />
					</view>
					<view class="menuBarTop_2" @click="menuBar('community')">
						<image src="http://cdn.xiaodingdang1.com/2024/11/23/b40ab11de0a24a8ab9d006775509ac9b.png"
							mode="" style="width:100%;height:248rpx" />
					</view>
				</view>
				<view class="menuBarBottom">
					<view class="menuBarBottom_1" @click="menuBar('recovery')">
						<image src="http://cdn.xiaodingdang1.com/2024/11/23/49b05c869d774bae85ca9da82fe0de48.png"
							mode="" style="width:100%;height: 220rpx;" />
					</view>
					<view class="menuBarBottom_2" @click="menuBar('meal')">
						<image src="http://cdn.xiaodingdang1.com/2024/11/23/a87305d023f7416ebba3aa12a639e9c3.png"
							mode="" style="width: 100%;height: 220rpx;" />
					</view>
					<view class="menuBarBottom_3" @click="menuBar('staff')">
						<image src="http://cdn.xiaodingdang1.com/2024/11/23/f9daefe6a7024432a5d771f04e0144af.png"
							mode="" style="width:100%;height: 220rpx;" />
					</view>
				</view>
			</view>
			<view class="recently">
				<view class="recently1">
					最近访问
				</view>
				<view class="recently2" @click="more()">
					<view class="recently2_1">
						更多
					</view>
					<image src="http://cdn.xiaodingdang1.com/2025/05/21/938b2a470a9b47c0b4a8580e27dc37fc.png" mode=""
						style="width: 30rpx;height: 30rpx;"></image>
				</view>
			</view>
			<view class="analysisList" v-for="item,index in lastList" :key="index" @click="details(item)">
				<view class="analysisList3">
					<image
						:src="item.avatar?item.avatar:'http://cdn.xiaodingdang1.com/2024/11/08/6d8894bb65e1418880530c88baf5b001.png'"
						mode="" style="width: 110rpx;height: 110rpx;border-radius: 100rpx;"></image>
					<view class="analysisList1">
						<view class="analysisList1_1">{{item.nickname==null||item.nickname==''?'微信用户':item.nickname}}
						</view>
						<view class="analysisList1_2">
							<view class="analysisList1_3">
								{{item.ipLocation==null?'---':item.ipLocation}}
							</view>
							<view class="analysisList1_4">
								{{item.tel==null?'':item.tel}}
							</view>
						</view>
						<view class="analysisList1_5">
							<view class="analysisList1_6">
								最近访问：
							</view>
							<view class="analysisList1_7">
								{{item.lastLoginTime}}
							</view>
						</view>
					</view>
				</view>
				<view class="analysisList2" @click="ringup(item.tel)">
					联系客户
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				bgColor: '',
				lastList: [],
				clubName: '',
				pageNum: 1
			}
		},
		methods: {
			details(item) { //详情
				let items = JSON.stringify(item)
				uni.navigateTo({
					url: '/pageA/pageB/community/dataAnalysis/analysisListDetails?item=' + items
				})
			},
			ringup(tel) {
				// 拨打电话
				const phoneNumber = tel; // 客户的电话号码
				uni.makePhoneCall({
					phoneNumber: phoneNumber,
					success: () => {
						console.log('拨打电话成功！');
					},
					fail: () => {
						console.log('拨打电话失败！');
					}
				});
			},
			more() { //更多
				uni.navigateTo({
					url: '/pageA/pageB/community/dataAnalysis/moreDataAnalysis?type=1'
				})
			},
			menuBar(module) {
				uni.navigateTo({
					url: '/pageA/pageB/community/dataAnalysis/dataAnalysisDetails?module=' + module
				})
			},
			getlastList() {
				//获取所有节
				let data = {
					pageSize: 10,
					pageNum: this.pageNum
				};
				this.$axios.get(this.$api.lastList, data).then((res) => {
					if (res.data.code == 200) {
						let data = res.data.rows
						this.types = data.length == 10 ? 1 : 2;
						let lastList = this.lastList.concat(data);
						this.setData({
							lastList: lastList
						});
					}
				});
			},
		},
		onShow() {
			this.lastList = []
			this.pageNum = 1
			this.getlastList()
			this.clubName = uni.getStorageSync('clubName')
		},
		onPageScroll: function(e) {
			if (e.scrollTop > 0) {
				this.setData({
					bgColor: '#ffffff'
				});
			} else if (e.scrollTop < 2) {
				this.setData({
					bgColor: ''
				});
			}
		},
		onReachBottom() {
			if (this.types == 1) {
				this.pageNum = this.pageNum + 1
				this.getlastList();
			}
		}
	}
</script>

<style lang="less" scoped>
	@import './dataAnalysis.less';
</style>