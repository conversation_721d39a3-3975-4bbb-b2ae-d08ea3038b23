<template>
	<view class="submain">
		<view class="first">
			<view class="overview">
				<view class="titleName" v-if="inforList.projectIntroduction">
					<view class="fgx"></view>
					<view class="titleName1">项目概览</view>
				</view>
				<view class="overview1" v-if="inforList.projectIntroduction">
					{{inforList.projectIntroduction}}
				</view>
				<view class="titleName" v-if="inforList.projectAlias">
					<view class="fgx"></view>
					<view class="titleName1">项目别名</view>
				</view>
				<view class="overview1" v-if="inforList.projectAlias">
					{{inforList.projectAlias}}
				</view>
				<view class="titleName" v-if="inforList.projectEfficacy.length>0">
					<view class="fgx"></view>
					<view class="titleName1">项目功效</view>
				</view>
				<view class="effect" v-if="inforList.projectEfficacy.length>0">
					<view class="effect-title" v-for="item,index in inforList.projectEfficacy" :key="index">
						{{item}}
					</view>
				</view>
			</view>
		</view>
		<view class="first">
			<view class="first-title">项目简介</view>
			<view class="overview">
				<view class="titleName" v-if="inforList.suitableCrowd">
					<view class="fgx"></view>
					<view class="titleName1">适用人群</view>
				</view>
				<view class="titleName2" v-if="inforList.suitableCrowd">
					{{inforList.suitableCrowd}}
				</view>
				<view class="titleName" v-if="inforList.tabooGroups">
					<view class="fgx"></view>
					<view class="titleName1">禁忌人群</view>
				</view>
				<view class="titleName2" v-if="inforList.tabooGroups">
					{{inforList.tabooGroups}}
				</view>
				<view class="titleName" v-if="inforList.projectAdvantages">
					<view class="fgx"></view>
					<view class="titleName1">项目优点</view>
				</view>
				<view class="titleName2" v-if="inforList.projectAdvantages">
					{{inforList.projectAdvantages}}
				</view>
				<view class="titleName" v-if="inforList.projectDisadvantages">
					<view class="fgx"></view>
					<view class="titleName1">项目缺点</view>
				</view>
				<view class="titleName2" v-if="inforList.projectDisadvantages">
					{{inforList.projectDisadvantages}}
				</view>
				<view class="titleName">
					<view class="fgx"></view>
					<view class="titleName1">项目明细</view>
				</view>
				<view class="first-border">
					<view class="first-border-title">
						<view class="first-border-title-left">{{ inforList.projectName }}</view>
						<view class="first-border-title-right">1份</view>
					</view>
					<view class="first-border-line"></view>
					<view class="flex" v-if="inforList.serviceCategory">
						<view class="first-border-label">服务分类</view>
						<view class="first-border-value">{{ inforList.serviceCategory }}</view>
					</view>
					<view class="flex" v-if="inforList.serviceCount">
						<view class="first-border-label">服务次数</view>
						<view class="first-border-value">{{ inforList.serviceCount }}次</view>
					</view>
					<view class="flex" v-if="inforList.singleDuration">
						<view class="first-border-label">单次时长</view>
						<view class="first-border-value">{{ inforList.singleDuration }}分钟</view>
					</view>
					<view class="flex" v-if="inforList.serviceEffect">
						<view class="first-border-label">服务功效</view>
						<view class="first-border-value">{{ inforList.serviceEffect }}</view>
					</view>
					<view class="flex" v-if="inforList.serviceDevice">
						<view class="first-border-label">使用仪器</view>
						<view class="first-border-value">{{inforList.serviceDevice}}</view>
					</view>
					<view class="flex" v-if="inforList.serviceProcess">
						<view class="first-border-label">服务流程</view>
						<view class="first-border-value">{{inforList.serviceProcess}}</view>
					</view>
					<view class="flex" v-if="inforList.serviceCountType">
						<view class="first-border-label">服务次数</view>
						<view class="first-border-value">{{inforList.serviceCountType}}</view>
					</view>
					<view class="flex" v-if="inforList.maintenanceMethod">
						<view class="first-border-label">修护方式</view>
						<view class="first-border-value">{{inforList.maintenanceMethod}}</view>
					</view>
					<view class="flex" v-if="inforList.deviceType">
						<view class="first-border-label">仪器类型</view>
						<view class="first-border-value">{{inforList.deviceType}}</view>
					</view>
					<view class="flex" v-if="inforList.deviceManufacturer">
						<view class="first-border-label">仪器生产来源</view>
						<view class="first-border-value">{{inforList.deviceManufacturer}}</view>
					</view>
					<view class="flex">
						<view class="first-border-label">是否含耗 材费</view>
						<view class="first-border-value">{{inforList.includeConsumablesFee?'是':'否'}}</view>
					</view>
					<view class="flex" v-if="inforList.feeDetails">
						<view class="first-border-label">费用内耗 材信息</view>
						<view class="first-border-value">{{inforList.feeDetails}}</view>
					</view>
					<view class="flex" v-if="inforList.serviceMode">
						<view class="first-border-label">服务方式</view>
						<view class="first-border-value">
							{{ inforList.serviceMode == 0 ? '可上门' : inforList.serviceMode == 1 ? '仅到店' : '在线服务' }}
						</view>
					</view>
				</view>
			</view>

		</view>
		<view class="first">
			<view class="overview" >
				<view class="titleName" style="padding-top: 20rpx;">
					<view class="fgx"></view>
					<view class="titleName1">项目周期</view>
				</view>
				<view class="period">
					<view class="period1">
						<view class="period1_1">
							操作时长
						</view>
						<view class="period1_2">
							{{inforList.operationDuration}}
						</view>
					</view>
					<view class="period2">
						<view class="period2_1">
							作用呈现
						</view>
						<view class="period2_2">
							{{inforList.effectPresentation}}
						</view>
					</view>
					
					<view class="period1">
						<view class="period1_1">
							恢复周期
						</view>
						<view class="period1_2">
							{{inforList.recoveryCycle}}
						</view>
					</view>
					<view class="period2">
						<view class="period2_1">
							间隔周期
						</view>
						<view class="period2_2">
							{{inforList.intervalCycle}}
						</view>
					</view>
					<view class="period1">
						<view class="period1_1">
							项目痛感
						</view>
						<view class="period1_2">
							{{inforList.projectPainLevel}}
						</view>
					</view>
					<view class="period3">
						以上信息均为参考，实际以到院操作为准
					</view>
				</view>
			</view>
		</view>
		<view class="third">
			<view class="head">
				<view class="head1">
					<view class="head1_1"></view>
					<view class="head1_2">图文详情</view>
				</view>
				<view class="head2"></view>
			</view>
			<image class="third-img" style="width: 100%;" :src="item" mode="widthFix" v-for="(item, index) in inforList.descriptionPhotos"
				:key="index"></image>
		</view>
		<view class="second">
			<view class="head" style="margin-bottom: 20rpx;">
				<view class="head1">
					<view class="head1_1"></view>
					<view class="head1_2">大家都在问</view>
				</view>
				<view class="head2"></view>
			</view>
			<question-vue></question-vue>
		</view>
	
		<view class="fourth">
			<recomment-vue></recomment-vue>
		</view>
	</view>
</template>
<script setup>
	// 产康页面 项目明细
	import questionVue from "./question.vue";
	import recommentVue from "./recomment.vue";
	import {
		onShow,
		onHide,
		onLoad,
		onPageScroll
	} from "@dcloudio/uni-app";
	import {
		ref,
		reactive,
		onMounted,
		computed,
		getCurrentInstance
	} from "vue";
	const instance = getCurrentInstance();
	const App = instance.appContext.config.globalProperties
	const props = defineProps({
		inforList: {
			type: Object,
			default: () => {}
		},
		// projectId: {
		//     type: String,
		//     default: ''
		// }
	})
	onMounted(() => {
		pageInit()
	})
	const pageInit = async () => {
		try {

		} catch {

		}
	}
</script>
<style lang="less" scoped>
	.submain {
		overflow: hidden;



		.flex {
			display: flex;
		}

		.head {
			display: flex;
			justify-content: space-between;
		}

		.head1 {
			display: flex;
			align-items: center;
		}

		.head2 {
			display: flex;
			align-items: center;
		}

		.head1_1 {
			width: 4rpx;
			height: 30rpx;
			background: #333333;
			border-radius: 5rpx;
		}

		.head1_2 {
			font-weight: bold;
			font-size: 28rpx;
			color: #333333;
			line-height: 33rpx;
			text-align: left;
			font-style: normal;
			text-transform: none;
			padding: 12rpx 0;
			margin-left: 10rpx;
		}

		.head2_1 {
			color: #aaaaaa;
			font-size: 22rpx;
			margin-right: 10rpx;
		}
	}

	.first {
		// padding: 0 0rpx 24rpx;
		background-color: white;
		padding-bottom: 24rpx;
		margin-bottom: 24rpx;

		.overview {
			padding: 0rpx 24rpx;

			.titleName {
				display: flex;
				align-items: center;

				.fgx { 
					width: 4rpx;
					height: 26rpx;
					background: #333333;
				}

				.titleName1 {
					color: #333333;
					font-size: 28rpx;
					margin-left: 10rpx;
					font-weight: bold;
				}
			}
            .period{
				margin-top: 20rpx;
				background: #F9F9F9;
				padding: 36rpx 32rpx;
				.period1{
					display: flex;
					justify-content: space-between;
					align-items: center;
					color: #777777;
					font-size: 24rpx;
					padding: 22rpx 18rpx;
				}
				.period2{
					display: flex;
					justify-content: space-between;
					align-items: center;
					color: #777777;
					font-size: 24rpx;
					background: #ffffff;
					padding: 22rpx 18rpx;
				}
				.period3{
					color: #BDBDBD;
					font-size: 22rpx;
					margin-top: 32rpx;
					text-align: center;
				}
			}
			.titleName2 {
				color: #777777;
				font-size: 24rpx;
				margin: 24rpx 0;
			}

			.effect {
				display: flex;
				margin-top: 24rpx;

				.effect-title {
					padding: 4rpx 18rpx;
					background: #FFEDED;
					color: #FF4F61;
					font-size: 24rpx;
					border-radius: 100rpx;
				}
			}

			.overview1 {
				color: #333333;
				font-size: 24rpx;
				margin: 24rpx 0;
			}
		}

		&-title {
			padding-top: 24rpx;
			padding-bottom: 20rpx;
			padding-left: 24rpx;
			font-weight: bold;
			font-size: 32rpx;
			color: #333333;
			line-height: 33rpx;
			text-align: left;
			font-style: normal;
			text-transform: none;
		}

		&-border {
			border-radius: 20rpx 20rpx 20rpx 20rpx;
			border: 2rpx solid #D1D1D1;
			margin-top: 24rpx;

			&-title {
				padding: 24rpx;
				display: flex;
				justify-content: space-between;
				align-items: center;
				font-weight: 500;
				font-size: 24rpx;
				color: #333333;
				line-height: 28rpx;
				text-align: left;
				font-style: normal;
				text-transform: none;
				background: #F8F9F9;
				border-radius: 20rpx 20rpx 0rpx 0rpx;
				// border: 2rpx solid #D1D1D1;
				// &-left {}

				// &-right {}
			}

			&-line {
				background-color: #D1D1D1;
				height: 2rpx;
				width: 100%;
				margin-bottom: 24rpx;
			}

			// &-title::after {
			//     content: '';
			//     display: block;
			//     width: 100%;
			//     height: 2rpx;
			//     background: #D1D1D1;
			// }

			&-label {
				width: 96rpx;
				margin: 0 24rpx 24rpx 24rpx;
				font-weight: 400;
				font-size: 24rpx;
				color: #777777;
				line-height: 28rpx;
				text-align: left;
				font-style: normal;
				text-transform: none;
			}


			&-value {
				margin-bottom: 24rpx;
				flex: 1;
				font-weight: 500;
				font-size: 24rpx;
				color: #333333;
				line-height: 28rpx;
				text-align: left;
				font-style: normal;
				text-transform: none;
			}

		}
	}

	.second {
		margin-top: 20rpx;
		margin-bottom: 20rpx;
		padding: 20rpx 24rpx;
		background: #ffffff;

	}



	.third {
		background: #ffffff;
		padding: 20rpx 24rpx;

		&-img {
			min-height: 360rpx;
			width: 100%;
			margin-top: 20rpx;
			border-radius: 20rpx;
		}
	}

	.fourth {
		background: #ffffff;
		padding: 20rpx 24rpx;
		margin-top: 20rpx;
	}
</style>