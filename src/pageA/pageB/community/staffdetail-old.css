.preciousMother {
    background: #f8f9f9;
    /* border-radius: 20rpx; */
    min-height: 316rpx;
    padding: 180rpx 0rpx 124rpx 0;
}

.top {
    background-color: white;
}

.more-card {
    margin-bottom: 24rpx;
}

.mother1 {
    display: flex;
}

.pd-24 {
    padding: 0 24rpx;
}

.mother1_2 {
    margin-left: 10rpx;
}

.mother1Text1 {
    color: hsla(0, 0%, 67%, 1);
    font-size: 24rpx;
    margin-top: 8rpx;
}

.mother1Text2 {
    color: hsla(26, 100%, 50%, 1);
    font-size: 30rpx;
}

.mother1Text3 {
    background: hsla(40, 100%, 90%, 1);
    color: hsla(15, 97%, 50%, 1);
    border-radius: 10rpx;
    padding: 4rpx 8rpx;
    font-size: 24rpx;
    font-weight: bold;
}

.motherImgs {
    margin: 20rpx 0 24rpx 0;
    display: flex;
    flex-wrap: wrap;
}

.motherImgs image {
    width: 220rpx;
    height: 220rpx;
    margin-left: 10rpx;
    margin-bottom: 15rpx;
}

.tagName {
    padding: 4rpx 16rpx;
    background: #7e6dfc;
    border-radius: 2px 2px 2px 2px;
    color: #ffffff;
    font-size: 24rpx;
    margin-left: 10rpx;
}

.discuss3_1 {
    width: 220rpx;
    height: 220rpx;
}

.comment1 {
    margin: 24rpx 0;
    font-size: 24rpx;
}

.mmcomment {
    color: #7e6dfc;
}

.mmcomments {
    color: #777777;
    margin-left: 8rpx;
}

.comment2 {
    padding: 6rpx 0;
}

.discuss4 {
    width: 100%;
    height: 1rpx;
    background: hsla(0, 0%, 92%, 1);
}

.discuss5 {
    display: flex;
    justify-content: space-around;
    padding: 26rpx 0 32rpx 0;
    color: hsla(0, 0%, 47%, 1);
    font-size: 24rpx;
}

.discuss5_1 {
    display: flex;
    align-items: center;
}

.discuss5_2 {
    margin-left: 4rpx;
}

.discuss5Active_2 {
    color: #fe5b86;
    font-size: 24rpx;
    margin-left: 4rpx;
}

.img_box {
    margin-top: 20rpx;
    /* padding-left: 4rpx; */
}

.videos {
    width: 340rpx;
    height: 340rpx;
}

.videos video {
    width: 100%;
    height: 100%;
}

.many_img {
    display: flex;
    justify-self: start;
    flex-wrap: wrap;
    width: 100%;
}

.many {
    width: 49%;
    height: 280rpx;
    margin-bottom: 10rpx;
    border-radius: 10px;
    overflow: hidden;
}

.many image {
    width: 100%;
    height: 100%;
}

.many_img .many:nth-child(2n) {
    margin-left: 1%;
}

.img_item.four {
    width: 32%;
    height: 220rpx;
    margin-bottom: 10rpx;
    border-radius: 16rpx;
    overflow: hidden;
}

.img_item.four image {
    width: 100%;
    height: 100%;
}

.img_item.four:nth-child(2) {
    margin: 0 1%;
}

.img_item.four:nth-child(5) {
    margin: 0 1%;
}

.img_item.four:nth-child(8) {
    margin: 0 1%;
}

.img_item.many {
    width: 48%;
    height: 340rpx;
    margin-bottom: 10rpx;
    border-radius: 10px;
    overflow: hidden;
}

.img_item.many image {
    width: 100%;
    height: 100%;
}

.img_item.many:nth-child(2n) {
    margin-left: 1%;
}

.discusss5 {
    color: hsla(0, 0%, 47%, 1);
    font-size: 28rpx;
}

.custom-button {
    background: #fff;
    color: hsla(0, 0%, 47%, 1);
    font-size: 24rpx;
    margin: 0;
}

.discuss5_1 {
    display: flex;
    align-items: center;
    justify-content: center;
}

.packUp {
    color: #4d669b;
    font-size: 30rpx;
}

.mother2 {
    font-size: 30rpx;
    color: hsla(0, 0%, 20%, 1);
    margin: 16rpx 0 10rpx 0;
}

.beyond {
    font-size: 30rpx;
    color: hsla(0, 0%, 20%, 1);
    margin: 16rpx 0 10rpx 0;
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 5;
}

.more {
    background-color: white;
    padding: 24rpx 0;
}

.more-title {
    font-weight: 500;
    font-size: 34rpx;
    color: #333333;
    margin-bottom: 24rpx;
}



.devide {
    width: 100%;
    height: 1rpx;
    background-color: #EBEBEB;
    margin: 24rpx 0 12rpx 0;
}

.devide-content {
    width: 100%;
    height: 20rpx;
    background-color: #F8F9F9;
}

.pad {
    padding-bottom: 12rpx;
}