<template>
    <view class="main">
        <view class="mains">
            <view class="next">
                <view class="headImg" @tap="next">
                    <image src="http://cdn.xiaodingdang1.com/1/120240201/183e1847-20bc-461e-aecf-444fe898e91f.png"
                        mode="" style="width: 18rpx; height: 34rpx" />
                </view>
                <view class="headTitle"></view>
            </view>
            <view class="text1">欢迎</view>
            <view class="text1">使用宝妈小叮当</view>
            <view class="content">
                <view class="text2">为了更好的服务您，请您填写以下信息</view>
                <view class="img" @tap="chooseImg" v-if="identification != 1">
                    <image :src="avatar|| $defaultAvatar" mode=""
                        style="width: 140rpx; height: 140rpx; border-radius: 100rpx" />
                </view>
                <view class="list" v-if="identification == 1">
                    <view class="text3">电话号码</view>
                    <view class="text4">
                        <input type="text" placeholder="请输入您的电话号码" :value="tel" @input="searchInput" data-name="tel"
                            v-if="tel" :disabled="true" />
                        <button open-type="getPhoneNumber" @getphonenumber="getPhoneNumber" v-if="!tel"
                            style="background: #fff; position: absolute; top: -38rpx; this: -12rpx">
                            点击获取手机号
                        </button>
                    </view>
                </view>
                <view v-if="identification == 2 || identification == 3">
                    <view class="fgx"></view>
                    <view class="list">
                        <view class="text3">名称</view>
                        <view class="text4">
                            <input type="text" :value="nickname" placeholder="请输入您的名称" data-name="nickname"
                                @input="searchInput" />
                        </view>
                    </view>
                    <view class="fgx"></view>
                    <view class="list">
                        <view class="text3">性别</view>
                        <view class="text4">
                            <view id="text4">
                                <picker :value="index" :range="sexList" @change="bindPickerChange">
                                    <view class="picker">
                                        {{ sex }}
                                    </view>
                                </picker>
                                <image src="http://cdn.xiaodingdang1.com/2024/04/16/87c84295a6ae4500b284defeee418729png"
                                    mode="" style="width: 12rpx; height: 24rpx" />
                            </view>
                        </view>
                    </view>
                    <view class="fgx"></view>
                    <view class="list">
                        <view class="text3">出生日期</view>
                        <view class="text4">
                            <view id="text4">
                                <picker mode="date" :value="birthDate" start="" end="" @change="dateOfDirth">
                                    <view class="picker">
                                        {{ birthDate }}
                                    </view>
                                </picker>
                                <image src="http://cdn.xiaodingdang1.com/2024/04/16/87c84295a6ae4500b284defeee418729png"
                                    mode="" style="width: 12rpx; height: 24rpx" />
                            </view>
                        </view>
                    </view>
                    <view class="fgx"></view>
                    <view class="fgx"></view>
                </view>
            </view>
            <view class="btn" @tap="confirm">确定</view>
        </view>
        <view class="section" v-if="pickerShow">
            <picker @change="bindPickerChange" :value="index" :range="sexList" v-if="selIndex == 1">
                <view class="picker">当前选择：{{ sexList[index] }}</view>
            </picker>
        </view>
    </view>
</template>

<script>
    export default {
        data() {
            return {
                identification: '',
                // dueDate: '请选择您的预产期',
                birthDate: '请选择您的出生日期',
                tel: '',
                nickname: '',
                sex: '请选择性别',
                avatar: '',
                pickerShow: false,
                index: '',
                sexList: ['男', '女'],
                imgUrl: [],
                avatars: '',
                selIndex: '',
                date: '',
                defaultImg: ''
            };
        }
        /**
         * 生命周期函数--监听页面加载
         */
        ,
        onLoad(options) {
            let identification = options.identification; //1登录没有手机号码
            this.setData({
                identification: identification
            });
            if (options.userInfo) {
                let userInfo = JSON.parse(options.userInfo);
                console.log(userInfo);
                this.setData({
                    index: userInfo.sex,
                    sex: userInfo.sex == 0 ? '男' : userInfo.sex == 1 ? '女' : '请选择性别',
                    tel: userInfo.tel,
                    nickname: userInfo.nickname ? userInfo.nickname : '',
                    birthDate: userInfo.birthDate ? userInfo.birthDate : '请选择您的出生日期',
                    // dueDate: userInfo.dueDate ? userInfo.dueDate : '请选择您的预产期',
                    avatar: userInfo.avatar ? userInfo.avatar : this.defaultImg
                });
            }
        },
        /**
         * 生命周期函数--监听页面初次渲染完成
         */
        onReady() {},
        /**
         * 生命周期函数--监听页面显示
         */
        onShow() {},
        /**
         * 生命周期函数--监听页面隐藏
         */
        onHide() {},
        /**
         * 生命周期函数--监听页面卸载
         */
        onUnload() {},
        /**
         * 页面相关事件处理函数--监听用户下拉动作
         */
        onPullDownRefresh() {},
        /**
         * 页面上拉触底事件的处理函数
         */
        onReachBottom() {},
        /**
         * 用户点击右上角分享
         */
        onShareAppMessage() {},

        onShareTimeline() {},
        methods: {
            getLogin() {
                let userId = uni.getStorageSync('uid');
                let data = {
                    userId: userId
                };
                if (this.identification == 4) {
                    data.avatar = this.avatar;
                }
                if (this.identification == 1) {
                    return this.$auth.bindPhone(this.tel).then((res) => {
                        uni.navigateBack();
                    });
                }
                if (this.identification == 2 || this.identification == 3) {
                    data.avatar = this.avatar;
                    data.nickname = this.nickname;
                    data.sex = this.index;
                    // data.dueDate = this.dueDate;
                    data.birthDate = this.birthDate;
                }
                this.$axios.post(this.$api.userUpdateInfo, data).then((res) => {
                    if (res.data.code == 200) {
                        this.$auth.getUserInfo()
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000 //持续的时间
                        });
                        setTimeout(() => {
                            uni.navigateBack({
                                delta: 1
                            });
                        }, 1000);
                    }
                });
            },

            confirm() {
                if (this.identification == 4) {
                    if (this.avatar == '') {
                        uni.showToast({
                            title: '请上传头像',
                            icon: 'none',
                            duration: 2000 //持续的时间
                        });
                        return;
                    }
                    this.getLogin();
                }
                if (this.identification == 1) {
                    if (this.tel === '') {
                        uni.showToast({
                            title: '请获取手机号码',
                            icon: 'none',
                            duration: 2000 //持续的时间
                        });
                        return;
                    }
                    this.getLogin();
                }
                if (this.identification == 2 || this.identification == 3) {
                    if (this.avatar == '') {
                        uni.showToast({
                            title: '请上传头像',
                            icon: 'none',
                            duration: 2000 //持续的时间
                        });
                        return;
                    }
                    if (this.nickname == '') {
                        uni.showToast({
                            title: '请填写名称',
                            icon: 'none',
                            duration: 2000 //持续的时间
                        });
                        return;
                    }
                    if (this.sex == '请选择性别') {
                        uni.showToast({
                            title: '请选择性别',
                            icon: 'none',
                            duration: 2000 //持续的时间
                        });
                        return;
                    }
                    if (this.birthDate == '请选择您的出生日期') {
                        uni.showToast({
                            title: '请选择您的出生日期',
                            icon: 'none',
                            duration: 2000 //持续的时间
                        });
                        return;
                    }
                    this.getLogin();
                }
            },

            choice(e) {
                let index = e.currentTarget.dataset.index;
                this.setData({
                    pickerShow: !this.pickerShow,
                    selIndex: index
                });
            },

            // expectedDate: function(e) {
            //     //预产期
            //     console.log('picker发送选择改变，携带值为', e.detail.value);
            //     this.setData({
            //         dueDate: e.detail.value
            //     });
            // },

            dateOfDirth: function(e) {
                //出生日期
                console.log('picker发送选择改变，携带值为', e.detail.value);
                this.setData({
                    birthDate: e.detail.value
                });
            },

            bindPickerChange: function(e) {
                console.log(e);
                this.setData({
                    index: e.detail.value,
                    sex: this.sexList[e.detail.value]
                });
            },

            next() {
                uni.navigateBack({
                    delta: 1
                });
            },

            // 上传图片
            chooseImg: function(e) {
                var that = this;
                uni.chooseImage({
                    // count: 1, // 默认9
                    sizeType: ['compressed'],
                    // 可以指定是原图还是压缩图，默认二者都有
                    sourceType: ['album', 'camera'],
                    // 可以指定来源是相册还是相机，默认二者都有
                    success: function(res) {
                        var tempFilePaths = res.tempFilePaths;
                        let picNum = tempFilePaths.length;
                        uni.showLoading({
                            title: '图片上传中'
                        });
                        uni.uploadFile({
                            filePath: tempFilePaths[0],
                            name: 'file',
                            url: that.$api.uploadOSS,
                            formData: {},
                            header: {
                                'Content-Type': 'multipart/form-data',
                                Authorization: 'Bearer ' + uni.getStorageSync('token'),
                                'Accept-Encoding': 'gzip',
                                Clientid: '428a8310cd442757ae699df5d894f051'
                            },
                            success: function(rests) {
                                let data = JSON.parse(rests.data);
                                if (data.code == 200) {
                                    uni.hideLoading();
                                    that.setData({
                                        avatar: data.data.url
                                    });
                                }
                            }
                        });
                    }
                });
            },

            searchInput(e) {
                let name = e.currentTarget.dataset.name;
                let value = e.detail.value;
                this.setData({
                    [`${name}`]: value
                });
                console.log(this.tel);
            },

            userPhone(code) {
                let data = {
                    appid: uni.getAccountInfoSync().miniProgram.appId,
                    code: code
                };
                this.$axios
                    .get(this.$api.userPhone, data)
                    .then((res) => {
                        this.setData({
                            tel: res.data.data.phoneNumber
                        });
                    })
            },

            getPhoneNumber(e) {
                console.log(e);
                this.userPhone(e.detail.code);
            }
        }
    };
</script>
<style>
    @import './login.css';
</style>