<template>
  <view class="next">
    <view class="contentTitle">我的服务</view>
    <u-grid :col="3" :border="false">
      <!-- 每日餐点 -->
      <u-grid-item @tap="handleDailyMeal" v-if="mealShow">
        <view class="grid-icon-wrapper">
          <image
            src="http://cdn.xiaodingdang1.com/2025/06/25/ca53b50e31464335962de96a388c0950.png"
            mode=""
            style="width: 48rpx; height: 48rpx"
          />
        </view>
        <view class="grid-text">每日餐点</view>
      </u-grid-item>
      
      <!-- 我的请帖 -->
      <u-grid-item @tap="handleInvitation">
        <view class="grid-icon-wrapper">
          <image
            src="/src/static/images/icons/icon_qt.png"
            mode=""
            style="width: 48rpx; height: 48rpx"
          />
          <image
            src="http://cdn.xiaodingdang1.com/2025/06/20/fd5edb3e2404422ca7d199f848374bad.png"
            class="hot-tag"
          />
        </view>
        <view class="grid-text">我的请帖</view>
      </u-grid-item>
      
      <!-- 护理记录 -->
      <u-grid-item @tap="handleNurseRecord" v-if="nurseShow">
        <view class="grid-icon-wrapper">
          <image
            src="http://cdn.xiaodingdang1.com/2025/03/21/e08baa1b52304a5c9a62afa367c99bdb.png"
            mode=""
            style="width: 48rpx; height: 48rpx"
          />
        </view>
        <view class="grid-text">护理记录</view>
      </u-grid-item>
      
      <!-- 意见反馈 -->
      <u-grid-item @tap="handleComplaint" v-if="complaintShow">
        <view class="grid-icon-wrapper">
          <image
            src="http://cdn.xiaodingdang1.com/2025/05/26/2e6a945f79404101905f5c1f856a3b39.png"
            mode=""
            style="width: 48rpx; height: 48rpx"
          />
        </view>
        <view class="grid-text">意见反馈</view>
      </u-grid-item>

      <!-- 客服中心 -->
      <u-grid-item v-if="isCustomerService" @tap="handleCustomerService">
        <view class="grid-icon-wrapper">
          <image
            src="http://cdn.xiaodingdang1.com/2025/03/25/d5c7d98ccfc64a239716a5fe6fe3c0a1.jpg"
            mode=""
            style="width: 48rpx; height: 48rpx"
          />
          <u-badge
            :count="unreadMessageCount"
            :is-dot="false"
            type="error"
            v-if="unreadMessageCount > 0"
            class="badge-position"
          ></u-badge>
        </view>
        <view class="grid-text">客服中心</view>
      </u-grid-item>

      <!-- 敬请期待 -->
      <u-grid-item>
        <view class="grid-icon-wrapper">
          <image
            src="http://cdn.xiaodingdang1.com/2025/03/21/784969d859fe4e33a52b27b35e18ae74.png"
            mode=""
            style="width: 48rpx; height: 48rpx"
          />
        </view>
        <view class="grid-text">敬请期待</view>
      </u-grid-item>
    </u-grid>
  </view>
</template>

<script setup>
import { defineProps } from 'vue'
import subscriptionManager, { TEMPLATE_TYPES } from '@/utils/subscriptionManager.js'

// Props
const props = defineProps({
  mealShow: {
    type: Boolean,
    default: false
  },
  nurseShow: {
    type: Boolean,
    default: false
  },
  complaintShow: {
    type: Boolean,
    default: false
  },
  isCustomerService: {
    type: Boolean,
    default: false
  },
  unreadMessageCount: {
    type: Number,
    default: 0
  }
})

// Methods - 内部处理跳转逻辑
const handleDailyMeal = () => {
  uni.navigateTo({
    url: '/pageA/pageB/community/customermenu/customermenu',
  })
}

const handleInvitation = () => {
  uni.navigateTo({
    url: '/pageA/pageB/home/<USER>',
  })
}

const handleNurseRecord = () => {
  uni.navigateTo({
    url: '/pageA/pageB/community/nurse/nursemenu',
  })
}

const handleComplaint = () => {
  uni.navigateTo({
    url: '/pageA/pageB/mine/complaint',
  })
}

const handleCustomerService = async () => {
  try {
    await subscriptionManager.requestSubscription(
      [
        TEMPLATE_TYPES.CUSTOMER_SERVICE_MESSAGE_UNREAD,
        TEMPLATE_TYPES.CLIENT_CONSULTATION_REMINDER,
      ],
      {
        onSuccess: (res) => {
          console.log('客服订阅成功', res)
          // 订阅成功后跳转到客服页面
          uni.navigateTo({
            url: '/subPackages/customerService/pages/serviceReception',
          })
        },
        onFail: (err) => {
          console.error('客服订阅失败', err)
          // 即使订阅失败也允许进入客服页面
          uni.navigateTo({
            url: '/subPackages/customerService/pages/serviceReception',
          })
        },
        onError: (error) => {
          console.error('客服订阅出错', error)
          uni.showToast({
            title: '订阅消息失败',
            icon: 'none',
          })
          // 出错时也允许进入客服页面
          uni.navigateTo({
            url: '/subPackages/customerService/pages/serviceReception',
          })
        },
      },
    )
  } catch (error) {
    console.error('客服订阅异常', error)
    // 发生异常时直接跳转到客服页面
    uni.navigateTo({
      url: '/subPackages/customerService/pages/serviceReception',
    })
  }
}
</script>

<style scoped>
.next {
  border-radius: 14rpx;
  background-color: #ffffff;
  padding: 28rpx 30rpx;
  margin-bottom: 20rpx;
}

.contentTitle {
  font-size: 32rpx;
  color: #333333;
  font-weight: bold;
}

.grid-text {
  color: #878787;
  font-size: 24rpx;
  margin-top: 14rpx;
}

.grid-icon-wrapper {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 48rpx;
  width: 100%;
  margin-bottom: 10rpx;
}

.badge-position {
  position: absolute !important;
  right: 35rpx !important;
  top: -35rpx !important;
  z-index: 1;
}

.hot-tag {
  position: absolute;
  width: 28rpx;
  height: 18rpx;
  left: 55%;
  top: -9rpx;
  z-index: 2;
}
</style>
