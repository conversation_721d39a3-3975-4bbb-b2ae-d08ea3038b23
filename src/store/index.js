// #ifndef VUE3
import Vue from 'vue';
import Vuex from 'vuex';
Vue.use(Vuex);
// #endif

// #ifdef VUE3
import { createStore } from 'vuex';
// #endif

import userModule from './modules/user.js';
import commonModule from './modules/common.js';
import rehabilitation from './modules/rehabilitation';

const store = createStore({
  modules: {
    m_user: userModule,
    m_common: commonModule,
    m_rehabilitation: rehabilitation,
  },
});

export default store;
