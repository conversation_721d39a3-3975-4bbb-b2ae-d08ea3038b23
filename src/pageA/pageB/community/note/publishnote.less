.main {
    background:#F6F7FB;
}

.content {
    width: 100%;
    background: #fff;
    padding: 20rpx 24rpx;
}

.head {
    display: flex;
    justify-content: space-between;
}

.cancel {
    color: #333333;
    font-size: 28rpx;
}

.publish-upload {
    display: block;
}

.publish {
    width: 96rpx;
    text-align: center;
    height: 56rpx;
    line-height: 56rpx;
    background: #7e6dfc;
    color: #ffffff;
    border-radius: 10rpx;
    font-size: 28rpx;
}
.textarea {
    margin-top: 20rpx;
    margin-bottom: 20rpx;
    width: 690rpx;
    font-size: 26rpx;
}

/deep/.publish-form {
    .u-input, .u-form-item__message{
        text-align: right !important;
        margin-right: 8rpx;
    }
}

.uni-input {
    padding: 30rpx 12rpx;
    font-size: 28rpx;
    border-bottom: 1rpx solid #F3F4F8;
}
.basics{
	display: flex;
	justify-content: space-between;
}
.roomNUm {
	color: #333333;
	font-size: 28rpx;
	font-weight: bold;
}
.grid-container1 {
	display: grid;
	margin: 20rpx 0rpx 20rpx 0rpx;
	grid-template-columns: repeat(auto-fill, calc(33.3333% - 30rpx));
	/** 平铺宽度 */
	grid-template-rows: auto;
	/** 设置高度为  */
	grid-auto-rows: auto;
	/** 当容易高度不够时，多余的组件高度将怎么分配，默认的高度由单元格内容决定 */
	justify-content: space-between;
	/** 水平居中  */
	/** 水平和垂直间距*/
	align-items: center;
}
.grid-item1 {
	display: flex;
	height: 64rpx;
	align-items: center;
	justify-content: center;
	border-radius: 8rpx;
	background: #f3f4f8;
	color: #333333;
	font-size: 28rpx;
	margin-bottom: 20rpx;
}

.gridActive {
	display: flex;
	height: 64rpx;
	align-items: center;
	justify-content: center;
	border-radius: 8rpx;
	background: #e3eaff;
	color: #3f6fff;
	font-size: 28rpx;
	margin-bottom: 20rpx;
}


.tabsHead{
	position:sticky;
	top: 0;
	z-index: 999;
	background: #fff;
}
.tabs1{
	background: #F6F7FB;
}
.tabs {
	background: #fff;
	border-radius: 20rpx;
	margin: 0rpx 24rpx 24rpx 24rpx;
	height: 700rpx;
	overflow-y: auto;
	position: relative;
}

.cutOff {
	width: 100%;
	height: 1rpx;
	background: #F0F0F0;
	margin: 14rpx 0;
}

.tabsContent1 {
	padding: 24rpx 36rpx;
	color: #737373;
	font-size: 28rpx;
	z-index: 9;
}
.maskContent{
	background: #fff;
	width: 70%;
	border-radius: 20rpx;
	margin:40% 15%;
	padding: 36rpx;
	position: fixed;
}
.maskTitle{
	color:#333333 ;
	font-size: 34rpx;
	font-weight: bold;
}
.maskContent1{
	border: 1rpx solid #E8E8E8;
	background: #FAFAFA;
	border-radius: 10rpx;
	padding: 28rpx 28rpx;
	font-size: 28rpx;
    color: #6F6F6F;
	margin: 22rpx 0;
	height: 500rpx;
	width: 90%;

}
.maskBtn{
	display: flex;
	justify-content: space-between;
}
.formBoard{
	display: flex;
	justify-content:space-between;
	align-items:center;
	padding: 20rpx 0;
}

.container {
  width: 100%;
  overflow: hidden;
}
.tab-scroll-view {
  white-space: nowrap;
}
.tab-wrapper {
  display: flex;
}
.tab-item {
  padding: 10px;
  margin-right: 10px;
  position: relative; /* 用于定位下划线 */
  cursor: pointer;
}
.tab-item.active::after {
  content: '';
  position: absolute;
  left: 25%;
  bottom: 0;
  width: 50%;
  height: 2px; /* 下划线的高度 */
  background-color: #007AFF; /* 下划线的颜色 */
}

.unfold{
	display: flex;
	justify-content: center;
align-items: center;
margin-bottom: 20rpx;
}
.switchName{
	width: 30%;
}
.switchName1{
	display: flex;
	align-items: center;
	justify-content: space-between;
	width: 70%;
}
