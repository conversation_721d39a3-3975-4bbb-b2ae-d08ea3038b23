.main{
	height: 100vh;
	background: #F6F6F6;
	padding: 42rpx 32rpx;
}
.title{
	color: #333333;
	font-size: 32rpx;
	font-weight: bold;
}
.content{
	margin-top: 17rpx;
	background: #fff;
	border-radius: 20rpx;
	padding: 28rpx 28rpx;
	height: fit-content;
}
.counter{
	text-align: right;
	color: #333333;
	font-size: 24rpx;
}
.publish-upload {
	display: block;
}
.addbutton{
	position: fixed;
    bottom: 0;
	padding: 20rpx 32rpx 62rpx 32rpx;
	background: #fff;
	width: 100%;
}
.add{
	height: 90rpx;
	line-height: 90rpx;
	text-align: center;
	color: #fff;
	font-size: 30rpx;
	font-weight: bold;
	width: 100%;
	background: #3F6FFF;
	border-radius: 100rpx;
}