.main-content {
    padding: 20rpx 24rpx;
    background: #f8f9f9;
    min-height: calc(100vh - 70px)
}

/*界面收藏*/
.title {
    color: #333333;
    font-weight: bold;
    font-size: 34rpx;
}

.content {
    background: #ffffff;
    padding: 24rpx 24rpx;
    margin-top: 20rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.content_2 {
    display: flex;
}

.content_1 {
    margin-left: 20rpx;
}

.contentTitle1 {
    color: #333333;
    font-size: 32rpx;
}

.contentTitle2 {
    margin: 10rpx 0;
    color: #7e6dfc;
    background: #f2efff;
    font-size: 24rpx;
    width: 64rpx;
    height: 40rpx;
    line-height: 40rpx;
    text-align: center;
    border-radius: 4rpx;
}

.contentTitle3 {
    color: #aaaaaa;
    font-size: 28rpx;
    margin-top: 10rpx;
}

/*动态收藏*/
.tagName {
    padding: 4rpx 16rpx;
    background: #7e6dfc;
    border-radius: 2px 2px 2px 2px;
    color: #ffffff;
    font-size: 24rpx;
    margin-left: 10rpx;
}

.title1 {
    color: #333333;
    font-weight: bold;
    font-size: 34rpx;
    margin-top: 40rpx;
}

.contentTitle2_1 {
    display: flex;
    align-items: center;
}

.contentTitle2_4 {
    font-size: 32rpx;
    color: #333333;
    margin-left: 12rpx;
}

.content2_1 {
    margin-left: 28rpx;
}

.contentTitle2_2 {
    display: flex;
    margin-top: 12rpx;
}

.content2 {
    display: flex;
}

.contentTitle2_6 {
    padding: 8rpx 8rpx;
    background: #f2efff;
    font-size: 24rpx;
    color: #7e6dfc;
    margin-right: 12rpx;
}

.contentTitle2_3 {
    width: 396rpx;
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    color: #777777;
    font-size: 28rpx;
    margin-top: 14rpx;
}

.facility1 {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 20rpx;
}

.facilityImg {
    margin-left: 8rpx;
}

.text3 {
    color: #5b6799;
    font-size: 24rpx;
}

/*tab*/
.contentTab {
    display: flex;
    justify-content: space-around;
    align-items: center;
    background: #ffffff;
    padding: 20rpx 0 4rpx 0;
}

.title-sel {
    color: #777777;
    font-size: 32rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    font-weight: bold;
    height: 56rpx;
}

.title-sel-selected {
    color: #ff4f61;
    font-size: 32rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    font-weight: bold;
}

.title-sel-selected .line-style {
    background: #ff4f61;
    height: 6rpx;
    width: 40rpx;
    position: relative;
    margin-top: 10rpx;
    border-radius: 20rpx;
}

/*话题*/
.topic {
    background: #ffffff;
    border-radius: 20rpx;
    padding: 16rpx 0;
    position: relative;
    margin-bottom: 20rpx;
}

.topic1 {
    width: 96%;
    display: flex;
    align-items: flex-end;
    margin: 0rpx auto;
}

.topic1Text1 {
    margin-bottom: 12rpx;
    height: 42rpx;
}

.topic1Text1_1 {
    color: #ff4f61;
    font-size: 30rpx;
    font-weight: bold;
}

.topic1Text1_2 {
    color: hsla(0, 0%, 20%, 1);
    font-size: 30rpx;
}

.topic1Text2 {
    margin-right: 30rpx;
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    margin-bottom: 38rpx;
    height: 68rpx;
}

.topicImg {
    width: 30%;
    height: 200rpx;
}

.topic2 {
    width: 68%;
    margin-left: 2%;
}

.topic1Text2 {
    line-height: 30rpx;
}

.topic1Text2_1 {
    color: hsla(0, 0%, 20%, 1);
    font-size: 24rpx;
}

.topic1Text2_2 {
    color: hsla(0, 0%, 47%, 1);
    font-size: 24rpx;
}

.topic1Text3 {
    display: flex;
    justify-content: space-between;
    margin-right: 12rpx;
}

.topic1Text3_1 {
    color: hsla(0, 0%, 67%, 1);
    font-size: 24rpx;
}

.topic1Text3_2 {
    display: flex;
    align-items: center;
}

.topic1Text3topic1Text3 {
    width: 40rpx;
    height: 40rpx;
    border-radius: 100rpx;
}

.avatar-list-stacked .avatar {
    box-shadow: 0 0 0 1px #fff;
}

.avatar-list-stacked .avatar {
    margin-right: -0.8em !important;
}

.avatar {
    width: 1.5rem;
    height: 1.5rem;
    line-height: 5rem;
    display: inline-block;
    background: #2ddcd3 no-repeat center/cover;
    position: relative;
    text-align: center;
    color: #fff;
    font-weight: 600;
    vertical-align: bottom;
    font-size: 0.875rem;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    border-radius: 50%;
}

.title {
    position: absolute;
    right: 0;
    top: 0;
    background: hsla(44, 100%, 83%, 1);
    color: hsla(7, 100%, 50%, 1);
    border-top-right-radius: 20rpx;
    border-bottom-left-radius: 20rpx;
    width: 74rpx;
    height: 44rpx;
    text-align: center;
    line-height: 44rpx;
    font-size: 24rpx;
}

.participation {
    padding: 6rpx 24rpx;
    background: hsla(247, 96%, 71%, 1);
    color: #ffffff;
    border-radius: 68rpx;
    font-size: 24rpx;
}

.titles {
    margin-bottom: 15rpx;
}