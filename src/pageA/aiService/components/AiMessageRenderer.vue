<template>
  <view class="ai-message-renderer">
    <mp-html
      :content="renderedContent"
      :tag-style="mpHtmlConfig.tagStyle"
      :show-img-menu="mpHtmlConfig.showImgMenu"
      :selectable="mpHtmlConfig.selectable"
      :error-img="mpHtmlConfig.errorImg"
      :loading-img="mpHtmlConfig.loadingImg"
      @linktap="handleLinkTap"
      class="message-html"
    />
  </view>
</template>

<script setup>
import { computed } from 'vue';
import mpHtml from 'mp-html/dist/uni-app/components/mp-html/mp-html';
import { getMpHtmlConfig, handleLinkTap as utilHandleLinkTap, renderMessage } from '@/utils/messageRenderer.js';

// 定义 props
const props = defineProps({
  content: {
    type: String,
    required: true,
    default: ''
  }
});

// mp-html 配置
const mpHtmlConfig = getMpHtmlConfig();

// 渲染后的内容
const renderedContent = computed(() => {
  return renderMessage(props.content);
});

// 处理链接点击事件（内部处理，不向外暴露）
const handleLinkTap = (event) => {
  utilHandleLinkTap(event);
};
</script>

<style lang="scss" scoped>
.ai-message-renderer {
  width: 100%;
}

.message-html {
  font-size: 15px;
  line-height: 1.6;
  color: #333;
  word-wrap: break-word;
  word-break: break-word;
  overflow-wrap: break-word;
  margin: 0;
  padding: 0;
}
</style>
