#!/usr/bin/env node

/**
 * 测试租户ID配置脚本
 * 验证项目配置中的租户ID设置是否正确
 */

const { projects } = require('../config/config-loader.js');

// 颜色输出
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

/**
 * 测试租户ID配置
 */
function testTenantConfig() {
  log('🧪 租户ID配置测试', 'cyan');
  log('='.repeat(50));
  
  let hasIssues = false;
  
  // 检查每个项目的配置
  Object.entries(projects).forEach(([projectName, project]) => {
    log(`\n🏢 项目: ${project.name} (${projectName})`, 'blue');
    console.log(`   AppID: ${project.appid}`);
    
    // 检查默认租户ID
    if (project.defaultTenantId) {
      logSuccess(`默认租户ID: ${project.defaultTenantId}`);
    } else {
      logWarning(`缺少默认租户ID，将使用系统默认值: 194338`);
      hasIssues = true;
    }
    
    // 检查环境配置
    if (project.environments) {
      Object.entries(project.environments).forEach(([envName, envConfig]) => {
        const effectiveTenantId = envConfig.tenantId || project.defaultTenantId || '194338';
        const source = envConfig.tenantId ? '环境专用' : 
                      project.defaultTenantId ? '项目默认' : '系统默认';
        
        console.log(`   ${envName}: 租户ID ${effectiveTenantId} (${source})`);
        
        // 验证租户ID格式
        if (!/^\d+$/.test(effectiveTenantId)) {
          logError(`   环境 ${envName} 的租户ID格式不正确: ${effectiveTenantId}`);
          hasIssues = true;
        }
      });
    } else {
      logError(`项目 ${projectName} 缺少环境配置`);
      hasIssues = true;
    }
  });
  
  log('\n📊 测试结果:', 'cyan');
  if (hasIssues) {
    logWarning('发现配置问题，请检查上述警告和错误');
    return false;
  } else {
    logSuccess('所有租户ID配置正确！');
    return true;
  }
}

/**
 * 显示租户ID优先级说明
 */
function showPriorityInfo() {
  log('\n📋 租户ID优先级说明:', 'cyan');
  log('1. 环境特定的租户ID (environments[env].tenantId)', 'yellow');
  log('2. 项目默认租户ID (defaultTenantId)', 'yellow');
  log('3. 系统默认租户ID (194338)', 'yellow');
  
  log('\n💡 建议:', 'cyan');
  log('- 为每个项目设置 defaultTenantId', 'green');
  log('- 如果某个环境需要特殊租户ID，在环境配置中设置 tenantId', 'green');
  log('- 租户ID必须是纯数字字符串', 'green');
}

/**
 * 主函数
 */
function main() {
  const success = testTenantConfig();
  showPriorityInfo();
  
  if (!success) {
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = {
  testTenantConfig,
  showPriorityInfo
};
