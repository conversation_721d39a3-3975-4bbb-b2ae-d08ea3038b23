<template>
    <view>
        <navigation :background="bgColor" title="" :isSowArrow="true"></navigation>
        <view class="main" @tap="overallLike">
            <view class="prosonal">
                <view class="head">
                    <view class="title">
                        {{ userList.nickname }}
                    </view>
                    <image :src="userList.avatar || $defaultAvatar" mode=""
                        style="width: 134rpx; height: 134rpx; border-radius: 10rpx" />
                </view>
            </view>
            <view v-for="(item, index) in listData" :key="index">
                <view class="mains">
                    <view class="dynamicTitle" @tap="details">
                        <view class="dynamicTitle1">
                            <view class="discuss1_3">
                                <image :src="item.avatar || $defaultAvatar" mode="" class="img-avatar" />
                                <view>
                                    {{ item.nickname || $defaultName }}
                                </view>
                                <!-- <view class="head1_2" v-if="item.staffPost">
                                    <image
                                        src="http://cdn.xiaodingdang1.com/1/120240125/5b82ebb2-4d5c-43ae-83b8-f4621e4f3336.png"
                                        mode="" style="width: 40rpx; height: 34rpx" class="head1Img" />
                                    <view class="head1_3">{{ item.staffPost }}</view>
                                </view> -->
                            </view>
                            <view :class="item.moreBtns ? 'beyond' : 'dynamicTitle1_2'">
                                {{ item.content }}
                            </view>
                            <view class="packUp" v-if="item.moreBtn" @tap="packUp" :data-index="index">
                                {{ item.packUpShow ? '收回' : '展开' }}
                            </view>
                            <!-- 视频 -->
                            <view class="img_box" v-if="item.videos && item.videos.length > 0">
                                <view class="videos" v-for="(res, index1) in item.videos" :key="index1">
                                    <video class="video" :src="res" @tap="preview" :data-src="res"
                                        :controls="false"></video>
                                </view>
                            </view>
                            <!-- 图片 -->
                            <view class="img_box" v-else>
                                <template v-if="item.contentPhotos">
                                    <view :class="item.contentPhotos.length > 1 ? 'many_img' : ''">
                                        <!-- 遍历 -->
                                        <!-- 在这里判断图片的数量是单站还是四张，分别给不同样式，来实现，一排是两张还是三张 -->
                                        <view :class="
                                            'img_item ' +
                                            (item.contentPhotos.length == 1 || item.contentPhotos.length == 2 || item.contentPhotos.length == 4
                                                ? 'many'
                                                : item.contentPhotos.length >= 3
                                                ? 'four'
                                                : '')
                                        " v-for="(res, index1) in item.contentPhotos" :key="index1">
                                            <!-- 如果只有一张图片 bindload="oneImageLoad" 获取图片宽高，并计算宽高比，来判断图片的 mode 是 widthFix 还是 heightFix -->

                                            <!-- <view class="video_box" wx:if="{{item.type == 2}}" style="width: {{width}}rpx;height: {{height}}rpx;">
									<view class="video_marks" catchtap="previewImage" data-index="{{index}}" data-sources="{{item.contentPhotos}}"></view>
									<video class="video" src="{{res}}" bindloadedmetadata="videometa" controls="{{false}}"></video>
								</view> -->

                                            <!-- <block wx:else> -->

                                            <!-- <image wx:if="{{item.contentPhotos.length == 1}}" class="img1" data-url="{{item.contentPhotos}}" data-src="{{res}}" bindtap="previewImage" bindload="oneImageLoad" src="{{res}}" data-sources="{{item.contentPhotos}}" data-index="{{index}}"></image> -->

                                            <!-- 多张图片 一律使用 aspectFill 并固定宽高 -->

                                            <image class="img" :src="res" @tap="previewImage"
                                                :data-url="item.contentPhotos" :data-src="res"
                                                :data-sources="item.contentPhotos" :data-index="index"
                                                mode="aspectFill"></image>

                                            <!-- </block> -->
                                        </view>
                                    </view>
                                </template>
                            </view>
                            <view class="dynamicTitle1_3">
                                <view class="dynamicTitle1_3_1">
                                    {{ item.hisTimeStr }}
                                </view>
                                <view @tap="like" :data-index="index" :data-postid="item.postId">
                                    <image
                                        src="http://cdn.xiaodingdang1.com/1/120240115/2db46e59-414e-48cc-85a4-fc8559a13a9c.png"
                                        mode="" style="width: 42rpx; height: 22rpx" />
                                </view>
                                <view class="like" v-if="item.show">
                                    <view class="like1" @tap="upvote">
                                        <image
                                            src="http://cdn.xiaodingdang1.com/2024/11/22/080002305bb84596bda39b7b231f9a21.png"
                                            v-if="item.isLike" mode="" style="width: 26rpx; height: 26rpx"></image>
                                        <image v-else
                                            src="http://cdn.xiaodingdang1.com/2024/11/22/488f992580e44ff0a7bf6779c0b28f13.png"
                                            mode="" style="width: 26rpx; height: 26rpx" />
                                        <view class="like1_1">赞</view>
                                    </view>
                                    <view class="fgxs"></view>
                                    <view class="like2" @tap="ifInput">
                                        <image
                                            src="http://cdn.xiaodingdang1.com/2024/04/03/c8250680665a41a484770804f828253fpng"
                                            mode="" style="width: 26rpx; height: 26rpx" />
                                        <view class="like1_1">评论</view>
                                    </view>
                                </view>
                            </view>
                            <!-- <template v-if="item.likeUsers && item.likeUsers.length > 0">
                                <view class="liker">
                                    <view class="like-user" v-for="(res, index1) in item.likeUsers" :key="index1">
                                        <image
                                            src="http://cdn.xiaodingdang1.com/2024/11/22/eb33cc7f1b7a48b08a69630117ba6831.png"
                                            mode="" style="width: 24rpx; height: 24rpx" />
                                        <view class="like-name">{{ res.nickname || '微信用户' }}</view>
                                    </view>
                                </view>
                            </template> -->
                            <view class="likeContent" v-if="likeList[index] && likeList[index].length > 0">
                                <view class="likeContent1">
                                    <view class="likeIcon">
                                        <image lazy-load
                                            src="http://cdn.xiaodingdang1.com/2024/06/03/bfcdf618014d4b11b0c9e0457d16fdc6png"
                                            mode="" style="width: 28rpx; height: 28rpx" />
                                    </view>
                                    <view class="likeAvatar" v-if="likeList[index] && likeList[index].length < 7">
                                        <image lazy-load :src="item.avatar || $defaultAvatar" mode=""
                                            style="width: 68rpx; height: 68rpx; margin-right: 8rpx; border-radius: 68rpx"
                                            v-for="(item, index1) in likeList[index]" :key="index1">
                                        </image>

                                    </view>
                                    <view class="likeAvatar" v-if="likeList[index] && likeList[index].length > 6">
                                        <template v-if="likeList[index].isLikeShow">
                                            <image lazy-load :src="item.avatar || $defaultAvatar" mode=""
                                                style="width: 68rpx; height: 68rpx; margin-right: 8rpx; border-radius: 68rpx"
                                                v-for="(item, index1) in likeList[index]" :key="index1">
                                            </image>
                                        </template>
                                        <template v-else>
                                            <image lazy-load :src="item.avatar || $defaultAvatar" mode=""
                                                style="width: 68rpx; height: 68rpx; margin-right: 8rpx; border-radius: 68rpx"
                                                v-for="(item, index1) in likeList[index].slice(0, 6)" :key="index1">
                                            </image>
                                        </template>
                                        <view class="likeCount" v-if="likeList[index] && likeList[index].length > 0">
                                            <view class="likeBotton" @tap="likeShow" :data-index="index">
                                                <image
                                                    src="http://cdn.xiaodingdang1.com/1/120240115/2db46e59-414e-48cc-85a4-fc8559a13a9c.png"
                                                    mode="" style="width: 42rpx; height: 22rpx" />
                                                <view class="text">{{ likeList[index].length }}人</view>
                                            </view>
                                        </view>
                                    </view>
                                </view>
                            </view>
                            <view class="message" v-if="item.comments && item.comments.length > 0">
                                <view class="message1" v-for="(res, index1) in item.comments" :key="index1">
                                    <view class="messageName">{{ res.nickname || $defaultName }}:</view>

                                    <view class="messageTitle">{{ res.comment }}</view>
                                </view>
                            </view>
                        </view>
                    </view>
                </view>

                <view class="fgx"></view>
            </view>
            <view class="shadow" v-if="shadowIf" @tap="shadow"></view>
            <view style="height: 150rpx"></view>
            <!-- <publishbutton-vue roleIf='1'></publishbutton-vue> -->
        </view>
        <view class="cu-bar tabbar bg-white">
            <block v-for="(item, index) in list" :key="index">
                <view :class="'text-active ' + (item.isSpecial ? 'adAction' : '')" @tap="nav" :data-path="item.pagePath"
                    :data-index="index" :style="'color:' + (selected === index ? selectedColor : color)">
                    <image :src="selected === index ? item.selectedIconPath : item.iconPath" class="tabImg"
                        mode="aspectFit"></image>
                    <view class="tabTitle">{{ item.text }}{{ phone }}</view>
                </view>
            </block>
        </view>
        <view class="input-section" v-if="showInput">
            <input type="text" maxlength="100" class="input_input" :value="inputMessage" focus="auto" placeholder="评论"
                @input="bindinput" @blur="send" confirm-type="send" @confirm="sendTextMsg" />
            <button type="primary" class="send_out"
                style="width: 140rpx; height: 80rpx; line-height: 80rpx; margin-bottom: 5rpx"
                :disabled="inputMessage ? false : true" @tap="send">
                发送
            </button>
        </view>
    </view>
</template>

<script>
    export default {
        data() {
            return {
                pageNum: 1,
                types: '',
                showIndex: '',
                btnIf: false,
                bottomHeight: 0,

                //定义comment容器与page容器下边界之间的距离
                content: '',

                replyShow: false,

                userList: {
                    nickname: '',
                    avatar: ''
                },

                postId: '',
                inputMessage: '',
                showInput: false,
                listData: [],
                selected: 0,

                //默认选中首页
                color: '#777777',

                selectedColor: '#7E6DFC',
                backgroundColor: '#ffffff',
                userId: '',
                shadowIf: '',
                bgColor: '',
                res: [],
                list: [],
                phone: '',
                more: false,
                normore: false,
                likeList: []
            };
        },
        /**
         * 生命周期函数--监听页面加载
         */
        onLoad(options) {
            this.setData({
                userId: options.userId
            });
            this.getList();
            this.userInfo();
        },
        /**
         * 生命周期函数--监听页面初次渲染完成
         */
        onReady() {},
        /**
         * 生命周期函数--监听页面显示
         */
        onShow() {},
        /**
         * 生命周期函数--监听页面隐藏
         */
        onHide() {},
        /**
         * 生命周期函数--监听页面卸载
         */
        onUnload() {},
        /**
         * 页面相关事件处理函数--监听用户下拉动作
         */
        onPullDownRefresh() {},
        /**
         * 页面上拉触底事件的处理函数
         */
        onReachBottom() {
            if (this.types == 1) {
                this.setData({
                    pageNum: this.pageNum + 1
                });
                this.getList();
            }
        },
        /**
         * 用户点击右上角分享
         */
        updated() {
            // console.log('updateeeee', this.listData)
            // console.log('this.$refsthis.$refs', this.$refs)
        },
        onShareAppMessage() {},

        onShareTimeline() {},
        methods: {
            nav(e) {
                const data = e.currentTarget.dataset;
                const url = data.path;
                if (url) {
                    uni.redirectTo({
                        url: url
                    });
                }
            },

            getList() {
                //列表
                let data = {
                    pageSize: 10,
                    pageNum: this.pageNum,
                    userId: this.userId
                };
                this.$axios.get(this.$api.customerServiceStep, data).then((res) => {
                    if (res.data.code == 200) {
                        let data = res.data.rows;
                        let types = data.length == 10 ? 1 : 2;
                        let dtnow;
                        let likeList;
                        if (this.pageNum == 1) {
                            dtnow = [];
                            likeList = []
                        } else {
                            dtnow = this.listData;
                            likeList = this.likeList
                        }
                        data.forEach((item) => {
                            item.show = false;
                            if (item.content.length > 100) {
                                item.packUpShow = false;
                                item.moreBtns = true;
                                item.moreBtn = true;
                            } else {
                                item.packUpShow = false;
                                item.moreBtns = false;
                                item.moreBtn = false;
                            }
                            item.likeUsers.forEach((item) => item.isLikeShow = false)
                            likeList.push(item.likeUsers && item.likeUsers.length > 0 ? item
                                .likeUsers : [])
                        });
                        this.setData({
                            listData: dtnow.concat(data),
                            types: types,
                            likeList
                        });
                    } else {
                        // uni.showToast({
                        //     title: res.data.msg,
                        //     icon: 'none',
                        //     duration: 2000 //持续的时间
                        // });
                    }
                });
            },

            like(e) {
                let listData = this.listData;
                let index = e.currentTarget.dataset.index;
                let postid = e.currentTarget.dataset.postid;
                this.setData({
                    [`listData[${index}].show`]: !listData[index].show,
                    postId: postid,
                    showIndex: index,
                    shadowIf: !this.shadowIf
                });
            },

            // overallLike(){
            //   let listData=this.data.listData
            //   listData.forEach(item => {
            //     if(item.show){
            //       item.show=!item.show
            //     }
            //   });
            //   this.setData({
            //     listData:listData
            //   })
            // }
            //点击出现输入框
            ifInput() {
                let listData = this.listData;
                listData.forEach((item) => {
                    if (item.show) {
                        item.show = !item.show;
                    }
                });
                this.setData({
                    listData: listData,
                    shadowIf: false
                });
                this.setData({
                    showInput: true
                });
            },

            // //隐藏输入框
            // onHideInput() {
            //   this.setData({
            //     showInput: false
            //   })
            // },
            // 评论输入框
            bindInputMsg(e) {
                this.setData({
                    inputMessage: e.detail.value.replace(/\s+/g, '')
                });
            },

            // confirm-type="send" 键盘底部发送按钮触发函数
            sendTextMsg(e) {},

            send() {
                //发送
                if (!this.inputMessage) {
                    // uni.showToast({
                    //     title: '请输入内容',
                    //     icon: 'none',
                    //     duration: 2000 //持续的时间
                    // });
                    this.setData({
                        inputMessage: '',
                        showInput: false
                    });
                    return
                }
                let data = {
                    postId: this.postId,
                    comment: this.inputMessage
                };
                this.$axios.post(this.$api.feedPostComment, data).then((res) => {
                    if (res.data.code == 200) {
                        const userInfo = uni.getStorageSync('userInfo') || {}
                        const commentobj = {
                            nickname: userInfo.nickname,
                            comment: this.inputMessage
                        }
                        const list = this.listData[this.showIndex].comments || []
                        const commentList = list.push(commentobj)
                        this.setData({
                            inputMessage: '',
                            showInput: false
                        });
                        // uni.showToast({
                        //     title: res.data.msg,
                        //     icon: 'none',
                        //     duration: 2000 //持续的时间
                        // });
                        // this.getList();
                    } else {
                        this.setData({
                            showInput: false
                        });
                    }
                });
            },
            likeShow(e) {
                var index = e.currentTarget.dataset.index;
                this.likeList[index].isLikeShow = !this.likeList[index].isLikeShow
            },
            upvote() {
                //点赞
                let data = {
                    postId: this.postId
                };
                const index = this.showIndex
                const userInfo = uni.getStorageSync('userInfo') || {}
                const likeListItem = {
                    avatar: userInfo.avatar,
                    nickname: userInfo.nickname,
                    userId: userInfo.userId,
                    postId: this.postId,
                }
                const length = this.likeList[index].length
                if (!this.listData[index].isLike) {
                    let list = this.likeList[index]
                    list.unshift(likeListItem)
                    // this.setData({
                    // [`likeList[${index}][${length}]`]: likeListItem
                    // })
                } else {
                    const list = this.likeList
                    // list[index].splice(this.likeList[index].length - 1, 1)
                    list[index].splice(0, 1)
                    this.setData({
                        likeList: list
                    })
                }
                this.setData({
                    [`listData[${index}].isLike`]: !this.listData[index].isLike,
                    [`listData[${index}].show`]: false,
                    shadowIf: false
                })
                this.$axios.get(this.$api.feedPostLikes, data).then((res) => {
                    if (res.data.code == 200) {

                    }
                });
            },

            userInfo() {
                //获取登录用户信息

                this.$axios.get(this.$api.userInfo).then((res) => {
                    if (res.data.code == 200) {
                        this.setData({
                            userList: res.data.data
                        });
                    } else {
                        // uni.showToast({
                        //     title: res.data.msg,
                        //     icon: 'none',
                        //     duration: 2000 //持续的时间
                        // });
                    }
                });
            },

            // 预览图片
            previewImage(e) {
                var current = e.currentTarget.dataset.src;
                var url = e.currentTarget.dataset.url;
                uni.previewImage({
                    current: current,
                    // 当前显示图片的http链接
                    urls: url // 需要预览的图片http链接列表
                });
            },

            preview(event) {
                let src = event.currentTarget.dataset.src;
                let maparr = [];
                maparr.push({
                    type: 'video',
                    url: src
                });
                let index = 0;
                // 既有视频又有图片用这个
                console.log(maparr);
                uni.previewMedia({
                    sources: maparr,
                    // 官方文档 : 素材展示列表 是个数组对象 一个url链接 一个type 判断这是视频还是图片
                    // 接收image 和 video 所以处理数据时 最好给个type 指定 image 或者 video
                    current: index
                    // 当前显示的资源序号
                });
            },

            packUp(e) {
                //收起全文
                let index = e.currentTarget.dataset.index;
                this.setData({
                    [`listData[${index}].packUpShow`]: !this.listData[index].packUpShow,
                    [`listData[${index}].moreBtns`]: !this.listData[index].moreBtns
                });
            },

            // 获取焦点 唤起软键盘
            bindfocus(e) {
                this.setData({
                    bottomHeight: e.detail.height //将键盘的高度设置为comment容器与page容器下边界之间的距离。
                });
            },

            // 输入内容
            bindinput(e) {
                this.setData({
                    inputMessage: e.detail.value
                });
            },

            // 失去焦点
            bindblur(e) {
                this.setData({
                    bottomHeight: 0
                });
            },

            showInputFun() {
                this.setData({
                    showInput: true
                });
            },

            //     //隐藏输入框
            // onHideInput() {
            //   this.setData({
            //     showInput: false
            //   })
            //   if(this.data.inputMessage==''){
            //     return
            //   }
            //   this.send()
            // },
            shadow() {
                this.setData({
                    shadowIf: false,
                    showInput: false,
                    [`listData[${this.showIndex}].show`]: !this.listData[this.showIndex].show
                });
            },

            overallLike() {
                console.log('占位：函数 overallLike 未声明');
            },

            details() {
                console.log('占位：函数 details 未声明');
            }
        }
    };
</script>
<style scoped>
    @import './mydynamics.css';
</style>