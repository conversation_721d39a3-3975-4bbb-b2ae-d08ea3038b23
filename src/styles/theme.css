page{
    /* 颜色系统 */
  --color-primary: #fe5b86;
  --color-primary-light: #fc867a;
  --color-secondary: #7e6dfc;
  --color-secondary-light: #908ff0;
  --color-accent: #ff4f61;

  /* 文本颜色 */
  --color-text-primary: #333333;
  --color-text-secondary: #777777;
  --color-text-tertiary: #aaaaaa;
  --color-text-inverse: #ffffff;

  /* 背景颜色 */
  --color-bg-primary: #ffffff;
  --color-bg-secondary: #f8f9f9;
  --color-bg-tertiary: #efefef;
  --color-bg-overlay: rgba(0, 0, 0, 0.5);

  /* 边框颜色 */
  --color-border-light: #ebebeb;
  --color-border-primary: #ffd6d7;

  /* 字体大小 */
  --font-size-xs: 20rpx;
  --font-size-sm: 22rpx;
  --font-size-base: 24rpx;
  --font-size-md: 28rpx;
  --font-size-lg: 30rpx;
  --font-size-xl: 32rpx;
  --font-size-2xl: 34rpx;
  --font-size-3xl: 44rpx;

  /* 间距系统 */
  --spacing-xs: 4rpx;
  --spacing-sm: 8rpx;
  --spacing-md: 12rpx;
  --spacing-lg: 16rpx;
  --spacing-xl: 20rpx;
  --spacing-2xl: 24rpx;
  --spacing-3xl: 32rpx;
  --spacing-4xl: 40rpx;

  /* 圆角 */
  --radius-sm: 6rpx;
  --radius-md: 10rpx;
  --radius-lg: 20rpx;
  --radius-xl: 30rpx;
  --radius-full: 100rpx;

  /* 阴影 */
  --shadow-sm: 0px 0px 6px rgba(178, 178, 178, 0.3);
  --shadow-md: 0px 0px 12px rgba(0, 0, 0, 0.05);
  --shadow-lg: -8rpx -8rpx -8rpx hsla(0, 0%, 96%, 0.5);

  /* 渐变 */
  --gradient-primary: linear-gradient(180deg, #fc867a 0%, #fe5b86 100%);
  --gradient-secondary: linear-gradient(180deg, #908ff0 0%, #b391ff 100%);
  --gradient-overlay: linear-gradient(
    180deg,
    rgba(0, 0, 0, 0) 0%,
    rgba(0, 0, 0, 0.5) 100%
  );
}