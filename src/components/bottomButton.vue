<template>
    <view class="bottom">
        <view class="bottom-left" @tap="onComment">
            <image src="http://cdn.xiaodingdang1.com/2024/12/31/53eba0609b6749bf9e22ab45c5afa253.png"></image>
            说点什么...
        </view>
        <view class="bottom-right">
            <image @tap="like()"
                :src="isLike? 'http://cdn.xiaodingdang1.com/2024/12/31/2899047a49fd47fe97e0ec71c5d21635.png': 'http://cdn.xiaodingdang1.com/2024/12/31/6bb4463717ba467595e886bea2eb1f66.png'">
            </image>
            <view>{{likesCount}}</view>
            <image @tap="collect()"
                :src="isFavorite? 'http://cdn.xiaodingdang1.com/2024/12/31/ba03d7e1a9344a63aea664f0bb37bae0.png': 'http://cdn.xiaodingdang1.com/2024/12/31/c3845e950d834cad9602ab1a60203d7d.png'">
            </image>
            <view>{{favoriteCount}}</view>
            <image @tap="onComment" src="http://cdn.xiaodingdang1.com/2024/12/31/713c916b4d9a455fa74c334e561cba21.png">
            </image>
            <view>{{commentsCount}}</view>
            <button class="custom-button" open-type="share">
                <image src="http://cdn.xiaodingdang1.com/2024/12/31/5f6bc1ff9f33459fa192961d56f657f9.png">
                </image>
                <!-- <view class="discuss5_2">{{ shareCount }}</view> -->
            </button>

        </view>
        <bottom-input-vue :inputBottomHeight="inputBottomHeight" :show="show" @callback="callback"
            @close="close"></bottom-input-vue>
    </view>
</template>

<script setup>
    import bottomInputVue from "./bottomInput.vue";
    import {
        onLoad
    } from "@dcloudio/uni-app";
    import {
        ref,
        reactive,
        getCurrentInstance,
        onMounted,
        watch
    } from "vue";
    const instance = getCurrentInstance();
    const App = instance.appContext.config.globalProperties
    const commentsCount = ref(0)
    const favoriteCount = ref(0)
    const likesCount = ref(0)
    const isFavorite = ref(false)
    const isLike = ref(false)
    const content = ref('')
    const show = ref(false)
    const emits = defineEmits(['input'])
    const props = defineProps({
        inputBottomHeight: {
            type: Number,
            default: 0
        },
        item: {
            type: Object,
            default: () => {}
        }
    })
    watch(() => props.item, (newVal) => {
        console.log('itemmmmm', newVal)
        commentsCount.value = newVal.commentsCount || 0
        favoriteCount.value = newVal.favoriteCount || 0
        likesCount.value = newVal.likesCount || 0
        isFavorite.value = newVal.isFavorite
        isLike.value = newVal.isLike
    })
    const onComment = () => {
        show.value = true
    }
    const callback = (value) => {
        if (value) {
            commentsCount.value = commentsCount.value + 1
        }
        emits('input', value)
        // close()
    }
    const close = () => {
        show.value = false
    }
    // const shareCallback = () => {
    //     shareCount.value++
    // }
    const collect = () => {
        favoriteCount.value = !isFavorite.value ? favoriteCount.value + 1 : favoriteCount.value - 1
        isFavorite.value = !isFavorite.value
        const api = App.$api.feedPostFavorite
        const $options = App.$axios.get
        $options(api, {
            postId: props.item.postId
        })
    }
    const like = () => {
        likesCount.value = !isLike.value ? likesCount.value + 1 : likesCount.value - 1
        isLike.value = !isLike.value
        const api = App.$api.feedPostLikes
        const $options = App.$axios.get
        $options(api, {
            postId: props.item.postId
        })
    }
</script>

<style lang="less" scoped>
    .bottom {
        .custom-button {
            background: #fff;
            color: hsla(0, 0%, 47%, 1);
            font-size: 0;
            margin: 0;
            padding: 6rpx;
        }

        background: #FFFFFF;
        border: 1rpx solid #F0F0F0;
        position: fixed;
        bottom: 0;
        left: 0;
        z-index: 9;
        background-color: white;
        width: 100%;
        display: flex;
        align-items: center;
        padding: 30rpx 32rpx 50rpx 32rpx;

        .input-section {
            position: fixed;
            display: flex;
            align-items: center;
            width: 100%;
            height: 134rpx;
            bottom: 0rpx;
            left: 0rpx;
            z-index: 500;
            background: #f2f2f2;
        }

        .send_out {
            margin: 0;
            padding: 0;
            text-align: center;
            border: 1rpx solid #cccccc;
            border-radius: 10rpx;
            width: 140rpx;
            height: 80rpx;
            line-height: 80rpx;
            margin-bottom: 10rpx;
            /* 将发送按钮固定在底部 */
            position: absolute;
            right: 24rpx;
            bottom: 20rpx;
        }

        .input_input {
            background: #fff;
            margin-left: 12rpx;
            z-index: 500;
            width: 70%;
            height: 94rpx;
            padding-left: 35rpx;
            font-size: 30rpx;
            border-radius: 6rpx;
        }

        &-left {
            font-weight: 500;
            font-size: 24rpx;
            color: #ACACAC;
            width: 286rpx;
            height: 60rpx;
            background: #F6F6F6;
            border-radius: 76rpx;
            margin-right: 40rpx;
            display: flex;
            align-items: center;

            image {
                width: 40rpx;
                height: 40rpx;
                margin-left: 18rpx;
                margin-right: 2rpx;
            }
        }

        &-right {
            font-weight: 500;
            font-size: 24rpx;
            color: #7D7D7D;
            display: flex;
            align-items: center;
            justify-content: center;
            flex: 1;

            image {
                width: 40rpx;
                height: 40rpx;
                margin-right: 12rpx;

                &+view {
                    margin-right: 24rpx;
                }
            }
        }
    }
</style>