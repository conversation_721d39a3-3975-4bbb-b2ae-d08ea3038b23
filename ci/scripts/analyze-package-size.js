#!/usr/bin/env node

/**
 * 小程序包大小分析工具
 * 分析构建产物大小，提供优化建议
 */

const fs = require('fs');
const path = require('path');

// 颜色输出
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

/**
 * 格式化文件大小
 */
function formatFileSize(bytes) {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 获取目录大小
 */
function getDirectorySize(dirPath) {
  let totalSize = 0;
  const files = [];

  function scanDirectory(dir) {
    if (!fs.existsSync(dir)) return;
    
    const items = fs.readdirSync(dir);
    
    items.forEach(item => {
      const itemPath = path.join(dir, item);
      const stats = fs.statSync(itemPath);
      
      if (stats.isDirectory()) {
        scanDirectory(itemPath);
      } else {
        const size = stats.size;
        totalSize += size;
        files.push({
          path: path.relative(dirPath, itemPath),
          size: size
        });
      }
    });
  }

  scanDirectory(dirPath);
  return { totalSize, files };
}

/**
 * 分析主包大小
 */
function analyzeMainPackage() {
  const distPath = path.resolve(__dirname, '../../dist/build/mp-weixin');
  
  if (!fs.existsSync(distPath)) {
    logError('构建产物不存在，请先运行构建命令');
    return null;
  }

  const { totalSize, files } = getDirectorySize(distPath);
  
  // 按大小排序
  files.sort((a, b) => b.size - a.size);
  
  // 分类文件
  const categories = {
    js: files.filter(f => f.path.endsWith('.js')),
    wxss: files.filter(f => f.path.endsWith('.wxss')),
    wxml: files.filter(f => f.path.endsWith('.wxml')),
    json: files.filter(f => f.path.endsWith('.json')),
    images: files.filter(f => /\.(png|jpg|jpeg|gif|svg|webp)$/i.test(f.path)),
    others: files.filter(f => !/\.(js|wxss|wxml|json|png|jpg|jpeg|gif|svg|webp)$/i.test(f.path))
  };

  return {
    totalSize,
    files,
    categories,
    isOverLimit: totalSize > 2048 * 1024 // 2MB限制
  };
}

/**
 * 显示包大小分析结果
 */
function showPackageAnalysis() {
  log('📦 小程序包大小分析', 'blue');
  log('='.repeat(50));

  const analysis = analyzeMainPackage();
  if (!analysis) return;

  const { totalSize, categories, isOverLimit } = analysis;
  
  // 显示总体信息
  const sizeInfo = `总大小: ${formatFileSize(totalSize)}`;
  if (isOverLimit) {
    logError(`${sizeInfo} (超出2MB限制)`);
  } else {
    logSuccess(`${sizeInfo} (在限制范围内)`);
  }

  const remainingSize = 2048 * 1024 - totalSize;
  if (remainingSize > 0) {
    logInfo(`剩余空间: ${formatFileSize(remainingSize)}`);
  } else {
    logError(`超出限制: ${formatFileSize(-remainingSize)}`);
  }

  // 显示各类文件统计
  log('\n📊 文件类型统计:', 'blue');
  Object.entries(categories).forEach(([type, files]) => {
    if (files.length === 0) return;
    
    const typeSize = files.reduce((sum, f) => sum + f.size, 0);
    const percentage = ((typeSize / totalSize) * 100).toFixed(1);
    
    console.log(`  ${type.toUpperCase()}: ${formatFileSize(typeSize)} (${percentage}%) - ${files.length}个文件`);
  });

  // 显示最大的文件
  log('\n🔍 最大的10个文件:', 'blue');
  analysis.files.slice(0, 10).forEach((file, index) => {
    const percentage = ((file.size / totalSize) * 100).toFixed(1);
    console.log(`  ${index + 1}. ${file.path}: ${formatFileSize(file.size)} (${percentage}%)`);
  });
}

/**
 * 提供优化建议
 */
function provideOptimizationSuggestions() {
  log('\n💡 包大小优化建议', 'blue');
  log('='.repeat(50));

  const analysis = analyzeMainPackage();
  if (!analysis) return;

  const { categories, totalSize } = analysis;
  
  // JavaScript优化建议
  const jsSize = categories.js.reduce((sum, f) => sum + f.size, 0);
  if (jsSize > 1024 * 1024) { // 超过1MB
    logWarning('JavaScript文件过大，建议优化:');
    console.log('  • 启用代码压缩和混淆');
    console.log('  • 移除未使用的代码 (Tree Shaking)');
    console.log('  • 将大型第三方库移到分包');
    console.log('  • 使用动态导入延迟加载');
  }

  // 图片优化建议
  const imageSize = categories.images.reduce((sum, f) => sum + f.size, 0);
  if (imageSize > 512 * 1024) { // 超过512KB
    logWarning('图片文件过大，建议优化:');
    console.log('  • 压缩图片质量');
    console.log('  • 使用WebP格式');
    console.log('  • 将大图片上传到CDN');
    console.log('  • 使用雪碧图合并小图标');
  }

  // 分包建议
  if (totalSize > 1536 * 1024) { // 超过1.5MB
    logWarning('主包过大，建议使用分包:');
    console.log('  • 将非首页功能移到分包');
    console.log('  • 按功能模块划分分包');
    console.log('  • 使用分包预下载优化体验');
  }

  // vendor.js特别检查
  const vendorFile = categories.js.find(f => f.path.includes('vendor.js'));
  if (vendorFile && vendorFile.size > 512 * 1024) {
    logWarning('vendor.js文件过大，建议:');
    console.log('  • 检查是否引入了不必要的第三方库');
    console.log('  • 使用按需导入减少包大小');
    console.log('  • 考虑替换体积较大的库');
  }
}

/**
 * 生成优化报告
 */
function generateOptimizationReport() {
  const analysis = analyzeMainPackage();
  if (!analysis) return;

  const reportPath = path.resolve(__dirname, '../reports/package-size-report.json');
  const reportDir = path.dirname(reportPath);
  
  if (!fs.existsSync(reportDir)) {
    fs.mkdirSync(reportDir, { recursive: true });
  }

  const report = {
    timestamp: new Date().toISOString(),
    totalSize: analysis.totalSize,
    totalSizeFormatted: formatFileSize(analysis.totalSize),
    isOverLimit: analysis.isOverLimit,
    categories: {},
    topFiles: analysis.files.slice(0, 20).map(f => ({
      path: f.path,
      size: f.size,
      sizeFormatted: formatFileSize(f.size),
      percentage: ((f.size / analysis.totalSize) * 100).toFixed(2)
    }))
  };

  // 添加分类统计
  Object.entries(analysis.categories).forEach(([type, files]) => {
    const typeSize = files.reduce((sum, f) => sum + f.size, 0);
    report.categories[type] = {
      count: files.length,
      size: typeSize,
      sizeFormatted: formatFileSize(typeSize),
      percentage: ((typeSize / analysis.totalSize) * 100).toFixed(2)
    };
  });

  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  logSuccess(`优化报告已生成: ${reportPath}`);
}

/**
 * 显示帮助信息
 */
function showHelp() {
  log('小程序包大小分析工具', 'blue');
  log('');
  log('使用方法:', 'yellow');
  log('  node ci/scripts/analyze-package-size.js [选项]', 'white');
  log('');
  log('选项:', 'yellow');
  log('  --help, -h     显示帮助信息', 'white');
  log('  --report       生成详细报告', 'white');
  log('  --suggestions  显示优化建议', 'white');
  log('');
  log('功能说明:', 'yellow');
  log('  - 分析构建产物大小', 'white');
  log('  - 按文件类型统计', 'white');
  log('  - 识别最大的文件', 'white');
  log('  - 提供优化建议', 'white');
  log('');
}

// 主函数
function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--help') || args.includes('-h')) {
    showHelp();
    return;
  }

  showPackageAnalysis();
  
  if (args.includes('--suggestions')) {
    provideOptimizationSuggestions();
  }
  
  if (args.includes('--report')) {
    generateOptimizationReport();
  }
}

// 运行主函数
if (require.main === module) {
  main();
}

module.exports = {
  analyzeMainPackage,
  formatFileSize
};
