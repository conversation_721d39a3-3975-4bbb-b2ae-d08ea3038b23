import store from '@/store/index.js';
import { getEnv, ENV_CONFIG } from '@/utils/config';
console.log(getEnv(), ENV_CONFIG);

const config = {
  baseUrl: ENV_CONFIG[getEnv()].VITE_BASE_API,
  timeout: 30000,
};

const getHeader = () => {
  const header = {
    'Clientid': '428a8310cd442757ae699df5d894f051',
    'X-Tenant-ID': '194338',
  };
  if (uni.getStorageSync('token')) {
    header['Authorization'] = 'Bearer ' + uni.getStorageSync('token');
  }
  if (uni.getStorageSync('tenantId')) {
    header['X-Tenant-ID'] = uni.getStorageSync('tenantId');
  }
  return header;
};

const request = (options) => {
  return new Promise((resolve, reject) => {
    if (!options.url) {
      console.error('请求地址异常：', options.url);
      reject('请求地址异常：' + options.url);
      return;
    }

    const url = `${config.baseUrl}${options.url}`;

    let headers = getHeader();

    headers = Object.assign(headers, options?.headers || {});

    uni.request({
      url,
      header: headers,
      method: options?.method || 'GET',
      data: options?.data || {},
      success: (res) => {
        if (res.statusCode == 200) {
          if (res.data.code !== 401) {
            resolve(res.data);
          }
          // 登录校验失败
          if (res.data.code === 401) {
            uni.showModal({
              title: '提示',
              content: '登录已过期，请立即登录，否则无法正常使用',
              success: (res) => {
                uni.hideLoading();
                if (res.confirm) {
                  uni.redirectTo({
                    url: '/pageA/login',
                  });
                }
              },
            });
          }
        } else if (res.statusCode == 401) {
          //未登录，跳转登录界面
          reject(res.data);
          uni.showModal({
            title: '提示',
            content: '登录已过期，请立即登录，否则无法正常使用',
            success(res) {
              if (res.confirm) {
                uni.reLaunch({
                  url: '/pageA/login',
                });
              }
            },
          });
        } else {
          reject(res.data);
          uni.showToast({
            title: res?.data?.msg || '网络开小差了~ 稍后重试',
            icon: 'none',
            duration: 2000, //持续的时间
          });
        }
      },
      fail: (err) => {
        reject(err);
        uni.showToast({
          title: '网络开小差了~ 稍后重试',
          icon: 'none',
          duration: 2000, //持续的时间
        });
      },
      complete: (res) => {
        store.commit('m_user/changeLoading', false);
        uni.hideToast();
        uni.hideLoading();
        uni.stopPullDownRefresh();
      },
      timeout: config.timeout,
    });
  });
};

export const uploadFile = (options) => {
  return new Promise((resolve, reject) => {
    uni.uploadFile({
      filePath: options.filePath,
      name: 'file',
      url: config.baseUrl + 'common/uploadOSS',
      header: {
        'Content-Type': 'multipart/form-data;charset=utf-8',
        'Authorization': 'Bearer ' + uni.getStorageSync('token'),
        'Accept-Encoding': 'gzip',
        'Clientid': '428a8310cd442757ae699df5d894f051',
      },
      success: function (res) {
        console.log(res);
        let data = JSON.parse(res.data);
        console.log(data);
        if (data.code == 200) {
          resolve(data.data.url);
        }
      },
    });
  });
};

export default request;
