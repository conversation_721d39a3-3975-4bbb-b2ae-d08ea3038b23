.publish-service {
    .content {
        min-height: calc(100vh - 142rpx);
        overflow-y: scroll;
        padding: 24rpx 30rpx;
        .top-service {
            /deep/.u-input {
                width: 482rpx !important;
                padding-right: 24rpx !important;
            }
        }
        .title {
            font-weight: 400;
            font-size: 48rpx;
            color: #333333;
            line-height: 56rpx;
        }
        .item {
            display: flex;
            margin-bottom: 24rpx;
        }
        .left {
            width: 166rpx;
            text-align: right;
            font-weight: 400;
            font-size: 28rpx;
            color: #333333;
            line-height: 72rpx;
        }
        .list {
             display: flex;
             align-items: center;
             margin-bottom: 28rpx;
        }
        .right {
            margin-left: 24rpx;
            flex: 1;       
            /deep/.u-input {
                width: 380rpx;
                background-color: #F4F7FD !important;
                padding-left: 24rpx !important;
            }
            /deep/.u-icon {
                 margin-left: 45rpx !important;
            }
        }
        .add {
            margin-top: 16rpx;
            width: 100%;
            text-align: center;
            image {
                width: 80rpx;
                height: 80rpx;   
            }
        }
       
    }
    .bottom {
        width: 100vw;
        padding: 30rpx 15rpx;
        background-color: white;
        z-index: 999;
        position: sticky;
        bottom: 0;
        display: flex;
        align-items: center;
        view {
            padding: 0 15rpx;
            width: 50%;
        }
    }
}

