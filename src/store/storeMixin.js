import {
    mapState,
    mapGetters,
    mapMutations,
    mapActions
} from 'vuex';

export default {
    computed: {
        // 将vuex的state中的所有变量，解构到全局混入的mixin中
        ...mapState('m_user', ['user_isLogin', 'user_loading', 'avatorName', 'unreadCounts']),
        ...mapGetters('m_user', ['getTotalUnreadCount', 'getUserUnreadCount', 'getAgentUnreadCount']),
        ...mapState('m_common', ['currentPlayingVideo']),
        ...mapState('m_rehabilitation', ['isShare', 'shareData'])
    },
    methods: {
        ...mapMutations('m_user', ['setLoginStatus', 'checkLogin', 'changeLoading', 'setAvatorName', 'setUnreadCounts', 'clearUnreadCounts']),
        ...mapMutations('m_common', ['playVideo', 'pauseVideo']),
        ...mapMutations('m_rehabilitation', ['changeShareState', 'setShareData']),
    },
};