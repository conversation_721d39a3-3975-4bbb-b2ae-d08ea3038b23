<template>
  <view class="brand-module">
    <!-- 标题区域 -->
    <view class="section-header">
      <view class="section-header__title">品牌介绍</view>
      <view class="section-header__more"></view>
    </view>

    <!-- 品牌介绍 -->
    <view class="brand" @click="handleBrandClick">
      <view class="brand-img">
        <image lazy-load :src="brandImageUrl" mode="aspectFill" />
      </view>
      <view class="brand-text2">
        <view class="brand-text2_1">
          {{ homeBrand.clubName }}
        </view>
        <view class="package-right-text4_3">点击进入</view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, computed, onMounted, getCurrentInstance } from 'vue';

// 响应式数据
const homeBrand = reactive({
  clubFacilityPhotos: '',
  clubName: '',
});

// 获取当前实例
const instance = getCurrentInstance();

console.log('instance', instance);

// 计算属性
const brandImageUrl = computed(() => {
  return (
    (homeBrand.clubFacilityPhotos && homeBrand.clubFacilityPhotos[0]) || ''
  );
});

// 生命周期
onMounted(() => {
  initBrandData();
});

// 初始化品牌数据
const initBrandData = () => {
  homeBrandFun();
};

// 获取品牌介绍数据
const homeBrandFun = () => {
  instance.proxy.$axios
    .get(instance.proxy.$api.homeBrand)
    .then((res) => {
      if (res.data.code == 200) {
        Object.assign(homeBrand, res.data.data);
      }
    })
    .catch((error) => {
      console.error('获取品牌介绍数据失败:', error);
    });
};

// 事件处理 - 内部实现页面跳转
const handleBrandClick = () => {
  uni.navigateTo({
    url: '/pageA/pageB/home/<USER>',
  });
};
</script>

<style scoped>
/* ===== 品牌介绍模块样式 ===== */
.brand-module {
  width: 100%;
  margin: 40rpx 0;
}

/* 标题区域 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 40rpx 0 20rpx 0;
}

.section-header__title {
  font-weight: bold;
  font-size: 34rpx;
  color: #333333;
}

.section-header__more {
  display: flex;
  color: #aaaaaa;
  font-size: 28rpx;
  align-items: center;
}

/* 品牌介绍 - 与原有brand样式一致 */
.brand {
  margin: 20rpx 0;
}

.brand-img {
  width: 100%;
  height: 480rpx;
}

.brand-img image {
  border-top-left-radius: 20rpx;
  border-top-right-radius: 20rpx;
  width: 100%;
  height: 100%;
}

.brand-text2 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 20rpx;
  background: #fff;
  border-bottom-left-radius: 20rpx;
  border-bottom-right-radius: 20rpx;
  box-shadow: 0px 0px 12px 0px rgba(0, 0, 0, 0.05);
}

.brand-text2_1 {
  font-size: 32rpx;
  color: #353b5b;
}

.package-right-text4_3 {
  background: -webkit-gradient(
    linear,
    left top,
    left bottom,
    color-stop(0%, #fc867a),
    color-stop(100%, #fe5b86)
  );
  background: -webkit-linear-gradient(180deg, #fc867a 0%, #fe5b86 100%);
  background: -o-linear-gradient(180deg, #fc867a 0%, #fe5b86 100%);
  background: -ms-linear-gradient(180deg, #fc867a 0%, #fe5b86 100%);
  background: linear-gradient(180deg, #fc867a 0%, #fe5b86 100%);
  color: #fff;
  border-radius: 6rpx;
  font-size: 28rpx;
  width: 144rpx;
  height: 52rpx;
  text-align: center;
  line-height: 52rpx;
}
</style>
