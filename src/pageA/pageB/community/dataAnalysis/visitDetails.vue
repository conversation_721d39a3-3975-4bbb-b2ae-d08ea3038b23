<template>
	<navigation title="访问详情" :background="bgColor"></navigation>
	<view class="main">
		<view class="content">
			<view class="list">
				<view class="coment1" v-for="res,index in item" :key="index">
					<view class="coment1_1">
						{{res.lastTime}}
					</view>
					<view class="coment1_2">
						访问了{{res.title}} 浏览{{res.durationStrMinute}}
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				bgColor: '',
				item: []
			}
		},
		methods: {},
		onLoad(options) {
			let item = JSON.parse(options.item)
			this.item = item
		},
		onPageScroll: function(e) {
			if (e.scrollTop > 0) {
				this.setData({
					bgColor: '#ffffff'
				});
			} else if (e.scrollTop < 2) {
				this.setData({
					bgColor: ''
				});
			}
		},
	}
</script>

<style lang="less" scoped>
	@import './visitDetails.less';
</style>