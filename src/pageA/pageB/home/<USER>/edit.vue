<template>
  <view class="page-container">
    <web-view :src="webViewUrl"></web-view>
  </view>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import utils from '@/utils/util';
import { createUserH5Api, getEditStatusApi } from './api/rehabilitation';

const state = reactive({});
const webViewUrl = ref('');

onLoad((options) => {
  const { tenantId, userId } = uni.getStorageSync('userInfo') || {};
  Object.assign(state, {
    type: options.type,
    id: options.id,
    tenantId,
    userId,
  });

  if (options.id) {
    getEditStatus();
  }
});

const getEditStatus = async () => {
  const res = await getEditStatusApi(state.id);
  if (res.data.status) {
    generateWebView(res.data.cardId);
  } else {
    createUserH5();
  }
};

const createUserH5 = async () => {
  const res = await createUserH5Api({
    templateId: state.id,
  });
  if (res.data) {
    generateWebView(res.data);
  }
};

const generateWebView = (cardId) => {
  webViewUrl.value = `https://h5.xiaodingdang1.com/mobile.html?${utils.queryParams(
    {
      cardId,
      tenantId: state.tenantId,
      userId: state.userId,
    },
  )}#wechat_redirect`;
};
</script>
<style scoped lang="less">
.page-container {
  height: 100%;
  width: 100%;
  overflow: hidden;
}
</style>
