{"name": "mother-club", "appid": "__UNI__EDE3360", "description": "uni-app 框架示例，一套代码，同时发行到iOS、Android、H5、小程序等多个平台，请使用手机扫码快速体验 uni-app 的强大功能", "versionName": "1.0.0", "versionCode": 200, "transformPx": false, "app-plus": {"usingComponents": true, "nvueCompiler": "uni-app", "nvueStyleCompiler": "uni-app", "compilerVersion": 3, "nvueLaunchMode": "fast", "splashscreen": {"alwaysShowBeforeRender": true, "waiting": true, "autoclose": true, "delay": 0}, "modules": {"OAuth": {}, "Payment": {}, "Push": {}, "Share": {}, "Speech": {}, "VideoPlayer": {}}, "distribute": {"android": {"permissions": []}, "ios": {"UIBackgroundModes": ["audio"], "urlschemewhitelist": ["b<PERSON><PERSON><PERSON>", "iosamap"]}, "sdkConfigs": {"speech": {"ifly": {}}}, "orientation": ["portrait-primary"]}, "uniStatistics": {"enable": true}}, "quickapp": {}, "quickapp-native": {"icon": "/static/logo.png", "package": "com.example.demo", "features": [{"name": "system.clipboard"}]}, "quickapp-webview": {"icon": "/static/logo.png", "package": "com.example.demo", "minPlatformVersion": 1070, "versionName": "1.0.0", "versionCode": 100}, "mp-weixin": {"appid": "wx1df89e340decbc7a", "setting": {"urlCheck": false, "es6": true, "postcss": true, "minified": true}, "usingComponents": true, "lazyCodeLoading": "requiredComponents", "rendererOptions": {"skyline": {"defaultDisplayBlock": true, "disableABTest": true}}, "optimization": {"subPackages": true}, "uniStatistics": {"enable": true}, "permission": {"scope.userLocation": {"desc": "告知用户具体地理位置"}}, "requiredPrivateInfos": ["getLocation", "<PERSON><PERSON><PERSON><PERSON>", "chooseLocation", "choosePoi", "onLocationChange", "startLocationUpdateBackground", "startLocationUpdate"]}, "mp-alipay": {"usingComponents": true, "uniStatistics": {"enable": true}}, "mp-baidu": {"usingComponents": true, "uniStatistics": {"enable": true}, "dynamicLib": {"editorLib": {"provider": "swan-editor"}}}, "mp-toutiao": {"usingComponents": true, "uniStatistics": {"enable": true}}, "mp-jd": {"usingComponents": true, "uniStatistics": {"enable": true}}, "h5": {"template": "template.h5.html", "router": {"mode": "history", "base": ""}, "sdkConfigs": {"maps": {"qqmap": {"key": "TKUBZ-D24AF-GJ4JY-JDVM2-IBYKK-KEBCU"}}}, "async": {"timeout": 20000}, "uniStatistics": {"enable": true}}, "vueVersion": "3", "mp-kuaishou": {"uniStatistics": {"enable": true}}, "mp-lark": {"uniStatistics": {"enable": true}}, "mp-qq": {"uniStatistics": {"enable": true}}, "quickapp-webview-huawei": {"uniStatistics": {"enable": true}}, "quickapp-webview-union": {"uniStatistics": {"enable": true}}}