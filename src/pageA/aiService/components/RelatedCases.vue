<template>
  <view class="related-cases-container" v-if="shouldShow">
    <view class="related-slider"></view>
    <view class="related-cases-card" @click="handleViewMore">
      <!-- 标题 -->
      <view class="related-cases-title">相关案例</view>

      <!-- 案例内容 -->
      <view class="case-content">
        <text class="case-text u-line-3">{{
          currentCase.content
        }}</text>
      </view>

      <!-- 案例图片 -->
      <view
        class="case-images"
        v-if="currentCase.imgs && currentCase.imgs.length > 0"
      >
        <view
          v-for="(img, index) in currentCase.imgs.slice(0, 2)"
          :key="index"
          class="case-image-container"
        >
          <image
            :src="img"
            class="case-image"
            mode="aspectFill"
            @error="handleImageError"
          />
        </view>
      </view>

      <!-- 查看更多按钮 -->
      <view class="more-button">
        <view class="more-button-content">
          <text class="more-text">点击查看更多</text>
          <view class="more-arrow">
            <image src="/static/images/chat/arrow-right.svg" mode="aspectFit" />
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue';
import { navigateToPage } from '@/utils/urlUtils.js';

// Props
const props = defineProps({
  cases: {
    type: Array,
    default: () => [],
  },
  conversationId: {
    type: String,
    default: '',
  },
  userMessage: {
    type: String,
    default: '',
  },
});

// Events
const emit = defineEmits(['view-more']);

// 数据
const currentIndex = ref(0);

// 计算属性
const currentCase = computed(() => {
  if (props.cases.length > 0) {
    return props.cases[currentIndex.value];
  }
  return {};
});

const shouldShow = computed(() => {
  return props.cases.length > 0;
});

// 处理查看更多
const handleViewMore = () => {
  console.log('点击查看更多相关案例');

  // 将相关案例数据存储到全局状态中，供列表页面使用
  const relatedCasesData = {
    list: props.cases,
    conversationId: props.conversationId,
    userMessage: props.userMessage,
    timestamp: Date.now() // 添加时间戳，确保数据新鲜度
  };

  // 存储到全局状态
  uni.setStorageSync('relatedCasesListData', relatedCasesData);

  // 构建跳转参数
  const params = {};
  if (props.conversationId) {
    params.conversationId = props.conversationId;
  }
  if (props.userMessage) {
    params.userMessage = props.userMessage;
  }

  // 跳转到相关案例列表页面
  navigateToPage('/subPackages/aiService/relatedCasesList', params);

  emit('view-more', {
    cases: props.cases,
    current: currentCase.value,
    conversationId: props.conversationId,
    userMessage: props.userMessage,
  });
};

// 处理图片加载失败
const handleImageError = (e) => {
  console.log('案例图片加载失败:', e);
  // 可以在这里隐藏加载失败的图片
};

// 暴露方法给父组件
defineExpose({
  currentCase,
});
</script>

<style lang="scss" scoped>
.related-cases-container {
  .related-slider {
    margin: 50rpx 0;
    width: 100%;
    height: 1rpx;
    border-radius: 50%;
    background-color: #eff0f6;
  }
}

.related-cases-card {
  background-color: #ffffff;
  position: relative;
}

.related-cases-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #000000;
  margin-bottom: 28rpx;
}

.case-content {
  margin-bottom: 32rpx;
}

.case-text {
  font-size: 26rpx;
  font-weight: 400;
  color: #333333;
  line-height: 1.54;
  text-align: left;
  display: block;
}

.case-images {
  display: flex;
  gap: 24rpx;
  margin-bottom: 32rpx;
}

.case-image-container {
  width: 305rpx;
  height: 305rpx;
  border-radius: 20rpx;
  overflow: hidden;
  background-color: #d9d9d9;
  flex-shrink: 0;
}

.case-image {
  width: 100%;
  height: 100%;
}

.more-button {
  background-color: #eff3ff;
  border-radius: 60rpx;
  padding: 15rpx 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.more-button-content {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.more-text {
  font-size: 24rpx;
  font-weight: 400;
  color: #406cff;
}

.more-arrow {
  width: 28rpx;
  height: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;

  image {
    width: 28rpx;
    height: 28rpx;
    filter: brightness(0) saturate(100%) invert(39%) sepia(100%) saturate(1000%)
      hue-rotate(220deg) brightness(95%) contrast(105%);
  }
}
</style>
