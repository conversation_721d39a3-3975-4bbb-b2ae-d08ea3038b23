.main{
	background: linear-gradient(to bottom, #E0F1FF, #FFF6EB);
	background-size: 100% auto;
padding: 32rpx 24rpx;
}

.mealType{
	background: #fff;
	border-radius: 20rpx;
	padding: 24rpx 24rpx;
	margin-top: 24rpx;
}
.mealType1{
	display: flex;
}
.mealType-1{
	width: 45rpx;
	height: 45rpx;
	background: #E8EEFF;
	text-align: center;
	line-height: 45rpx;
	border-radius: 100rpx;
}
.mealType-2{
	color: #333333;
	font-size: 28rpx;
	font-weight: bold;
	margin-left: 16rpx;
}
.mealType-3{
	padding: 30rpx 30rpx;
	background: #F6F6F6;
	border-radius: 10rpx;
	margin-top: 18rpx;
}
.textarea{
	height: 100rpx;
	width: 100%;
}









.calendar-container {
	width: 100%;
	padding: 20rpx;
	box-sizing: border-box;
	background: #fff;
	border-radius: 20rpx;
	margin-top: 20rpx;
  }
  
.calendar-title{
	color: #333333;
	font-size: 32rpx;
	font-weight: bold;
}
  .fgx{
	  width: 100%;
	  height: 1rpx;
	  background: #EBEBEB;
	  margin: 26rpx 0;
  }

  
  .form-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
  }
  .picker{
	  display: flex;
	  align-items: center;
  }
  .label {
	font-size: 28rpx;
	color: #828282;
  }
  
  .picker-view {
	padding: 10rpx;
	border-radius: 4rpx;
  }
  
  .placeholder {
	color: #333333;
  }
  
  .selected-value {
	font-size: 28rpx;
	color: #333333;
  }
  
  .value {
	font-size: 28rpx;
	color: #333333;
  }
  