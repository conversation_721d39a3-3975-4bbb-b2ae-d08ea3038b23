<template>
    <view class="head" v-if="category === 'recovery'">
        <view class="head1">
            <view class="head1_1"></view>
            <view class="head1_2">热门活动</view>
        </view>
        <view class="head2"></view>
    </view>
    <view class="recommend">
        <view class="recommend1" @tap="nextDetails(item)" v-for="(item, index) in list" :key="index">
            <view style="width: 340rpx; height: 340rpx; " >
              <!--  <image v-if="item.image" :src="item.image" mode="‌widthFix"
                    style="width: 100%; height: 100%; border-radius: 20rpx;" /> -->
					<view class="fit-img" :style="{'backgroundImage': 'url('+ item.image +')'}">
					</view>
            </view>

            <view class="recommend1_1">
                <view class="recommend2">{{ item.name }}</view>
                <view class="recommend3">价格随时波动 欢迎在线咨询</view>
                <view class="recommend-button">查看详情</view>
            </view>
        </view>
    </view>
</template>

<script setup>
    // 底部推荐产康/房间
    import {
        ref,
        reactive,
        onMounted,
        computed,
        getCurrentInstance
    } from "vue";
    const props = defineProps({
        category: {
            type: String,
            default: 'recovery'
        }
    })
    const instance = getCurrentInstance();
    const App = instance.appContext.config.globalProperties
    onMounted(async () => {
        await recoveryRecommendation()
    })
    const list = reactive([])
    const recoveryRecommendation = async () => {
        //查询套房热门推荐列表
        const url = props.category == 'recovery' ? App.$api.suiteRrecommendation : App.$api
            .recoveryRecommendation
        const res = await App.$axios.get(url, {
            limit: 2
        })
        if (res.data.code == 200) {
            let data = res.data.data
            data.forEach((item) => {
                if (props.category == 'recovery') {
                    item.image = item?.suitePhotos[0] ? item?.suitePhotos[0] : item?.suitePhotos
                    item.name = item?.roomName
                } else {
                    item.image = item?.displayPhotos[0] ? item?.displayPhotos[0] : item?.displayPhotos
                    item.name = item?.projectName
                }
            })
            Object.assign(list, data)
        }
    }
    const nextDetails = (e) => {
        let params = {}
        let url = ''
        if (e.suiteId) {
            params.suiteId = e.suiteId
            url = '/pageA/pageB/home/<USER>/detail'
        } else {
            params.projectId = e.projectId
            url = '/pageA/pageB/home/<USER>/detail'
        }
        // 判断上一个页面是否是详情页面 防止套娃
        // let currentPage = getCurrentPages()
        // const arr = ['pageA/pageB/home/<USER>/detail', 'pageA/pageB/home/<USER>/detail']
        // console.log('currentPagecurrentPagecurrentPage', currentPage)
        // if (currentPage.length > 0) {
        //     const rt = currentPage[currentPage.length - 1].route
        //     console.log('current22222', rt, arr.includes(rt))
        //     if (arr.includes(rt)) {  
        //         return
        //     }
        // }
        App.$jumpPage(url, params)
    }
</script>

<style lang="less" scoped>
    .head {
        display: flex;
        justify-content: space-between;
    }

    .head1 {
        display: flex;
        align-items: center;
    }

    .head2 {
        display: flex;
        align-items: center;
    }

    .head1_1 {
        // width: 10rpx;
        // height: 30rpx;
        // background: #ff4f61;
        // border-radius: 5rpx;
    }

    .head1_2 {
        font-weight: 500;
        font-size: 28rpx;
        color: #333333;
        line-height: 33rpx;
        text-align: left;
        font-style: normal;
        text-transform: none;
        padding: 12rpx 0;
    }

    .head2_1 {
        color: #aaaaaa;
        font-size: 22rpx;
        margin-right: 10rpx;
    }

    .recommend {
        padding-top: 20rpx;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
    }

    .recommend1 {
        margin-bottom: 20rpx;
        position: relative;
        box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.1);
        border-radius: 20rpx;
    }

    .recommend2 {
        color: #333333;
        font-size: 30rpx;
        margin-top: 16rpx;
        text-align: center;
    }

    .recommend3 {
        font-weight: 300;
        font-size: 24rpx;
        color: #777777;
        line-height: 28rpx;
        text-align: left;
        font-style: normal;
        text-transform: none;
        margin-top: 8rpx;
        text-align: center;
    }

    .recommend-button {
        width: 136rpx;
        height: 58rpx;
        background: #FF4F61;
        border-radius: 46rpx 46rpx 46rpx 46rpx;
        margin-top: 16rpx;
        text-align: center;
        font-weight: 400;
        font-size: 24rpx;
        color: #FFFFFF;
        line-height: 58rpx;
        font-style: normal;
        text-transform: none;
        margin: 16rpx auto;
    }

    .recommend1_1 {
        background: #ffffff;
        border-top: none;
        border-bottom-left-radius: 10rpx;
        border-bottom-right-radius: 10rpx;
        padding: 0 15rpx;
    }
	.fit-img {
	    width: 100%;
	    height: 100%;
	    border-radius: 10rpx;
	    background-size: cover;
	    background-position: center;
	}
</style>