.mains {
	background: #f8f9f9;
	/* padding: 20rpx 0; */
}

.main {
	padding: 0 24rpx;
}

.teamImg {
	width: 25%;
}

.teamImg image {
	width: 150rpx;
	height: 150rpx;
	border-radius: 150rpx;
}

.experience {
	font-size: 24rpx;
	margin-bottom: 8rpx;
}

.teamContent2 {
	color: hsla(15, 97%, 50%, 1);
	background: hsla(40, 100%, 90%, 1);
	font-size: 24rpx;
	padding: 4rpx 8rpx;
	border-radius: 10rpx;
}

.experience1 {
	color: hsla(0, 0%, 20%, 1);
}

.experience2 {
	color: hsla(0, 0%, 47%, 1);
	margin-right: 20rpx;
}

.team {
	display: flex;
	padding: 20rpx 20rpx;
	background: #ffffff;
	border-radius: 20rpx;
	margin-bottom: 20rpx;
}

.teamContent {
	width: 70%;
}

.text1 {
	font-size: 34rpx;
	color: #333333;
	font-weight: bold;
	margin-top: 3rpx;
}

.text2 {
	font-size: 24rpx;
	font-weight: 500;
	color: #ff4f61;
	border-radius: 6rpx;
	background: #ffeef0;
	margin-top: 8rpx;
	padding: 4rpx 8rpx;
	margin-right: 10rpx;
}

.text3 {
	font-size: 24rpx;
	font-weight: bold;
	color: #ffffff;
	height: 34rpx;
	border-radius: 6rpx;
	background: #b9a9ff;
	padding: 4rpx 8rpx;
	margin-top: 8rpx;
	margin-left: 22rpx;
}

.text4 {
	color: hsla(0, 0%, 20%, 1);
	font-size: 24rpx;
	margin: 12rpx 0;
}

.introductory {
	font-size: 24rpx;
	overflow: hidden;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2;
}

.introductoryText {
	color: #333333;
	font-weight: bold;
}

.introductoryContent {
	color: #777777;
}

.evaluate {
	display: flex;
	margin-top: 16rpx;
}

.evaluateText {
	font-size: 24rpx;
	color: #7e6dfc;
	font-weight: bold;
}

.evaluateFgx {
	width: 1rpx;
	height: 18rpx;
	background: #ebebeb;
	margin: 0 15rpx;
}

.score image {
	width: 166rpx;
	height: 30rpx;
}

.blank {
	height: 150rpx;
}

.tag {
	position: fixed;
	left: 10rpx;
	bottom: 400rpx;
}

.tag view {
	margin-bottom: 10rpx;
}

.teamContent1 {
	display: flex;
	align-items: center;
}

.discuss1_3 {
	display: flex;
	font-size: 30rpx;
	align-items: center;
}

.head1_2 {
	display: flex;
	position: relative;
	margin-left: 16rpx;
}

.head1_3 {
	background: #ffeecc;
	padding: 0 8rpx 0 30rpx;
	border-radius: 10rpx;
	color: #fb4105;
	font-size: 24rpx;
	height: 34rpx;
	font-weight: 500;
	margin-left: 4rpx;
}

.heaad1_4 {
	color: #ff6e00;
}

.head1Img {
	position: absolute;
	left: -10rpx;
}

.headTab {
	width: 100%;
	position: fixed;
	z-index: 999;
}

.black {
	height: 100rpx;
}







.container {
	padding: 24rpx;
}

.header {
	display: flex;
	margin-bottom: 20px;
	position: relative;
}

.header1_1 {
	color: #5B6799;
	font-size: 30rpx;
	font-weight: bold;
}

.header1_2 {
	color: #777777;
	font-size: 24rpx;
	margin-top: 12rpx;
}

.header image {
	width: 84rpx;
	height: 84rpx;
	border-radius: 10rpx;
	margin-right: 20rpx;
}


.content {
	margin-bottom: 20px;
	color: #333333;
	font-size: 28rpx;
}

.image-gallery {
	display: flex;
	flex-wrap: wrap;
}

.image-item {
	width: 49%;
	height: 347rpx;
	margin-bottom: 20rpx;
}
.image-gallery .image-item:nth-child(2n) {
  margin-left: 1%;
}
.image-item image {
	width: 100%;
	height: 347rpx;
	border-radius: 10rpx;
}
.cutoffrule{
	width: 100%;
	height: 1rpx;
	background: #F3F3F3;
}

.dynamic {
  padding: 20rpx 36rpx;
  background: #ff4f61;
  border-radius: 110rpx;
  display: flex;
  align-items: center;
  color: #fff;
  font-size: 32rpx;
  position: fixed;
  bottom: 100rpx;
  left: 38%;
  z-index: 99;
}
.delBtn{
	position: absolute;
	bottom: 0;
	right: 0;
}
.video {
  width: 100%;
  height: 100%;
  border-radius: 10rpx;
  display: block;
}
