<template>
    <view class="introduce">
        <view class="title">
            <view class="title-tag" :style="{'backgroundColor': mainColor}"></view>
            服务介绍
            <view class="title-line"></view>
        </view>
        <view class="card">
            <view>
                <view class="card-title">服务内容</view>
                <view @tap="toDetail" class="card-des" :style="{'color': mainColor}">盆骨修复</view>
            </view>
            <view>
                <view class="card-title">服务标签</view>
                <view class="card-des">化脓消肿</view>
                <view class="card-des">化脓消肿</view>
            </view>
        </view>
        <view class="input">
            <image src="http://cdn.xiaodingdang1.com/2024/12/31/4f797eddb4434f67a593d10bb53d1e81.png"></image>
            <view class="input-text">护理模式是怎样的</view>
            <view class="input-btn">官方解答</view>
        </view>
    </view>
</template>

<script setup>
    import {
        ref,
        reactive,
        getCurrentInstance
    } from "vue";
    const instance = getCurrentInstance();
    const App = instance.appContext.config.globalProperties
    const props = defineProps({
        mainColor: {
            type: String,
            default: '#3994FF'
        }
    })
    const toDetail = () => {
        const params = {
            userId: '1795279859326488580',
            postId: '1477'
        }
        App.$jumpPage('/pageA/pageB/community/note/tagdetail', params)
    }
</script>

<style scoped lang="less">
    .introduce {
        padding: 32rpx 24rpx;
        background-color: white;
    }

    .title {
        font-weight: 500;
        font-size: 32rpx;
        color: #333333;
        line-height: 38rpx;
        display: flex;
        align-items: center;
        margin-bottom: 8rpx;

        &-tag {
            width: 6rpx;
            height: 28rpx;
            border-radius: 28rpx;
            margin-right: 12rpx;
        }

        &-line {
            flex: 1;
            height: 1rpx;
            background: #F0F0F0;
        }
    }


    .card {
        background: #FFFFFF;
        border-radius: 8rpx;
        border: 8rpx solid #F7F9F8;
        padding: 24rpx 0;
        display: flex;
        justify-content: space-around;

        &>view {
            text-align: center;
        }

        &-title {
            font-weight: 500;
            font-size: 20rpx;
            color: #777777;
            line-height: 23rpx;
            margin-bottom: 8rpx;
        }

        &-des {
            font-weight: 500;
            font-size: 24rpx;
            margin-bottom: 10rpx;
        }
    }

    .input {
        margin-top: 32rpx;
        position: relative;
        padding: 14rpx 0;
        background: #FFFFFF;
        border-radius: 8rpx;
        border: 1rpx solid #DFDFDF;
        display: flex;
        align-items: center;

        image {
            width: 30rpx;
            height: 30rpx;
            display: inline-block;
            margin: 0 18rpx;
        }

        &-text {
            font-weight: 400;
            font-size: 28rpx;
            color: #686868;
        }

        &-btn {
            position: absolute;
            right: 18rpx;
            font-weight: 500;
            font-size: 24rpx;
            color: #3994FF;
            background: #F0F7FF;
            border-radius: 6rpx;
            padding: 5rpx 15rpx;
        }
    }
</style>