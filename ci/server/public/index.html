<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小程序 CI 管理平台</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <script src="https://unpkg.com/@element-plus/icons-vue/dist/index.iife.min.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <style>
        body {
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f7fa;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }
        
        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
        }
        
        .header p {
            margin: 5px 0 0 0;
            opacity: 0.9;
            font-size: 14px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .project-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
            margin-bottom: 20px;
            overflow: hidden;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        
        .project-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
        }
        
        .project-header {
            padding: 20px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            gap: 20px;
        }
        
        .project-info {
            flex: 1;
        }
        
        .project-name {
            font-size: 18px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 5px;
        }
        
        .project-appid {
            color: #909399;
            font-size: 14px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .project-tenant {
            color: #606266;
            font-size: 13px;
            margin-top: 5px;
        }

        .tenant-label {
            color: #909399;
            margin-right: 5px;
        }

        .tenant-value {
            color: #409eff;
            font-weight: 500;
            font-family: 'SF Mono', Monaco, monospace;
        }
        
        .project-status {
            display: flex;
            flex-direction: column;
            gap: 15px;
            align-items: flex-end;
        }

        .status-badges {
            display: flex;
            gap: 10px;
            align-items: center;
            flex-wrap: wrap;
        }

        .project-actions {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .project-actions .el-button {
            min-width: 70px;
            font-size: 12px;
        }

        .project-actions .el-button--primary {
            background: linear-gradient(135deg, #409eff 0%, #3a8ee6 100%);
            border: none;
        }

        .project-actions .el-button--info {
            background: linear-gradient(135deg, #909399 0%, #82848a 100%);
            border: none;
        }

        .project-actions .el-button--danger {
            background: linear-gradient(135deg, #f56c6c 0%, #f25c5c 100%);
            border: none;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-success {
            background: #f0f9ff;
            color: #0369a1;
            border: 1px solid #bae6fd;
        }
        
        .status-warning {
            background: #fffbeb;
            color: #d97706;
            border: 1px solid #fed7aa;
        }
        
        .status-error {
            background: #fef2f2;
            color: #dc2626;
            border: 1px solid #fecaca;
        }
        
        .project-body {
            padding: 20px;
        }
        
        .action-section {
            margin-bottom: 20px;
        }
        
        .action-title {
            font-size: 16px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 10px;
        }
        
        .action-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .log-container {
            background: #1e1e1e;
            color: #d4d4d4;
            border-radius: 8px;
            padding: 15px;
            font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
            font-size: 13px;
            line-height: 1.5;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 15px;
        }
        
        .log-line {
            margin-bottom: 2px;
        }
        
        .log-error {
            color: #f87171;
        }
        
        .log-success {
            color: #34d399;
        }
        
        .log-info {
            color: #60a5fa;
        }
        
        .upload-area {
            border: 2px dashed #d1d5db;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            background: #f9fafb;
            transition: all 0.2s;
            cursor: pointer;
        }
        
        .upload-area:hover {
            border-color: #667eea;
            background: #f0f4ff;
        }
        
        .upload-area.dragover {
            border-color: #667eea;
            background: #e0e7ff;
        }
        
        .preview-qrcode {
            max-width: 200px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #909399;
        }
        
        .empty-state img {
            width: 120px;
            opacity: 0.5;
            margin-bottom: 20px;
        }
        
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }
        
        .loading-content {
            background: white;
            padding: 30px;
            border-radius: 12px;
            text-align: center;
            min-width: 200px;
        }

        .toolbar {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
            margin-bottom: 20px;
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .toolbar {
                flex-direction: column;
                align-items: stretch;
                gap: 10px;
            }

            .project-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 15px;
            }

            .project-status {
                align-items: flex-start;
                width: 100%;
            }

            .status-badges {
                justify-content: flex-start;
            }

            .project-actions {
                justify-content: flex-start;
                width: 100%;
            }

            .action-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="header">
            <h1>🚀 小程序 CI 管理平台</h1>
            <p>支持多项目配置、构建、上传和预览的图形化管理界面</p>
        </div>
        
        <div class="container">
            <!-- 操作工具栏 -->
            <div class="toolbar">
                <el-button type="primary" @click="showCreateDialog">
                    <el-icon><Plus /></el-icon>
                    新建项目
                </el-button>
                <el-button @click="refreshProjects">
                    <el-icon><Refresh /></el-icon>
                    刷新列表
                </el-button>
                <el-button @click="showBackupDialog">
                    <el-icon><FolderOpened /></el-icon>
                    备份管理
                </el-button>
                <el-button @click="exportConfig">
                    <el-icon><Download /></el-icon>
                    导出配置
                </el-button>
                <el-button @click="reloadConfig" type="warning">
                    <el-icon><Refresh /></el-icon>
                    重载配置
                </el-button>
                <el-divider direction="vertical" />
                <el-checkbox v-model="batchMode" @change="handleBatchModeChange">
                    批量操作
                </el-checkbox>
                <el-button
                    v-if="batchMode && selectedProjects.length > 0"
                    type="danger"
                    @click="showBatchDeleteDialog"
                >
                    <el-icon><Delete /></el-icon>
                    批量删除 ({{ selectedProjects.length }})
                </el-button>
            </div>

            <!-- 加载状态 -->
            <div v-if="loading" class="loading-overlay">
                <div class="loading-content">
                    <el-icon class="is-loading" size="24"><Loading /></el-icon>
                    <p style="margin-top: 15px;">{{ loadingText }}</p>
                </div>
            </div>
            
            <!-- 项目列表 -->
            <div v-if="projects.length === 0 && !loading" class="empty-state">
                <p>暂无项目配置</p>
                <p>请检查 ci.config.js 配置文件</p>
            </div>
            
            <div v-for="project in projects" :key="project.id" class="project-card">
                <!-- 项目头部 -->
                <div class="project-header">
                    <div class="project-info">
                        <div class="project-name">{{ project.name }}</div>
                        <div class="project-appid">{{ project.appid }}</div>
                        <div class="project-tenant" v-if="project.defaultTenantId">
                            <span class="tenant-label">默认租户ID:</span>
                            <span class="tenant-value">{{ project.defaultTenantId }}</span>
                        </div>
                    </div>
                    <div class="project-status">
                        <div class="status-badges">
                            <span
                                :class="['status-badge', project.status.configValid ? 'status-success' : 'status-error']"
                            >
                                {{ project.status.configValid ? '配置正常' : '配置错误' }}
                            </span>
                            <span
                                :class="['status-badge', project.status.privateKeyExists ? 'status-success' : 'status-warning']"
                            >
                                {{ project.status.privateKeyExists ? '私钥已配置' : '缺少私钥' }}
                            </span>
                            <span
                                :class="['status-badge', project.status.buildPathExists ? 'status-success' : 'status-warning']"
                            >
                                {{ project.status.buildPathExists ? '构建产物存在' : '需要构建' }}
                            </span>
                        </div>
                        <div class="project-actions">
                            <el-button
                                size="small"
                                type="primary"
                                @click="handleProjectAction({action: 'edit', project})"
                                title="编辑项目"
                            >
                                <el-icon><Edit /></el-icon>
                                编辑
                            </el-button>
                            <el-button
                                size="small"
                                type="info"
                                @click="handleProjectAction({action: 'clone', project})"
                                title="克隆项目"
                            >
                                <el-icon><CopyDocument /></el-icon>
                                克隆
                            </el-button>
                            <el-button
                                size="small"
                                type="danger"
                                @click="handleProjectAction({action: 'delete', project})"
                                title="删除项目"
                            >
                                <el-icon><Delete /></el-icon>
                                删除
                            </el-button>
                        </div>
                    </div>
                </div>
                
                <!-- 项目操作 -->
                <div class="project-body">
                    <!-- 私钥上传 -->
                    <div v-if="!project.status.privateKeyExists" class="action-section">
                        <div class="action-title">📁 上传私钥文件</div>
                        <div 
                            class="upload-area"
                            @click="triggerFileUpload(project.id)"
                            @dragover.prevent="handleDragOver"
                            @dragleave.prevent="handleDragLeave"
                            @drop.prevent="handleFileDrop($event, project.id)"
                        >
                            <el-icon size="24"><UploadFilled /></el-icon>
                            <p>点击或拖拽上传 .key 私钥文件</p>
                            <p style="font-size: 12px; color: #909399;">从微信公众平台下载的私钥文件</p>
                        </div>
                        <input 
                            type="file" 
                            :ref="`fileInput-${project.id}`"
                            @change="handleFileSelect($event, project.id)"
                            accept=".key"
                            style="display: none"
                        >
                    </div>
                    
                    <!-- 环境操作 -->
                    <div class="action-section">
                        <div class="action-title">🔧 环境操作</div>
                        <div style="margin-bottom: 15px;">
                            <el-select v-model="selectedEnvironments[project.id]" placeholder="选择环境">
                                <el-option
                                    v-for="(envConfig, envKey) in project.environments"
                                    :key="envKey"
                                    :label="`${getEnvironmentName(envKey)} (租户ID: ${envConfig.tenantId || project.defaultTenantId || '194338'})`"
                                    :value="envKey"
                                />
                            </el-select>
                        </div>
                        <div class="action-buttons">
                            <el-button 
                                type="primary" 
                                @click="buildProject(project.id)"
                                :disabled="!selectedEnvironments[project.id] || isProcessing(project.id)"
                                :loading="isProcessing(project.id, 'build')"
                            >
                                🔨 构建项目
                            </el-button>
                            <el-button 
                                type="success" 
                                @click="showUploadDialog(project.id)"
                                :disabled="!selectedEnvironments[project.id] || !project.status.privateKeyExists || isProcessing(project.id)"
                            >
                                📤 上传小程序
                            </el-button>
                            <el-button 
                                type="info" 
                                @click="showPreviewDialog(project.id)"
                                :disabled="!selectedEnvironments[project.id] || !project.status.privateKeyExists || isProcessing(project.id)"
                            >
                                👁️ 生成预览
                            </el-button>
                        </div>
                    </div>
                    
                    <!-- 实时日志 -->
                    <div v-if="logs[project.id] && logs[project.id].length > 0" class="action-section">
                        <div class="action-title">📋 实时日志</div>
                        <div class="log-container">
                            <div 
                                v-for="(log, index) in logs[project.id]" 
                                :key="index"
                                :class="['log-line', getLogClass(log.type)]"
                            >
                                [{{ formatTime(log.timestamp) }}] {{ log.message }}
                            </div>
                        </div>
                        <div style="margin-top: 10px;">
                            <el-button size="small" @click="clearLogs(project.id)">清空日志</el-button>
                        </div>
                    </div>
                    
                    <!-- 预览二维码 -->
                    <div v-if="previewQRCodes[project.id]" class="action-section">
                        <div class="action-title">📱 预览二维码</div>
                        <img :src="previewQRCodes[project.id]" class="preview-qrcode" alt="预览二维码">
                        <p style="font-size: 12px; color: #909399; margin-top: 10px;">
                            使用微信扫描二维码进行预览
                        </p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 上传对话框 -->
        <el-dialog v-model="uploadDialogVisible" title="上传小程序" width="500px">
            <el-form :model="uploadForm" label-width="80px">
                <el-form-item label="版本号">
                    <el-input 
                        v-model="uploadForm.version" 
                        placeholder="留空自动生成（格式：年.月.日.时分）"
                    />
                </el-form-item>
                <el-form-item label="版本描述">
                    <el-input 
                        v-model="uploadForm.desc" 
                        type="textarea" 
                        :rows="3"
                        placeholder="版本更新说明"
                    />
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button @click="uploadDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="uploadProject">确认上传</el-button>
            </template>
        </el-dialog>
        
        <!-- 预览对话框 -->
        <el-dialog v-model="previewDialogVisible" title="生成预览" width="500px">
            <el-form :model="previewForm" label-width="80px">
                <el-form-item label="页面路径">
                    <el-input
                        v-model="previewForm.pagePath"
                        placeholder="如：pages/index/index（留空使用默认页面）"
                    />
                </el-form-item>
                <el-form-item label="页面参数">
                    <el-input
                        v-model="previewForm.searchQuery"
                        placeholder="如：id=123&name=test"
                    />
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button @click="previewDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="generatePreview">生成预览</el-button>
            </template>
        </el-dialog>

        <!-- 项目管理对话框 -->
        <el-dialog
            v-model="projectDialogVisible"
            :title="projectDialogMode === 'create' ? '新建项目' : '编辑项目'"
            width="600px"
        >
            <el-form :model="projectForm" :rules="projectRules" ref="projectFormRef" label-width="100px">
                <el-form-item label="项目ID" prop="id" v-if="projectDialogMode === 'create'">
                    <el-input v-model="projectForm.id" placeholder="项目唯一标识，如：main, test" />
                </el-form-item>
                <el-form-item label="项目名称" prop="name">
                    <el-input v-model="projectForm.name" placeholder="项目显示名称" />
                </el-form-item>
                <el-form-item label="AppID" prop="appid">
                    <el-input v-model="projectForm.appid" placeholder="微信小程序 AppID" />
                </el-form-item>
                <el-form-item label="构建路径" prop="projectPath">
                    <el-input v-model="projectForm.projectPath" placeholder="构建产物路径" />
                </el-form-item>
                <el-form-item label="私钥路径" prop="privateKeyPath">
                    <el-input v-model="projectForm.privateKeyPath" placeholder="私钥文件路径" />
                </el-form-item>
                <el-form-item label="默认租户ID" prop="defaultTenantId">
                    <el-input v-model="projectForm.defaultTenantId" placeholder="项目默认租户ID" />
                </el-form-item>
                <el-form-item label="忽略文件">
                    <el-input
                        v-model="projectForm.ignoresText"
                        type="textarea"
                        :rows="3"
                        placeholder="每行一个忽略规则，如：node_modules/**/*"
                    />
                </el-form-item>
                <el-form-item label="环境配置">
                    <div v-for="(env, envKey) in projectForm.environments" :key="envKey" style="margin-bottom: 15px; border: 1px solid #ddd; padding: 15px; border-radius: 6px;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                            <strong>{{ getEnvironmentName(envKey) }}</strong>
                            <el-button size="small" type="danger" @click="removeEnvironment(envKey)">删除</el-button>
                        </div>
                        <el-form-item label="名称" style="margin-bottom: 10px;">
                            <el-input v-model="env.name" size="small" />
                        </el-form-item>
                        <el-form-item label="描述" style="margin-bottom: 10px;">
                            <el-input v-model="env.desc" size="small" />
                        </el-form-item>
                        <el-form-item label="租户ID" style="margin-bottom: 10px;">
                            <el-input v-model="env.tenantId" size="small" placeholder="环境专用租户ID（可选）" />
                        </el-form-item>
                        <el-form-item label="构建命令" style="margin-bottom: 0;">
                            <el-input v-model="env.buildCommand" size="small" />
                        </el-form-item>
                    </div>
                    <el-button @click="addEnvironment" icon="Plus" size="small">添加环境</el-button>
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button @click="projectDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="saveProject">保存</el-button>
            </template>
        </el-dialog>

        <!-- 备份管理对话框 -->
        <el-dialog v-model="backupDialogVisible" title="备份管理" width="700px">
            <el-table :data="backups" style="width: 100%">
                <el-table-column prop="name" label="备份文件" />
                <el-table-column prop="size" label="文件大小" :formatter="formatFileSize" />
                <el-table-column prop="mtime" label="创建时间" :formatter="formatTime" />
                <el-table-column label="操作" width="120">
                    <template #default="scope">
                        <el-button size="small" @click="restoreBackup(scope.row.name)">恢复</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <template #footer>
                <el-button @click="backupDialogVisible = false">关闭</el-button>
            </template>
        </el-dialog>
    </div>

    <script>
        const { createApp, ref, reactive, onMounted, onUnmounted, computed } = Vue;
        const { ElMessage, ElMessageBox, ElLoading } = ElementPlus;

        // 导入图标
        const {
            Plus, Refresh, FolderOpened, Download, Edit, CopyDocument, Delete,
            More, Upload, Loading, UploadFilled
        } = ElementPlusIconsVue;

        createApp({
            setup() {
                // 响应式数据
                const projects = ref([]);
                const loading = ref(true);
                const loadingText = ref('加载项目列表...');
                const logs = ref({});
                const selectedEnvironments = ref({});
                const previewQRCodes = ref({});
                const processingStates = ref({});

                // 对话框状态
                const uploadDialogVisible = ref(false);
                const previewDialogVisible = ref(false);
                const projectDialogVisible = ref(false);
                const backupDialogVisible = ref(false);
                const currentProjectId = ref('');
                const projectDialogMode = ref('create'); // 'create' | 'edit'

                // 表单数据
                const uploadForm = reactive({
                    version: '',
                    desc: ''
                });

                const previewForm = reactive({
                    pagePath: '',
                    searchQuery: ''
                });

                const projectForm = reactive({
                    id: '',
                    name: '',
                    appid: '',
                    projectPath: '',
                    privateKeyPath: '',
                    defaultTenantId: '',
                    ignoresText: '',
                    environments: {}
                });

                const backups = ref([]);

                // 表单验证规则
                const projectRules = {
                    id: [
                        { required: true, message: '请输入项目ID', trigger: 'blur' },
                        { pattern: /^[a-zA-Z][a-zA-Z0-9_-]*$/, message: '项目ID只能包含字母、数字、下划线和连字符，且必须以字母开头', trigger: 'blur' }
                    ],
                    name: [
                        { required: true, message: '请输入项目名称', trigger: 'blur' }
                    ],
                    appid: [
                        { required: true, message: '请输入AppID', trigger: 'blur' },
                        { pattern: /^wx[a-f0-9]{16}$/, message: 'AppID格式不正确，应为wx开头的18位字符串', trigger: 'blur' }
                    ],
                    projectPath: [
                        { required: true, message: '请输入构建路径', trigger: 'blur' }
                    ],
                    privateKeyPath: [
                        { required: true, message: '请输入私钥路径', trigger: 'blur' }
                    ],
                    defaultTenantId: [
                        { required: true, message: '请输入默认租户ID', trigger: 'blur' },
                        { pattern: /^\d+$/, message: '租户ID只能包含数字', trigger: 'blur' }
                    ]
                };

                // WebSocket 连接
                let ws = null;

                // 环境名称映射
                const environmentNames = {
                    develop: '开发环境',
                    trial: '体验环境',
                    release: '正式环境'
                };

                // 方法
                const getEnvironmentName = (env) => {
                    return environmentNames[env] || env;
                };

                const formatTime = (timestamp) => {
                    return new Date(timestamp).toLocaleTimeString();
                };

                const formatFileSize = (row, column, cellValue) => {
                    const bytes = cellValue;
                    if (bytes === 0) return '0 B';
                    const k = 1024;
                    const sizes = ['B', 'KB', 'MB', 'GB'];
                    const i = Math.floor(Math.log(bytes) / Math.log(k));
                    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
                };

                const getLogClass = (type) => {
                    if (type.includes('error') || type.includes('Error')) return 'log-error';
                    if (type.includes('success') || type.includes('completed')) return 'log-success';
                    return 'log-info';
                };

                const isProcessing = (projectId, type = null) => {
                    const state = processingStates.value[projectId];
                    if (!state) return false;
                    if (type) return state[type];
                    return Object.values(state).some(Boolean);
                };

                const addLog = (projectId, type, message) => {
                    if (!logs.value[projectId]) {
                        logs.value[projectId] = [];
                    }
                    logs.value[projectId].push({
                        type,
                        message,
                        timestamp: Date.now()
                    });

                    // 限制日志数量
                    if (logs.value[projectId].length > 100) {
                        logs.value[projectId] = logs.value[projectId].slice(-50);
                    }
                };

                const clearLogs = (projectId) => {
                    logs.value[projectId] = [];
                };

                // API 调用
                const fetchProjects = async () => {
                    try {
                        loading.value = true;
                        loadingText.value = '加载项目列表...';

                        const response = await fetch('/api/projects');
                        const result = await response.json();

                        if (result.success) {
                            projects.value = result.data;
                            // 初始化选中的环境
                            result.data.forEach(project => {
                                if (!selectedEnvironments.value[project.id]) {
                                    selectedEnvironments.value[project.id] = project.environments[0];
                                }
                            });
                        } else {
                            ElMessage.error(result.error);
                        }
                    } catch (error) {
                        ElMessage.error('加载项目列表失败: ' + error.message);
                    } finally {
                        loading.value = false;
                    }
                };

                const uploadPrivateKey = async (projectId, file) => {
                    try {
                        const formData = new FormData();
                        formData.append('privateKey', file);

                        const response = await fetch(`/api/projects/${projectId}/upload-key`, {
                            method: 'POST',
                            body: formData
                        });

                        const result = await response.json();

                        if (result.success) {
                            ElMessage.success('私钥文件上传成功');
                            await fetchProjects(); // 刷新项目状态
                        } else {
                            ElMessage.error(result.error);
                        }
                    } catch (error) {
                        ElMessage.error('上传失败: ' + error.message);
                    }
                };

                const buildProject = async (projectId) => {
                    try {
                        const environment = selectedEnvironments.value[projectId];
                        if (!environment) {
                            ElMessage.warning('请选择环境');
                            return;
                        }

                        if (!processingStates.value[projectId]) {
                            processingStates.value[projectId] = {};
                        }
                        processingStates.value[projectId].build = true;

                        addLog(projectId, 'info', `开始构建项目 (${getEnvironmentName(environment)})`);

                        const response = await fetch(`/api/projects/${projectId}/build`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({ environment })
                        });

                        const result = await response.json();

                        if (result.success) {
                            addLog(projectId, 'info', '构建命令已发送');
                        } else {
                            addLog(projectId, 'error', '构建失败: ' + result.error);
                            processingStates.value[projectId].build = false;
                        }
                    } catch (error) {
                        addLog(projectId, 'error', '构建失败: ' + error.message);
                        if (processingStates.value[projectId]) {
                            processingStates.value[projectId].build = false;
                        }
                    }
                };

                const showUploadDialog = (projectId) => {
                    currentProjectId.value = projectId;
                    uploadForm.version = '';
                    uploadForm.desc = '';
                    uploadDialogVisible.value = true;
                };

                const uploadProject = async () => {
                    try {
                        const projectId = currentProjectId.value;
                        const environment = selectedEnvironments.value[projectId];

                        if (!processingStates.value[projectId]) {
                            processingStates.value[projectId] = {};
                        }
                        processingStates.value[projectId].upload = true;

                        uploadDialogVisible.value = false;

                        addLog(projectId, 'info', `开始上传到 ${getEnvironmentName(environment)}`);

                        const response = await fetch(`/api/projects/${projectId}/upload`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                environment,
                                version: uploadForm.version,
                                desc: uploadForm.desc
                            })
                        });

                        const result = await response.json();

                        if (result.success) {
                            addLog(projectId, 'info', '上传命令已发送');
                        } else {
                            addLog(projectId, 'error', '上传失败: ' + result.error);
                            processingStates.value[projectId].upload = false;
                        }
                    } catch (error) {
                        addLog(currentProjectId.value, 'error', '上传失败: ' + error.message);
                        if (processingStates.value[currentProjectId.value]) {
                            processingStates.value[currentProjectId.value].upload = false;
                        }
                    }
                };

                const showPreviewDialog = (projectId) => {
                    currentProjectId.value = projectId;
                    previewForm.pagePath = '';
                    previewForm.searchQuery = '';
                    previewDialogVisible.value = true;
                };

                const generatePreview = async () => {
                    try {
                        const projectId = currentProjectId.value;
                        const environment = selectedEnvironments.value[projectId];

                        if (!processingStates.value[projectId]) {
                            processingStates.value[projectId] = {};
                        }
                        processingStates.value[projectId].preview = true;

                        previewDialogVisible.value = false;

                        addLog(projectId, 'info', `开始生成预览 (${getEnvironmentName(environment)})`);

                        const response = await fetch(`/api/projects/${projectId}/preview`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                environment,
                                pagePath: previewForm.pagePath,
                                searchQuery: previewForm.searchQuery
                            })
                        });

                        const result = await response.json();

                        if (result.success) {
                            addLog(projectId, 'info', '预览生成命令已发送');
                        } else {
                            addLog(projectId, 'error', '预览生成失败: ' + result.error);
                            processingStates.value[projectId].preview = false;
                        }
                    } catch (error) {
                        addLog(currentProjectId.value, 'error', '预览生成失败: ' + error.message);
                        if (processingStates.value[currentProjectId.value]) {
                            processingStates.value[currentProjectId.value].preview = false;
                        }
                    }
                };

                // 文件上传处理
                const triggerFileUpload = (projectId) => {
                    const input = document.querySelector(`input[ref="fileInput-${projectId}"]`);
                    if (input) input.click();
                };

                const handleFileSelect = (event, projectId) => {
                    const file = event.target.files[0];
                    if (file) {
                        uploadPrivateKey(projectId, file);
                    }
                };

                const handleDragOver = (event) => {
                    event.target.classList.add('dragover');
                };

                const handleDragLeave = (event) => {
                    event.target.classList.remove('dragover');
                };

                const handleFileDrop = (event, projectId) => {
                    event.target.classList.remove('dragover');
                    const file = event.dataTransfer.files[0];
                    if (file) {
                        uploadPrivateKey(projectId, file);
                    }
                };

                // ==================== 项目管理方法 ====================

                const refreshProjects = () => {
                    fetchProjects();
                };

                const showCreateDialog = () => {
                    projectDialogMode.value = 'create';
                    resetProjectForm();
                    projectDialogVisible.value = true;
                };

                const showEditDialog = (project) => {
                    projectDialogMode.value = 'edit';
                    fillProjectForm(project);
                    projectDialogVisible.value = true;
                };

                const resetProjectForm = () => {
                    Object.assign(projectForm, {
                        id: '',
                        name: '',
                        appid: '',
                        projectPath: 'path.resolve(__dirname, "../../dist/build/mp-weixin")',
                        privateKeyPath: '',
                        defaultTenantId: '194338',
                        ignoresText: 'node_modules/**/*',
                        environments: {
                            develop: {
                                name: '开发版',
                                desc: '开发环境版本',
                                buildCommand: 'pnpm run build:mp-weixin --mode test',
                                tenantId: ''
                            },
                            trial: {
                                name: '体验版',
                                desc: '体验环境版本',
                                buildCommand: 'pnpm run build:mp-weixin --mode trial',
                                tenantId: ''
                            }
                        }
                    });
                };

                const fillProjectForm = (project) => {
                    Object.assign(projectForm, {
                        id: project.id,
                        name: project.name,
                        appid: project.appid,
                        projectPath: project.projectPath,
                        privateKeyPath: project.privateKeyPath,
                        defaultTenantId: project.defaultTenantId || '194338',
                        ignoresText: (project.ignores || []).join('\n'),
                        environments: { ...project.environments }
                    });
                };

                const addEnvironment = () => {
                    const envKey = prompt('请输入环境标识（develop/trial/release）:');
                    if (envKey && !projectForm.environments[envKey]) {
                        projectForm.environments[envKey] = {
                            name: getEnvironmentName(envKey),
                            desc: `${getEnvironmentName(envKey)}版本`,
                            buildCommand: 'pnpm run build:mp-weixin --mode test',
                            tenantId: ''
                        };
                    }
                };

                const removeEnvironment = (envKey) => {
                    delete projectForm.environments[envKey];
                };

                const saveProject = async () => {
                    try {
                        const projectData = {
                            ...projectForm,
                            ignores: projectForm.ignoresText.split('\n').filter(line => line.trim())
                        };
                        delete projectData.ignoresText;

                        let response;
                        if (projectDialogMode.value === 'create') {
                            response = await fetch('/api/projects', {
                                method: 'POST',
                                headers: { 'Content-Type': 'application/json' },
                                body: JSON.stringify(projectData)
                            });
                        } else {
                            const { id, ...updateData } = projectData;
                            response = await fetch(`/api/projects/${id}`, {
                                method: 'PUT',
                                headers: { 'Content-Type': 'application/json' },
                                body: JSON.stringify(updateData)
                            });
                        }

                        const result = await response.json();
                        if (result.success) {
                            ElMessage.success(projectDialogMode.value === 'create' ? '项目创建成功' : '项目更新成功');
                            projectDialogVisible.value = false;
                            await fetchProjects();
                        } else {
                            ElMessage.error(result.error);
                        }
                    } catch (error) {
                        ElMessage.error('操作失败: ' + error.message);
                    }
                };

                const handleProjectAction = async ({ action, project }) => {
                    switch (action) {
                        case 'edit':
                            showEditDialog(project);
                            break;
                        case 'clone':
                            projectDialogMode.value = 'create';
                            fillProjectForm(project);
                            projectForm.id = project.id + '_copy';
                            projectForm.name = project.name + ' (副本)';
                            projectDialogVisible.value = true;
                            break;
                        case 'delete':
                            try {
                                await ElMessageBox.confirm(
                                    `确定要删除项目 "${project.name}" 吗？此操作不可恢复。`,
                                    '删除确认',
                                    { type: 'warning' }
                                );

                                const response = await fetch(`/api/projects/${project.id}`, {
                                    method: 'DELETE'
                                });

                                const result = await response.json();
                                if (result.success) {
                                    ElMessage.success('项目删除成功');
                                    await fetchProjects();
                                } else {
                                    ElMessage.error(result.error);
                                }
                            } catch (error) {
                                if (error !== 'cancel') {
                                    ElMessage.error('删除失败: ' + error.message);
                                }
                            }
                            break;
                    }
                };

                const showBackupDialog = async () => {
                    try {
                        const response = await fetch('/api/config/backups');
                        const result = await response.json();
                        if (result.success) {
                            backups.value = result.data;
                            backupDialogVisible.value = true;
                        } else {
                            ElMessage.error(result.error);
                        }
                    } catch (error) {
                        ElMessage.error('获取备份列表失败: ' + error.message);
                    }
                };

                const restoreBackup = async (backupName) => {
                    try {
                        await ElMessageBox.confirm(
                            `确定要恢复备份 "${backupName}" 吗？当前配置将被覆盖。`,
                            '恢复确认',
                            { type: 'warning' }
                        );

                        const response = await fetch('/api/config/restore', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({ backupName })
                        });

                        const result = await response.json();
                        if (result.success) {
                            ElMessage.success('配置恢复成功');
                            backupDialogVisible.value = false;
                            await fetchProjects();
                        } else {
                            ElMessage.error(result.error);
                        }
                    } catch (error) {
                        if (error !== 'cancel') {
                            ElMessage.error('恢复失败: ' + error.message);
                        }
                    }
                };

                const exportConfig = async () => {
                    try {
                        const response = await fetch('/api/projects/all');
                        const result = await response.json();
                        if (result.success) {
                            const dataStr = JSON.stringify(result.data, null, 2);
                            const dataBlob = new Blob([dataStr], { type: 'application/json' });
                            const url = URL.createObjectURL(dataBlob);
                            const link = document.createElement('a');
                            link.href = url;
                            link.download = `ci-config-${new Date().toISOString().slice(0, 10)}.json`;
                            link.click();
                            URL.revokeObjectURL(url);
                            ElMessage.success('配置导出成功');
                        } else {
                            ElMessage.error(result.error);
                        }
                    } catch (error) {
                        ElMessage.error('导出失败: ' + error.message);
                    }
                };

                const reloadConfig = async () => {
                    try {
                        const response = await fetch('/api/config/reload', {
                            method: 'POST'
                        });
                        const result = await response.json();
                        if (result.success) {
                            ElMessage.success(`配置重载成功，共加载 ${result.data.projectCount} 个项目`);
                            await fetchProjects(); // 重新获取项目列表
                        } else {
                            ElMessage.error(result.error);
                        }
                    } catch (error) {
                        ElMessage.error('重载配置失败: ' + error.message);
                    }
                };

                // WebSocket 连接
                const connectWebSocket = () => {
                    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                    const wsUrl = `${protocol}//${window.location.host}`;

                    ws = new WebSocket(wsUrl);

                    ws.onopen = () => {
                        console.log('WebSocket 连接已建立');
                    };

                    ws.onmessage = (event) => {
                        const message = JSON.parse(event.data);
                        handleWebSocketMessage(message);
                    };

                    ws.onclose = () => {
                        console.log('WebSocket 连接已断开，尝试重连...');
                        setTimeout(connectWebSocket, 3000);
                    };

                    ws.onerror = (error) => {
                        console.error('WebSocket 错误:', error);
                    };
                };

                const handleWebSocketMessage = (message) => {
                    const { type, projectId } = message;

                    switch (type) {
                        case 'buildStarted':
                            addLog(projectId, 'info', '构建已开始...');
                            break;
                        case 'buildOutput':
                            addLog(projectId, 'info', message.output.trim());
                            break;
                        case 'buildError':
                            addLog(projectId, 'error', message.error.trim());
                            break;
                        case 'buildCompleted':
                            if (processingStates.value[projectId]) {
                                processingStates.value[projectId].build = false;
                            }
                            if (message.success) {
                                addLog(projectId, 'success', '构建完成！');
                                fetchProjects(); // 刷新项目状态
                            } else {
                                addLog(projectId, 'error', `构建失败 (退出码: ${message.exitCode})`);
                            }
                            break;
                        case 'uploadStarted':
                            addLog(projectId, 'info', '上传已开始...');
                            break;
                        case 'uploadOutput':
                            addLog(projectId, 'info', message.output.trim());
                            break;
                        case 'uploadError':
                            addLog(projectId, 'error', message.error.trim());
                            break;
                        case 'uploadCompleted':
                            if (processingStates.value[projectId]) {
                                processingStates.value[projectId].upload = false;
                            }
                            if (message.success) {
                                addLog(projectId, 'success', '上传完成！');
                            } else {
                                addLog(projectId, 'error', `上传失败 (退出码: ${message.exitCode})`);
                            }
                            break;
                        case 'previewStarted':
                            addLog(projectId, 'info', '预览生成已开始...');
                            break;
                        case 'previewOutput':
                            addLog(projectId, 'info', message.output.trim());
                            break;
                        case 'previewError':
                            addLog(projectId, 'error', message.error.trim());
                            break;
                        case 'previewCompleted':
                            if (processingStates.value[projectId]) {
                                processingStates.value[projectId].preview = false;
                            }
                            if (message.success) {
                                addLog(projectId, 'success', '预览生成完成！');
                                // 加载预览二维码
                                const environment = selectedEnvironments.value[projectId];
                                previewQRCodes.value[projectId] = `/api/projects/${projectId}/preview-qrcode?environment=${environment}&t=${Date.now()}`;
                            } else {
                                addLog(projectId, 'error', `预览生成失败 (退出码: ${message.exitCode})`);
                            }
                            break;
                        case 'privateKeyUploaded':
                            addLog(projectId, 'success', `私钥文件已上传: ${message.filename}`);
                            break;
                        case 'projectCreated':
                            console.log('项目已创建:', message.projectId);
                            fetchProjects(); // 刷新项目列表
                            break;
                        case 'projectUpdated':
                            console.log('项目已更新:', message.projectId);
                            fetchProjects(); // 刷新项目列表
                            break;
                        case 'projectDeleted':
                            console.log('项目已删除:', message.projectId);
                            fetchProjects(); // 刷新项目列表
                            break;
                        case 'configReloaded':
                            console.log('配置已重新加载');
                            fetchProjects(); // 刷新项目列表
                            break;
                        case 'configRestored':
                            console.log('配置已恢复:', message.backupName);
                            fetchProjects(); // 刷新项目列表
                            break;
                    }
                };

                // 生命周期
                onMounted(() => {
                    fetchProjects();
                    connectWebSocket();
                });

                onUnmounted(() => {
                    if (ws) {
                        ws.close();
                    }
                });

                return {
                    projects,
                    loading,
                    loadingText,
                    logs,
                    selectedEnvironments,
                    previewQRCodes,
                    uploadDialogVisible,
                    previewDialogVisible,
                    projectDialogVisible,
                    backupDialogVisible,
                    projectDialogMode,
                    uploadForm,
                    previewForm,
                    projectForm,
                    projectRules,
                    backups,
                    getEnvironmentName,
                    formatTime,
                    formatFileSize,
                    getLogClass,
                    isProcessing,
                    clearLogs,
                    buildProject,
                    showUploadDialog,
                    uploadProject,
                    showPreviewDialog,
                    generatePreview,
                    triggerFileUpload,
                    handleFileSelect,
                    handleDragOver,
                    handleDragLeave,
                    handleFileDrop,
                    refreshProjects,
                    showCreateDialog,
                    saveProject,
                    handleProjectAction,
                    showBackupDialog,
                    restoreBackup,
                    exportConfig,
                    reloadConfig,
                    addEnvironment,
                    removeEnvironment
                };
            }
        }).use(ElementPlus)
        .component('Plus', Plus)
        .component('Refresh', Refresh)
        .component('FolderOpened', FolderOpened)
        .component('Download', Download)
        .component('Edit', Edit)
        .component('CopyDocument', CopyDocument)
        .component('Delete', Delete)
        .component('More', More)
        .component('Upload', Upload)
        .component('Loading', Loading)
        .component('UploadFilled', UploadFilled)
        .mount('#app');
    </script>
</body>
</html>
