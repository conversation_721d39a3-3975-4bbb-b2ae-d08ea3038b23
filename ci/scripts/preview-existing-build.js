#!/usr/bin/env node

/**
 * 使用现有构建产物生成预览
 * 跳过构建步骤，直接使用已有的构建产物
 */

const ci = require('miniprogram-ci');
const path = require('path');
const fs = require('fs');
const { projects, defaultConfig, getProject, validateProject } = require('../config/config-loader.js');

// 命令行参数解析
const args = process.argv.slice(2);

// 解析命令行参数
function parseArgs(args) {
  const result = {
    projectName: null,
    environment: null,
    pagePath: null,
    searchQuery: null
  };

  let positionalIndex = 0;
  let i = 0;
  
  while (i < args.length) {
    const arg = args[i];
    
    if (arg === '--page' && i + 1 < args.length) {
      result.pagePath = args[i + 1];
      i += 2;
    } else if (arg === '--query' && i + 1 < args.length) {
      result.searchQuery = args[i + 1];
      i += 2;
    } else if (arg === '--help' || arg === '-h') {
      result.showHelp = true;
      i++;
    } else if (!arg.startsWith('--')) {
      // 位置参数
      if (positionalIndex === 0) {
        result.projectName = arg;
      } else if (positionalIndex === 1) {
        result.environment = arg;
      }
      positionalIndex++;
      i++;
    } else {
      i++;
    }
  }

  // 设置默认值
  result.projectName = result.projectName || 'main';
  result.environment = result.environment || 'develop';

  return result;
}

const parsedArgs = parseArgs(args);
const projectName = parsedArgs.projectName;
const environment = parsedArgs.environment;
const pagePath = parsedArgs.pagePath;
const searchQuery = parsedArgs.searchQuery;

// 颜色输出
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

/**
 * 生成预览二维码
 */
async function generatePreview() {
  try {
    // 验证项目配置
    const validation = validateProject(projectName);
    if (!validation.valid) {
      logError(validation.error);
      return;
    }

    const project = getProject(projectName);
    const envConfig = project.environments[environment];
    
    if (!envConfig) {
      logError(`环境 "${environment}" 不存在`);
      logInfo(`可用环境: ${Object.keys(project.environments).join(', ')}`);
      return;
    }

    logInfo('='.repeat(50));
    logInfo('小程序预览工具 (使用现有构建)');
    logInfo('='.repeat(50));
    logInfo(`项目名称: ${project.name}`);
    logInfo(`项目 AppID: ${project.appid}`);
    logInfo(`部署环境: ${envConfig.name}`);
    if (pagePath) {
      logInfo(`预览页面: ${pagePath}`);
    }
    logInfo('');

    // 检查构建产物是否存在
    const projectPath = path.resolve(__dirname, '../../dist/build/mp-weixin');
    if (!fs.existsSync(projectPath)) {
      logError('构建产物不存在，请先运行构建命令');
      logInfo('可以运行: pnpm run build:mp-weixin');
      return;
    }

    // 检查私钥文件
    if (!fs.existsSync(project.privateKeyPath)) {
      logError(`私钥文件不存在: ${project.privateKeyPath}`);
      return;
    }

    logInfo('使用现有构建产物生成预览...');

    // 确保预览目录存在
    const previewDir = path.resolve(__dirname, '../preview');
    if (!fs.existsSync(previewDir)) {
      fs.mkdirSync(previewDir, { recursive: true });
      logInfo(`创建预览目录: ${previewDir}`);
    }

    // 创建项目实例
    const miniProgram = new ci.Project({
      appid: project.appid,
      type: 'miniProgram',
      projectPath: projectPath,
      privateKeyPath: project.privateKeyPath,
      ignores: project.ignores || defaultConfig.ignores
    });

    // 构建预览选项
    const previewOptions = {
      project: miniProgram,
      version: defaultConfig.version.auto ? 
        new Date().toLocaleString('zh-CN', { 
          year: 'numeric', 
          month: '2-digit', 
          day: '2-digit', 
          hour: '2-digit', 
          minute: '2-digit' 
        }).replace(/[^\d]/g, '.').slice(0, -1) : 
        '0.0.1',
      desc: `${project.name} - ${envConfig.name} 预览`,
      robot: defaultConfig.preview.robot,
      qrcodeFormat: 'image',
      qrcodeOutputDest: path.resolve(__dirname, '../preview/preview-existing-build.jpg'),
      onProgressUpdate: (progress) => {
        logInfo(`生成进度: ${JSON.stringify(progress)}%`);
      },
      // 启用压缩
      minify: true
    };

    // 添加页面路径参数
    if (pagePath) {
      previewOptions.pagePath = pagePath;
      if (searchQuery) {
        previewOptions.searchQuery = searchQuery;
      }
    }

    // 生成预览
    const result = await ci.preview(previewOptions);
    
    logSuccess('预览生成成功！');
    logInfo(`二维码已保存到: ${previewOptions.qrcodeOutputDest}`);
    
    if (result.subPackageInfo && result.subPackageInfo.length > 0) {
      logInfo('分包信息:');
      result.subPackageInfo.forEach(pkg => {
        logInfo(`  ${pkg.name}: ${pkg.size}KB`);
      });
    }

  } catch (error) {
    logError(`预览生成失败: ${error.message}`);
    
    // 提供详细的错误信息和解决建议
    if (error.message.includes('exceed max limit')) {
      logError('包大小超出限制，建议:');
      console.log('  1. 运行包大小分析: node ci/scripts/analyze-package-size.js --suggestions');
      console.log('  2. 将部分功能移到分包');
      console.log('  3. 压缩图片和静态资源');
      console.log('  4. 移除未使用的代码和依赖');
    }
  }
}

/**
 * 显示帮助信息
 */
function showHelp() {
  log('小程序预览工具 (使用现有构建)', 'cyan');
  log('');
  log('使用方法:', 'yellow');
  log('  node ci/scripts/preview-existing-build.js [项目名] [环境] [选项]', 'white');
  log('');
  log('参数:', 'yellow');
  log('  项目名    项目标识符 (默认: main)', 'white');
  log('  环境      部署环境 (默认: develop)', 'white');
  log('');
  log('选项:', 'yellow');
  log('  --page <路径>    指定预览页面路径', 'white');
  log('  --query <参数>   页面查询参数', 'white');
  log('  --help, -h       显示帮助信息', 'white');
  log('');
  log('示例:', 'yellow');
  log('  node ci/scripts/preview-existing-build.js main develop', 'white');
  log('  node ci/scripts/preview-existing-build.js main develop --page pageA/home', 'white');
  log('');
}

// 主函数
async function main() {
  try {
    // 显示帮助信息
    if (parsedArgs.showHelp) {
      showHelp();
      return;
    }

    await generatePreview();
  } catch (error) {
    logError(`执行失败: ${error.message}`);
    process.exit(1);
  }
}

// 运行主函数
if (require.main === module) {
  main();
}
