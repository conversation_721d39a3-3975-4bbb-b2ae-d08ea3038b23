<template>
  <view class="head" v-if="category != 'recovery' && category != 'room'">
    <view class="head1">
      <view class="head1_1"></view>
      <view class="head1_2">大家都在问</view>
    </view>
    <view class="head2"></view>
  </view>
  <view class="roomComments" v-if="questionList.length == 0">暂无数据</view>
  <view
    :style="{
      background:
        category != 'recovery' && category != 'room' ? 'white' : '#f4f6ff',
    }"
    class="next"
    v-if="questionList.length > 0"
    v-for="(item, index) in questionList"
    :key="index"
  >
    <button
      size="mini"
      @click="handleContact"
      :style="{
        width: '100%',
        background:
          category != 'recovery' && category != 'room' ? '#FFF3F7' : '#F8F9F9',
        padding: '0',
        textAlign: 'right',
      }"
    >
      <view class="next3">
        <view class="next1">
          <view class="next-img">
            <image
              src="http://cdn.xiaodingdang1.com/2024/09/04/c81e0c80ad804a6bbb98f7360299ff5d.png"
              mode=""
              style="width: 40rpx; height: 40rpx"
            />
          </view>
          <view class="next1_1">
            {{ item.questionText }}
          </view>
        </view>
        <view class="next2">
          <image
            src="http://cdn.xiaodingdang1.com/1/120240115/2ee8ae21-7a8f-45d9-a490-fc0879a0d56b.png"
            mode=""
            style="width: 12rpx; height: 18rpx"
          />
        </view>
      </view>
    </button>
  </view>
</template>

<script setup>
// 问题列表咨询客服
import { ref, reactive, onMounted, computed, getCurrentInstance } from 'vue';
const instance = getCurrentInstance();
const App = instance.appContext.config.globalProperties;
const props = defineProps({
  category: {
    type: String,
    default: 'recovery',
  },
});
const mapList = new Map([
  ['recovery', '产后康复'],
  ['room', '月子套餐'],
  ['meal_package', '月子膳食'],
  ['nanny', '移动月嫂'],
]);
const questionList = reactive([]);
const SessionFrom = ref('');
onMounted(async () => {
  SessionFrom.value =
    uni.getStorageSync('tenantId') +
    ',' +
    uni.getStorageSync('userInfo').nickname +
    ',' +
    mapList.get(props.category);
  await questionListFun();
});
const questionListFun = async () => {
  //查询问题列表
  let data = {
    pageSize: 1000,
    pageNum: 1,
    category: props.category,
  };
  const res = await App.$axios.get(App.$api.questionList, data);
  if (res.data.code == 200) {
    Object.assign(questionList, res.data.data);
  }
};

const handleContact = () => {
  uni.navigateTo({
    url: '/subPackages/customerService/pages/userChat',
  });
};
</script>

<style lang="less" scoped>
.head {
  display: flex;
  justify-content: space-between;
  padding: 0;
}

.head1 {
  display: flex;
  align-items: center;
}

.head2 {
  display: flex;
  align-items: center;
}

.head1_1 {
  width: 10rpx;
  height: 30rpx;
  background: #ff4f61;
  border-radius: 5rpx;
}

.head1_2 {
  font-size: 34rpx;
  font-weight: bold;
  color: #333333;
  margin-left: 20rpx;
}

.head2_1 {
  color: #aaaaaa;
  font-size: 22rpx;
  margin-right: 10rpx;
}

.roomComments {
  color: #aaaaaa;
  font-size: 28rpx;
  font-weight: Medium;
  text-align: center;
  padding: 60rpx 0;
}

.next {
  // background-color: #f4f6ff;
  // padding: 12rpx 20rpx;
  padding: 0 10rpx;
  border-radius: 10rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20rpx;
}

.next:first-child {
  margin-top: 0;
}

.next3 {
  padding: 0 24rpx;
  display: flex;
  justify-content: space-between;
}

.next1 {
  display: flex;
  align-items: center;
  width: 90%;
}

.next1_1 {
  width: 90%;
  margin-left: 12rpx;
  color: #333333;
  font-size: 28rpx;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  text-align: left;
}

.next2 {
  width: 20%;
}

.next-img {
  width: 40rpx;
  height: 40rpx;
  position: relative;
  bottom: 3rpx;
  left: 0rpx;
}
</style>
