#!/usr/bin/env node

/**
 * 重新生成CI配置文件
 * 从projects.json重新生成ci.config.js
 */

const ConfigManager = require('../config/manager.js');
const JsonConfigManager = require('../config/json-manager.js');

// 颜色输出
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

/**
 * 重新生成配置文件
 */
async function regenerateConfig() {
  log('🔄 重新生成CI配置文件', 'blue');
  log('='.repeat(50));

  try {
    // 创建配置管理器实例
    const configManager = new ConfigManager();
    const jsonManager = new JsonConfigManager();

    // 读取JSON配置
    logInfo('读取projects.json配置...');
    const jsonResult = jsonManager.readConfig();
    
    if (!jsonResult.success) {
      logError(`读取JSON配置失败: ${jsonResult.error}`);
      return false;
    }

    logSuccess('JSON配置读取成功');
    
    // 显示当前配置信息
    const { projects } = jsonResult.data;
    logInfo(`找到 ${Object.keys(projects).length} 个项目:`);
    
    Object.entries(projects).forEach(([projectId, project]) => {
      console.log(`  - ${projectId}: ${project.name}`);
      console.log(`    AppID: ${project.appid}`);
      console.log(`    私钥路径: ${project.privateKeyPath}`);
      console.log(`    默认租户ID: ${project.defaultTenantId || '未设置'}`);
    });

    // 生成新的ci.config.js
    logInfo('生成新的ci.config.js文件...');
    const generateResult = configManager.writeConfig(jsonResult.data);

    if (!generateResult.success) {
      logError(`生成配置失败: ${generateResult.error}`);
      return false;
    }

    logSuccess('ci.config.js文件生成成功');
    logInfo(`配置文件路径: ${configManager.configPath}`);

    // 验证生成的配置
    logInfo('验证生成的配置...');
    try {
      // 重新加载配置验证
      delete require.cache[require.resolve('../config/ci.config.js')];
      const newConfig = require('../config/ci.config.js');
      
      logSuccess('配置文件验证通过');
      logInfo(`包含项目: ${Object.keys(newConfig.projects).join(', ')}`);
      
    } catch (error) {
      logError(`配置文件验证失败: ${error.message}`);
      return false;
    }

    logSuccess('🎉 配置文件重新生成完成！');
    return true;

  } catch (error) {
    logError(`重新生成配置失败: ${error.message}`);
    return false;
  }
}

/**
 * 显示帮助信息
 */
function showHelp() {
  log('CI配置文件重新生成工具', 'blue');
  log('');
  log('使用方法:', 'yellow');
  log('  node ci/scripts/regenerate-config.js', 'white');
  log('');
  log('功能说明:', 'yellow');
  log('  - 从projects.json重新生成ci.config.js', 'white');
  log('  - 更新私钥文件路径格式', 'white');
  log('  - 添加租户ID配置', 'white');
  log('  - 验证生成的配置文件', 'white');
  log('');
}

// 主函数
async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--help') || args.includes('-h')) {
    showHelp();
    return;
  }

  const success = await regenerateConfig();
  process.exit(success ? 0 : 1);
}

// 运行主函数
if (require.main === module) {
  main().catch(error => {
    logError(`脚本执行失败: ${error.message}`);
    process.exit(1);
  });
}

module.exports = {
  regenerateConfig
};
