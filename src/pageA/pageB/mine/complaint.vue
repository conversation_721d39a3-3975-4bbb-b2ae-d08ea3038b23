<template>
    <view>
        <view class="main">
            <view class="headTitle">
                <view class="headTitle1">我要反馈</view>
                <view class="headTitle2" @tap="history">历史反馈</view>
            </view>
            <view class="coment">
                <view class="container-comment">
                    <textarea :value="inputValue" @input="onInput" :maxlength="maxLength" />
                    <view class="wordNumber">
                        <view>{{ inputValue.length }}/{{ maxLength }}</view>
                    </view>
                </view>
                <view class="uploading">
                    <view class="uploadingImg" v-for="(item, index) in imgUrl" :key="index">
                        <image :src="item" mode="" style="width: 198rpx; height: 198rpx; border: 1rpx solid #c2bfbf"
                            @tap="previewImage" :data-src="item" :data-url="imgUrl" />

                        <icon class="imgCancel" type="clear" size="25" :data-index="index"
                            @tap.stop.prevent="deleteImg"></icon>
                    </view>
                    <!-- <image src="http://cdn.xiaodingdang1.com/Group%2048098148%403x.png" mode="" style="width: 200rpx;height: 200rpx;" bindtap="chooseImg"/> -->
                    <view class="addImg1" @tap="chooseImg" v-if="imgUrl.length < 6">
                        <image src="http://cdn.xiaodingdang1.com/2024/05/07/866cb0bda2354e2e99cbe6c6f66572e7png" mode=""
                            style="width: 84rpx; height: 84rpx; margin-top: 40rpx" />
                        <view class="addImg">添加图</view>
                    </view>
                </view>
            </view>
        </view>
        <view class="btn">
            <view class="btn1" @tap="add">提交</view>
        </view>
    </view>
</template>

<script>
    export default {
        data() {
            return {
                inputValue: '',
                maxLength: 500,
                // 设置最大字数
                imgUrl: [],
                contentPhotos: [],
                photosShow: false,
                videosShow: false
            };
        }
        /**
         * 生命周期函数--监听页面加载
         */
        ,
        onLoad(options) {},
        /**
         * 生命周期函数--监听页面初次渲染完成
         */
        onReady() {},
        /**
         * 生命周期函数--监听页面显示
         */
        onShow() {},
        /**
         * 生命周期函数--监听页面隐藏
         */
        onHide() {},
        /**
         * 生命周期函数--监听页面卸载
         */
        onUnload() {},
        /**
         * 页面相关事件处理函数--监听用户下拉动作
         */
        onPullDownRefresh() {},
        /**
         * 页面上拉触底事件的处理函数
         */
        onReachBottom() {},
        /**
         * 用户点击右上角分享
         */
        onShareAppMessage() {},

        onShareTimeline() {},
        methods: {
            onInput(e) {
                this.setData({
                    inputValue: e.detail.value
                });
            },

            // 删除图片
            deleteImg: function(e) {
                var contentPhotos = this.contentPhotos;
                var imgUrl = this.imgUrl;
                var index = e.currentTarget.dataset.index;
                contentPhotos.splice(index, 1);
                imgUrl.splice(index, 1);
                if (imgUrl.length == 0) {
                    this.setData({
                        photosShow: true,
                        videosShow: true
                    });
                }
                this.setData({
                    contentPhotos: contentPhotos,
                    imgUrl: imgUrl
                });
            },

            // 上传图片
            chooseImg: function(e) {
                var that = this;
                var contentPhotos = this.contentPhotos;
                if (contentPhotos.length >= 6) {
                    uni.showToast({
                        icon: 'none',
                        title: '最多上传6张图片'
                    });
                    return false;
                }
                uni.chooseImage({
                    // count: 1, // 默认9
                    sizeType: ['compressed'],
                    // 可以指定是原图还是压缩图，默认二者都有
                    sourceType: ['album', 'camera'],
                    // 可以指定来源是相册还是相机，默认二者都有
                    success: function(res) {
                        var tempFilePaths = res.tempFilePaths;
                        var contentPhotos = that.contentPhotos;
                        let picNum = tempFilePaths.length;
                        uni.showLoading({
                            title: '图片上传中'
                        });
                        for (var i = 0; i < tempFilePaths.length; i++) {
                            if (contentPhotos.length >= 6) {
                                that.setData({
                                    contentPhotos: contentPhotos
                                });
                                return false;
                            } else {
                                contentPhotos.push(tempFilePaths[i]);
                                uni.uploadFile({
                                    filePath: tempFilePaths[i],
                                    name: 'file',
                                    url: that.$api.uploadOSS,
                                    formData: {},
                                    header: {
                                        'Content-Type': 'multipart/form-data',
                                        Authorization: 'Bearer ' + uni.getStorageSync('token'),
                                        'Accept-Encoding': 'gzip',
                                        Clientid: '428a8310cd442757ae699df5d894f051'
                                    },
                                    success: function(rests) {
                                        let data = JSON.parse(rests.data);
                                        if (data.code == 200) {
                                            uni.hideLoading();
                                            let datas = that.imgUrl;
                                            datas.push(data.data.url);
                                            that.setData({
                                                imgUrl: datas
                                            });
                                        }
                                    }
                                });
                            }
                        }
                        // 返回选定照片的本地文件路径列表，tempFilePath可以作为img标签的src属性显示图片
                        that.setData({
                            contentPhotos: contentPhotos
                        });
                    }
                });
            },

            history() {
                //历史反馈
                uni.navigateTo({
                    url: '/pageA/pageB/mine/complaint/history'
                });
            },

            add() {
                //提交
                if (this.inputValue == '') {
                    uni.showToast({
                        icon: 'none',
                        title: '请填写反馈内容'
                    });
                    return;
                }
                this.getInsert();
            },

            getInsert() {
                //提交
                let data = {
                    content: this.inputValue
                };
                if (this.imgUrl.length > 0) {
                    data.contentPhotos = this.imgUrl;
                }
                this.$axios.post(this.$api.insert, data).then((res) => {
                    if (res.data.code == 200) {
                        uni.showToast({
                            title: '反馈成功,客服正在处理中',
                            icon: 'none',
                            duration: 2000 //持续的时间
                        });
                        setTimeout(() => {
                            uni.navigateBack({
                                delta: 1 // 返回的页面数，如果 delta 大于现有页面数，则返回到首页。
                            });
                        }, 2000);
                    }
                });
            },

            previewImage() {
                console.log('占位：函数 previewImage 未声明');
            }
        }
    };
</script>
<style scoped>
    @import './complaint.css';
</style>