.main{
	background: #F3F3F3;
	height: 100vh;
	padding: 24rpx 24rpx;
	.content{
		background: #ffffff;
		padding: 40rpx 24rpx;
		border-radius: 18rpx;
		height: 85vh;
		.content1{
			display: flex;
			justify-content: space-between;
			align-items: center;
			font-size: 28rpx;
			font-weight: bold;
			margin-bottom: 50rpx;
			.content1_1{
				color: #828282;
			}
			.content1_2{
				display: flex;
				.content1_3{
					color: #333333;
				}
				.content1_4{
					margin-left: 10rpx;
				}
			}
		}
	}
	.addBtn{
		background: #1FA2FF;
		border-radius: 20rpx;
		color: #ffffff;
        font-size: 28rpx;
		height: 88rpx;
		line-height: 88rpx;
		text-align: center;
		margin-top: 40rpx;
	}
	.textarea{
		background: #F7F7F7;
		border-radius: 20rpx;
		padding: 24rpx 24rpx;
	}
}