.main {
    /* background: #f8f9f9; */
}

.mains {
    padding: 20rpx 30rpx;
}

.list {
    background: #ffffff;
    display: flex;
    margin-bottom: 40rpx;
    align-items: center;
}

.title {
    font-size: 20rpx;
    color: #656565;
}

image {
    height: 86rpx;
    width: 86rpx;
    border-radius: 86rpx;
    margin-right: 22rpx;
}

.content {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.flex {
    padding: 4rpx 0;
    display: flex;
    align-items: center;
}

.left {
    line-height: 30rpx;
}

.name {
    font-weight: 500;
    font-size: 28rpx;
    color: #000000;
    margin-right: 16rpx;
}

.tag {
    background: #FFEEF0;
    border-radius: 44rpx;
    font-weight: bold;
    font-size: 14rpx;
    color: #FF4F61;
    padding: 4rpx 12rpx;
}

.right {
    width: 140rpx;
    height: 54rpx;
    background: #FF4F61;
    border-radius: 29rpx;
    font-weight: 500;
    font-size: 24rpx;
    color: #FFFFFF;
    text-align: center;
    line-height: 54rpx;
}