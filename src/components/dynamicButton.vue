<template>
  <view class="dynamicTitle1_3">
    <view class="dynamicTitle1_3_1">
      <!-- {{ dyData.endTime }} -->
    </view>
    <view style="padding-left: 30rpx" @tap="open">
      <image
        src="http://cdn.xiaodingdang1.com/2025/04/02/dc81a9205c3347959ae6f59ee3705e41.png"
        mode=""
        style="width: 48rpx; height: 28rpx"
      />
    </view>
    <view class="like" v-if="show">
      <view class="like1" v-if="buttonArray.includes('delete')" @tap="onDelete">
        <image src="../static/images/icons/icon_delete.png" mode=""> </image>
        删除
      </view>
      <button class="share-button" open-type="share">
        <view class="like1">
          <image src="../static/images/icons/icon_share.png" mode=""> </image>
          分享
        </view>
      </button>
      <view class="like1" @tap="onPoster" v-if="buttonArray.includes('poster')">
        <u-icon name="pushpin" size="32" color="#ffffff"></u-icon>
        海报
      </view>
      <view class="like1" @tap="onPushMessage" v-if="buttonArray.includes('pushMessage')">
        <u-icon name="chat" size="32" color="#ffffff"></u-icon>
        消息推送
      </view>
    </view>
    <!-- 移除 shadow 遮罩层，改为通过滚动事件关闭菜单 -->
  </view>
</template>

<script>
export default {
  props: {
    dyData: {
      type: Object,
      default: () => {},
    },
    // 需要展示的按钮数组
    buttonArray: {
      type: Array,
      default: () => ['share'],
    },
  },
  data() {
    return {
      show: false,
      pushMessageIf: false,
    };
  },
  onshow() {
	this.onPushMessage();
    // let roles = uni.getStorageSync('roles');
    // this.pushMessageIf =true
    //   // roles[0] == 'SALES' || roles[0] == 'ADMIN' ? true : false;
  },
  mounted() {
    // 监听页面滚动事件，当滚动时关闭菜单
    this.addScrollListener();
  },
  beforeDestroy() {
    // 移除滚动监听
    this.removeScrollListener();
  },
  methods: {
    onPoster() {
      this.$emit('onPoster', this.dyData);
      this.show = false;
    },
    onPushMessage() {
      let momId = this.dyData.momId;
      let diaryId = this.dyData.diaryId;
      uni.navigateTo({
        url:
          '/pageA/pageB/community/pushMessage/pushMessage?diaryId=' +
          diaryId +
          '&momId=' +
          momId +
          '&receivingType=3',
      });
      this.show = false;
    },
    open() {
      this.show = true;
    },
    onDelete() {
      this.$emit('onDelete', this.dyData);
      this.show = false;
    },
    shadow() {
      this.show = false;
    },
    // 暴露关闭方法给父组件调用
    closeMenu() {
      this.shadow();
    },
    // 添加滚动监听
    addScrollListener() {
      // 使用 uni.$on 监听全局滚动事件
      uni.$on('pageScroll', this.handlePageScroll);
    },
    // 移除滚动监听
    removeScrollListener() {
      // 移除全局滚动事件监听
      uni.$off('pageScroll', this.handlePageScroll);
    },
    // 处理页面滚动事件
    handlePageScroll() {
      // 如果菜单是打开的，则关闭它
      if (this.show) {
        this.shadow();
      }
    },
  },
};
</script>

<style lang="less" scoped>
.share-button {
  font-size: 28rpx;
  color: #ffffff;
  background: #4c4c4c;
  font-size: 0rpx;
  margin: 0;
  padding: 0rpx;
  height: 50rpx;
  line-height: 50rpx;
}

.share-button::after {
  padding: 0;
  margin: 0;
}

.dynamicTitle1_3 {
  margin-top: 6rpx;
  margin-bottom: 6rpx;
  display: flex;
  justify-content: space-between;
  position: relative;
  align-items: center;
  padding: 0 4rpx;
}

.dynamicTitle1_3_1 {
  font-size: 18rpx;
  color: #777777;
}

.like {
  // width: 260rpx;
  height: 50rpx;
  background: #4c4c4c;
  border-radius: 4rpx;
  display: flex;
  background: #4c4c4c;
  align-items: center;
  font-weight: 400;
  font-size: 28rpx;
  color: #ffffff;
  position: absolute;
  top: -14rpx;
  right: 50rpx;
  z-index: 1001;

  image {
    width: 26rpx;
    height: 26rpx;
  }

  &:not(:last-child) {
    margin-right: 15rpx;
  }
}

.like1 {
  display: flex;
  align-items: center;
  padding: 0 15rpx;
  justify-content: center;
  font-size: 28rpx;
  color: #ffffff;

  image {
    margin-right: 8rpx;
  }
}

.like2 {
  display: flex;
  align-items: center;
  width: 50%;
  justify-content: center;
}

.like1_1 {
  margin-left: 4rpx;
}

/* 移除 shadow 样式，不再需要遮罩层 */
</style>
