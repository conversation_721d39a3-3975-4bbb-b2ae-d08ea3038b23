#!/usr/bin/env node

/**
 * 测试预览路径配置
 * 创建一个模拟的二维码文件来验证路径配置
 */

const fs = require('fs');
const path = require('path');

// 颜色输出
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

/**
 * 创建模拟预览二维码文件
 */
function createMockPreviewFiles() {
  log('🧪 创建模拟预览二维码文件', 'blue');
  log('='.repeat(50));
  
  const previewDir = path.resolve(__dirname, '../preview');
  
  // 确保目录存在
  if (!fs.existsSync(previewDir)) {
    fs.mkdirSync(previewDir, { recursive: true });
    logInfo(`创建预览目录: ${previewDir}`);
  }
  
  // 创建模拟文件
  const mockFiles = [
    'preview-main-develop.jpg',
    'preview-main-trial.jpg',
    'preview-xinxiyue-develop.jpg'
  ];
  
  mockFiles.forEach(filename => {
    const filePath = path.join(previewDir, filename);
    const mockContent = `Mock QR Code for ${filename}\nGenerated at: ${new Date().toISOString()}`;
    
    fs.writeFileSync(filePath, mockContent, 'utf8');
    logSuccess(`创建模拟文件: ${filename}`);
  });
  
  // 列出目录内容
  logInfo('\n预览目录内容:');
  const files = fs.readdirSync(previewDir);
  files.forEach(file => {
    const filePath = path.join(previewDir, file);
    const stats = fs.statSync(filePath);
    const size = stats.size;
    const mtime = stats.mtime.toLocaleString();
    console.log(`  ${file} (${size} bytes, ${mtime})`);
  });
  
  logSuccess('\n模拟文件创建完成！');
  logInfo('可以通过以下命令查看文件:');
  logInfo(`ls -la ${previewDir}`);
  
  // 清理提示
  log('\n💡 清理命令:', 'yellow');
  log(`rm ${path.join(previewDir, '*.jpg')}`, 'yellow');
}

/**
 * 清理模拟文件
 */
function cleanupMockFiles() {
  log('🧹 清理模拟预览文件', 'blue');
  log('='.repeat(50));
  
  const previewDir = path.resolve(__dirname, '../preview');
  
  if (!fs.existsSync(previewDir)) {
    logInfo('预览目录不存在，无需清理');
    return;
  }
  
  const files = fs.readdirSync(previewDir);
  const jpgFiles = files.filter(file => file.endsWith('.jpg'));
  
  if (jpgFiles.length === 0) {
    logInfo('没有找到 .jpg 文件，无需清理');
    return;
  }
  
  jpgFiles.forEach(file => {
    const filePath = path.join(previewDir, file);
    fs.unlinkSync(filePath);
    logSuccess(`删除文件: ${file}`);
  });
  
  logSuccess(`清理完成，删除了 ${jpgFiles.length} 个文件`);
}

/**
 * 主函数
 */
function main() {
  const args = process.argv.slice(2);
  const command = args[0];
  
  switch (command) {
    case 'create':
      createMockPreviewFiles();
      break;
    case 'cleanup':
      cleanupMockFiles();
      break;
    default:
      log('使用方法:', 'yellow');
      log('  node test-preview-path.js create   - 创建模拟预览文件', 'yellow');
      log('  node test-preview-path.js cleanup  - 清理模拟预览文件', 'yellow');
      break;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = {
  createMockPreviewFiles,
  cleanupMockFiles
};
