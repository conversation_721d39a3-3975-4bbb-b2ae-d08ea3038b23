.main {
    background: url('http://cdn.xiaodingdang1.com/1/120240127/cca26a05-d435-4557-ae33-affb0c93ccd7.png') no-repeat;
    background-size: 100% 468rpx;
    padding: 174rpx 0rpx 24rpx;
}


.loadImg.active {
    transition: all 3s ease-in-out;
    opacity: 1
}



.mainss {
    padding: 0 24rpx;
}

.next {
    display: flex;
    position: fixed;
    top: 120rpx;
    width: 100%;
}

.headImg {
    width: 41%;
}

.headTitle {
    font-size: 34rpx;
    color: #ffffff;
    font-weight: bold;
}

.headTitle1 {
    display: flex;
    align-items: center;
    margin-top: 56rpx;
}

.headTitle1_1 {
    color: #ffffff;
    font-size: 32rpx;
    font-weight: bold;
    margin-left: 16rpx;
}

.topic1Text3 {
    display: flex;
    justify-content: space-between;
    margin-right: 12rpx;
    width: 100%;
    align-items: center;
}

.topicImg {
    width: 50%;
    display: flex;
    align-items: center;
}

.topic1Text3_1 {
    color: #ffffff;
    font-size: 24rpx;
    margin: 34rpx 0 30rpx 40rpx;
}

.topic1Text3_2 {
    display: flex;
    align-items: center;
}

.topic1Text3topic1Text3 {
    width: 40rpx;
    height: 40rpx;
    border: none;
    border-radius: 100rpx;
}

.subscription {
    color: #ff4f61;
    padding: 8rpx 28rpx;
    background: #fff;
    border-radius: 8rpx;
    font-size: 28rpx;
}

.subscribed {
    display: flex;
    align-items: center;
    background: #ff4f61;
    border-radius: 8rpx;
    color: #fff;
    font-size: 28rpx;
    padding: 8rpx 20rpx;
}

.contentTab {
    display: flex;
    align-items: center;
    padding: 24rpx 20rpx;
    margin-top: 20rpx;
}

.title-sel-selected {
    color: #333333;
    font-size: 32rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    font-weight: bold;
    margin-right: 36rpx;
}

.title-sel-selected .line-style {
    background: #ff4f61;
    height: 6rpx;
    width: 40rpx;
    position: relative;
    margin-top: 10rpx;
    border-radius: 20rpx;
}

.title-sel {
    color: #aaaaaa;
    font-size: 32rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    font-weight: bold;
    height: 56rpx;
    margin-right: 36rpx;
}

/*宝妈讨论*/
.discuss {
    background: #ffffff;
    margin-bottom: 32rpx;
}

.discus {
    /* padding: 24rpx 24rpx 0 24rpx; */
}

.discuss1 {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.discuss1_1 {
    display: flex;
}

.discuss1_2 {
    margin-left: 16rpx;
}

.discuss1_3 {
    color: hsla(26, 100%, 50%, 1);
    font-size: 30rpx;
}

.discuss1_4 {
    font-size: 24rpx;
    color: hsla(0, 0%, 67%, 1);
    margin-top: 4rpx;
}

.discussName {
    margin-left: 24rpx;
}

.discussTitle {
    color: hsla(0, 0%, 47%, 1);
    font-size: 24rpx;
}

.discuss2 {
    margin-top: 16rpx;
    font-size: 28rpx;
}

.discuss2_1 {
    color: hsla(228, 25%, 48%, 1);
}

.discuss2_2 {
    color: hsla(0, 0%, 20%, 1);
}

.discuss3 {
    display: flex;
    flex-wrap: wrap;
    margin: 24rpx 0;
}

.discuss3_1 {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: 15rpx;
    margin-right: 10rpx;
}

.discuss1_5 {
    display: flex;
    align-items: center;
}

.discuss4 {
    width: 100%;
    height: 1rpx;
    background: hsla(0, 0%, 92%, 1);
}

.discuss5 {
    display: flex;
    justify-content: space-around;
    padding: 26rpx 0 32rpx 0;
    color: hsla(0, 0%, 47%, 1);
    font-size: 24rpx;
}

.discuss5_2 {
    margin-left: 4rpx;
}

.blank {
    height: 150rpx;
}

.dynamic {
    background: #ff4f61;
    width: 198rpx;
    height: 84rpx;
    text-align: center;
    line-height: 84rpx;
    border-radius: 110rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    position: fixed;
    bottom: 120rpx;
    left: 37%;
    color: #ffffff;
    font-size: 32rpx;
}

.avatar-list-stacked {
    margin-right: 10rpx;
}

.avatar-list-stacked .avatar {}

.avatar-list-stacked .avatar {
    margin-right: -0.8em !important;
}

.avatar {
    width: 1.5rem;
    height: 1.5rem;
    line-height: 5rem;
    display: inline-block;
    background: #2ddcd3 no-repeat center/cover;
    position: relative;
    text-align: center;
    color: #fff;
    font-weight: 600;
    vertical-align: bottom;
    font-size: 0.875rem;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    border-radius: 50%;
}

.img_box {
    margin-top: 20rpx;
    padding-left: 4rpx;
}

.videos {
    width: 340rpx;
    height: 340rpx;
}

.videos video {
    width: 100%;
    height: 100%;
}

.img_box .many_img {
    display: flex;
    justify-self: start;
    flex-wrap: wrap;
	width: 100%;
}

.img_item.four {
    width: 32%;
    height: 220rpx;
    margin-bottom: 10rpx;
    border-radius: 16rpx;
    overflow: hidden;
}

.img_item.four image {
    width: 100%;
    height: 100%;
}

.img_item.four:nth-child(2) {
    margin: 0 1%;
}

.img_item.four:nth-child(5) {
    margin: 0 1%;
}

.img_item.four:nth-child(8) {
    margin: 0 1%;
}

.img_item.many {
    width: 48%;
    height: 340rpx;
    margin-bottom: 10rpx;
    border-radius: 10px;
    overflow: hidden;
}

.img_item.many image {
    width: 100%;
    height: 100%;
}

.img_item.many:nth-child(2n) {
    margin-left: 1%;
}

.discusss5 {
    color: hsla(0, 0%, 47%, 1);
    font-size: 28rpx;
    font-weight: 500;
}

.custom-button {
    background: #fff;
    color: hsla(0, 0%, 47%, 1);
    font-size: 24rpx;
    margin: 0;
}

.discuss5_1 {
    display: flex;
    align-items: center;
    justify-content: center;
}